import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PlayCircleFilledSvg from "@ant-design/icons-svg/es/asn/PlayCircleFilled";
import AntdIcon from "../components/AntdIcon";
var PlayCircleFilled = function PlayCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PlayCircleFilledSvg
  }));
};

/**![play-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNDQuMSA0NTQuOUw0MzcuNyA2NzcuOGE4LjAyIDguMDIgMCAwMS0xMi43LTYuNVYzNTMuN2E4IDggMCAwMTEyLjctNi41TDY1Ni4xIDUwNmE3LjkgNy45IDAgMDEwIDEyLjl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(PlayCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PlayCircleFilled';
}
export default RefIcon;