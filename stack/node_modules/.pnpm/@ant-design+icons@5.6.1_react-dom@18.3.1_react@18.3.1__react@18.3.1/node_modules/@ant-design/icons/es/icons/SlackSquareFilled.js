import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SlackSquareFilledSvg from "@ant-design/icons-svg/es/asn/SlackSquareFilled";
import AntdIcon from "../components/AntdIcon";
var SlackSquareFilled = function SlackSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SlackSquareFilledSvg
  }));
};

/**![slack-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTI5IDMxMS40YzAtMjcuOCAyMi41LTUwLjQgNTAuMy01MC40IDI3LjggMCA1MC4zIDIyLjYgNTAuMyA1MC40djEzNC40YzAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40LTI3LjggMC01MC4zLTIyLjYtNTAuMy01MC40VjMxMS40ek0zNjEuNSA1ODAuMmMwIDI3LjgtMjIuNSA1MC40LTUwLjMgNTAuNGE1MC4zNSA1MC4zNSAwIDAxLTUwLjMtNTAuNGMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGg1MC4zdjUwLjR6bTEzNCAxMzQuNGMwIDI3LjgtMjIuNSA1MC40LTUwLjMgNTAuNC0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNFY1ODAuMmMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGE1MC4zNSA1MC4zNSAwIDAxNTAuMyA1MC40djEzNC40em0tNTAuMi0yMTguNGgtMTM0Yy0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNCAwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGgxMzRjMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjQtLjEgMjcuOS0yMi42IDUwLjQtNTAuMyA1MC40em0wLTEzNC40Yy0xMy4zIDAtMjYuMS01LjMtMzUuNi0xNC44UzM5NSAzMjQuOCAzOTUgMzExLjRjMC0yNy44IDIyLjUtNTAuNCA1MC4zLTUwLjQgMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjR2NTAuNGgtNTAuM3ptMTM0IDQwMy4yYy0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNHYtNTAuNGg1MC4zYzI3LjggMCA1MC4zIDIyLjYgNTAuMyA1MC40IDAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40em0xMzQtMTM0LjRoLTEzNGE1MC4zNSA1MC4zNSAwIDAxLTUwLjMtNTAuNGMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGgxMzRjMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjQgMCAyNy44LTIyLjUgNTAuNC01MC4zIDUwLjR6bTAtMTM0LjRINjYzdi01MC40YzAtMjcuOCAyMi41LTUwLjQgNTAuMy01MC40czUwLjMgMjIuNiA1MC4zIDUwLjRjMCAyNy44LTIyLjUgNTAuNC01MC4zIDUwLjR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SlackSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlackSquareFilled';
}
export default RefIcon;