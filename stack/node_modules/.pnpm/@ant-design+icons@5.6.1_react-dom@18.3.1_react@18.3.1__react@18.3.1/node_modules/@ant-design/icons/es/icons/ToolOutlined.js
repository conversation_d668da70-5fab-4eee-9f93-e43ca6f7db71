import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ToolOutlinedSvg from "@ant-design/icons-svg/es/asn/ToolOutlined";
import AntdIcon from "../components/AntdIcon";
var ToolOutlined = function ToolOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ToolOutlinedSvg
  }));
};

/**![tool](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3Ni42IDIzOS41Yy0uNS0uOS0xLjItMS44LTItMi41LTUtNS0xMy4xLTUtMTguMSAwTDY4NC4yIDQwOS4zbC02Ny45LTY3LjlMNzg4LjcgMTY5Yy44LS44IDEuNC0xLjYgMi0yLjUgMy42LTYuMSAxLjYtMTMuOS00LjUtMTcuNS05OC4yLTU4LTIyNi44LTQ0LjctMzExLjMgMzkuNy02NyA2Ny04OS4yIDE2Mi02Ni41IDI0Ny40bC0yOTMgMjkzYy0zIDMtMi44IDcuOS4zIDExbDE2OS43IDE2OS43YzMuMSAzLjEgOC4xIDMuMyAxMSAuM2wyOTIuOS0yOTIuOWM4NS41IDIyLjggMTgwLjUuNyAyNDcuNi02Ni40IDg0LjQtODQuNSA5Ny43LTIxMy4xIDM5LjctMzExLjN6TTc4NiA0OTkuOGMtNTguMSA1OC4xLTE0NS4zIDY5LjMtMjE0LjYgMzMuNmwtOC44IDguOC0uMS0uMS0yNzQgMjc0LjEtNzkuMi03OS4yIDIzMC4xLTIzMC4xczAgLjEuMS4xbDUyLjgtNTIuOGMtMzUuNy02OS4zLTI0LjUtMTU2LjUgMzMuNi0yMTQuNmExODQuMiAxODQuMiAwIDAxMTQ0LTUzLjVMNTM3IDMxOC45YTMyLjA1IDMyLjA1IDAgMDAwIDQ1LjNsMTI0LjUgMTI0LjVhMzIuMDUgMzIuMDUgMCAwMDQ1LjMgMGwxMzIuOC0xMzIuOGMzLjcgNTEuOC0xNC40IDEwNC44LTUzLjYgMTQzLjl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ToolOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ToolOutlined';
}
export default RefIcon;