import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UserDeleteOutlinedSvg from "@ant-design/icons-svg/es/asn/UserDeleteOutlined";
import AntdIcon from "../components/AntdIcon";
var UserDeleteOutlined = function UserDeleteOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: UserDeleteOutlinedSvg
  }));
};

/**![user-delete](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3OC4zIDY1NS40YzI0LjItMTMgNTEuOS0yMC40IDgxLjQtMjAuNGguMWMzIDAgNC40LTMuNiAyLjItNS42YTM3MS42NyAzNzEuNjcgMCAwMC0xMDMuNy02NS44Yy0uNC0uMi0uOC0uMy0xLjItLjVDNzE5LjIgNTE4IDc1OS42IDQ0NC43IDc1OS42IDM2MmMwLTEzNy0xMTAuOC0yNDgtMjQ3LjUtMjQ4UzI2NC43IDIyNSAyNjQuNyAzNjJjMCA4Mi43IDQwLjQgMTU2IDEwMi42IDIwMS4xLS40LjItLjguMy0xLjIuNS00NC43IDE4LjktODQuOCA0Ni0xMTkuMyA4MC42YTM3My40MiAzNzMuNDIgMCAwMC04MC40IDExOS41QTM3My42IDM3My42IDAgMDAxMzcgOTAxLjhhOCA4IDAgMDA4IDguMmg1OS45YzQuMyAwIDcuOS0zLjUgOC03LjggMi03Ny4yIDMyLjktMTQ5LjUgODcuNi0yMDQuM0MzNTcgNjQxLjIgNDMyLjIgNjEwIDUxMi4yIDYxMGM1Ni43IDAgMTExLjEgMTUuNyAxNTggNDUuMWE4LjEgOC4xIDAgMDA4LjEuM3pNNTEyLjIgNTM0Yy00NS44IDAtODguOS0xNy45LTEyMS40LTUwLjRBMTcxLjIgMTcxLjIgMCAwMTM0MC41IDM2MmMwLTQ1LjkgMTcuOS04OS4xIDUwLjMtMTIxLjZTNDY2LjMgMTkwIDUxMi4yIDE5MHM4OC45IDE3LjkgMTIxLjQgNTAuNEExNzEuMiAxNzEuMiAwIDAxNjgzLjkgMzYyYzAgNDUuOS0xNy45IDg5LjEtNTAuMyAxMjEuNkM2MDEuMSA1MTYuMSA1NTggNTM0IDUxMi4yIDUzNHpNODgwIDc3Mkg2NDBjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoMjQwYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(UserDeleteOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UserDeleteOutlined';
}
export default RefIcon;