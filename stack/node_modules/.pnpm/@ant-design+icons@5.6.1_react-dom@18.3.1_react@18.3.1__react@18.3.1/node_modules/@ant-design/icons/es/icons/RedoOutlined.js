import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RedoOutlinedSvg from "@ant-design/icons-svg/es/asn/RedoOutlined";
import AntdIcon from "../components/AntdIcon";
var RedoOutlined = function RedoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RedoOutlinedSvg
  }));
};

/**![redo](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc1OC4yIDgzOS4xQzg1MS44IDc2NS45IDkxMiA2NTEuOSA5MTIgNTIzLjkgOTEyIDMwMyA3MzMuNSAxMjQuMyA1MTIuNiAxMjQgMjkxLjQgMTIzLjcgMTEyIDMwMi44IDExMiA1MjMuOWMwIDEyNS4yIDU3LjUgMjM2LjkgMTQ3LjYgMzEwLjIgMy41IDIuOCA4LjYgMi4yIDExLjQtMS4zbDM5LjQtNTAuNWMyLjctMy40IDIuMS04LjMtMS4yLTExLjEtOC4xLTYuNi0xNS45LTEzLjctMjMuNC0yMS4yYTMxOC42NCAzMTguNjQgMCAwMS02OC42LTEwMS43QzIwMC40IDYwOSAxOTIgNTY3LjEgMTkyIDUyMy45czguNC04NS4xIDI1LjEtMTI0LjVjMTYuMS0zOC4xIDM5LjItNzIuMyA2OC42LTEwMS43IDI5LjQtMjkuNCA2My42LTUyLjUgMTAxLjctNjguNkM0MjYuOSAyMTIuNCA0NjguOCAyMDQgNTEyIDIwNHM4NS4xIDguNCAxMjQuNSAyNS4xYzM4LjEgMTYuMSA3Mi4zIDM5LjIgMTAxLjcgNjguNiAyOS40IDI5LjQgNTIuNSA2My42IDY4LjYgMTAxLjcgMTYuNyAzOS40IDI1LjEgODEuMyAyNS4xIDEyNC41cy04LjQgODUuMS0yNS4xIDEyNC41YTMxOC42NCAzMTguNjQgMCAwMS02OC42IDEwMS43Yy05LjMgOS4zLTE5LjEgMTgtMjkuMyAyNkw2NjguMiA3MjRhOCA4IDAgMDAtMTQuMSAzbC0zOS42IDE2Mi4yYy0xLjIgNSAyLjYgOS45IDcuNyA5LjlsMTY3IC44YzYuNyAwIDEwLjUtNy43IDYuMy0xMi45bC0zNy4zLTQ3Ljl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(RedoOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RedoOutlined';
}
export default RefIcon;