import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import StarTwoToneSvg from "@ant-design/icons-svg/es/asn/StarTwoTone";
import AntdIcon from "../components/AntdIcon";
var StarTwoTone = function StarTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: StarTwoToneSvg
  }));
};

/**![star](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi41IDE5MC40bC05NC40IDE5MS4zLTIxMS4yIDMwLjcgMTUyLjggMTQ5LTM2LjEgMjEwLjMgMTg4LjktOTkuMyAxODguOSA5OS4yLTM2LjEtMjEwLjMgMTUyLjgtMTQ4LjktMjExLjItMzAuN3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTkwOC42IDM1Mi44bC0yNTMuOS0zNi45TDU0MS4yIDg1LjhjLTMuMS02LjMtOC4yLTExLjQtMTQuNS0xNC41LTE1LjgtNy44LTM1LTEuMy00Mi45IDE0LjVMMzcwLjMgMzE1LjlsLTI1My45IDM2LjljLTcgMS0xMy40IDQuMy0xOC4zIDkuM2EzMi4wNSAzMi4wNSAwIDAwLjYgNDUuM2wxODMuNyAxNzkuMUwyMzkgODM5LjRhMzEuOTUgMzEuOTUgMCAwMDQ2LjQgMzMuN2wyMjcuMS0xMTkuNCAyMjcuMSAxMTkuNGM2LjIgMy4zIDEzLjQgNC40IDIwLjMgMy4yIDE3LjQtMyAyOS4xLTE5LjUgMjYuMS0zNi45bC00My40LTI1Mi45IDE4My43LTE3OS4xYzUtNC45IDguMy0xMS4zIDkuMy0xOC4zIDIuNy0xNy41LTkuNS0zMy43LTI3LTM2LjN6TTY2NS4zIDU2MS4zbDM2LjEgMjEwLjMtMTg4LjktOTkuMi0xODguOSA5OS4zIDM2LjEtMjEwLjMtMTUyLjgtMTQ5IDIxMS4yLTMwLjcgOTQuNC0xOTEuMyA5NC40IDE5MS4zIDIxMS4yIDMwLjctMTUyLjggMTQ4Ljl6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(StarTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StarTwoTone';
}
export default RefIcon;