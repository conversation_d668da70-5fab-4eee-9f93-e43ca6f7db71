{"name": "@cropper/element", "version": "2.0.1", "description": "An abstract class for constructing Cropper elements.", "main": "dist/element.raw.js", "module": "dist/element.esm.raw.js", "types": "dist/element.d.ts", "unpkg": "dist/element.js", "jsdelivr": "dist/element.js", "files": ["dist"], "exports": {".": {"import": {"types": "./dist/element.d.ts", "node": {"production": "./dist/element.esm.min.js", "development": "./dist/element.esm.js", "default": "./dist/element.esm.raw.js"}, "default": "./dist/element.esm.raw.js"}, "require": {"types": "./dist/element.d.ts", "node": {"production": "./dist/element.min.js", "development": "./dist/element.js", "default": "./dist/element.raw.js"}, "default": "./dist/element.raw.js"}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "scripts": {"api-extractor": "api-extractor run --local --verbose", "build": "npm run tsc && npm run api-extractor && npm run rollup", "clean": "del-cli dist .temp", "release": "npm run clean && npm run build", "rollup": "rollup -c ../../rollup.config.js", "tsc": "tsc --outDir ./.temp --declaration --emitDeclarationOnly"}, "repository": {"type": "git", "url": "https://github.com/fengyuanchen/cropperjs.git", "directory": "packages/element"}, "keywords": ["element", "cropper", "cropper.js", "cropper-element", "custom-element", "web-component"], "author": "<PERSON> (https://chenfengyuan.com/)", "license": "MIT", "bugs": "https://github.com/fengyuanchen/cropperjs/issues", "homepage": "https://github.com/fengyuanchen/cropperjs/tree/main/packages/element/#readme", "dependencies": {"@cropper/utils": "^2.0.1"}, "publishConfig": {"access": "public"}, "gitHead": "6b8e5ff146012939d08b13fae360a1a6ad2dda29"}