!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).CropperElement=e()}(this,(function(){"use strict";const t="undefined"!=typeof window&&void 0!==window.document,e=t?window:{};t&&e.document.documentElement;const s=Number.isNaN||e.isNaN;const o=/([a-z\d])([A-Z])/g;function n(t){return String(t).replace(o,"$1-$2").toLowerCase()}const i=/-[A-z\d]/g;function r(t){return t.replace(i,(t=>t.slice(1).toUpperCase()))}const a={bubbles:!0,cancelable:!0,composed:!0};const c=Promise.resolve();const l=/left|top|width|height/i,h="open",d=new WeakMap,u=new WeakMap,p=new Map,m=e.document&&Array.isArray(e.document.adoptedStyleSheets)&&"replaceSync"in e.CSSStyleSheet.prototype;class b extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var t,e;super(),this.shadowRootMode=h,this.slottable=!0;const s=null===(e=null===(t=Object.getPrototypeOf(this))||void 0===t?void 0:t.constructor)||void 0===e?void 0:e.$name;s&&p.set(s,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,s){if(Object.is(s,e))return;const o=r(t);let n=s;switch(typeof this[o]){case"boolean":n=null!==s&&"false"!==s;break;case"number":n=Number(s)}switch(this[o]=n,t){case"theme-color":{const t=u.get(this),e=this.$sharedStyle;t&&e&&(m?t.replaceSync(e):t.textContent=e);break}}}$propertyChangedCallback(t,e,o){if(!Object.is(o,e))switch(t=n(t),typeof o){case"boolean":!0===o?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":o=s(o)?"":String(o);default:o?this.getAttribute(t)!==o&&this.setAttribute(t,o):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((t=>{const e=r(t);let s=this[e];(function(t){return void 0===t})(s)||this.$propertyChangedCallback(e,void 0,s),Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:()=>s,set(t){const o=s;s=t,this.$propertyChangedCallback(e,o,t)}})}));const t=this.attachShadow({mode:this.shadowRootMode||h});if(this.shadowRoot||d.set(this,t),u.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){u.has(this)&&u.delete(this),d.has(this)&&d.delete(this)}$getTagNameOf(t){var e;return null!==(e=p.get(t))&&void 0!==e?e:t}$setStyles(t){return Object.keys(t).forEach((e=>{let o=t[e];(function(t){return"number"==typeof t&&!s(t)})(o)&&(o=0!==o&&l.test(e)?`${o}px`:String(o)),this.style[e]=o})),this}$getShadowRoot(){return this.shadowRoot||d.get(this)}$addStyles(t){let e;const s=this.$getShadowRoot();return m?(e=new CSSStyleSheet,e.replaceSync(t),s.adoptedStyleSheets=s.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,s.appendChild(e)),e}$emit(t,e,s){return function(t,e,s,o){return t.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign(Object.assign({},a),{detail:s}),o)))}(this,t,e,s)}$nextTick(t){return function(t,e){return e?c.then(t?e.bind(t):e):c}(this,t)}static $define(s,o){var i;"object"==typeof(i=s)&&null!==i&&(o=s,s=""),s||(s=this.$name||this.name),s=n(s),t&&e.customElements&&!e.customElements.get(s)&&customElements.define(s,this,o)}}return b.$version="2.0.0",b}));
