export declare const ACTION_MOVE = "move";

export declare const ACTION_NONE = "none";

export declare const ACTION_RESIZE_EAST = "e-resize";

export declare const ACTION_RESIZE_NORTH = "n-resize";

export declare const ACTION_RESIZE_NORTHEAST = "ne-resize";

export declare const ACTION_RESIZE_NORTHWEST = "nw-resize";

export declare const ACTION_RESIZE_SOUTH = "s-resize";

export declare const ACTION_RESIZE_SOUTHEAST = "se-resize";

export declare const ACTION_RESIZE_SOUTHWEST = "sw-resize";

export declare const ACTION_RESIZE_WEST = "w-resize";

export declare const ACTION_ROTATE = "rotate";

export declare const ACTION_SCALE = "scale";

export declare const ACTION_SELECT = "select";

export declare const ACTION_TRANSFORM = "transform";

export declare const ATTRIBUTE_ACTION = "action";

export declare const CROPPER_CANVAS = "cropper-canvas";

export declare const CROPPER_CROSSHAIR = "cropper-crosshair";

export declare const CROPPER_GIRD = "cropper-grid";

export declare const CROPPER_HANDLE = "cropper-handle";

export declare const CROPPER_IMAGE = "cropper-image";

export declare const CROPPER_SELECTION = "cropper-selection";

export declare const CROPPER_SHADE = "cropper-shade";

export declare const CROPPER_VIEWER = "cropper-viewer";

/**
 * Dispatch event on the event target.
 * {@link https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent}
 * @param {EventTarget} target The target of the event.
 * @param {string} type The name of the event.
 * @param {*} [detail] The data passed when initializing the event.
 * @param {CustomEventInit} [options] The other event options.
 * @returns {boolean} Returns the result value.
 */
export declare function emit(target: EventTarget, type: string, detail?: unknown, options?: CustomEventInit): boolean;

export declare const EVENT_ACTION = "action";

export declare const EVENT_ACTION_END = "actionend";

export declare const EVENT_ACTION_MOVE = "actionmove";

export declare const EVENT_ACTION_START = "actionstart";

export declare const EVENT_CHANGE = "change";

export declare const EVENT_ERROR = "error";

export declare const EVENT_KEYDOWN = "keydown";

export declare const EVENT_LOAD = "load";

export declare const EVENT_POINTER_DOWN: string;

export declare const EVENT_POINTER_MOVE: string;

export declare const EVENT_POINTER_UP: string;

export declare const EVENT_RESIZE = "resize";

export declare const EVENT_TOUCH_END: string;

export declare const EVENT_TOUCH_MOVE: string;

export declare const EVENT_TOUCH_START: string;

export declare const EVENT_TRANSFORM = "transform";

export declare const EVENT_WHEEL = "wheel";

/**
 * Get the max sizes in a rectangle under the given aspect ratio.
 * @param {object} data The original sizes.
 * @param {string} [type] The adjust type.
 * @returns {object} Returns the result sizes.
 */
export declare function getAdjustedSizes(data: SizeAdjustmentData | SizeAdjustmentDataWithoutWidth | SizeAdjustmentDataWithoutHeight, type?: SizeAdjustmentType): {
    width: number;
    height: number;
};

/**
 * Get the offset base on the document.
 * @param {Element} element The target element.
 * @returns {object} The offset data.
 */
export declare function getOffset(element: Element): {
    left: number;
    top: number;
};

export declare const HAS_POINTER_EVENT: boolean;

export declare const IS_BROWSER: boolean;

export declare const IS_TOUCH_DEVICE: boolean;

/**
 * Check if the given node is an element.
 * @param {*} node The node to check.
 * @returns {boolean} Returns `true` if the given node is an element; otherwise, `false`.
 */
export declare function isElement(node: unknown): node is Element;

/**
 * Check if the given value is a function.
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if the given value is a function, else `false`.
 */
export declare function isFunction(value: unknown): value is (...args: unknown[]) => unknown;

/**
 * Check if the given value is not a number.
 */
declare const isNaN_2: (number: unknown) => boolean;
export { isNaN_2 as isNaN }

/**
 * Check if the given value is a number.
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if the given value is a number, else `false`.
 */
export declare function isNumber(value: unknown): value is number;

/**
 * Check if the given value is an object.
 * @param {*} value - The value to check.
 * @returns {boolean} Returns `true` if the given value is an object, else `false`.
 */
export declare function isObject(value: unknown): value is Record<string, unknown>;

/**
 * Check if the given value is a plain object.
 * @param {*} value - The value to check.
 * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.
 */
export declare function isPlainObject(value: unknown): value is Record<string, unknown>;

/**
 * Check if the given value is a positive number.
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.
 */
export declare function isPositiveNumber(value: unknown): value is number;

/**
 * Check if the given value is a string.
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if the given value is a string, else `false`.
 */
export declare function isString(value: unknown): value is string;

/**
 * Check if the given value is undefined.
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if the given value is undefined, else `false`.
 */
export declare function isUndefined(value: unknown): value is undefined;

/**
 * Multiply multiple matrices.
 * @param {Array} matrix The first matrix.
 * @param {Array} args The rest matrices.
 * @returns {Array} Returns the result matrix.
 */
export declare function multiplyMatrices(matrix: number[], ...args: number[][]): number[];

export declare const NAMESPACE = "cropper";

/**
 * Defers the callback to be executed after the next DOM update cycle.
 * @param {*} [context] The `this` context.
 * @param {Function} [callback] The callback to execute after the next DOM update cycle.
 * @returns {Promise} A promise that resolves to nothing.
 */
export declare function nextTick(context?: unknown, callback?: () => void): Promise<void>;

/**
 * Remove event listener from the event target.
 * {@link https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/removeEventListener}
 * @param {EventTarget} target The target of the event.
 * @param {string} types The types of the event.
 * @param {EventListenerOrEventListenerObject} listener The listener of the event.
 * @param {EventListenerOptions} [options] The options specify characteristics about the event listener.
 */
export declare function off(target: EventTarget, types: string, listener: EventListenerOrEventListenerObject, options?: EventListenerOptions): void;

/**
 * Add event listener to the event target.
 * {@link https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener}
 * @param {EventTarget} target The target of the event.
 * @param {string} types The types of the event.
 * @param {EventListenerOrEventListenerObject} listener The listener of the event.
 * @param {AddEventListenerOptions} [options] The options specify characteristics about the event listener.
 */
export declare function on(target: EventTarget, types: string, listener: EventListenerOrEventListenerObject, options?: AddEventListenerOptions): void;

/**
 * Add once event listener to the event target.
 * @param {EventTarget} target The target of the event.
 * @param {string} types The types of the event.
 * @param {EventListenerOrEventListenerObject} listener The listener of the event.
 * @param {AddEventListenerOptions} [options] The options specify characteristics about the event listener.
 */
export declare function once(target: EventTarget, types: string, listener: EventListenerOrEventListenerObject, options?: AddEventListenerOptions): void;

declare interface SizeAdjustmentData {
    aspectRatio: number;
    height: number;
    width: number;
}

declare interface SizeAdjustmentDataWithoutHeight {
    aspectRatio: number;
    width: number;
}

declare interface SizeAdjustmentDataWithoutWidth {
    aspectRatio: number;
    height: number;
}

declare type SizeAdjustmentType = 'contain' | 'cover';

/**
 * Convert an angle to a radian number.
 * {@link https://developer.mozilla.org/en-US/docs/Web/CSS/angle}
 * @param {number|string} angle The angle to convert.
 * @returns {number} Returns the radian number.
 */
export declare function toAngleInRadian(angle: number | string): number;

/**
 * Transform the given string from kebab-case to camelCase.
 * @param {string} value The value to transform.
 * @returns {string} Returns the transformed value.
 */
export declare function toCamelCase(value: string): string;

/**
 * Transform the given string from camelCase to kebab-case.
 * @param {string} value The value to transform.
 * @returns {string} Returns the transformed value.
 */
export declare function toKebabCase(value: string): string;

export declare const WINDOW: any;

export { }
