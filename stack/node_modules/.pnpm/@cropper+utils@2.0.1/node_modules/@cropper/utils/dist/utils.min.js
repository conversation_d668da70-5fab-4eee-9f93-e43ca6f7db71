!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).CropperUtils={})}(this,(function(e){"use strict";const t="undefined"!=typeof window&&void 0!==window.document,n=t?window:{},o=!!t&&"ontouchstart"in n.document.documentElement,i=!!t&&"PointerEvent"in n,r="cropper",s=`${r}-canvas`,c=`${r}-crosshair`,E=`${r}-grid`,u=`${r}-handle`,a=`${r}-image`,T=`${r}-selection`,O=`${r}-shade`,N=`${r}-viewer`,_=o?"touchend touchcancel":"mouseup",f=o?"touchmove":"mousemove",d=o?"touchstart":"mousedown",R=i?"pointerdown":d,l=i?"pointermove":f,C=i?"pointerup pointercancel":_;const p=Number.isNaN||n.isNaN;function I(e){return"number"==typeof e&&!p(e)}function A(e){return I(e)&&e>0&&e<1/0}function S(e){return"object"==typeof e&&null!==e}const{hasOwnProperty:m}=Object.prototype;const P=/([a-z\d])([A-Z])/g;const h=/-[A-z\d]/g;const g=/\s\s*/;function b(e,t,n,o){t.trim().split(g).forEach((t=>{e.addEventListener(t,n,o)}))}const V={bubbles:!0,cancelable:!0,composed:!0};const w=Promise.resolve();const v=/deg|g?rad|turn$/i;const y="contain";e.ACTION_MOVE="move",e.ACTION_NONE="none",e.ACTION_RESIZE_EAST="e-resize",e.ACTION_RESIZE_NORTH="n-resize",e.ACTION_RESIZE_NORTHEAST="ne-resize",e.ACTION_RESIZE_NORTHWEST="nw-resize",e.ACTION_RESIZE_SOUTH="s-resize",e.ACTION_RESIZE_SOUTHEAST="se-resize",e.ACTION_RESIZE_SOUTHWEST="sw-resize",e.ACTION_RESIZE_WEST="w-resize",e.ACTION_ROTATE="rotate",e.ACTION_SCALE="scale",e.ACTION_SELECT="select",e.ACTION_TRANSFORM="transform",e.ATTRIBUTE_ACTION="action",e.CROPPER_CANVAS=s,e.CROPPER_CROSSHAIR=c,e.CROPPER_GIRD=E,e.CROPPER_HANDLE=u,e.CROPPER_IMAGE=a,e.CROPPER_SELECTION=T,e.CROPPER_SHADE=O,e.CROPPER_VIEWER=N,e.EVENT_ACTION="action",e.EVENT_ACTION_END="actionend",e.EVENT_ACTION_MOVE="actionmove",e.EVENT_ACTION_START="actionstart",e.EVENT_CHANGE="change",e.EVENT_ERROR="error",e.EVENT_KEYDOWN="keydown",e.EVENT_LOAD="load",e.EVENT_POINTER_DOWN=R,e.EVENT_POINTER_MOVE=l,e.EVENT_POINTER_UP=C,e.EVENT_RESIZE="resize",e.EVENT_TOUCH_END=_,e.EVENT_TOUCH_MOVE=f,e.EVENT_TOUCH_START=d,e.EVENT_TRANSFORM="transform",e.EVENT_WHEEL="wheel",e.HAS_POINTER_EVENT=i,e.IS_BROWSER=t,e.IS_TOUCH_DEVICE=o,e.NAMESPACE=r,e.WINDOW=n,e.emit=function(e,t,n,o){return e.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign(Object.assign({},V),{detail:n}),o)))},e.getAdjustedSizes=function(e,t=y){const{aspectRatio:n}=e;let{width:o,height:i}=e;const r=A(o),s=A(i);if(r&&s){const e=i*n;t===y&&e>o||"cover"===t&&e<o?i=o/n:o=i*n}else r?i=o/n:s&&(o=i*n);return{width:o,height:i}},e.getOffset=function(e){const{documentElement:t}=e.ownerDocument,o=e.getBoundingClientRect();return{left:o.left+(n.pageXOffset-t.clientLeft),top:o.top+(n.pageYOffset-t.clientTop)}},e.isElement=function(e){return"object"==typeof e&&null!==e&&1===e.nodeType},e.isFunction=function(e){return"function"==typeof e},e.isNaN=p,e.isNumber=I,e.isObject=S,e.isPlainObject=function(e){if(!S(e))return!1;try{const{constructor:t}=e,{prototype:n}=t;return t&&n&&m.call(n,"isPrototypeOf")}catch(e){return!1}},e.isPositiveNumber=A,e.isString=function(e){return"string"==typeof e},e.isUndefined=function(e){return void 0===e},e.multiplyMatrices=function e(t,...n){if(0===n.length)return t;const[o,i,r,s,c,E]=t,[u,a,T,O,N,_]=n[0];return e(t=[o*u+r*a,i*u+s*a,o*T+r*O,i*T+s*O,o*N+r*_+c,i*N+s*_+E],...n.slice(1))},e.nextTick=function(e,t){return t?w.then(e?t.bind(e):t):w},e.off=function(e,t,n,o){t.trim().split(g).forEach((t=>{e.removeEventListener(t,n,o)}))},e.on=b,e.once=function(e,t,n,o){b(e,t,n,Object.assign(Object.assign({},o),{once:!0}))},e.toAngleInRadian=function(e){const t=parseFloat(e)||0;if(0!==t){const[n="rad"]=String(e).match(v)||[];switch(n.toLowerCase()){case"deg":return t/360*(2*Math.PI);case"grad":return t/400*(2*Math.PI);case"turn":return t*(2*Math.PI)}}return t},e.toCamelCase=function(e){return e.replace(h,(e=>e.slice(1).toUpperCase()))},e.toKebabCase=function(e){return String(e).replace(P,"$1-$2").toLowerCase()},Object.defineProperty(e,"__esModule",{value:!0})}));
