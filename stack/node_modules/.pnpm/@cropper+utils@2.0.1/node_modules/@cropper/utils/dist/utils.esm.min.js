const e="undefined"!=typeof window&&void 0!==window.document,t=e?window:{},n=!!e&&"ontouchstart"in t.document.documentElement,o=!!e&&"PointerEvent"in t,r="cropper",c=`${r}-canvas`,i=`${r}-crosshair`,s=`${r}-grid`,u=`${r}-handle`,a=`${r}-image`,f=`${r}-selection`,d=`${r}-shade`,p=`${r}-viewer`,l="select",h="move",m="scale",g="rotate",w="transform",v="none",b="n-resize",y="e-resize",z="s-resize",$="w-resize",O="ne-resize",E="nw-resize",j="se-resize",P="sw-resize",C="action",L=n?"touchend touchcancel":"mouseup",N=n?"touchmove":"mousemove",I=n?"touchstart":"mousedown",M=o?"pointerdown":I,A=o?"pointermove":N,R=o?"pointerup pointercancel":L,S="error",T="keydown",k="load",x="resize",B="wheel",D="action",F="actionend",U="actionmove",X="actionstart",Y="change",Z="transform";function q(e){return"string"==typeof e}const G=Number.isNaN||t.isNaN;function H(e){return"number"==typeof e&&!G(e)}function J(e){return H(e)&&e>0&&e<1/0}function K(e){return void 0===e}function Q(e){return"object"==typeof e&&null!==e}const{hasOwnProperty:V}=Object.prototype;function W(e){if(!Q(e))return!1;try{const{constructor:t}=e,{prototype:n}=t;return t&&n&&V.call(n,"isPrototypeOf")}catch(e){return!1}}function _(e){return"function"==typeof e}function ee(e){return"object"==typeof e&&null!==e&&1===e.nodeType}const te=/([a-z\d])([A-Z])/g;function ne(e){return String(e).replace(te,"$1-$2").toLowerCase()}const oe=/-[A-z\d]/g;function re(e){return e.replace(oe,(e=>e.slice(1).toUpperCase()))}const ce=/\s\s*/;function ie(e,t,n,o){t.trim().split(ce).forEach((t=>{e.removeEventListener(t,n,o)}))}function se(e,t,n,o){t.trim().split(ce).forEach((t=>{e.addEventListener(t,n,o)}))}function ue(e,t,n,o){se(e,t,n,Object.assign(Object.assign({},o),{once:!0}))}const ae={bubbles:!0,cancelable:!0,composed:!0};function fe(e,t,n,o){return e.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign(Object.assign({},ae),{detail:n}),o)))}const de=Promise.resolve();function pe(e,t){return t?de.then(e?t.bind(e):t):de}function le(e){const{documentElement:n}=e.ownerDocument,o=e.getBoundingClientRect();return{left:o.left+(t.pageXOffset-n.clientLeft),top:o.top+(t.pageYOffset-n.clientTop)}}const he=/deg|g?rad|turn$/i;function me(e){const t=parseFloat(e)||0;if(0!==t){const[n="rad"]=String(e).match(he)||[];switch(n.toLowerCase()){case"deg":return t/360*(2*Math.PI);case"grad":return t/400*(2*Math.PI);case"turn":return t*(2*Math.PI)}}return t}const ge="contain";function we(e,t=ge){const{aspectRatio:n}=e;let{width:o,height:r}=e;const c=J(o),i=J(r);if(c&&i){const e=r*n;t===ge&&e>o||"cover"===t&&e<o?r=o/n:o=r*n}else c?r=o/n:i&&(o=r*n);return{width:o,height:r}}function ve(e,...t){if(0===t.length)return e;const[n,o,r,c,i,s]=e,[u,a,f,d,p,l]=t[0];return ve(e=[n*u+r*a,o*u+c*a,n*f+r*d,o*f+c*d,n*p+r*l+i,o*p+c*l+s],...t.slice(1))}export{h as ACTION_MOVE,v as ACTION_NONE,y as ACTION_RESIZE_EAST,b as ACTION_RESIZE_NORTH,O as ACTION_RESIZE_NORTHEAST,E as ACTION_RESIZE_NORTHWEST,z as ACTION_RESIZE_SOUTH,j as ACTION_RESIZE_SOUTHEAST,P as ACTION_RESIZE_SOUTHWEST,$ as ACTION_RESIZE_WEST,g as ACTION_ROTATE,m as ACTION_SCALE,l as ACTION_SELECT,w as ACTION_TRANSFORM,C as ATTRIBUTE_ACTION,c as CROPPER_CANVAS,i as CROPPER_CROSSHAIR,s as CROPPER_GIRD,u as CROPPER_HANDLE,a as CROPPER_IMAGE,f as CROPPER_SELECTION,d as CROPPER_SHADE,p as CROPPER_VIEWER,D as EVENT_ACTION,F as EVENT_ACTION_END,U as EVENT_ACTION_MOVE,X as EVENT_ACTION_START,Y as EVENT_CHANGE,S as EVENT_ERROR,T as EVENT_KEYDOWN,k as EVENT_LOAD,M as EVENT_POINTER_DOWN,A as EVENT_POINTER_MOVE,R as EVENT_POINTER_UP,x as EVENT_RESIZE,L as EVENT_TOUCH_END,N as EVENT_TOUCH_MOVE,I as EVENT_TOUCH_START,Z as EVENT_TRANSFORM,B as EVENT_WHEEL,o as HAS_POINTER_EVENT,e as IS_BROWSER,n as IS_TOUCH_DEVICE,r as NAMESPACE,t as WINDOW,fe as emit,we as getAdjustedSizes,le as getOffset,ee as isElement,_ as isFunction,G as isNaN,H as isNumber,Q as isObject,W as isPlainObject,J as isPositiveNumber,q as isString,K as isUndefined,ve as multiplyMatrices,pe as nextTick,ie as off,se as on,ue as once,me as toAngleInRadian,re as toCamelCase,ne as toKebabCase};
