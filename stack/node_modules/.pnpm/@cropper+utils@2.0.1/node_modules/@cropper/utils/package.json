{"name": "@cropper/utils", "version": "2.0.1", "description": "A series of common constants and utility functions for Cropper.", "main": "dist/utils.raw.js", "module": "dist/utils.esm.raw.js", "types": "dist/utils.d.ts", "unpkg": "dist/utils.js", "jsdelivr": "dist/utils.js", "files": ["dist"], "exports": {".": {"import": {"types": "./dist/utils.d.ts", "node": {"production": "./dist/utils.esm.min.js", "development": "./dist/utils.esm.js", "default": "./dist/utils.esm.raw.js"}, "default": "./dist/utils.esm.raw.js"}, "require": {"types": "./dist/utils.d.ts", "node": {"production": "./dist/utils.min.js", "development": "./dist/utils.js", "default": "./dist/utils.raw.js"}, "default": "./dist/utils.raw.js"}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "scripts": {"api-extractor": "api-extractor run --local --verbose", "build": "npm run tsc && npm run api-extractor && npm run rollup", "clean": "del-cli dist .temp", "release": "npm run clean && npm run build", "rollup": "rollup -c ../../rollup.config.js", "tsc": "tsc --outDir ./.temp --declaration --emitDeclarationOnly"}, "repository": {"type": "git", "url": "https://github.com/fengyuanchen/cropperjs.git", "directory": "packages/utils"}, "keywords": ["utils", "utilities", "constants", "functions", "cropper", "cropper.js"], "author": "<PERSON> (https://chenfengyuan.com/)", "license": "MIT", "bugs": "https://github.com/fengyuanchen/cropperjs/issues", "homepage": "https://github.com/fengyuanchen/cropperjs/tree/main/packages/utils/#readme", "publishConfig": {"access": "public"}, "gitHead": "6b8e5ff146012939d08b13fae360a1a6ad2dda29"}