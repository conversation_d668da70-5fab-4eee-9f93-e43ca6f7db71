/*! Cropper.js v2.0.0 | (c) 2015-present <PERSON> | MIT */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Cropper={})}(this,(function(t){"use strict";const e="undefined"!=typeof window&&void 0!==window.document,i=e?window:{},s=!!e&&"ontouchstart"in i.document.documentElement,n=!!e&&"PointerEvent"in i,a="cropper",o=`${a}-canvas`,r=`${a}-crosshair`,h=`${a}-grid`,c=`${a}-handle`,l=`${a}-image`,d=`${a}-selection`,u=`${a}-shade`,$=`${a}-viewer`,p="select",g="move",m="scale",b="rotate",f="transform",v="none",C="n-resize",w="e-resize",y="s-resize",E="w-resize",S="ne-resize",A="nw-resize",T="se-resize",k="sw-resize",x="action",O=s?"touchend touchcancel":"mouseup",N=s?"touchmove":"mousemove",I=s?"touchstart":"mousedown",R=n?"pointerdown":I,z=n?"pointermove":N,M=n?"pointerup pointercancel":O,P="error",D="keydown",_="load",W="wheel",Y="action",L="actionend",X="actionmove",H="actionstart",j="change",V="transform";function U(t){return"string"==typeof t}const q=Number.isNaN||i.isNaN;function B(t){return"number"==typeof t&&!q(t)}function K(t){return B(t)&&t>0&&t<1/0}function Z(t){return void 0===t}function F(t){return"object"==typeof t&&null!==t}const{hasOwnProperty:G}=Object.prototype;function J(t){if(!F(t))return!1;try{const{constructor:e}=t,{prototype:i}=e;return e&&i&&G.call(i,"isPrototypeOf")}catch(t){return!1}}function Q(t){return"function"==typeof t}function tt(t){return"object"==typeof t&&null!==t&&1===t.nodeType}const et=/([a-z\d])([A-Z])/g;function it(t){return String(t).replace(et,"$1-$2").toLowerCase()}const st=/-[A-z\d]/g;function nt(t){return t.replace(st,(t=>t.slice(1).toUpperCase()))}const at=/\s\s*/;function ot(t,e,i,s){e.trim().split(at).forEach((e=>{t.removeEventListener(e,i,s)}))}function rt(t,e,i,s){e.trim().split(at).forEach((e=>{t.addEventListener(e,i,s)}))}function ht(t,e,i,s){rt(t,e,i,Object.assign(Object.assign({},s),{once:!0}))}const ct={bubbles:!0,cancelable:!0,composed:!0};function lt(t,e,i,s){return t.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign(Object.assign({},ct),{detail:i}),s)))}const dt=Promise.resolve();function ut(t,e){return e?dt.then(t?e.bind(t):e):dt}function $t(t){const{documentElement:e}=t.ownerDocument,s=t.getBoundingClientRect();return{left:s.left+(i.pageXOffset-e.clientLeft),top:s.top+(i.pageYOffset-e.clientTop)}}const pt=/deg|g?rad|turn$/i;function gt(t){const e=parseFloat(t)||0;if(0!==e){const[i="rad"]=String(t).match(pt)||[];switch(i.toLowerCase()){case"deg":return e/360*(2*Math.PI);case"grad":return e/400*(2*Math.PI);case"turn":return e*(2*Math.PI)}}return e}const mt="contain";function bt(t,e=mt){const{aspectRatio:i}=t;let{width:s,height:n}=t;const a=K(s),o=K(n);if(a&&o){const t=n*i;e===mt&&t>s||"cover"===e&&t<s?n=s/i:s=n*i}else a?n=s/i:o&&(s=n*i);return{width:s,height:n}}function ft(t,...e){if(0===e.length)return t;const[i,s,n,a,o,r]=t,[h,c,l,d,u,$]=e[0];return ft(t=[i*h+n*c,s*h+a*c,i*l+n*d,s*l+a*d,i*u+n*$+o,s*u+a*$+r],...e.slice(1))}const vt=/left|top|width|height/i,Ct="open",wt=new WeakMap,yt=new WeakMap,Et=new Map,St=i.document&&Array.isArray(i.document.adoptedStyleSheets)&&"replaceSync"in i.CSSStyleSheet.prototype;class At extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var t,e;super(),this.shadowRootMode=Ct,this.slottable=!0;const i=null===(e=null===(t=Object.getPrototypeOf(this))||void 0===t?void 0:t.constructor)||void 0===e?void 0:e.$name;i&&Et.set(i,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,i){if(Object.is(i,e))return;const s=nt(t);let n=i;switch(typeof this[s]){case"boolean":n=null!==i&&"false"!==i;break;case"number":n=Number(i)}switch(this[s]=n,t){case"theme-color":{const t=yt.get(this),e=this.$sharedStyle;t&&e&&(St?t.replaceSync(e):t.textContent=e);break}}}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(t=it(t),typeof i){case"boolean":!0===i?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":i=q(i)?"":String(i);default:i?this.getAttribute(t)!==i&&this.setAttribute(t,i):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((t=>{const e=nt(t);let i=this[e];Z(i)||this.$propertyChangedCallback(e,void 0,i),Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:()=>i,set(t){const s=i;i=t,this.$propertyChangedCallback(e,s,t)}})}));const t=this.attachShadow({mode:this.shadowRootMode||Ct});if(this.shadowRoot||wt.set(this,t),yt.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){yt.has(this)&&yt.delete(this),wt.has(this)&&wt.delete(this)}$getTagNameOf(t){var e;return null!==(e=Et.get(t))&&void 0!==e?e:t}$setStyles(t){return Object.keys(t).forEach((e=>{let i=t[e];B(i)&&(i=0!==i&&vt.test(e)?`${i}px`:String(i)),this.style[e]=i})),this}$getShadowRoot(){return this.shadowRoot||wt.get(this)}$addStyles(t){let e;const i=this.$getShadowRoot();return St?(e=new CSSStyleSheet,e.replaceSync(t),i.adoptedStyleSheets=i.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,i.appendChild(e)),e}$emit(t,e,i){return lt(this,t,e,i)}$nextTick(t){return ut(this,t)}static $define(t,s){F(t)&&(s=t,t=""),t||(t=this.$name||this.name),t=it(t),e&&i.customElements&&!i.customElements.get(t)&&customElements.define(t,this,s)}}At.$version="2.0.0";class Tt extends At{constructor(){super(...arguments),this.$onPointerDown=null,this.$onPointerMove=null,this.$onPointerUp=null,this.$onWheel=null,this.$wheeling=!1,this.$pointers=new Map,this.$style=':host{display:block;min-height:100px;min-width:200px;overflow:hidden;position:relative;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([background]){background-color:#fff;background-image:repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc),repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc);background-image:repeating-conic-gradient(#ccc 0 25%,#fff 0 50%);background-position:0 0,.5rem .5rem;background-size:1rem 1rem}:host([disabled]){pointer-events:none}:host([disabled]):after{bottom:0;content:"";cursor:not-allowed;display:block;left:0;pointer-events:none;position:absolute;right:0;top:0}',this.$action=v,this.background=!1,this.disabled=!1,this.scaleStep=.1,this.themeColor="#39f"}static get observedAttributes(){return super.observedAttributes.concat(["background","disabled","scale-step"])}connectedCallback(){super.connectedCallback(),this.disabled||this.$bind()}disconnectedCallback(){this.disabled||this.$unbind(),super.disconnectedCallback()}$propertyChangedCallback(t,e,i){if(!Object.is(i,e)&&(super.$propertyChangedCallback(t,e,i),"disabled"===t))i?this.$unbind():this.$bind()}$bind(){this.$onPointerDown||(this.$onPointerDown=this.$handlePointerDown.bind(this),rt(this,R,this.$onPointerDown)),this.$onPointerMove||(this.$onPointerMove=this.$handlePointerMove.bind(this),rt(this.ownerDocument,z,this.$onPointerMove)),this.$onPointerUp||(this.$onPointerUp=this.$handlePointerUp.bind(this),rt(this.ownerDocument,M,this.$onPointerUp)),this.$onWheel||(this.$onWheel=this.$handleWheel.bind(this),rt(this,W,this.$onWheel,{passive:!1,capture:!0}))}$unbind(){this.$onPointerDown&&(ot(this,R,this.$onPointerDown),this.$onPointerDown=null),this.$onPointerMove&&(ot(this.ownerDocument,z,this.$onPointerMove),this.$onPointerMove=null),this.$onPointerUp&&(ot(this.ownerDocument,M,this.$onPointerUp),this.$onPointerUp=null),this.$onWheel&&(ot(this,W,this.$onWheel,{capture:!0}),this.$onWheel=null)}$handlePointerDown(t){const{buttons:e,button:i,type:s}=t;if(this.disabled||("pointerdown"===s&&"mouse"===t.pointerType||"mousedown"===s)&&(B(e)&&1!==e||B(i)&&0!==i||t.ctrlKey))return;const{$pointers:n}=this;let a="";if(t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:i})=>{n.set(t,{startX:e,startY:i,endX:e,endY:i})}));else{const{pointerId:e=0,pageX:i,pageY:s}=t;n.set(e,{startX:i,startY:s,endX:i,endY:s})}n.size>1?a=f:tt(t.target)&&(a=t.target.action||t.target.getAttribute(x)||""),!1!==this.$emit(H,{action:a,relatedEvent:t})&&(t.preventDefault(),this.$action=a,this.style.willChange="transform")}$handlePointerMove(t){const{$action:e,$pointers:i}=this;if(this.disabled||e===v||0===i.size)return;if(!1===this.$emit(X,{action:e,relatedEvent:t}))return;if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:s})=>{const n=i.get(t);n&&Object.assign(n,{endX:e,endY:s})}));else{const{pointerId:e=0,pageX:s,pageY:n}=t,a=i.get(e);a&&Object.assign(a,{endX:s,endY:n})}const s={action:e,relatedEvent:t};if(e===f){const e=new Map(i);let n=0,a=0,o=0,r=0,h=t.pageX,c=t.pageY;i.forEach(((t,i)=>{e.delete(i),e.forEach((e=>{let i=e.startX-t.startX,s=e.startY-t.startY,l=e.endX-t.endX,d=e.endY-t.endY,u=0,$=0,p=0,g=0;if(0===i?s<0?p=2*Math.PI:s>0&&(p=Math.PI):i>0?p=Math.PI/2+Math.atan(s/i):i<0&&(p=1.5*Math.PI+Math.atan(s/i)),0===l?d<0?g=2*Math.PI:d>0&&(g=Math.PI):l>0?g=Math.PI/2+Math.atan(d/l):l<0&&(g=1.5*Math.PI+Math.atan(d/l)),g>0||p>0){const i=g-p,s=Math.abs(i);s>n&&(n=s,o=i,h=(t.startX+e.startX)/2,c=(t.startY+e.startY)/2)}if(i=Math.abs(i),s=Math.abs(s),l=Math.abs(l),d=Math.abs(d),i>0&&s>0?u=Math.sqrt(i*i+s*s):i>0?u=i:s>0&&(u=s),l>0&&d>0?$=Math.sqrt(l*l+d*d):l>0?$=l:d>0&&($=d),u>0&&$>0){const i=($-u)/u,s=Math.abs(i);s>a&&(a=s,r=i,h=(t.startX+e.startX)/2,c=(t.startY+e.startY)/2)}}))}));const l=n>0,d=a>0;l&&d?(s.rotate=o,s.scale=r,s.centerX=h,s.centerY=c):l?(s.action=b,s.rotate=o,s.centerX=h,s.centerY=c):d?(s.action=m,s.scale=r,s.centerX=h,s.centerY=c):s.action=v}else{const[t]=Array.from(i.values());Object.assign(s,t)}i.forEach((t=>{t.startX=t.endX,t.startY=t.endY})),s.action!==v&&this.$emit(Y,s,{cancelable:!1})}$handlePointerUp(t){const{$action:e,$pointers:i}=this;if(!this.disabled&&e!==v&&!1!==this.$emit(L,{action:e,relatedEvent:t})){if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t})=>{i.delete(t)}));else{const{pointerId:e=0}=t;i.delete(e)}0===i.size&&(this.style.willChange="",this.$action=v)}}$handleWheel(t){if(this.disabled)return;if(t.preventDefault(),this.$wheeling)return;this.$wheeling=!0,setTimeout((()=>{this.$wheeling=!1}),50);const e=(t.deltaY>0?-1:1)*this.scaleStep;this.$emit(Y,{action:m,scale:e,relatedEvent:t},{cancelable:!1})}$setAction(t){return U(t)&&(this.$action=t),this}$toCanvas(t){return new Promise(((e,i)=>{if(!this.isConnected)return void i(new Error("The current element is not connected to the DOM."));const s=document.createElement("canvas");let n=this.offsetWidth,a=this.offsetHeight,o=1;J(t)&&(K(t.width)||K(t.height))&&(({width:n,height:a}=bt({aspectRatio:n/a,width:t.width,height:t.height})),o=n/this.offsetWidth),s.width=n,s.height=a;const r=this.querySelector(this.$getTagNameOf(l));r?r.$ready().then((i=>{const h=s.getContext("2d");if(h){const[e,c,l,d,u,$]=r.$getTransform();let p=u,g=$,m=i.naturalWidth,b=i.naturalHeight;1!==o&&(p*=o,g*=o,m*=o,b*=o);const f=m/2,v=b/2;h.fillStyle="transparent",h.fillRect(0,0,n,a),J(t)&&Q(t.beforeDraw)&&t.beforeDraw.call(this,h,s),h.save(),h.translate(f,v),h.transform(e,c,l,d,p,g),h.translate(-f,-v),h.drawImage(i,0,0,m,b),h.restore()}e(s)})).catch(i):e(s)}))}}Tt.$name=o,Tt.$version="2.0.0";const kt=new WeakMap,xt=["alt","crossorigin","decoding","importance","loading","referrerpolicy","sizes","src","srcset"];class Ot extends At{constructor(){super(...arguments),this.$matrix=[1,0,0,1,0,0],this.$onLoad=null,this.$onCanvasAction=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$actionStartTarget=null,this.$style=":host{display:inline-block}img{display:block;height:100%;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}",this.$image=new Image,this.initialCenterSize="contain",this.rotatable=!1,this.scalable=!1,this.skewable=!1,this.slottable=!1,this.translatable=!1}set $canvas(t){kt.set(this,t)}get $canvas(){return kt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(xt,["initial-center-size","rotatable","scalable","skewable","translatable"])}attributeChangedCallback(t,e,i){Object.is(i,e)||(super.attributeChangedCallback(t,e,i),xt.includes(t)&&this.$image.setAttribute(t,i))}$propertyChangedCallback(t,e,i){if(!Object.is(i,e)&&(super.$propertyChangedCallback(t,e,i),"initialCenterSize"===t))this.$nextTick((()=>{this.$center(i)}))}connectedCallback(){super.connectedCallback();const{$image:t}=this,e=this.closest(this.$getTagNameOf(o));e&&(this.$canvas=e,this.$setStyles({display:"block",position:"absolute"}),this.$onCanvasActionStart=t=>{var e,i;this.$actionStartTarget=null===(i=null===(e=t.detail)||void 0===e?void 0:e.relatedEvent)||void 0===i?void 0:i.target},this.$onCanvasActionEnd=()=>{this.$actionStartTarget=null},this.$onCanvasAction=this.$handleAction.bind(this),rt(e,H,this.$onCanvasActionStart),rt(e,L,this.$onCanvasActionEnd),rt(e,Y,this.$onCanvasAction)),this.$onLoad=this.$handleLoad.bind(this),rt(t,_,this.$onLoad),this.$getShadowRoot().appendChild(t)}disconnectedCallback(){const{$image:t,$canvas:e}=this;e&&(this.$onCanvasActionStart&&(ot(e,H,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(ot(e,L,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(ot(e,Y,this.$onCanvasAction),this.$onCanvasAction=null)),t&&this.$onLoad&&(ot(t,_,this.$onLoad),this.$onLoad=null),this.$getShadowRoot().removeChild(t),super.disconnectedCallback()}$handleLoad(){const{$image:t}=this;this.$setStyles({width:t.naturalWidth,height:t.naturalHeight}),this.$canvas&&this.$center(this.initialCenterSize)}$handleAction(t){if(this.hidden||!(this.rotatable||this.scalable||this.translatable))return;const{$canvas:e}=this,{detail:i}=t;if(i){const{relatedEvent:t}=i;let{action:s}=i;switch(s!==f||this.rotatable&&this.scalable||(s=this.rotatable?b:this.scalable?m:v),s){case g:if(this.translatable){let s=null;t&&(s=t.target.closest(this.$getTagNameOf(d))),s||(s=e.querySelector(this.$getTagNameOf(d))),s&&s.multiple&&!s.active&&(s=e.querySelector(`${this.$getTagNameOf(d)}[active]`)),s&&!s.hidden&&s.movable&&!s.dynamic&&this.$actionStartTarget&&s.contains(this.$actionStartTarget)||this.$move(i.endX-i.startX,i.endY-i.startY)}break;case b:if(this.rotatable)if(t){const{x:e,y:s}=this.getBoundingClientRect();this.$rotate(i.rotate,t.clientX-e,t.clientY-s)}else this.$rotate(i.rotate);break;case m:if(this.scalable)if(t){const e=t.target.closest(this.$getTagNameOf(d));if(!e||!e.zoomable||e.zoomable&&e.dynamic){const{x:e,y:s}=this.getBoundingClientRect();this.$zoom(i.scale,t.clientX-e,t.clientY-s)}}else this.$zoom(i.scale);break;case f:if(this.rotatable&&this.scalable){const{rotate:e}=i;let{scale:s}=i;s<0?s=1/(1-s):s+=1;const n=Math.cos(e),a=Math.sin(e),[o,r,h,c]=[n*s,a*s,-a*s,n*s];if(t){const e=this.getBoundingClientRect(),i=t.clientX-e.x,s=t.clientY-e.y,[n,a,l,d]=this.$matrix,u=i-e.width/2,$=s-e.height/2,p=(u*d-l*$)/(n*d-l*a),g=($*n-a*u)/(n*d-l*a);this.$transform(o,r,h,c,p*(1-o)+g*h,g*(1-c)+p*r)}else this.$transform(o,r,h,c,0,0)}}}}$ready(t){const{$image:e}=this,i=new Promise(((t,i)=>{const s=new Error("Failed to load the image source");if(e.complete)e.naturalWidth>0&&e.naturalHeight>0?t(e):i(s);else{const n=()=>{ot(e,P,a),t(e)},a=()=>{ot(e,_,n),i(s)};ht(e,_,n),ht(e,P,a)}}));return Q(t)&&i.then((e=>(t(e),e))),i}$center(t){const{parentElement:e}=this;if(!e)return this;const i=e.getBoundingClientRect(),s=i.width,n=i.height,{x:a,y:o,width:r,height:h}=this.getBoundingClientRect(),c=a+r/2,l=o+h/2,d=i.x+s/2,u=i.y+n/2;if(this.$move(d-c,u-l),t&&(r!==s||h!==n)){const e=s/r,i=n/h;switch(t){case"cover":this.$scale(Math.max(e,i));break;case"contain":this.$scale(Math.min(e,i))}}return this}$move(t,e=t){if(this.translatable&&B(t)&&B(e)){const[i,s,n,a]=this.$matrix,o=(t*a-n*e)/(i*a-n*s),r=(e*i-s*t)/(i*a-n*s);this.$translate(o,r)}return this}$moveTo(t,e=t){if(this.translatable&&B(t)&&B(e)){const[i,s,n,a]=this.$matrix,o=(t*a-n*e)/(i*a-n*s),r=(e*i-s*t)/(i*a-n*s);this.$setTransform(i,s,n,a,o,r)}return this}$rotate(t,e,i){if(this.rotatable){const s=gt(t),n=Math.cos(s),a=Math.sin(s),[o,r,h,c]=[n,a,-a,n];if(B(e)&&B(i)){const[t,s,n,a]=this.$matrix,{width:l,height:d}=this.getBoundingClientRect(),u=e-l/2,$=i-d/2,p=(u*a-n*$)/(t*a-n*s),g=($*t-s*u)/(t*a-n*s);this.$transform(o,r,h,c,p*(1-o)-g*h,g*(1-c)-p*r)}else this.$transform(o,r,h,c,0,0)}return this}$zoom(t,e,i){if(!this.scalable||0===t)return this;if(t<0?t=1/(1-t):t+=1,B(e)&&B(i)){const[s,n,a,o]=this.$matrix,{width:r,height:h}=this.getBoundingClientRect(),c=e-r/2,l=i-h/2,d=(c*o-a*l)/(s*o-a*n),u=(l*s-n*c)/(s*o-a*n);this.$transform(t,0,0,t,d*(1-t),u*(1-t))}else this.$scale(t);return this}$scale(t,e=t){return this.scalable&&this.$transform(t,0,0,e,0,0),this}$skew(t,e=0){if(this.skewable){const i=gt(t),s=gt(e);this.$transform(1,Math.tan(s),Math.tan(i),1,0,0)}return this}$translate(t,e=t){return this.translatable&&B(t)&&B(e)&&this.$transform(1,0,0,1,t,e),this}$transform(t,e,i,s,n,a){return B(t)&&B(e)&&B(i)&&B(s)&&B(n)&&B(a)?this.$setTransform(ft(this.$matrix,[t,e,i,s,n,a])):this}$setTransform(t,e,i,s,n,a){if((this.rotatable||this.scalable||this.skewable||this.translatable)&&(Array.isArray(t)&&([t,e,i,s,n,a]=t),B(t)&&B(e)&&B(i)&&B(s)&&B(n)&&B(a))){const o=[...this.$matrix],r=[t,e,i,s,n,a];if(!1===this.$emit(V,{matrix:r,oldMatrix:o}))return this;this.$matrix=r,this.style.transform=`matrix(${r.join(", ")})`}return this}$getTransform(){return this.$matrix.slice()}$resetTransform(){return this.$setTransform([1,0,0,1,0,0])}}Ot.$name=l,Ot.$version="2.0.0";const Nt=new WeakMap;class It extends At{constructor(){super(...arguments),this.$onCanvasChange=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$style=":host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}",this.x=0,this.y=0,this.width=0,this.height=0,this.slottable=!1,this.themeColor="rgba(0, 0, 0, 0.65)"}set $canvas(t){Nt.set(this,t)}get $canvas(){return Nt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["height","width","x","y"])}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(o));if(t){this.$canvas=t,this.style.position="absolute";const e=t.querySelector(this.$getTagNameOf(d));e&&(this.$onCanvasActionStart=t=>{e.hidden&&t.detail.action===p&&(this.hidden=!1)},this.$onCanvasActionEnd=t=>{e.hidden&&t.detail.action===p&&(this.hidden=!0)},this.$onCanvasChange=t=>{const{x:i,y:s,width:n,height:a}=t.detail;this.$change(i,s,n,a),(e.hidden||0===i&&0===s&&0===n&&0===a)&&(this.hidden=!0)},rt(t,H,this.$onCanvasActionStart),rt(t,L,this.$onCanvasActionEnd),rt(t,j,this.$onCanvasChange))}this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(ot(t,H,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(ot(t,L,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasChange&&(ot(t,j,this.$onCanvasChange),this.$onCanvasChange=null)),super.disconnectedCallback()}$change(t,e,i=this.width,s=this.height){return B(t)&&B(e)&&B(i)&&B(s)&&(t!==this.x||e!==this.y||i!==this.width||s!==this.height)?(this.hidden&&(this.hidden=!1),this.x=t,this.y=e,this.width=i,this.height=s,this.$render()):this}$reset(){return this.$change(0,0,0,0)}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height,outlineWidth:i.innerWidth})}}It.$name=u,It.$version="2.0.0";class Rt extends At{constructor(){super(...arguments),this.$onCanvasCropEnd=null,this.$onCanvasCropStart=null,this.$style=':host{background-color:var(--theme-color);display:block}:host([action=move]),:host([action=select]){height:100%;left:0;position:absolute;top:0;width:100%}:host([action=move]){cursor:move}:host([action=select]){cursor:crosshair}:host([action$=-resize]){background-color:transparent;height:15px;position:absolute;width:15px}:host([action$=-resize]):after{background-color:var(--theme-color);content:"";display:block;height:5px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:5px}:host([action=n-resize]),:host([action=s-resize]){cursor:ns-resize;left:50%;transform:translateX(-50%);width:100%}:host([action=n-resize]){top:-8px}:host([action=s-resize]){bottom:-8px}:host([action=e-resize]),:host([action=w-resize]){cursor:ew-resize;height:100%;top:50%;transform:translateY(-50%)}:host([action=e-resize]){right:-8px}:host([action=w-resize]){left:-8px}:host([action=ne-resize]){cursor:nesw-resize;right:-8px;top:-8px}:host([action=nw-resize]){cursor:nwse-resize;left:-8px;top:-8px}:host([action=se-resize]){bottom:-8px;cursor:nwse-resize;right:-8px}:host([action=se-resize]):after{height:15px;width:15px}@media (pointer:coarse){:host([action=se-resize]):after{height:10px;width:10px}}@media (pointer:fine){:host([action=se-resize]):after{height:5px;width:5px}}:host([action=sw-resize]){bottom:-8px;cursor:nesw-resize;left:-8px}:host([plain]){background-color:transparent}',this.action=v,this.plain=!1,this.slottable=!1,this.themeColor="rgba(51, 153, 255, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["action","plain"])}}Rt.$name=c,Rt.$version="2.0.0";const zt=new WeakMap;class Mt extends At{constructor(){super(...arguments),this.$onCanvasAction=null,this.$onCanvasActionStart=null,this.$onCanvasActionEnd=null,this.$onDocumentKeyDown=null,this.$action="",this.$actionStartTarget=null,this.$changing=!1,this.$style=':host{display:block;left:0;position:relative;right:0}:host([outlined]){outline:1px solid var(--theme-color)}:host([multiple]){outline:1px dashed hsla(0,0%,100%,.5)}:host([multiple]):after{bottom:0;content:"";cursor:pointer;display:block;left:0;position:absolute;right:0;top:0}:host([multiple][active]){outline-color:var(--theme-color);z-index:1}:host([multiple])>*{visibility:hidden}:host([multiple][active])>*{visibility:visible}:host([multiple][active]):after{display:none}',this.$initialSelection={x:0,y:0,width:0,height:0},this.x=0,this.y=0,this.width=0,this.height=0,this.aspectRatio=NaN,this.initialAspectRatio=NaN,this.initialCoverage=NaN,this.active=!1,this.linked=!1,this.dynamic=!1,this.movable=!1,this.resizable=!1,this.zoomable=!1,this.multiple=!1,this.keyboard=!1,this.outlined=!1,this.precise=!1}set $canvas(t){zt.set(this,t)}get $canvas(){return zt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["active","aspect-ratio","dynamic","height","initial-aspect-ratio","initial-coverage","keyboard","linked","movable","multiple","outlined","precise","resizable","width","x","y","zoomable"])}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(super.$propertyChangedCallback(t,e,i),t){case"x":case"y":case"width":case"height":this.$changing||this.$nextTick((()=>{this.$change(this.x,this.y,this.width,this.height,this.aspectRatio,!0)}));break;case"aspectRatio":case"initialAspectRatio":this.$nextTick((()=>{this.$initSelection()}));break;case"initialCoverage":this.$nextTick((()=>{K(i)&&i<=1&&this.$initSelection(!0,!0)}));break;case"keyboard":this.$nextTick((()=>{this.$canvas&&(i?this.$onDocumentKeyDown||(this.$onDocumentKeyDown=this.$handleKeyDown.bind(this),rt(this.ownerDocument,D,this.$onDocumentKeyDown)):this.$onDocumentKeyDown&&(ot(this.ownerDocument,D,this.$onDocumentKeyDown),this.$onDocumentKeyDown=null))}));break;case"multiple":this.$nextTick((()=>{if(this.$canvas){const t=this.$getSelections();i?(t.forEach((t=>{t.active=!1})),this.active=!0,this.$emit(j,{x:this.x,y:this.y,width:this.width,height:this.height})):(this.active=!1,t.slice(1).forEach((t=>{this.$removeSelection(t)})))}}));break;case"precise":this.$nextTick((()=>{this.$change(this.x,this.y)}));break;case"linked":i&&(this.dynamic=!0)}}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(o));t?(this.$canvas=t,this.$setStyles({position:"absolute",transform:`translate(${this.x}px, ${this.y}px)`}),this.hidden||this.$render(),this.$initSelection(!0),this.$onCanvasActionStart=this.$handleActionStart.bind(this),this.$onCanvasActionEnd=this.$handleActionEnd.bind(this),this.$onCanvasAction=this.$handleAction.bind(this),rt(t,H,this.$onCanvasActionStart),rt(t,L,this.$onCanvasActionEnd),rt(t,Y,this.$onCanvasAction)):this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(ot(t,H,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(ot(t,L,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(ot(t,Y,this.$onCanvasAction),this.$onCanvasAction=null)),super.disconnectedCallback()}$getSelections(){let t=[];return this.parentElement&&(t=Array.from(this.parentElement.querySelectorAll(this.$getTagNameOf(d)))),t}$initSelection(t=!1,e=!1){const{initialCoverage:i,parentElement:s}=this;if(K(i)&&s){const n=this.aspectRatio||this.initialAspectRatio;let a=(e?0:this.width)||s.offsetWidth*i,o=(e?0:this.height)||s.offsetHeight*i;K(n)&&({width:a,height:o}=bt({aspectRatio:n,width:a,height:o})),this.$change(this.x,this.y,a,o),t&&this.$center(),this.$initialSelection={x:this.x,y:this.y,width:this.width,height:this.height}}}$createSelection(){const t=this.cloneNode(!0);return this.hasAttribute("id")&&t.removeAttribute("id"),t.initialCoverage=NaN,this.active=!1,this.parentElement&&this.parentElement.insertBefore(t,this.nextSibling),t}$removeSelection(t=this){if(this.parentElement){const e=this.$getSelections();if(e.length>1){const i=e.indexOf(t),s=e[i+1]||e[i-1];s&&(t.active=!1,this.parentElement.removeChild(t),s.active=!0,s.$emit(j,{x:s.x,y:s.y,width:s.width,height:s.height}))}else this.$clear()}}$handleActionStart(t){var e,i;const s=null===(i=null===(e=t.detail)||void 0===e?void 0:e.relatedEvent)||void 0===i?void 0:i.target;this.$action="",this.$actionStartTarget=s,!this.hidden&&this.multiple&&!this.active&&s===this&&this.parentElement&&(this.$getSelections().forEach((t=>{t.active=!1})),this.active=!0,this.$emit(j,{x:this.x,y:this.y,width:this.width,height:this.height}))}$handleAction(t){const{currentTarget:e,detail:i}=t;if(!e||!i)return;const{relatedEvent:s}=i;let{action:n}=i;if(!n&&this.multiple&&(n=this.$action||(null==s?void 0:s.target.action),this.$action=n),!n||this.hidden&&n!==p||this.multiple&&!this.active&&n!==m)return;const a=i.endX-i.startX,o=i.endY-i.startY,{width:r,height:h}=this;let{aspectRatio:c}=this;switch(!K(c)&&s.shiftKey&&(c=K(r)&&K(h)?r/h:1),n){case p:if(0!==a&&0!==o){const{$canvas:t}=this,s=$t(e);(this.multiple&&!this.hidden?this.$createSelection():this).$change(i.startX-s.left,i.startY-s.top,Math.abs(a),Math.abs(o),c),a<0?o<0?n=A:o>0&&(n=k):a>0&&(o<0?n=S:o>0&&(n=T)),t&&(t.$action=n)}break;case g:this.movable&&(this.dynamic||this.$actionStartTarget&&this.contains(this.$actionStartTarget))&&this.$move(a,o);break;case m:if(s&&this.zoomable&&(this.dynamic||this.contains(s.target))){const t=$t(e);this.$zoom(i.scale,s.pageX-t.left,s.pageY-t.top)}break;default:this.$resize(n,a,o,c)}}$handleActionEnd(){this.$action="",this.$actionStartTarget=null}$handleKeyDown(t){if(this.hidden||!this.keyboard||this.multiple&&!this.active||t.defaultPrevented)return;const{activeElement:e}=document;if(!e||!["INPUT","TEXTAREA"].includes(e.tagName)&&!["true","plaintext-only"].includes(e.contentEditable))switch(t.key){case"Backspace":t.metaKey&&(t.preventDefault(),this.$removeSelection());break;case"Delete":t.preventDefault(),this.$removeSelection();break;case"ArrowLeft":t.preventDefault(),this.$move(-1,0);break;case"ArrowRight":t.preventDefault(),this.$move(1,0);break;case"ArrowUp":t.preventDefault(),this.$move(0,-1);break;case"ArrowDown":t.preventDefault(),this.$move(0,1);break;case"+":t.preventDefault(),this.$zoom(.1);break;case"-":t.preventDefault(),this.$zoom(-.1)}}$center(){const{parentElement:t}=this;if(!t)return this;const e=(t.offsetWidth-this.width)/2,i=(t.offsetHeight-this.height)/2;return this.$change(e,i)}$move(t,e=t){return this.$moveTo(this.x+t,this.y+e)}$moveTo(t,e=t){return this.movable?this.$change(t,e):this}$resize(t,e=0,i=0,s=this.aspectRatio){if(!this.resizable)return this;const n=K(s),{$canvas:a}=this;let{x:o,y:r,width:h,height:c}=this;switch(t){case C:r+=i,c-=i,c<0&&(t=y,c=-c,r-=c),n&&(o+=(e=i*s)/2,h-=e,h<0&&(h=-h,o-=h));break;case w:h+=e,h<0&&(t=E,h=-h,o-=h),n&&(r-=(i=e/s)/2,c+=i,c<0&&(c=-c,r-=c));break;case y:c+=i,c<0&&(t=C,c=-c,r-=c),n&&(o-=(e=i*s)/2,h+=e,h<0&&(h=-h,o-=h));break;case E:o+=e,h-=e,h<0&&(t=w,h=-h,o-=h),n&&(r+=(i=e/s)/2,c-=i,c<0&&(c=-c,r-=c));break;case S:n&&(i=-e/s),r+=i,c-=i,h+=e,h<0&&c<0?(t=k,h=-h,c=-c,o-=h,r-=c):h<0?(t=A,h=-h,o-=h):c<0&&(t=T,c=-c,r-=c);break;case A:n&&(i=e/s),o+=e,r+=i,h-=e,c-=i,h<0&&c<0?(t=T,h=-h,c=-c,o-=h,r-=c):h<0?(t=S,h=-h,o-=h):c<0&&(t=k,c=-c,r-=c);break;case T:n&&(i=e/s),h+=e,c+=i,h<0&&c<0?(t=A,h=-h,c=-c,o-=h,r-=c):h<0?(t=k,h=-h,o-=h):c<0&&(t=S,c=-c,r-=c);break;case k:n&&(i=-e/s),o+=e,h-=e,c+=i,h<0&&c<0?(t=S,h=-h,c=-c,o-=h,r-=c):h<0?(t=T,h=-h,o-=h):c<0&&(t=A,c=-c,r-=c)}return a&&a.$setAction(t),this.$change(o,r,h,c)}$zoom(t,e,i){if(!this.zoomable||0===t)return this;t<0?t=1/(1-t):t+=1;const{width:s,height:n}=this,a=s*t,o=n*t;let r=this.x,h=this.y;return B(e)&&B(i)?(r-=(a-s)*((e-this.x)/s),h-=(o-n)*((i-this.y)/n)):(r-=(a-s)/2,h-=(o-n)/2),this.$change(r,h,a,o)}$change(t,e,i=this.width,s=this.height,n=this.aspectRatio,a=!1){return this.$changing||!B(t)||!B(e)||!B(i)||!B(s)||i<0||s<0?this:(K(n)&&({width:i,height:s}=bt({aspectRatio:n,width:i,height:s},"cover")),this.precise||(t=Math.round(t),e=Math.round(e),i=Math.round(i),s=Math.round(s)),t===this.x&&e===this.y&&i===this.width&&s===this.height&&Object.is(n,this.aspectRatio)&&!a?this:(this.hidden&&(this.hidden=!1),!1===this.$emit(j,{x:t,y:e,width:i,height:s})?this:(this.$changing=!0,this.x=t,this.y=e,this.width=i,this.height=s,this.$changing=!1,this.$render())))}$reset(){const{x:t,y:e,width:i,height:s}=this.$initialSelection;return this.$change(t,e,i,s)}$clear(){return this.$change(0,0,0,0,NaN,!0),this.hidden=!0,this}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height})}$toCanvas(t){return new Promise(((e,i)=>{if(!this.isConnected)return void i(new Error("The current element is not connected to the DOM."));const s=document.createElement("canvas");let{width:n,height:a}=this,o=1;if(J(t)&&(K(t.width)||K(t.height))&&(({width:n,height:a}=bt({aspectRatio:n/a,width:t.width,height:t.height})),o=n/this.width),s.width=n,s.height=a,!this.$canvas)return void e(s);const r=this.$canvas.querySelector(this.$getTagNameOf(l));r?r.$ready().then((i=>{const h=s.getContext("2d");if(h){const[e,c,l,d,u,$]=r.$getTransform(),p=-this.x,g=-this.y,m=(p*d-l*g)/(e*d-l*c),b=(g*e-c*p)/(e*d-l*c);let f=e*m+l*b+u,v=c*m+d*b+$,C=i.naturalWidth,w=i.naturalHeight;1!==o&&(f*=o,v*=o,C*=o,w*=o);const y=C/2,E=w/2;h.fillStyle="transparent",h.fillRect(0,0,n,a),J(t)&&Q(t.beforeDraw)&&t.beforeDraw.call(this,h,s),h.save(),h.translate(y,E),h.transform(e,c,l,d,f,v),h.translate(-y,-E),h.drawImage(i,0,0,C,w),h.restore()}e(s)})).catch(i):e(s)}))}}Mt.$name=d,Mt.$version="2.0.0";class Pt extends At{constructor(){super(...arguments),this.$style=":host{display:flex;flex-direction:column;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([bordered]){border:1px dashed var(--theme-color)}:host([covered]){bottom:0;left:0;position:absolute;right:0;top:0}:host>span{display:flex;flex:1}:host>span+span{border-top:1px dashed var(--theme-color)}:host>span>span{flex:1}:host>span>span+span{border-left:1px dashed var(--theme-color)}",this.bordered=!1,this.columns=3,this.covered=!1,this.rows=3,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["bordered","columns","covered","rows"])}$propertyChangedCallback(t,e,i){Object.is(i,e)||(super.$propertyChangedCallback(t,e,i),"rows"!==t&&"columns"!==t||this.$nextTick((()=>{this.$render()})))}connectedCallback(){super.connectedCallback(),this.$render()}$render(){const t=this.$getShadowRoot(),e=document.createDocumentFragment();for(let t=0;t<this.rows;t+=1){const t=document.createElement("span");t.setAttribute("role","row");for(let e=0;e<this.columns;e+=1){const e=document.createElement("span");e.setAttribute("role","gridcell"),t.appendChild(e)}e.appendChild(t)}t&&(t.innerHTML="",t.appendChild(e))}}Pt.$name=h,Pt.$version="2.0.0";class Dt extends At{constructor(){super(...arguments),this.$style=':host{display:inline-block;height:1em;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle;width:1em}:host:after,:host:before{background-color:var(--theme-color);content:"";display:block;position:absolute}:host:before{height:1px;left:0;top:50%;transform:translateY(-50%);width:100%}:host:after{height:100%;left:50%;top:0;transform:translateX(-50%);width:1px}:host([centered]){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}',this.centered=!1,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["centered"])}}Dt.$name=r,Dt.$version="2.0.0";const _t=new WeakMap,Wt=new WeakMap,Yt=new WeakMap,Lt=new WeakMap,Xt="vertical";class Ht extends At{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}",this.resize=Xt,this.selection="",this.slottable=!1}set $image(t){Wt.set(this,t)}get $image(){return Wt.get(this)}set $sourceImage(t){Lt.set(this,t)}get $sourceImage(){return Lt.get(this)}set $canvas(t){_t.set(this,t)}get $canvas(){return _t.get(this)}set $selection(t){Yt.set(this,t)}get $selection(){return Yt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let t=null;if(t=this.selection?this.ownerDocument.querySelector(this.selection):this.closest(this.$getTagNameOf(d)),tt(t)){this.$selection=t,this.$onSelectionChange=this.$handleSelectionChange.bind(this),rt(t,j,this.$onSelectionChange);const e=t.closest(this.$getTagNameOf(o));if(e){this.$canvas=e;const t=e.querySelector(this.$getTagNameOf(l));t&&(this.$sourceImage=t,this.$image=t.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),rt(t.$image,_,this.$onSourceImageLoad),rt(t,V,this.$onSourceImageTransform))}this.$render()}}disconnectedCallback(){const{$selection:t,$sourceImage:e}=this;t&&this.$onSelectionChange&&(ot(t,j,this.$onSelectionChange),this.$onSelectionChange=null),e&&this.$onSourceImageLoad&&(ot(e.$image,_,this.$onSourceImageLoad),this.$onSourceImageLoad=null),e&&this.$onSourceImageTransform&&(ot(e,V,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(t){this.$render(t.detail)}$handleSourceImageLoad(){const{$image:t,$sourceImage:e}=this,i=t.getAttribute("src"),s=e.getAttribute("src");s&&s!==i&&(t.setAttribute("src",s),t.$ready((()=>{setTimeout((()=>{this.$render()}),50)})))}$handleSourceImageTransform(t){this.$render(void 0,t.detail.matrix)}$render(t,e){const{$canvas:i,$selection:s}=this;t||s.hidden||(t=s),(!t||0===t.x&&0===t.y&&0===t.width&&0===t.height)&&(t={x:0,y:0,width:i.offsetWidth,height:i.offsetHeight});const{x:n,y:a,width:o,height:r}=t,h={},{clientWidth:c,clientHeight:l}=this;let d=c,u=l,$=NaN;switch(this.resize){case"both":$=1,d=o,u=r,h.width=o,h.height=r;break;case"horizontal":$=r>0?l/r:0,d=o*$,h.width=d;break;case Xt:$=o>0?c/o:0,u=r*$,h.height=u;break;default:c>0?$=o>0?c/o:0:l>0&&($=r>0?l/r:0)}this.$scale=$,this.$setStyles(h),this.$sourceImage&&this.$transformImageByOffset(null!=e?e:this.$sourceImage.$getTransform(),-n,-a)}$transformImageByOffset(t,e,i){const{$image:s,$scale:n,$sourceImage:a}=this;if(a&&s&&n>=0){const[a,o,r,h,c,l]=t,d=(e*h-r*i)/(a*h-r*o),u=(i*a-o*e)/(a*h-r*o),$=a*d+r*u+c,p=o*d+h*u+l;s.$ready((t=>{this.$setStyles.call(s,{width:t.naturalWidth*n,height:t.naturalHeight*n})})),s.$setTransform(a,o,r,h,$*n,p*n)}}}Ht.$name=$,Ht.$version="2.0.0";var jt='<cropper-canvas background><cropper-image rotatable scalable skewable translatable></cropper-image><cropper-shade hidden></cropper-shade><cropper-handle action="select" plain></cropper-handle><cropper-selection initial-coverage="0.5" movable resizable><cropper-grid role="grid" bordered covered></cropper-grid><cropper-crosshair centered></cropper-crosshair><cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)"></cropper-handle><cropper-handle action="n-resize"></cropper-handle><cropper-handle action="e-resize"></cropper-handle><cropper-handle action="s-resize"></cropper-handle><cropper-handle action="w-resize"></cropper-handle><cropper-handle action="ne-resize"></cropper-handle><cropper-handle action="nw-resize"></cropper-handle><cropper-handle action="se-resize"></cropper-handle><cropper-handle action="sw-resize"></cropper-handle></cropper-selection></cropper-canvas>';const Vt=/^img|canvas$/,Ut=/<(\/?(?:script|style)[^>]*)>/gi,qt={template:jt};Tt.$define(),Dt.$define(),Pt.$define(),Rt.$define(),Ot.$define(),Mt.$define(),It.$define(),Ht.$define();class Bt{constructor(t,e){if(this.options=qt,U(t)&&(t=document.querySelector(t)),!tt(t)||!Vt.test(t.localName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,e=Object.assign(Object.assign({},qt),e),this.options=e;const{ownerDocument:i}=t;let{container:s}=e;if(s&&(U(s)&&(s=i.querySelector(s)),!tt(s)))throw new Error("The `container` option must be an element or a valid selector.");tt(s)||(s=t.parentElement?t.parentElement:i.body),this.container=s;const n=t.localName;let a="";"img"===n?({src:a}=t):"canvas"===n&&window.HTMLCanvasElement&&(a=t.toDataURL());const{template:o}=e;if(o&&U(o)){const e=document.createElement("template"),i=document.createDocumentFragment();e.innerHTML=o.replace(Ut,"&lt;$1&gt;"),i.appendChild(e.content),Array.from(i.querySelectorAll(l)).forEach((e=>{e.setAttribute("src",a),e.setAttribute("alt",t.alt||"The image to crop")})),t.parentElement?(t.style.display="none",s.insertBefore(i,t.nextSibling)):s.appendChild(i)}}getCropperCanvas(){return this.container.querySelector(o)}getCropperImage(){return this.container.querySelector(l)}getCropperSelection(){return this.container.querySelector(d)}getCropperSelections(){return this.container.querySelectorAll(d)}}Bt.version="2.0.0",t.ACTION_MOVE=g,t.ACTION_NONE=v,t.ACTION_RESIZE_EAST=w,t.ACTION_RESIZE_NORTH=C,t.ACTION_RESIZE_NORTHEAST=S,t.ACTION_RESIZE_NORTHWEST=A,t.ACTION_RESIZE_SOUTH=y,t.ACTION_RESIZE_SOUTHEAST=T,t.ACTION_RESIZE_SOUTHWEST=k,t.ACTION_RESIZE_WEST=E,t.ACTION_ROTATE=b,t.ACTION_SCALE=m,t.ACTION_SELECT=p,t.ACTION_TRANSFORM=f,t.ATTRIBUTE_ACTION=x,t.CROPPER_CANVAS=o,t.CROPPER_CROSSHAIR=r,t.CROPPER_GIRD=h,t.CROPPER_HANDLE=c,t.CROPPER_IMAGE=l,t.CROPPER_SELECTION=d,t.CROPPER_SHADE=u,t.CROPPER_VIEWER=$,t.CropperCanvas=Tt,t.CropperCrosshair=Dt,t.CropperElement=At,t.CropperGrid=Pt,t.CropperHandle=Rt,t.CropperImage=Ot,t.CropperSelection=Mt,t.CropperShade=It,t.CropperViewer=Ht,t.DEFAULT_TEMPLATE=jt,t.EVENT_ACTION=Y,t.EVENT_ACTION_END=L,t.EVENT_ACTION_MOVE=X,t.EVENT_ACTION_START=H,t.EVENT_CHANGE=j,t.EVENT_ERROR=P,t.EVENT_KEYDOWN=D,t.EVENT_LOAD=_,t.EVENT_POINTER_DOWN=R,t.EVENT_POINTER_MOVE=z,t.EVENT_POINTER_UP=M,t.EVENT_RESIZE="resize",t.EVENT_TOUCH_END=O,t.EVENT_TOUCH_MOVE=N,t.EVENT_TOUCH_START=I,t.EVENT_TRANSFORM=V,t.EVENT_WHEEL=W,t.HAS_POINTER_EVENT=n,t.IS_BROWSER=e,t.IS_TOUCH_DEVICE=s,t.NAMESPACE=a,t.WINDOW=i,t.default=Bt,t.emit=lt,t.getAdjustedSizes=bt,t.getOffset=$t,t.isElement=tt,t.isFunction=Q,t.isNaN=q,t.isNumber=B,t.isObject=F,t.isPlainObject=J,t.isPositiveNumber=K,t.isString=U,t.isUndefined=Z,t.multiplyMatrices=ft,t.nextTick=ut,t.off=ot,t.on=rt,t.once=ht,t.toAngleInRadian=gt,t.toCamelCase=nt,t.toKebabCase=it,Object.defineProperty(t,"__esModule",{value:!0})}));
