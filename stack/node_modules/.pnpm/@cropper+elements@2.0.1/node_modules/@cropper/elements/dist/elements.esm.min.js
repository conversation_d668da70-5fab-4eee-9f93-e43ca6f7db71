const t="undefined"!=typeof window&&void 0!==window.document,e=t?window:{},i=!!t&&"ontouchstart"in e.document.documentElement,s=!!t&&"PointerEvent"in e,n="cropper",a=`${n}-canvas`,o=`${n}-crosshair`,h=`${n}-grid`,r=`${n}-handle`,c=`${n}-image`,l=`${n}-selection`,d=`${n}-shade`,$=`${n}-viewer`,u="select",g="move",p="scale",m="rotate",b="transform",f="none",v="n-resize",w="e-resize",y="s-resize",C="w-resize",k="ne-resize",S="nw-resize",x="se-resize",A="sw-resize",E=s?"pointerdown":i?"touchstart":"mousedown",T=s?"pointermove":i?"touchmove":"mousemove",z=s?"pointerup pointercancel":i?"touchend touchcancel":"mouseup",M="error",D="keydown",P="load",O="wheel",I="action",N="actionend",R="actionstart",X="change",Y="transform";const W=Number.isNaN||e.isNaN;function L(t){return"number"==typeof t&&!W(t)}function j(t){return L(t)&&t>0&&t<1/0}function H(t){return"object"==typeof t&&null!==t}const{hasOwnProperty:B}=Object.prototype;function K(t){if(!H(t))return!1;try{const{constructor:e}=t,{prototype:i}=e;return e&&i&&B.call(i,"isPrototypeOf")}catch(t){return!1}}function U(t){return"function"==typeof t}function q(t){return"object"==typeof t&&null!==t&&1===t.nodeType}const F=/([a-z\d])([A-Z])/g;function Z(t){return String(t).replace(F,"$1-$2").toLowerCase()}const G=/-[A-z\d]/g;function J(t){return t.replace(G,(t=>t.slice(1).toUpperCase()))}const Q=/\s\s*/;function V(t,e,i,s){e.trim().split(Q).forEach((e=>{t.removeEventListener(e,i,s)}))}function _(t,e,i,s){e.trim().split(Q).forEach((e=>{t.addEventListener(e,i,s)}))}function tt(t,e,i,s){_(t,e,i,Object.assign(Object.assign({},s),{once:!0}))}const et={bubbles:!0,cancelable:!0,composed:!0};const it=Promise.resolve();function st(t){const{documentElement:i}=t.ownerDocument,s=t.getBoundingClientRect();return{left:s.left+(e.pageXOffset-i.clientLeft),top:s.top+(e.pageYOffset-i.clientTop)}}const nt=/deg|g?rad|turn$/i;function at(t){const e=parseFloat(t)||0;if(0!==e){const[i="rad"]=String(t).match(nt)||[];switch(i.toLowerCase()){case"deg":return e/360*(2*Math.PI);case"grad":return e/400*(2*Math.PI);case"turn":return e*(2*Math.PI)}}return e}const ot="contain";function ht(t,e=ot){const{aspectRatio:i}=t;let{width:s,height:n}=t;const a=j(s),o=j(n);if(a&&o){const t=n*i;e===ot&&t>s||"cover"===e&&t<s?n=s/i:s=n*i}else a?n=s/i:o&&(s=n*i);return{width:s,height:n}}function rt(t,...e){if(0===e.length)return t;const[i,s,n,a,o,h]=t,[r,c,l,d,$,u]=e[0];return rt(t=[i*r+n*c,s*r+a*c,i*l+n*d,s*l+a*d,i*$+n*u+o,s*$+a*u+h],...e.slice(1))}const ct=/left|top|width|height/i,lt="open",dt=new WeakMap,$t=new WeakMap,ut=new Map,gt=e.document&&Array.isArray(e.document.adoptedStyleSheets)&&"replaceSync"in e.CSSStyleSheet.prototype;class pt extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var t,e;super(),this.shadowRootMode=lt,this.slottable=!0;const i=null===(e=null===(t=Object.getPrototypeOf(this))||void 0===t?void 0:t.constructor)||void 0===e?void 0:e.$name;i&&ut.set(i,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,i){if(Object.is(i,e))return;const s=J(t);let n=i;switch(typeof this[s]){case"boolean":n=null!==i&&"false"!==i;break;case"number":n=Number(i)}switch(this[s]=n,t){case"theme-color":{const t=$t.get(this),e=this.$sharedStyle;t&&e&&(gt?t.replaceSync(e):t.textContent=e);break}}}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(t=Z(t),typeof i){case"boolean":!0===i?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":i=W(i)?"":String(i);default:i?this.getAttribute(t)!==i&&this.setAttribute(t,i):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((t=>{const e=J(t);let i=this[e];(function(t){return void 0===t})(i)||this.$propertyChangedCallback(e,void 0,i),Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:()=>i,set(t){const s=i;i=t,this.$propertyChangedCallback(e,s,t)}})}));const t=this.attachShadow({mode:this.shadowRootMode||lt});if(this.shadowRoot||dt.set(this,t),$t.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){$t.has(this)&&$t.delete(this),dt.has(this)&&dt.delete(this)}$getTagNameOf(t){var e;return null!==(e=ut.get(t))&&void 0!==e?e:t}$setStyles(t){return Object.keys(t).forEach((e=>{let i=t[e];L(i)&&(i=0!==i&&ct.test(e)?`${i}px`:String(i)),this.style[e]=i})),this}$getShadowRoot(){return this.shadowRoot||dt.get(this)}$addStyles(t){let e;const i=this.$getShadowRoot();return gt?(e=new CSSStyleSheet,e.replaceSync(t),i.adoptedStyleSheets=i.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,i.appendChild(e)),e}$emit(t,e,i){return function(t,e,i,s){return t.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign(Object.assign({},et),{detail:i}),s)))}(this,t,e,i)}$nextTick(t){return function(t,e){return e?it.then(t?e.bind(t):e):it}(this,t)}static $define(i,s){H(i)&&(s=i,i=""),i||(i=this.$name||this.name),i=Z(i),t&&e.customElements&&!e.customElements.get(i)&&customElements.define(i,this,s)}}pt.$version="2.0.0";class mt extends pt{constructor(){super(...arguments),this.$onPointerDown=null,this.$onPointerMove=null,this.$onPointerUp=null,this.$onWheel=null,this.$wheeling=!1,this.$pointers=new Map,this.$style=':host{display:block;min-height:100px;min-width:200px;overflow:hidden;position:relative;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([background]){background-color:#fff;background-image:repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc),repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc);background-image:repeating-conic-gradient(#ccc 0 25%,#fff 0 50%);background-position:0 0,.5rem .5rem;background-size:1rem 1rem}:host([disabled]){pointer-events:none}:host([disabled]):after{bottom:0;content:"";cursor:not-allowed;display:block;left:0;pointer-events:none;position:absolute;right:0;top:0}',this.$action=f,this.background=!1,this.disabled=!1,this.scaleStep=.1,this.themeColor="#39f"}static get observedAttributes(){return super.observedAttributes.concat(["background","disabled","scale-step"])}connectedCallback(){super.connectedCallback(),this.disabled||this.$bind()}disconnectedCallback(){this.disabled||this.$unbind(),super.disconnectedCallback()}$propertyChangedCallback(t,e,i){if(!Object.is(i,e)&&(super.$propertyChangedCallback(t,e,i),"disabled"===t))i?this.$unbind():this.$bind()}$bind(){this.$onPointerDown||(this.$onPointerDown=this.$handlePointerDown.bind(this),_(this,E,this.$onPointerDown)),this.$onPointerMove||(this.$onPointerMove=this.$handlePointerMove.bind(this),_(this.ownerDocument,T,this.$onPointerMove)),this.$onPointerUp||(this.$onPointerUp=this.$handlePointerUp.bind(this),_(this.ownerDocument,z,this.$onPointerUp)),this.$onWheel||(this.$onWheel=this.$handleWheel.bind(this),_(this,O,this.$onWheel,{passive:!1,capture:!0}))}$unbind(){this.$onPointerDown&&(V(this,E,this.$onPointerDown),this.$onPointerDown=null),this.$onPointerMove&&(V(this.ownerDocument,T,this.$onPointerMove),this.$onPointerMove=null),this.$onPointerUp&&(V(this.ownerDocument,z,this.$onPointerUp),this.$onPointerUp=null),this.$onWheel&&(V(this,O,this.$onWheel,{capture:!0}),this.$onWheel=null)}$handlePointerDown(t){const{buttons:e,button:i,type:s}=t;if(this.disabled||("pointerdown"===s&&"mouse"===t.pointerType||"mousedown"===s)&&(L(e)&&1!==e||L(i)&&0!==i||t.ctrlKey))return;const{$pointers:n}=this;let a="";if(t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:i})=>{n.set(t,{startX:e,startY:i,endX:e,endY:i})}));else{const{pointerId:e=0,pageX:i,pageY:s}=t;n.set(e,{startX:i,startY:s,endX:i,endY:s})}n.size>1?a=b:q(t.target)&&(a=t.target.action||t.target.getAttribute("action")||""),!1!==this.$emit(R,{action:a,relatedEvent:t})&&(t.preventDefault(),this.$action=a,this.style.willChange="transform")}$handlePointerMove(t){const{$action:e,$pointers:i}=this;if(this.disabled||e===f||0===i.size)return;if(!1===this.$emit("actionmove",{action:e,relatedEvent:t}))return;if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:s})=>{const n=i.get(t);n&&Object.assign(n,{endX:e,endY:s})}));else{const{pointerId:e=0,pageX:s,pageY:n}=t,a=i.get(e);a&&Object.assign(a,{endX:s,endY:n})}const s={action:e,relatedEvent:t};if(e===b){const e=new Map(i);let n=0,a=0,o=0,h=0,r=t.pageX,c=t.pageY;i.forEach(((t,i)=>{e.delete(i),e.forEach((e=>{let i=e.startX-t.startX,s=e.startY-t.startY,l=e.endX-t.endX,d=e.endY-t.endY,$=0,u=0,g=0,p=0;if(0===i?s<0?g=2*Math.PI:s>0&&(g=Math.PI):i>0?g=Math.PI/2+Math.atan(s/i):i<0&&(g=1.5*Math.PI+Math.atan(s/i)),0===l?d<0?p=2*Math.PI:d>0&&(p=Math.PI):l>0?p=Math.PI/2+Math.atan(d/l):l<0&&(p=1.5*Math.PI+Math.atan(d/l)),p>0||g>0){const i=p-g,s=Math.abs(i);s>n&&(n=s,o=i,r=(t.startX+e.startX)/2,c=(t.startY+e.startY)/2)}if(i=Math.abs(i),s=Math.abs(s),l=Math.abs(l),d=Math.abs(d),i>0&&s>0?$=Math.sqrt(i*i+s*s):i>0?$=i:s>0&&($=s),l>0&&d>0?u=Math.sqrt(l*l+d*d):l>0?u=l:d>0&&(u=d),$>0&&u>0){const i=(u-$)/$,s=Math.abs(i);s>a&&(a=s,h=i,r=(t.startX+e.startX)/2,c=(t.startY+e.startY)/2)}}))}));const l=n>0,d=a>0;l&&d?(s.rotate=o,s.scale=h,s.centerX=r,s.centerY=c):l?(s.action=m,s.rotate=o,s.centerX=r,s.centerY=c):d?(s.action=p,s.scale=h,s.centerX=r,s.centerY=c):s.action=f}else{const[t]=Array.from(i.values());Object.assign(s,t)}i.forEach((t=>{t.startX=t.endX,t.startY=t.endY})),s.action!==f&&this.$emit(I,s,{cancelable:!1})}$handlePointerUp(t){const{$action:e,$pointers:i}=this;if(!this.disabled&&e!==f&&!1!==this.$emit(N,{action:e,relatedEvent:t})){if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t})=>{i.delete(t)}));else{const{pointerId:e=0}=t;i.delete(e)}0===i.size&&(this.style.willChange="",this.$action=f)}}$handleWheel(t){if(this.disabled)return;if(t.preventDefault(),this.$wheeling)return;this.$wheeling=!0,setTimeout((()=>{this.$wheeling=!1}),50);const e=(t.deltaY>0?-1:1)*this.scaleStep;this.$emit(I,{action:p,scale:e,relatedEvent:t},{cancelable:!1})}$setAction(t){return"string"==typeof t&&(this.$action=t),this}$toCanvas(t){return new Promise(((e,i)=>{if(!this.isConnected)return void i(new Error("The current element is not connected to the DOM."));const s=document.createElement("canvas");let n=this.offsetWidth,a=this.offsetHeight,o=1;K(t)&&(j(t.width)||j(t.height))&&(({width:n,height:a}=ht({aspectRatio:n/a,width:t.width,height:t.height})),o=n/this.offsetWidth),s.width=n,s.height=a;const h=this.querySelector(this.$getTagNameOf(c));h?h.$ready().then((i=>{const r=s.getContext("2d");if(r){const[e,c,l,d,$,u]=h.$getTransform();let g=$,p=u,m=i.naturalWidth,b=i.naturalHeight;1!==o&&(g*=o,p*=o,m*=o,b*=o);const f=m/2,v=b/2;r.fillStyle="transparent",r.fillRect(0,0,n,a),K(t)&&U(t.beforeDraw)&&t.beforeDraw.call(this,r,s),r.save(),r.translate(f,v),r.transform(e,c,l,d,g,p),r.translate(-f,-v),r.drawImage(i,0,0,m,b),r.restore()}e(s)})).catch(i):e(s)}))}}mt.$name=a,mt.$version="2.0.0";const bt=new WeakMap,ft=["alt","crossorigin","decoding","importance","loading","referrerpolicy","sizes","src","srcset"];class vt extends pt{constructor(){super(...arguments),this.$matrix=[1,0,0,1,0,0],this.$onLoad=null,this.$onCanvasAction=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$actionStartTarget=null,this.$style=":host{display:inline-block}img{display:block;height:100%;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}",this.$image=new Image,this.initialCenterSize="contain",this.rotatable=!1,this.scalable=!1,this.skewable=!1,this.slottable=!1,this.translatable=!1}set $canvas(t){bt.set(this,t)}get $canvas(){return bt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(ft,["initial-center-size","rotatable","scalable","skewable","translatable"])}attributeChangedCallback(t,e,i){Object.is(i,e)||(super.attributeChangedCallback(t,e,i),ft.includes(t)&&this.$image.setAttribute(t,i))}$propertyChangedCallback(t,e,i){if(!Object.is(i,e)&&(super.$propertyChangedCallback(t,e,i),"initialCenterSize"===t))this.$nextTick((()=>{this.$center(i)}))}connectedCallback(){super.connectedCallback();const{$image:t}=this,e=this.closest(this.$getTagNameOf(a));e&&(this.$canvas=e,this.$setStyles({display:"block",position:"absolute"}),this.$onCanvasActionStart=t=>{var e,i;this.$actionStartTarget=null===(i=null===(e=t.detail)||void 0===e?void 0:e.relatedEvent)||void 0===i?void 0:i.target},this.$onCanvasActionEnd=()=>{this.$actionStartTarget=null},this.$onCanvasAction=this.$handleAction.bind(this),_(e,R,this.$onCanvasActionStart),_(e,N,this.$onCanvasActionEnd),_(e,I,this.$onCanvasAction)),this.$onLoad=this.$handleLoad.bind(this),_(t,P,this.$onLoad),this.$getShadowRoot().appendChild(t)}disconnectedCallback(){const{$image:t,$canvas:e}=this;e&&(this.$onCanvasActionStart&&(V(e,R,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(V(e,N,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(V(e,I,this.$onCanvasAction),this.$onCanvasAction=null)),t&&this.$onLoad&&(V(t,P,this.$onLoad),this.$onLoad=null),this.$getShadowRoot().removeChild(t),super.disconnectedCallback()}$handleLoad(){const{$image:t}=this;this.$setStyles({width:t.naturalWidth,height:t.naturalHeight}),this.$canvas&&this.$center(this.initialCenterSize)}$handleAction(t){if(this.hidden||!(this.rotatable||this.scalable||this.translatable))return;const{$canvas:e}=this,{detail:i}=t;if(i){const{relatedEvent:t}=i;let{action:s}=i;switch(s!==b||this.rotatable&&this.scalable||(s=this.rotatable?m:this.scalable?p:f),s){case g:if(this.translatable){let s=null;t&&(s=t.target.closest(this.$getTagNameOf(l))),s||(s=e.querySelector(this.$getTagNameOf(l))),s&&s.multiple&&!s.active&&(s=e.querySelector(`${this.$getTagNameOf(l)}[active]`)),s&&!s.hidden&&s.movable&&!s.dynamic&&this.$actionStartTarget&&s.contains(this.$actionStartTarget)||this.$move(i.endX-i.startX,i.endY-i.startY)}break;case m:if(this.rotatable)if(t){const{x:e,y:s}=this.getBoundingClientRect();this.$rotate(i.rotate,t.clientX-e,t.clientY-s)}else this.$rotate(i.rotate);break;case p:if(this.scalable)if(t){const e=t.target.closest(this.$getTagNameOf(l));if(!e||!e.zoomable||e.zoomable&&e.dynamic){const{x:e,y:s}=this.getBoundingClientRect();this.$zoom(i.scale,t.clientX-e,t.clientY-s)}}else this.$zoom(i.scale);break;case b:if(this.rotatable&&this.scalable){const{rotate:e}=i;let{scale:s}=i;s<0?s=1/(1-s):s+=1;const n=Math.cos(e),a=Math.sin(e),[o,h,r,c]=[n*s,a*s,-a*s,n*s];if(t){const e=this.getBoundingClientRect(),i=t.clientX-e.x,s=t.clientY-e.y,[n,a,l,d]=this.$matrix,$=i-e.width/2,u=s-e.height/2,g=($*d-l*u)/(n*d-l*a),p=(u*n-a*$)/(n*d-l*a);this.$transform(o,h,r,c,g*(1-o)+p*r,p*(1-c)+g*h)}else this.$transform(o,h,r,c,0,0)}}}}$ready(t){const{$image:e}=this,i=new Promise(((t,i)=>{const s=new Error("Failed to load the image source");if(e.complete)e.naturalWidth>0&&e.naturalHeight>0?t(e):i(s);else{const n=()=>{V(e,M,a),t(e)},a=()=>{V(e,P,n),i(s)};tt(e,P,n),tt(e,M,a)}}));return U(t)&&i.then((e=>(t(e),e))),i}$center(t){const{parentElement:e}=this;if(!e)return this;const i=e.getBoundingClientRect(),s=i.width,n=i.height,{x:a,y:o,width:h,height:r}=this.getBoundingClientRect(),c=a+h/2,l=o+r/2,d=i.x+s/2,$=i.y+n/2;if(this.$move(d-c,$-l),t&&(h!==s||r!==n)){const e=s/h,i=n/r;switch(t){case"cover":this.$scale(Math.max(e,i));break;case"contain":this.$scale(Math.min(e,i))}}return this}$move(t,e=t){if(this.translatable&&L(t)&&L(e)){const[i,s,n,a]=this.$matrix,o=(t*a-n*e)/(i*a-n*s),h=(e*i-s*t)/(i*a-n*s);this.$translate(o,h)}return this}$moveTo(t,e=t){if(this.translatable&&L(t)&&L(e)){const[i,s,n,a]=this.$matrix,o=(t*a-n*e)/(i*a-n*s),h=(e*i-s*t)/(i*a-n*s);this.$setTransform(i,s,n,a,o,h)}return this}$rotate(t,e,i){if(this.rotatable){const s=at(t),n=Math.cos(s),a=Math.sin(s),[o,h,r,c]=[n,a,-a,n];if(L(e)&&L(i)){const[t,s,n,a]=this.$matrix,{width:l,height:d}=this.getBoundingClientRect(),$=e-l/2,u=i-d/2,g=($*a-n*u)/(t*a-n*s),p=(u*t-s*$)/(t*a-n*s);this.$transform(o,h,r,c,g*(1-o)-p*r,p*(1-c)-g*h)}else this.$transform(o,h,r,c,0,0)}return this}$zoom(t,e,i){if(!this.scalable||0===t)return this;if(t<0?t=1/(1-t):t+=1,L(e)&&L(i)){const[s,n,a,o]=this.$matrix,{width:h,height:r}=this.getBoundingClientRect(),c=e-h/2,l=i-r/2,d=(c*o-a*l)/(s*o-a*n),$=(l*s-n*c)/(s*o-a*n);this.$transform(t,0,0,t,d*(1-t),$*(1-t))}else this.$scale(t);return this}$scale(t,e=t){return this.scalable&&this.$transform(t,0,0,e,0,0),this}$skew(t,e=0){if(this.skewable){const i=at(t),s=at(e);this.$transform(1,Math.tan(s),Math.tan(i),1,0,0)}return this}$translate(t,e=t){return this.translatable&&L(t)&&L(e)&&this.$transform(1,0,0,1,t,e),this}$transform(t,e,i,s,n,a){return L(t)&&L(e)&&L(i)&&L(s)&&L(n)&&L(a)?this.$setTransform(rt(this.$matrix,[t,e,i,s,n,a])):this}$setTransform(t,e,i,s,n,a){if((this.rotatable||this.scalable||this.skewable||this.translatable)&&(Array.isArray(t)&&([t,e,i,s,n,a]=t),L(t)&&L(e)&&L(i)&&L(s)&&L(n)&&L(a))){const o=[...this.$matrix],h=[t,e,i,s,n,a];if(!1===this.$emit(Y,{matrix:h,oldMatrix:o}))return this;this.$matrix=h,this.style.transform=`matrix(${h.join(", ")})`}return this}$getTransform(){return this.$matrix.slice()}$resetTransform(){return this.$setTransform([1,0,0,1,0,0])}}vt.$name=c,vt.$version="2.0.0";const wt=new WeakMap;class yt extends pt{constructor(){super(...arguments),this.$onCanvasChange=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$style=":host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}",this.x=0,this.y=0,this.width=0,this.height=0,this.slottable=!1,this.themeColor="rgba(0, 0, 0, 0.65)"}set $canvas(t){wt.set(this,t)}get $canvas(){return wt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["height","width","x","y"])}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(a));if(t){this.$canvas=t,this.style.position="absolute";const e=t.querySelector(this.$getTagNameOf(l));e&&(this.$onCanvasActionStart=t=>{e.hidden&&t.detail.action===u&&(this.hidden=!1)},this.$onCanvasActionEnd=t=>{e.hidden&&t.detail.action===u&&(this.hidden=!0)},this.$onCanvasChange=t=>{const{x:i,y:s,width:n,height:a}=t.detail;this.$change(i,s,n,a),(e.hidden||0===i&&0===s&&0===n&&0===a)&&(this.hidden=!0)},_(t,R,this.$onCanvasActionStart),_(t,N,this.$onCanvasActionEnd),_(t,X,this.$onCanvasChange))}this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(V(t,R,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(V(t,N,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasChange&&(V(t,X,this.$onCanvasChange),this.$onCanvasChange=null)),super.disconnectedCallback()}$change(t,e,i=this.width,s=this.height){return L(t)&&L(e)&&L(i)&&L(s)&&(t!==this.x||e!==this.y||i!==this.width||s!==this.height)?(this.hidden&&(this.hidden=!1),this.x=t,this.y=e,this.width=i,this.height=s,this.$render()):this}$reset(){return this.$change(0,0,0,0)}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height,outlineWidth:e.innerWidth})}}yt.$name=d,yt.$version="2.0.0";class Ct extends pt{constructor(){super(...arguments),this.$onCanvasCropEnd=null,this.$onCanvasCropStart=null,this.$style=':host{background-color:var(--theme-color);display:block}:host([action=move]),:host([action=select]){height:100%;left:0;position:absolute;top:0;width:100%}:host([action=move]){cursor:move}:host([action=select]){cursor:crosshair}:host([action$=-resize]){background-color:transparent;height:15px;position:absolute;width:15px}:host([action$=-resize]):after{background-color:var(--theme-color);content:"";display:block;height:5px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:5px}:host([action=n-resize]),:host([action=s-resize]){cursor:ns-resize;left:50%;transform:translateX(-50%);width:100%}:host([action=n-resize]){top:-8px}:host([action=s-resize]){bottom:-8px}:host([action=e-resize]),:host([action=w-resize]){cursor:ew-resize;height:100%;top:50%;transform:translateY(-50%)}:host([action=e-resize]){right:-8px}:host([action=w-resize]){left:-8px}:host([action=ne-resize]){cursor:nesw-resize;right:-8px;top:-8px}:host([action=nw-resize]){cursor:nwse-resize;left:-8px;top:-8px}:host([action=se-resize]){bottom:-8px;cursor:nwse-resize;right:-8px}:host([action=se-resize]):after{height:15px;width:15px}@media (pointer:coarse){:host([action=se-resize]):after{height:10px;width:10px}}@media (pointer:fine){:host([action=se-resize]):after{height:5px;width:5px}}:host([action=sw-resize]){bottom:-8px;cursor:nesw-resize;left:-8px}:host([plain]){background-color:transparent}',this.action=f,this.plain=!1,this.slottable=!1,this.themeColor="rgba(51, 153, 255, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["action","plain"])}}Ct.$name=r,Ct.$version="2.0.0";const kt=new WeakMap;class St extends pt{constructor(){super(...arguments),this.$onCanvasAction=null,this.$onCanvasActionStart=null,this.$onCanvasActionEnd=null,this.$onDocumentKeyDown=null,this.$action="",this.$actionStartTarget=null,this.$changing=!1,this.$style=':host{display:block;left:0;position:relative;right:0}:host([outlined]){outline:1px solid var(--theme-color)}:host([multiple]){outline:1px dashed hsla(0,0%,100%,.5)}:host([multiple]):after{bottom:0;content:"";cursor:pointer;display:block;left:0;position:absolute;right:0;top:0}:host([multiple][active]){outline-color:var(--theme-color);z-index:1}:host([multiple])>*{visibility:hidden}:host([multiple][active])>*{visibility:visible}:host([multiple][active]):after{display:none}',this.$initialSelection={x:0,y:0,width:0,height:0},this.x=0,this.y=0,this.width=0,this.height=0,this.aspectRatio=NaN,this.initialAspectRatio=NaN,this.initialCoverage=NaN,this.active=!1,this.linked=!1,this.dynamic=!1,this.movable=!1,this.resizable=!1,this.zoomable=!1,this.multiple=!1,this.keyboard=!1,this.outlined=!1,this.precise=!1}set $canvas(t){kt.set(this,t)}get $canvas(){return kt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["active","aspect-ratio","dynamic","height","initial-aspect-ratio","initial-coverage","keyboard","linked","movable","multiple","outlined","precise","resizable","width","x","y","zoomable"])}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(super.$propertyChangedCallback(t,e,i),t){case"x":case"y":case"width":case"height":this.$changing||this.$nextTick((()=>{this.$change(this.x,this.y,this.width,this.height,this.aspectRatio,!0)}));break;case"aspectRatio":case"initialAspectRatio":this.$nextTick((()=>{this.$initSelection()}));break;case"initialCoverage":this.$nextTick((()=>{j(i)&&i<=1&&this.$initSelection(!0,!0)}));break;case"keyboard":this.$nextTick((()=>{this.$canvas&&(i?this.$onDocumentKeyDown||(this.$onDocumentKeyDown=this.$handleKeyDown.bind(this),_(this.ownerDocument,D,this.$onDocumentKeyDown)):this.$onDocumentKeyDown&&(V(this.ownerDocument,D,this.$onDocumentKeyDown),this.$onDocumentKeyDown=null))}));break;case"multiple":this.$nextTick((()=>{if(this.$canvas){const t=this.$getSelections();i?(t.forEach((t=>{t.active=!1})),this.active=!0,this.$emit(X,{x:this.x,y:this.y,width:this.width,height:this.height})):(this.active=!1,t.slice(1).forEach((t=>{this.$removeSelection(t)})))}}));break;case"precise":this.$nextTick((()=>{this.$change(this.x,this.y)}));break;case"linked":i&&(this.dynamic=!0)}}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(a));t?(this.$canvas=t,this.$setStyles({position:"absolute",transform:`translate(${this.x}px, ${this.y}px)`}),this.hidden||this.$render(),this.$initSelection(!0),this.$onCanvasActionStart=this.$handleActionStart.bind(this),this.$onCanvasActionEnd=this.$handleActionEnd.bind(this),this.$onCanvasAction=this.$handleAction.bind(this),_(t,R,this.$onCanvasActionStart),_(t,N,this.$onCanvasActionEnd),_(t,I,this.$onCanvasAction)):this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(V(t,R,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(V(t,N,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(V(t,I,this.$onCanvasAction),this.$onCanvasAction=null)),super.disconnectedCallback()}$getSelections(){let t=[];return this.parentElement&&(t=Array.from(this.parentElement.querySelectorAll(this.$getTagNameOf(l)))),t}$initSelection(t=!1,e=!1){const{initialCoverage:i,parentElement:s}=this;if(j(i)&&s){const n=this.aspectRatio||this.initialAspectRatio;let a=(e?0:this.width)||s.offsetWidth*i,o=(e?0:this.height)||s.offsetHeight*i;j(n)&&({width:a,height:o}=ht({aspectRatio:n,width:a,height:o})),this.$change(this.x,this.y,a,o),t&&this.$center(),this.$initialSelection={x:this.x,y:this.y,width:this.width,height:this.height}}}$createSelection(){const t=this.cloneNode(!0);return this.hasAttribute("id")&&t.removeAttribute("id"),t.initialCoverage=NaN,this.active=!1,this.parentElement&&this.parentElement.insertBefore(t,this.nextSibling),t}$removeSelection(t=this){if(this.parentElement){const e=this.$getSelections();if(e.length>1){const i=e.indexOf(t),s=e[i+1]||e[i-1];s&&(t.active=!1,this.parentElement.removeChild(t),s.active=!0,s.$emit(X,{x:s.x,y:s.y,width:s.width,height:s.height}))}else this.$clear()}}$handleActionStart(t){var e,i;const s=null===(i=null===(e=t.detail)||void 0===e?void 0:e.relatedEvent)||void 0===i?void 0:i.target;this.$action="",this.$actionStartTarget=s,!this.hidden&&this.multiple&&!this.active&&s===this&&this.parentElement&&(this.$getSelections().forEach((t=>{t.active=!1})),this.active=!0,this.$emit(X,{x:this.x,y:this.y,width:this.width,height:this.height}))}$handleAction(t){const{currentTarget:e,detail:i}=t;if(!e||!i)return;const{relatedEvent:s}=i;let{action:n}=i;if(!n&&this.multiple&&(n=this.$action||(null==s?void 0:s.target.action),this.$action=n),!n||this.hidden&&n!==u||this.multiple&&!this.active&&n!==p)return;const a=i.endX-i.startX,o=i.endY-i.startY,{width:h,height:r}=this;let{aspectRatio:c}=this;switch(!j(c)&&s.shiftKey&&(c=j(h)&&j(r)?h/r:1),n){case u:if(0!==a&&0!==o){const{$canvas:t}=this,s=st(e);(this.multiple&&!this.hidden?this.$createSelection():this).$change(i.startX-s.left,i.startY-s.top,Math.abs(a),Math.abs(o),c),a<0?o<0?n=S:o>0&&(n=A):a>0&&(o<0?n=k:o>0&&(n=x)),t&&(t.$action=n)}break;case g:this.movable&&(this.dynamic||this.$actionStartTarget&&this.contains(this.$actionStartTarget))&&this.$move(a,o);break;case p:if(s&&this.zoomable&&(this.dynamic||this.contains(s.target))){const t=st(e);this.$zoom(i.scale,s.pageX-t.left,s.pageY-t.top)}break;default:this.$resize(n,a,o,c)}}$handleActionEnd(){this.$action="",this.$actionStartTarget=null}$handleKeyDown(t){if(this.hidden||!this.keyboard||this.multiple&&!this.active||t.defaultPrevented)return;const{activeElement:e}=document;if(!e||!["INPUT","TEXTAREA"].includes(e.tagName)&&!["true","plaintext-only"].includes(e.contentEditable))switch(t.key){case"Backspace":t.metaKey&&(t.preventDefault(),this.$removeSelection());break;case"Delete":t.preventDefault(),this.$removeSelection();break;case"ArrowLeft":t.preventDefault(),this.$move(-1,0);break;case"ArrowRight":t.preventDefault(),this.$move(1,0);break;case"ArrowUp":t.preventDefault(),this.$move(0,-1);break;case"ArrowDown":t.preventDefault(),this.$move(0,1);break;case"+":t.preventDefault(),this.$zoom(.1);break;case"-":t.preventDefault(),this.$zoom(-.1)}}$center(){const{parentElement:t}=this;if(!t)return this;const e=(t.offsetWidth-this.width)/2,i=(t.offsetHeight-this.height)/2;return this.$change(e,i)}$move(t,e=t){return this.$moveTo(this.x+t,this.y+e)}$moveTo(t,e=t){return this.movable?this.$change(t,e):this}$resize(t,e=0,i=0,s=this.aspectRatio){if(!this.resizable)return this;const n=j(s),{$canvas:a}=this;let{x:o,y:h,width:r,height:c}=this;switch(t){case v:h+=i,c-=i,c<0&&(t=y,c=-c,h-=c),n&&(o+=(e=i*s)/2,r-=e,r<0&&(r=-r,o-=r));break;case w:r+=e,r<0&&(t=C,r=-r,o-=r),n&&(h-=(i=e/s)/2,c+=i,c<0&&(c=-c,h-=c));break;case y:c+=i,c<0&&(t=v,c=-c,h-=c),n&&(o-=(e=i*s)/2,r+=e,r<0&&(r=-r,o-=r));break;case C:o+=e,r-=e,r<0&&(t=w,r=-r,o-=r),n&&(h+=(i=e/s)/2,c-=i,c<0&&(c=-c,h-=c));break;case k:n&&(i=-e/s),h+=i,c-=i,r+=e,r<0&&c<0?(t=A,r=-r,c=-c,o-=r,h-=c):r<0?(t=S,r=-r,o-=r):c<0&&(t=x,c=-c,h-=c);break;case S:n&&(i=e/s),o+=e,h+=i,r-=e,c-=i,r<0&&c<0?(t=x,r=-r,c=-c,o-=r,h-=c):r<0?(t=k,r=-r,o-=r):c<0&&(t=A,c=-c,h-=c);break;case x:n&&(i=e/s),r+=e,c+=i,r<0&&c<0?(t=S,r=-r,c=-c,o-=r,h-=c):r<0?(t=A,r=-r,o-=r):c<0&&(t=k,c=-c,h-=c);break;case A:n&&(i=-e/s),o+=e,r-=e,c+=i,r<0&&c<0?(t=k,r=-r,c=-c,o-=r,h-=c):r<0?(t=x,r=-r,o-=r):c<0&&(t=S,c=-c,h-=c)}return a&&a.$setAction(t),this.$change(o,h,r,c)}$zoom(t,e,i){if(!this.zoomable||0===t)return this;t<0?t=1/(1-t):t+=1;const{width:s,height:n}=this,a=s*t,o=n*t;let h=this.x,r=this.y;return L(e)&&L(i)?(h-=(a-s)*((e-this.x)/s),r-=(o-n)*((i-this.y)/n)):(h-=(a-s)/2,r-=(o-n)/2),this.$change(h,r,a,o)}$change(t,e,i=this.width,s=this.height,n=this.aspectRatio,a=!1){return this.$changing||!L(t)||!L(e)||!L(i)||!L(s)||i<0||s<0?this:(j(n)&&({width:i,height:s}=ht({aspectRatio:n,width:i,height:s},"cover")),this.precise||(t=Math.round(t),e=Math.round(e),i=Math.round(i),s=Math.round(s)),t===this.x&&e===this.y&&i===this.width&&s===this.height&&Object.is(n,this.aspectRatio)&&!a?this:(this.hidden&&(this.hidden=!1),!1===this.$emit(X,{x:t,y:e,width:i,height:s})?this:(this.$changing=!0,this.x=t,this.y=e,this.width=i,this.height=s,this.$changing=!1,this.$render())))}$reset(){const{x:t,y:e,width:i,height:s}=this.$initialSelection;return this.$change(t,e,i,s)}$clear(){return this.$change(0,0,0,0,NaN,!0),this.hidden=!0,this}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height})}$toCanvas(t){return new Promise(((e,i)=>{if(!this.isConnected)return void i(new Error("The current element is not connected to the DOM."));const s=document.createElement("canvas");let{width:n,height:a}=this,o=1;if(K(t)&&(j(t.width)||j(t.height))&&(({width:n,height:a}=ht({aspectRatio:n/a,width:t.width,height:t.height})),o=n/this.width),s.width=n,s.height=a,!this.$canvas)return void e(s);const h=this.$canvas.querySelector(this.$getTagNameOf(c));h?h.$ready().then((i=>{const r=s.getContext("2d");if(r){const[e,c,l,d,$,u]=h.$getTransform(),g=-this.x,p=-this.y,m=(g*d-l*p)/(e*d-l*c),b=(p*e-c*g)/(e*d-l*c);let f=e*m+l*b+$,v=c*m+d*b+u,w=i.naturalWidth,y=i.naturalHeight;1!==o&&(f*=o,v*=o,w*=o,y*=o);const C=w/2,k=y/2;r.fillStyle="transparent",r.fillRect(0,0,n,a),K(t)&&U(t.beforeDraw)&&t.beforeDraw.call(this,r,s),r.save(),r.translate(C,k),r.transform(e,c,l,d,f,v),r.translate(-C,-k),r.drawImage(i,0,0,w,y),r.restore()}e(s)})).catch(i):e(s)}))}}St.$name=l,St.$version="2.0.0";class xt extends pt{constructor(){super(...arguments),this.$style=":host{display:flex;flex-direction:column;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([bordered]){border:1px dashed var(--theme-color)}:host([covered]){bottom:0;left:0;position:absolute;right:0;top:0}:host>span{display:flex;flex:1}:host>span+span{border-top:1px dashed var(--theme-color)}:host>span>span{flex:1}:host>span>span+span{border-left:1px dashed var(--theme-color)}",this.bordered=!1,this.columns=3,this.covered=!1,this.rows=3,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["bordered","columns","covered","rows"])}$propertyChangedCallback(t,e,i){Object.is(i,e)||(super.$propertyChangedCallback(t,e,i),"rows"!==t&&"columns"!==t||this.$nextTick((()=>{this.$render()})))}connectedCallback(){super.connectedCallback(),this.$render()}$render(){const t=this.$getShadowRoot(),e=document.createDocumentFragment();for(let t=0;t<this.rows;t+=1){const t=document.createElement("span");t.setAttribute("role","row");for(let e=0;e<this.columns;e+=1){const e=document.createElement("span");e.setAttribute("role","gridcell"),t.appendChild(e)}e.appendChild(t)}t&&(t.innerHTML="",t.appendChild(e))}}xt.$name=h,xt.$version="2.0.0";class At extends pt{constructor(){super(...arguments),this.$style=':host{display:inline-block;height:1em;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle;width:1em}:host:after,:host:before{background-color:var(--theme-color);content:"";display:block;position:absolute}:host:before{height:1px;left:0;top:50%;transform:translateY(-50%);width:100%}:host:after{height:100%;left:50%;top:0;transform:translateX(-50%);width:1px}:host([centered]){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}',this.centered=!1,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["centered"])}}At.$name=o,At.$version="2.0.0";const Et=new WeakMap,Tt=new WeakMap,zt=new WeakMap,Mt=new WeakMap,Dt="vertical";class Pt extends pt{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}",this.resize=Dt,this.selection="",this.slottable=!1}set $image(t){Tt.set(this,t)}get $image(){return Tt.get(this)}set $sourceImage(t){Mt.set(this,t)}get $sourceImage(){return Mt.get(this)}set $canvas(t){Et.set(this,t)}get $canvas(){return Et.get(this)}set $selection(t){zt.set(this,t)}get $selection(){return zt.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let t=null;if(t=this.selection?this.ownerDocument.querySelector(this.selection):this.closest(this.$getTagNameOf(l)),q(t)){this.$selection=t,this.$onSelectionChange=this.$handleSelectionChange.bind(this),_(t,X,this.$onSelectionChange);const e=t.closest(this.$getTagNameOf(a));if(e){this.$canvas=e;const t=e.querySelector(this.$getTagNameOf(c));t&&(this.$sourceImage=t,this.$image=t.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),_(t.$image,P,this.$onSourceImageLoad),_(t,Y,this.$onSourceImageTransform))}this.$render()}}disconnectedCallback(){const{$selection:t,$sourceImage:e}=this;t&&this.$onSelectionChange&&(V(t,X,this.$onSelectionChange),this.$onSelectionChange=null),e&&this.$onSourceImageLoad&&(V(e.$image,P,this.$onSourceImageLoad),this.$onSourceImageLoad=null),e&&this.$onSourceImageTransform&&(V(e,Y,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(t){this.$render(t.detail)}$handleSourceImageLoad(){const{$image:t,$sourceImage:e}=this,i=t.getAttribute("src"),s=e.getAttribute("src");s&&s!==i&&(t.setAttribute("src",s),t.$ready((()=>{setTimeout((()=>{this.$render()}),50)})))}$handleSourceImageTransform(t){this.$render(void 0,t.detail.matrix)}$render(t,e){const{$canvas:i,$selection:s}=this;t||s.hidden||(t=s),(!t||0===t.x&&0===t.y&&0===t.width&&0===t.height)&&(t={x:0,y:0,width:i.offsetWidth,height:i.offsetHeight});const{x:n,y:a,width:o,height:h}=t,r={},{clientWidth:c,clientHeight:l}=this;let d=c,$=l,u=NaN;switch(this.resize){case"both":u=1,d=o,$=h,r.width=o,r.height=h;break;case"horizontal":u=h>0?l/h:0,d=o*u,r.width=d;break;case Dt:u=o>0?c/o:0,$=h*u,r.height=$;break;default:c>0?u=o>0?c/o:0:l>0&&(u=h>0?l/h:0)}this.$scale=u,this.$setStyles(r),this.$sourceImage&&this.$transformImageByOffset(null!=e?e:this.$sourceImage.$getTransform(),-n,-a)}$transformImageByOffset(t,e,i){const{$image:s,$scale:n,$sourceImage:a}=this;if(a&&s&&n>=0){const[a,o,h,r,c,l]=t,d=(e*r-h*i)/(a*r-h*o),$=(i*a-o*e)/(a*r-h*o),u=a*d+h*$+c,g=o*d+r*$+l;s.$ready((t=>{this.$setStyles.call(s,{width:t.naturalWidth*n,height:t.naturalHeight*n})})),s.$setTransform(a,o,h,r,u*n,g*n)}}}Pt.$name=$,Pt.$version="2.0.0";export{mt as CropperCanvas,At as CropperCrosshair,pt as CropperElement,xt as CropperGrid,Ct as CropperHandle,vt as CropperImage,St as CropperSelection,yt as CropperShade,Pt as CropperViewer};
