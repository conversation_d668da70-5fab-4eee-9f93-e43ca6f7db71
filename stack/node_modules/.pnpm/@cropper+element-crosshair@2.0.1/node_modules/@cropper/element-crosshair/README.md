# @cropper/element-crosshair

> A custom crosshair element for the Cropper.

## Main npm package files

```text
dist/
├── element-crosshair.js         (UMD, bundled)
├── element-crosshair.min.js     (UMD, bundled, compressed)
├── element-crosshair.raw.js     (UMD, unbundled, default)
├── element-crosshair.esm.js     (ECMAScript Module, bundled)
├── element-crosshair.esm.min.js (ECMAScript Module, bundled, compressed)
├── element-crosshair.esm.raw.js (ECMAScript Module, unbundled)
└── element-crosshair.d.ts       (TypeScript Declaration File)
```

## Getting started

### Installation

```sh
npm install @cropper/element-crosshair
```

### Usage

```js
import CropperCrosshair from '@cropper/element-crosshair';

CropperCrosshair.$define();
```

```html
<cropper-crosshair></cropper-crosshair>
```

## Versioning

Maintained under the [Semantic Versioning guidelines](https://semver.org).

## License

[MIT](https://opensource.org/licenses/MIT)
