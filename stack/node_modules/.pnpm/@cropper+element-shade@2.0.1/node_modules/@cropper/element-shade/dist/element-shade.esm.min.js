const t="undefined"!=typeof window&&void 0!==window.document,e=t?window:{};t&&e.document.documentElement;const s="cropper",n=`${s}-canvas`,i=`${s}-selection`,o=`${s}-shade`,a="select",h="actionend",r="actionstart",c="change",l=Number.isNaN||e.isNaN;function d(t){return"number"==typeof t&&!l(t)}const u=/([a-z\d])([A-Z])/g;function $(t){return String(t).replace(u,"$1-$2").toLowerCase()}const p=/-[A-z\d]/g;function b(t){return t.replace(p,(t=>t.slice(1).toUpperCase()))}const g=/\s\s*/;function m(t,e,s,n){e.trim().split(g).forEach((e=>{t.removeEventListener(e,s,n)}))}function C(t,e,s,n){e.trim().split(g).forEach((e=>{t.addEventListener(e,s,n)}))}const v={bubbles:!0,cancelable:!0,composed:!0};const y=Promise.resolve();const f=/left|top|width|height/i,S="open",w=new WeakMap,A=new WeakMap,E=new Map,k=e.document&&Array.isArray(e.document.adoptedStyleSheets)&&"replaceSync"in e.CSSStyleSheet.prototype;class x extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var t,e;super(),this.shadowRootMode=S,this.slottable=!0;const s=null===(e=null===(t=Object.getPrototypeOf(this))||void 0===t?void 0:t.constructor)||void 0===e?void 0:e.$name;s&&E.set(s,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,s){if(Object.is(s,e))return;const n=b(t);let i=s;switch(typeof this[n]){case"boolean":i=null!==s&&"false"!==s;break;case"number":i=Number(s)}switch(this[n]=i,t){case"theme-color":{const t=A.get(this),e=this.$sharedStyle;t&&e&&(k?t.replaceSync(e):t.textContent=e);break}}}$propertyChangedCallback(t,e,s){if(!Object.is(s,e))switch(t=$(t),typeof s){case"boolean":!0===s?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":s=l(s)?"":String(s);default:s?this.getAttribute(t)!==s&&this.setAttribute(t,s):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((t=>{const e=b(t);let s=this[e];(function(t){return void 0===t})(s)||this.$propertyChangedCallback(e,void 0,s),Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:()=>s,set(t){const n=s;s=t,this.$propertyChangedCallback(e,n,t)}})}));const t=this.attachShadow({mode:this.shadowRootMode||S});if(this.shadowRoot||w.set(this,t),A.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){A.has(this)&&A.delete(this),w.has(this)&&w.delete(this)}$getTagNameOf(t){var e;return null!==(e=E.get(t))&&void 0!==e?e:t}$setStyles(t){return Object.keys(t).forEach((e=>{let s=t[e];d(s)&&(s=0!==s&&f.test(e)?`${s}px`:String(s)),this.style[e]=s})),this}$getShadowRoot(){return this.shadowRoot||w.get(this)}$addStyles(t){let e;const s=this.$getShadowRoot();return k?(e=new CSSStyleSheet,e.replaceSync(t),s.adoptedStyleSheets=s.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,s.appendChild(e)),e}$emit(t,e,s){return function(t,e,s,n){return t.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign(Object.assign({},v),{detail:s}),n)))}(this,t,e,s)}$nextTick(t){return function(t,e){return e?y.then(t?e.bind(t):e):y}(this,t)}static $define(s,n){var i;"object"==typeof(i=s)&&null!==i&&(n=s,s=""),s||(s=this.$name||this.name),s=$(s),t&&e.customElements&&!e.customElements.get(s)&&customElements.define(s,this,n)}}x.$version="2.0.0";const O=new WeakMap;class j extends x{constructor(){super(...arguments),this.$onCanvasChange=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$style=":host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}",this.x=0,this.y=0,this.width=0,this.height=0,this.slottable=!1,this.themeColor="rgba(0, 0, 0, 0.65)"}set $canvas(t){O.set(this,t)}get $canvas(){return O.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["height","width","x","y"])}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(n));if(t){this.$canvas=t,this.style.position="absolute";const e=t.querySelector(this.$getTagNameOf(i));e&&(this.$onCanvasActionStart=t=>{e.hidden&&t.detail.action===a&&(this.hidden=!1)},this.$onCanvasActionEnd=t=>{e.hidden&&t.detail.action===a&&(this.hidden=!0)},this.$onCanvasChange=t=>{const{x:s,y:n,width:i,height:o}=t.detail;this.$change(s,n,i,o),(e.hidden||0===s&&0===n&&0===i&&0===o)&&(this.hidden=!0)},C(t,r,this.$onCanvasActionStart),C(t,h,this.$onCanvasActionEnd),C(t,c,this.$onCanvasChange))}this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(m(t,r,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(m(t,h,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasChange&&(m(t,c,this.$onCanvasChange),this.$onCanvasChange=null)),super.disconnectedCallback()}$change(t,e,s=this.width,n=this.height){return d(t)&&d(e)&&d(s)&&d(n)&&(t!==this.x||e!==this.y||s!==this.width||n!==this.height)?(this.hidden&&(this.hidden=!1),this.x=t,this.y=e,this.width=s,this.height=n,this.$render()):this}$reset(){return this.$change(0,0,0,0)}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height,outlineWidth:e.innerWidth})}}j.$name=o,j.$version="2.0.0";export{j as default};
