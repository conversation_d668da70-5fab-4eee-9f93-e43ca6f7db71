declare class CropperCanvas extends CropperElement_2 {
    static $name: string;
    static $version: string;
    protected $onPointerDown: EventListener | null;
    protected $onPointerMove: EventListener | null;
    protected $onPointerUp: EventListener | null;
    protected $onWheel: EventListener | null;
    protected $wheeling: boolean;
    protected readonly $pointers: Map<number, any>;
    protected $style: string;
    protected $action: string;
    background: boolean;
    disabled: boolean;
    scaleStep: number;
    themeColor: string;
    protected static get observedAttributes(): string[];
    protected connectedCallback(): void;
    protected disconnectedCallback(): void;
    protected $propertyChangedCallback(name: string, oldValue: unknown, newValue: unknown): void;
    protected $bind(): void;
    protected $unbind(): void;
    protected $handlePointerDown(event: Event): void;
    protected $handlePointerMove(event: Event): void;
    protected $handlePointerUp(event: Event): void;
    protected $handleWheel(event: Event): void;
    /**
     * Changes the current action to a new one.
     * @param {string} action The new action.
     * @returns {CropperCanvas} Returns `this` for chaining.
     */
    $setAction(action: string): this;
    /**
     * Generates a real canvas element, with the image draw into if there is one.
     * @param {object} [options] The available options.
     * @param {number} [options.width] The width of the canvas.
     * @param {number} [options.height] The height of the canvas.
     * @param {Function} [options.beforeDraw] The function called before drawing the image onto the canvas.
     * @returns {Promise} Returns a promise that resolves to the generated canvas element.
     */
    $toCanvas(options?: {
        width?: number;
        height?: number;
        beforeDraw?: (context: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => void;
    }): Promise<HTMLCanvasElement>;
}

declare class CropperElement extends HTMLElement {
    static $name: string;
    static $version: string;
    protected $style?: string;
    protected $template?: string;
    protected get $sharedStyle(): string;
    shadowRootMode: ShadowRootMode;
    slottable: boolean;
    themeColor?: string;
    constructor();
    protected static get observedAttributes(): string[];
    protected attributeChangedCallback(name: string, oldValue: string, newValue: string): void;
    protected $propertyChangedCallback(name: string, oldValue: unknown, newValue: unknown): void;
    protected connectedCallback(): void;
    protected disconnectedCallback(): void;
    protected $getTagNameOf(name: string): string;
    protected $setStyles(properties: Record<string, any>): this;
    /**
     * Outputs the shadow root of the element.
     * @returns {ShadowRoot} Returns the shadow root.
     */
    $getShadowRoot(): ShadowRoot;
    /**
     * Adds styles to the shadow root.
     * @param {string} styles The styles to add.
     * @returns {CSSStyleSheet|HTMLStyleElement} Returns the generated style sheet.
     */
    $addStyles(styles: string): CSSStyleSheet | HTMLStyleElement;
    /**
     * Dispatches an event at the element.
     * @param {string} type The name of the event.
     * @param {*} [detail] The data passed when initializing the event.
     * @param {CustomEventInit} [options] The other event options.
     * @returns {boolean} Returns the result value.
     */
    $emit(type: string, detail?: unknown, options?: CustomEventInit): boolean;
    /**
     * Defers the callback to be executed after the next DOM update cycle.
     * @param {Function} [callback] The callback to execute after the next DOM update cycle.
     * @returns {Promise} A promise that resolves to nothing.
     */
    $nextTick(callback?: () => void): Promise<void>;
    /**
     * Defines the constructor as a new custom element.
     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CustomElementRegistry/define}
     * @param {string|object} [name] The element name.
     * @param {object} [options] The element definition options.
     */
    static $define(name?: string | ElementDefinitionOptions, options?: ElementDefinitionOptions): void;
}

declare class CropperElement_2 extends HTMLElement {
    static $name: string;
    static $version: string;
    protected $style?: string;
    protected $template?: string;
    protected get $sharedStyle(): string;
    shadowRootMode: ShadowRootMode;
    slottable: boolean;
    themeColor?: string;
    constructor();
    protected static get observedAttributes(): string[];
    protected attributeChangedCallback(name: string, oldValue: string, newValue: string): void;
    protected $propertyChangedCallback(name: string, oldValue: unknown, newValue: unknown): void;
    protected connectedCallback(): void;
    protected disconnectedCallback(): void;
    protected $getTagNameOf(name: string): string;
    protected $setStyles(properties: Record<string, any>): this;
    /**
     * Outputs the shadow root of the element.
     * @returns {ShadowRoot} Returns the shadow root.
     */
    $getShadowRoot(): ShadowRoot;
    /**
     * Adds styles to the shadow root.
     * @param {string} styles The styles to add.
     * @returns {CSSStyleSheet|HTMLStyleElement} Returns the generated style sheet.
     */
    $addStyles(styles: string): CSSStyleSheet | HTMLStyleElement;
    /**
     * Dispatches an event at the element.
     * @param {string} type The name of the event.
     * @param {*} [detail] The data passed when initializing the event.
     * @param {CustomEventInit} [options] The other event options.
     * @returns {boolean} Returns the result value.
     */
    $emit(type: string, detail?: unknown, options?: CustomEventInit): boolean;
    /**
     * Defers the callback to be executed after the next DOM update cycle.
     * @param {Function} [callback] The callback to execute after the next DOM update cycle.
     * @returns {Promise} A promise that resolves to nothing.
     */
    $nextTick(callback?: () => void): Promise<void>;
    /**
     * Defines the constructor as a new custom element.
     * {@link https://developer.mozilla.org/en-US/docs/Web/API/CustomElementRegistry/define}
     * @param {string|object} [name] The element name.
     * @param {object} [options] The element definition options.
     */
    static $define(name?: string | ElementDefinitionOptions, options?: ElementDefinitionOptions): void;
}

declare class CropperShade extends CropperElement {
    static $name: string;
    static $version: string;
    protected $onCanvasChange: EventListener | null;
    protected $onCanvasActionEnd: EventListener | null;
    protected $onCanvasActionStart: EventListener | null;
    protected $style: string;
    x: number;
    y: number;
    width: number;
    height: number;
    slottable: boolean;
    themeColor: string;
    protected set $canvas(element: CropperCanvas);
    protected get $canvas(): CropperCanvas;
    protected static get observedAttributes(): string[];
    protected connectedCallback(): void;
    protected disconnectedCallback(): void;
    /**
     * Changes the position and/or size of the shade.
     * @param {number} x The new position in the horizontal direction.
     * @param {number} y The new position in the vertical direction.
     * @param {number} [width] The new width.
     * @param {number} [height] The new height.
     * @returns {CropperShade} Returns `this` for chaining.
     */
    $change(x: number, y: number, width?: number, height?: number): this;
    /**
     * Resets the shade to its initial position and size.
     * @returns {CropperShade} Returns `this` for chaining.
     */
    $reset(): this;
    /**
     * Refreshes the position or size of the shade.
     * @returns {CropperShade} Returns `this` for chaining.
     */
    $render(): this;
}
export default CropperShade;

export { }
