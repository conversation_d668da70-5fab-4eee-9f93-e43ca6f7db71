(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('@cropper/element'), require('@cropper/utils')) :
  typeof define === 'function' && define.amd ? define(['@cropper/element', '@cropper/utils'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.CropperShade = factory(global.CropperElement, global.CropperUtils));
})(this, (function (CropperElement, utils) { 'use strict';

  function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

  var CropperElement__default = /*#__PURE__*/_interopDefaultLegacy(CropperElement);

  var style = `:host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}`;

  const canvasCache = new WeakMap();
  class CropperShade extends CropperElement__default["default"] {
      constructor() {
          super(...arguments);
          this.$onCanvasChange = null;
          this.$onCanvasActionEnd = null;
          this.$onCanvasActionStart = null;
          this.$style = style;
          this.x = 0;
          this.y = 0;
          this.width = 0;
          this.height = 0;
          this.slottable = false;
          this.themeColor = 'rgba(0, 0, 0, 0.65)';
      }
      set $canvas(element) {
          canvasCache.set(this, element);
      }
      get $canvas() {
          return canvasCache.get(this);
      }
      static get observedAttributes() {
          return super.observedAttributes.concat([
              'height',
              'width',
              'x',
              'y',
          ]);
      }
      connectedCallback() {
          super.connectedCallback();
          const $canvas = this.closest(this.$getTagNameOf(utils.CROPPER_CANVAS));
          if ($canvas) {
              this.$canvas = $canvas;
              this.style.position = 'absolute';
              const $selection = $canvas.querySelector(this.$getTagNameOf(utils.CROPPER_SELECTION));
              if ($selection) {
                  this.$onCanvasActionStart = (event) => {
                      if ($selection.hidden && event.detail.action === utils.ACTION_SELECT) {
                          this.hidden = false;
                      }
                  };
                  this.$onCanvasActionEnd = (event) => {
                      if ($selection.hidden && event.detail.action === utils.ACTION_SELECT) {
                          this.hidden = true;
                      }
                  };
                  this.$onCanvasChange = (event) => {
                      const { x, y, width, height, } = event.detail;
                      this.$change(x, y, width, height);
                      if ($selection.hidden || (x === 0 && y === 0 && width === 0 && height === 0)) {
                          this.hidden = true;
                      }
                  };
                  utils.on($canvas, utils.EVENT_ACTION_START, this.$onCanvasActionStart);
                  utils.on($canvas, utils.EVENT_ACTION_END, this.$onCanvasActionEnd);
                  utils.on($canvas, utils.EVENT_CHANGE, this.$onCanvasChange);
              }
          }
          this.$render();
      }
      disconnectedCallback() {
          const { $canvas } = this;
          if ($canvas) {
              if (this.$onCanvasActionStart) {
                  utils.off($canvas, utils.EVENT_ACTION_START, this.$onCanvasActionStart);
                  this.$onCanvasActionStart = null;
              }
              if (this.$onCanvasActionEnd) {
                  utils.off($canvas, utils.EVENT_ACTION_END, this.$onCanvasActionEnd);
                  this.$onCanvasActionEnd = null;
              }
              if (this.$onCanvasChange) {
                  utils.off($canvas, utils.EVENT_CHANGE, this.$onCanvasChange);
                  this.$onCanvasChange = null;
              }
          }
          super.disconnectedCallback();
      }
      /**
       * Changes the position and/or size of the shade.
       * @param {number} x The new position in the horizontal direction.
       * @param {number} y The new position in the vertical direction.
       * @param {number} [width] The new width.
       * @param {number} [height] The new height.
       * @returns {CropperShade} Returns `this` for chaining.
       */
      $change(x, y, width = this.width, height = this.height) {
          if (!utils.isNumber(x)
              || !utils.isNumber(y)
              || !utils.isNumber(width)
              || !utils.isNumber(height)
              || (x === this.x && y === this.y && width === this.width && height === this.height)) {
              return this;
          }
          if (this.hidden) {
              this.hidden = false;
          }
          this.x = x;
          this.y = y;
          this.width = width;
          this.height = height;
          return this.$render();
      }
      /**
       * Resets the shade to its initial position and size.
       * @returns {CropperShade} Returns `this` for chaining.
       */
      $reset() {
          return this.$change(0, 0, 0, 0);
      }
      /**
       * Refreshes the position or size of the shade.
       * @returns {CropperShade} Returns `this` for chaining.
       */
      $render() {
          return this.$setStyles({
              transform: `translate(${this.x}px, ${this.y}px)`,
              width: this.width,
              height: this.height,
              outlineWidth: utils.WINDOW.innerWidth,
          });
      }
  }
  CropperShade.$name = utils.CROPPER_SHADE;
  CropperShade.$version = '2.0.0';

  return CropperShade;

}));
