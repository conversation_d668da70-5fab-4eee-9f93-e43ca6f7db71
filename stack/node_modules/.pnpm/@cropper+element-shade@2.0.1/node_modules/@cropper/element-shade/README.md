# @cropper/element-shade

> A custom shade element for the Cropper.

## Main npm package files

```text
dist/
├── element-shade.js         (UMD, bundled)
├── element-shade.min.js     (UMD, bundled, compressed)
├── element-shade.raw.js     (UMD, unbundled, default)
├── element-shade.esm.js     (ECMAScript Module, bundled)
├── element-shade.esm.min.js (ECMAScript Module, bundled, compressed)
├── element-shade.esm.raw.js (ECMAScript Module, unbundled)
└── element-shade-shade.d.ts       (TypeScript Declaration File)
```

## Getting started

### Installation

```sh
npm install @cropper/element-shade
```

### Usage

```js
import CropperShade from '@cropper/element-shade';

CropperShade.$define();
```

```html
<cropper-shade></cropper-shade>
```

## Versioning

Maintained under the [Semantic Versioning guidelines](https://semver.org).

## License

[MIT](https://opensource.org/licenses/MIT)
