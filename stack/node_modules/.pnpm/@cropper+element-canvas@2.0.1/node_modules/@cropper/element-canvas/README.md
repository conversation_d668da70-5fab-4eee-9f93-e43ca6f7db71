# @cropper/element-canvas

> A custom canvas element for the Cropper.

## Main npm package files

```text
dist/
├── element-canvas.js         (UMD, bundled)
├── element-canvas.min.js     (UMD, bundled, compressed)
├── element-canvas.raw.js     (UMD, unbundled, default)
├── element-canvas.esm.js     (ECMAScript Module, bundled)
├── element-canvas.esm.min.js (ECMAScript Module, bundled, compressed)
├── element-canvas.esm.raw.js (ECMAScript Module, unbundled)
└── element-canvas.d.ts       (TypeScript Declaration File)
```

## Getting started

### Installation

```sh
npm install @cropper/element-canvas
```

### Usage

```js
import CropperCanvas from '@cropper/element-canvas';

CropperCanvas.$define();
```

```html
<cropper-canvas></cropper-canvas>
```

## Versioning

Maintained under the [Semantic Versioning guidelines](https://semver.org).

## License

[MIT](https://opensource.org/licenses/MIT)
