!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).CropperCanvas=e()}(this,(function(){"use strict";const t="undefined"!=typeof window&&void 0!==window.document,e=t?window:{},n=!!t&&"ontouchstart"in e.document.documentElement,o=!!t&&"PointerEvent"in e,s="cropper",i=`${s}-canvas`,a=`${s}-image`,r="scale",c="transform",h="none",l=o?"pointerdown":n?"touchstart":"mousedown",d=o?"pointermove":n?"touchmove":"mousemove",u=o?"pointerup pointercancel":n?"touchend touchcancel":"mouseup",p="wheel",b="action";const f=Number.isNaN||e.isNaN;function g(t){return"number"==typeof t&&!f(t)}function $(t){return g(t)&&t>0&&t<1/0}function m(t){return"object"==typeof t&&null!==t}const{hasOwnProperty:w}=Object.prototype;function y(t){if(!m(t))return!1;try{const{constructor:e}=t,{prototype:n}=e;return e&&n&&w.call(n,"isPrototypeOf")}catch(t){return!1}}const v=/([a-z\d])([A-Z])/g;function P(t){return String(t).replace(v,"$1-$2").toLowerCase()}const C=/-[A-z\d]/g;function M(t){return t.replace(C,(t=>t.slice(1).toUpperCase()))}const S=/\s\s*/;function k(t,e,n,o){e.trim().split(S).forEach((e=>{t.removeEventListener(e,n,o)}))}function E(t,e,n,o){e.trim().split(S).forEach((e=>{t.addEventListener(e,n,o)}))}const Y={bubbles:!0,cancelable:!0,composed:!0};const X=Promise.resolve();const O="contain";const A=/left|top|width|height/i,D="open",T=new WeakMap,j=new WeakMap,W=new Map,I=e.document&&Array.isArray(e.document.adoptedStyleSheets)&&"replaceSync"in e.CSSStyleSheet.prototype;class x extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var t,e;super(),this.shadowRootMode=D,this.slottable=!0;const n=null===(e=null===(t=Object.getPrototypeOf(this))||void 0===t?void 0:t.constructor)||void 0===e?void 0:e.$name;n&&W.set(n,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,n){if(Object.is(n,e))return;const o=M(t);let s=n;switch(typeof this[o]){case"boolean":s=null!==n&&"false"!==n;break;case"number":s=Number(n)}switch(this[o]=s,t){case"theme-color":{const t=j.get(this),e=this.$sharedStyle;t&&e&&(I?t.replaceSync(e):t.textContent=e);break}}}$propertyChangedCallback(t,e,n){if(!Object.is(n,e))switch(t=P(t),typeof n){case"boolean":!0===n?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":n=f(n)?"":String(n);default:n?this.getAttribute(t)!==n&&this.setAttribute(t,n):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((t=>{const e=M(t);let n=this[e];(function(t){return void 0===t})(n)||this.$propertyChangedCallback(e,void 0,n),Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:()=>n,set(t){const o=n;n=t,this.$propertyChangedCallback(e,o,t)}})}));const t=this.attachShadow({mode:this.shadowRootMode||D});if(this.shadowRoot||T.set(this,t),j.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){j.has(this)&&j.delete(this),T.has(this)&&T.delete(this)}$getTagNameOf(t){var e;return null!==(e=W.get(t))&&void 0!==e?e:t}$setStyles(t){return Object.keys(t).forEach((e=>{let n=t[e];g(n)&&(n=0!==n&&A.test(e)?`${n}px`:String(n)),this.style[e]=n})),this}$getShadowRoot(){return this.shadowRoot||T.get(this)}$addStyles(t){let e;const n=this.$getShadowRoot();return I?(e=new CSSStyleSheet,e.replaceSync(t),n.adoptedStyleSheets=n.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,n.appendChild(e)),e}$emit(t,e,n){return function(t,e,n,o){return t.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign(Object.assign({},Y),{detail:n}),o)))}(this,t,e,n)}$nextTick(t){return function(t,e){return e?X.then(t?e.bind(t):e):X}(this,t)}static $define(n,o){m(n)&&(o=n,n=""),n||(n=this.$name||this.name),n=P(n),t&&e.customElements&&!e.customElements.get(n)&&customElements.define(n,this,o)}}x.$version="2.0.0";class U extends x{constructor(){super(...arguments),this.$onPointerDown=null,this.$onPointerMove=null,this.$onPointerUp=null,this.$onWheel=null,this.$wheeling=!1,this.$pointers=new Map,this.$style=':host{display:block;min-height:100px;min-width:200px;overflow:hidden;position:relative;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([background]){background-color:#fff;background-image:repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc),repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc);background-image:repeating-conic-gradient(#ccc 0 25%,#fff 0 50%);background-position:0 0,.5rem .5rem;background-size:1rem 1rem}:host([disabled]){pointer-events:none}:host([disabled]):after{bottom:0;content:"";cursor:not-allowed;display:block;left:0;pointer-events:none;position:absolute;right:0;top:0}',this.$action=h,this.background=!1,this.disabled=!1,this.scaleStep=.1,this.themeColor="#39f"}static get observedAttributes(){return super.observedAttributes.concat(["background","disabled","scale-step"])}connectedCallback(){super.connectedCallback(),this.disabled||this.$bind()}disconnectedCallback(){this.disabled||this.$unbind(),super.disconnectedCallback()}$propertyChangedCallback(t,e,n){if(!Object.is(n,e)&&(super.$propertyChangedCallback(t,e,n),"disabled"===t))n?this.$unbind():this.$bind()}$bind(){this.$onPointerDown||(this.$onPointerDown=this.$handlePointerDown.bind(this),E(this,l,this.$onPointerDown)),this.$onPointerMove||(this.$onPointerMove=this.$handlePointerMove.bind(this),E(this.ownerDocument,d,this.$onPointerMove)),this.$onPointerUp||(this.$onPointerUp=this.$handlePointerUp.bind(this),E(this.ownerDocument,u,this.$onPointerUp)),this.$onWheel||(this.$onWheel=this.$handleWheel.bind(this),E(this,p,this.$onWheel,{passive:!1,capture:!0}))}$unbind(){this.$onPointerDown&&(k(this,l,this.$onPointerDown),this.$onPointerDown=null),this.$onPointerMove&&(k(this.ownerDocument,d,this.$onPointerMove),this.$onPointerMove=null),this.$onPointerUp&&(k(this.ownerDocument,u,this.$onPointerUp),this.$onPointerUp=null),this.$onWheel&&(k(this,p,this.$onWheel,{capture:!0}),this.$onWheel=null)}$handlePointerDown(t){const{buttons:e,button:n,type:o}=t;if(this.disabled||("pointerdown"===o&&"mouse"===t.pointerType||"mousedown"===o)&&(g(e)&&1!==e||g(n)&&0!==n||t.ctrlKey))return;const{$pointers:s}=this;let i="";if(t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:n})=>{s.set(t,{startX:e,startY:n,endX:e,endY:n})}));else{const{pointerId:e=0,pageX:n,pageY:o}=t;s.set(e,{startX:n,startY:o,endX:n,endY:o})}var a;s.size>1?i=c:"object"==typeof(a=t.target)&&null!==a&&1===a.nodeType&&(i=t.target.action||t.target.getAttribute("action")||""),!1!==this.$emit("actionstart",{action:i,relatedEvent:t})&&(t.preventDefault(),this.$action=i,this.style.willChange="transform")}$handlePointerMove(t){const{$action:e,$pointers:n}=this;if(this.disabled||e===h||0===n.size)return;if(!1===this.$emit("actionmove",{action:e,relatedEvent:t}))return;if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t,pageX:e,pageY:o})=>{const s=n.get(t);s&&Object.assign(s,{endX:e,endY:o})}));else{const{pointerId:e=0,pageX:o,pageY:s}=t,i=n.get(e);i&&Object.assign(i,{endX:o,endY:s})}const o={action:e,relatedEvent:t};if(e===c){const e=new Map(n);let s=0,i=0,a=0,c=0,l=t.pageX,d=t.pageY;n.forEach(((t,n)=>{e.delete(n),e.forEach((e=>{let n=e.startX-t.startX,o=e.startY-t.startY,r=e.endX-t.endX,h=e.endY-t.endY,u=0,p=0,b=0,f=0;if(0===n?o<0?b=2*Math.PI:o>0&&(b=Math.PI):n>0?b=Math.PI/2+Math.atan(o/n):n<0&&(b=1.5*Math.PI+Math.atan(o/n)),0===r?h<0?f=2*Math.PI:h>0&&(f=Math.PI):r>0?f=Math.PI/2+Math.atan(h/r):r<0&&(f=1.5*Math.PI+Math.atan(h/r)),f>0||b>0){const n=f-b,o=Math.abs(n);o>s&&(s=o,a=n,l=(t.startX+e.startX)/2,d=(t.startY+e.startY)/2)}if(n=Math.abs(n),o=Math.abs(o),r=Math.abs(r),h=Math.abs(h),n>0&&o>0?u=Math.sqrt(n*n+o*o):n>0?u=n:o>0&&(u=o),r>0&&h>0?p=Math.sqrt(r*r+h*h):r>0?p=r:h>0&&(p=h),u>0&&p>0){const n=(p-u)/u,o=Math.abs(n);o>i&&(i=o,c=n,l=(t.startX+e.startX)/2,d=(t.startY+e.startY)/2)}}))}));const u=s>0,p=i>0;u&&p?(o.rotate=a,o.scale=c,o.centerX=l,o.centerY=d):u?(o.action="rotate",o.rotate=a,o.centerX=l,o.centerY=d):p?(o.action=r,o.scale=c,o.centerX=l,o.centerY=d):o.action=h}else{const[t]=Array.from(n.values());Object.assign(o,t)}n.forEach((t=>{t.startX=t.endX,t.startY=t.endY})),o.action!==h&&this.$emit(b,o,{cancelable:!1})}$handlePointerUp(t){const{$action:e,$pointers:n}=this;if(!this.disabled&&e!==h&&!1!==this.$emit("actionend",{action:e,relatedEvent:t})){if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach((({identifier:t})=>{n.delete(t)}));else{const{pointerId:e=0}=t;n.delete(e)}0===n.size&&(this.style.willChange="",this.$action=h)}}$handleWheel(t){if(this.disabled)return;if(t.preventDefault(),this.$wheeling)return;this.$wheeling=!0,setTimeout((()=>{this.$wheeling=!1}),50);const e=(t.deltaY>0?-1:1)*this.scaleStep;this.$emit(b,{action:r,scale:e,relatedEvent:t},{cancelable:!1})}$setAction(t){return"string"==typeof t&&(this.$action=t),this}$toCanvas(t){return new Promise(((e,n)=>{if(!this.isConnected)return void n(new Error("The current element is not connected to the DOM."));const o=document.createElement("canvas");let s=this.offsetWidth,i=this.offsetHeight,r=1;y(t)&&($(t.width)||$(t.height))&&(({width:s,height:i}=function(t,e=O){const{aspectRatio:n}=t;let{width:o,height:s}=t;const i=$(o),a=$(s);if(i&&a){const t=s*n;e===O&&t>o||"cover"===e&&t<o?s=o/n:o=s*n}else i?s=o/n:a&&(o=s*n);return{width:o,height:s}}({aspectRatio:s/i,width:t.width,height:t.height})),r=s/this.offsetWidth),o.width=s,o.height=i;const c=this.querySelector(this.$getTagNameOf(a));c?c.$ready().then((n=>{const a=o.getContext("2d");if(a){const[e,h,l,d,u,p]=c.$getTransform();let b=u,f=p,g=n.naturalWidth,$=n.naturalHeight;1!==r&&(b*=r,f*=r,g*=r,$*=r);const m=g/2,w=$/2;a.fillStyle="transparent",a.fillRect(0,0,s,i),y(t)&&"function"==typeof t.beforeDraw&&t.beforeDraw.call(this,a,o),a.save(),a.translate(m,w),a.transform(e,h,l,d,b,f),a.translate(-m,-w),a.drawImage(n,0,0,g,$),a.restore()}e(o)})).catch(n):e(o)}))}}return U.$name=i,U.$version="2.0.0",U}));
