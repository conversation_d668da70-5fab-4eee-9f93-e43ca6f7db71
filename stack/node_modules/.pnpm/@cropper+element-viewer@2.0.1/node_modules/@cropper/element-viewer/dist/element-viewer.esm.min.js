const e="undefined"!=typeof window&&void 0!==window.document,t=e?window:{};e&&t.document.documentElement;const s="cropper",o=`${s}-canvas`,i=`${s}-image`,n=`${s}-selection`,a=`${s}-viewer`,r="load",h="change",c="transform",l=Number.isNaN||t.isNaN;const d=/([a-z\d])([A-Z])/g;function u(e){return String(e).replace(d,"$1-$2").toLowerCase()}const m=/-[A-z\d]/g;function $(e){return e.replace(m,(e=>e.slice(1).toUpperCase()))}const g=/\s\s*/;function b(e,t,s,o){t.trim().split(g).forEach((t=>{e.removeEventListener(t,s,o)}))}function p(e,t,s,o){t.trim().split(g).forEach((t=>{e.addEventListener(t,s,o)}))}const f={bubbles:!0,cancelable:!0,composed:!0};const S=Promise.resolve();const y=/left|top|width|height/i,w="open",v=new WeakMap,C=new WeakMap,I=new Map,k=t.document&&Array.isArray(t.document.adoptedStyleSheets)&&"replaceSync"in t.CSSStyleSheet.prototype;class T extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var e,t;super(),this.shadowRootMode=w,this.slottable=!0;const s=null===(t=null===(e=Object.getPrototypeOf(this))||void 0===e?void 0:e.constructor)||void 0===t?void 0:t.$name;s&&I.set(s,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(e,t,s){if(Object.is(s,t))return;const o=$(e);let i=s;switch(typeof this[o]){case"boolean":i=null!==s&&"false"!==s;break;case"number":i=Number(s)}switch(this[o]=i,e){case"theme-color":{const e=C.get(this),t=this.$sharedStyle;e&&t&&(k?e.replaceSync(t):e.textContent=t);break}}}$propertyChangedCallback(e,t,s){if(!Object.is(s,t))switch(e=u(e),typeof s){case"boolean":!0===s?this.hasAttribute(e)||this.setAttribute(e,""):this.removeAttribute(e);break;case"number":s=l(s)?"":String(s);default:s?this.getAttribute(e)!==s&&this.setAttribute(e,s):this.removeAttribute(e)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((e=>{const t=$(e);let s=this[t];(function(e){return void 0===e})(s)||this.$propertyChangedCallback(t,void 0,s),Object.defineProperty(this,t,{enumerable:!0,configurable:!0,get:()=>s,set(e){const o=s;s=e,this.$propertyChangedCallback(t,o,e)}})}));const e=this.attachShadow({mode:this.shadowRootMode||w});if(this.shadowRoot||v.set(this,e),C.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const t=document.createElement("template");t.innerHTML=this.$template,e.appendChild(t.content)}if(this.slottable){const t=document.createElement("slot");e.appendChild(t)}}disconnectedCallback(){C.has(this)&&C.delete(this),v.has(this)&&v.delete(this)}$getTagNameOf(e){var t;return null!==(t=I.get(e))&&void 0!==t?t:e}$setStyles(e){return Object.keys(e).forEach((t=>{let s=e[t];(function(e){return"number"==typeof e&&!l(e)})(s)&&(s=0!==s&&y.test(t)?`${s}px`:String(s)),this.style[t]=s})),this}$getShadowRoot(){return this.shadowRoot||v.get(this)}$addStyles(e){let t;const s=this.$getShadowRoot();return k?(t=new CSSStyleSheet,t.replaceSync(e),s.adoptedStyleSheets=s.adoptedStyleSheets.concat(t)):(t=document.createElement("style"),t.textContent=e,s.appendChild(t)),t}$emit(e,t,s){return function(e,t,s,o){return e.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign(Object.assign({},f),{detail:s}),o)))}(this,e,t,s)}$nextTick(e){return function(e,t){return t?S.then(e?t.bind(e):t):S}(this,e)}static $define(s,o){var i;"object"==typeof(i=s)&&null!==i&&(o=s,s=""),s||(s=this.$name||this.name),s=u(s),e&&t.customElements&&!t.customElements.get(s)&&customElements.define(s,this,o)}}T.$version="2.0.0";const A=new WeakMap,O=new WeakMap,E=new WeakMap,L=new WeakMap,N="both",j="horizontal",x="vertical",M="none";class W extends T{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}",this.resize=x,this.selection="",this.slottable=!1}set $image(e){O.set(this,e)}get $image(){return O.get(this)}set $sourceImage(e){L.set(this,e)}get $sourceImage(){return L.get(this)}set $canvas(e){A.set(this,e)}get $canvas(){return A.get(this)}set $selection(e){E.set(this,e)}get $selection(){return E.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let e=null;if(e=this.selection?this.ownerDocument.querySelector(this.selection):this.closest(this.$getTagNameOf(n)),"object"==typeof(t=e)&&null!==t&&1===t.nodeType){this.$selection=e,this.$onSelectionChange=this.$handleSelectionChange.bind(this),p(e,h,this.$onSelectionChange);const t=e.closest(this.$getTagNameOf(o));if(t){this.$canvas=t;const e=t.querySelector(this.$getTagNameOf(i));e&&(this.$sourceImage=e,this.$image=e.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),p(e.$image,r,this.$onSourceImageLoad),p(e,c,this.$onSourceImageTransform))}this.$render()}var t}disconnectedCallback(){const{$selection:e,$sourceImage:t}=this;e&&this.$onSelectionChange&&(b(e,h,this.$onSelectionChange),this.$onSelectionChange=null),t&&this.$onSourceImageLoad&&(b(t.$image,r,this.$onSourceImageLoad),this.$onSourceImageLoad=null),t&&this.$onSourceImageTransform&&(b(t,c,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(e){this.$render(e.detail)}$handleSourceImageLoad(){const{$image:e,$sourceImage:t}=this,s=e.getAttribute("src"),o=t.getAttribute("src");o&&o!==s&&(e.setAttribute("src",o),e.$ready((()=>{setTimeout((()=>{this.$render()}),50)})))}$handleSourceImageTransform(e){this.$render(void 0,e.detail.matrix)}$render(e,t){const{$canvas:s,$selection:o}=this;e||o.hidden||(e=o),(!e||0===e.x&&0===e.y&&0===e.width&&0===e.height)&&(e={x:0,y:0,width:s.offsetWidth,height:s.offsetHeight});const{x:i,y:n,width:a,height:r}=e,h={},{clientWidth:c,clientHeight:l}=this;let d=c,u=l,m=NaN;switch(this.resize){case N:m=1,d=a,u=r,h.width=a,h.height=r;break;case j:m=r>0?l/r:0,d=a*m,h.width=d;break;case x:m=a>0?c/a:0,u=r*m,h.height=u;break;default:c>0?m=a>0?c/a:0:l>0&&(m=r>0?l/r:0)}this.$scale=m,this.$setStyles(h),this.$sourceImage&&this.$transformImageByOffset(null!=t?t:this.$sourceImage.$getTransform(),-i,-n)}$transformImageByOffset(e,t,s){const{$image:o,$scale:i,$sourceImage:n}=this;if(n&&o&&i>=0){const[n,a,r,h,c,l]=e,d=(t*h-r*s)/(n*h-r*a),u=(s*n-a*t)/(n*h-r*a),m=n*d+r*u+c,$=a*d+h*u+l;o.$ready((e=>{this.$setStyles.call(o,{width:e.naturalWidth*i,height:e.naturalHeight*i})})),o.$setTransform(n,a,r,h,m*i,$*i)}}}W.$name=a,W.$version="2.0.0";export{N as RESIZE_BOTH,j as RESIZE_HORIZONTAL,M as RESIZE_NONE,x as RESIZE_VERTICAL,W as default};
