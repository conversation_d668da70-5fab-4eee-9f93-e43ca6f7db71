!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).CropperViewer={})}(this,(function(e){"use strict";const t="undefined"!=typeof window&&void 0!==window.document,s=t?window:{};t&&s.document.documentElement;const o="cropper",i=`${o}-canvas`,n=`${o}-image`,a=`${o}-selection`,r=`${o}-viewer`,h="load",c="change",l="transform",d=Number.isNaN||s.isNaN;const u=/([a-z\d])([A-Z])/g;function m(e){return String(e).replace(u,"$1-$2").toLowerCase()}const g=/-[A-z\d]/g;function $(e){return e.replace(g,(e=>e.slice(1).toUpperCase()))}const f=/\s\s*/;function p(e,t,s,o){t.trim().split(f).forEach((t=>{e.removeEventListener(t,s,o)}))}function b(e,t,s,o){t.trim().split(f).forEach((t=>{e.addEventListener(t,s,o)}))}const S={bubbles:!0,cancelable:!0,composed:!0};const y=Promise.resolve();const w=/left|top|width|height/i,C="open",v=new WeakMap,I=new WeakMap,E=new Map,k=s.document&&Array.isArray(s.document.adoptedStyleSheets)&&"replaceSync"in s.CSSStyleSheet.prototype;class T extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var e,t;super(),this.shadowRootMode=C,this.slottable=!0;const s=null===(t=null===(e=Object.getPrototypeOf(this))||void 0===e?void 0:e.constructor)||void 0===t?void 0:t.$name;s&&E.set(s,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(e,t,s){if(Object.is(s,t))return;const o=$(e);let i=s;switch(typeof this[o]){case"boolean":i=null!==s&&"false"!==s;break;case"number":i=Number(s)}switch(this[o]=i,e){case"theme-color":{const e=I.get(this),t=this.$sharedStyle;e&&t&&(k?e.replaceSync(t):e.textContent=t);break}}}$propertyChangedCallback(e,t,s){if(!Object.is(s,t))switch(e=m(e),typeof s){case"boolean":!0===s?this.hasAttribute(e)||this.setAttribute(e,""):this.removeAttribute(e);break;case"number":s=d(s)?"":String(s);default:s?this.getAttribute(e)!==s&&this.setAttribute(e,s):this.removeAttribute(e)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((e=>{const t=$(e);let s=this[t];(function(e){return void 0===e})(s)||this.$propertyChangedCallback(t,void 0,s),Object.defineProperty(this,t,{enumerable:!0,configurable:!0,get:()=>s,set(e){const o=s;s=e,this.$propertyChangedCallback(t,o,e)}})}));const e=this.attachShadow({mode:this.shadowRootMode||C});if(this.shadowRoot||v.set(this,e),I.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const t=document.createElement("template");t.innerHTML=this.$template,e.appendChild(t.content)}if(this.slottable){const t=document.createElement("slot");e.appendChild(t)}}disconnectedCallback(){I.has(this)&&I.delete(this),v.has(this)&&v.delete(this)}$getTagNameOf(e){var t;return null!==(t=E.get(e))&&void 0!==t?t:e}$setStyles(e){return Object.keys(e).forEach((t=>{let s=e[t];(function(e){return"number"==typeof e&&!d(e)})(s)&&(s=0!==s&&w.test(t)?`${s}px`:String(s)),this.style[t]=s})),this}$getShadowRoot(){return this.shadowRoot||v.get(this)}$addStyles(e){let t;const s=this.$getShadowRoot();return k?(t=new CSSStyleSheet,t.replaceSync(e),s.adoptedStyleSheets=s.adoptedStyleSheets.concat(t)):(t=document.createElement("style"),t.textContent=e,s.appendChild(t)),t}$emit(e,t,s){return function(e,t,s,o){return e.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign(Object.assign({},S),{detail:s}),o)))}(this,e,t,s)}$nextTick(e){return function(e,t){return t?y.then(e?t.bind(e):t):y}(this,e)}static $define(e,o){var i;"object"==typeof(i=e)&&null!==i&&(o=e,e=""),e||(e=this.$name||this.name),e=m(e),t&&s.customElements&&!s.customElements.get(e)&&customElements.define(e,this,o)}}T.$version="2.0.0";const O=new WeakMap,A=new WeakMap,N=new WeakMap,L=new WeakMap,j="both",x="horizontal",R="vertical",M="none";class W extends T{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}",this.resize=R,this.selection="",this.slottable=!1}set $image(e){A.set(this,e)}get $image(){return A.get(this)}set $sourceImage(e){L.set(this,e)}get $sourceImage(){return L.get(this)}set $canvas(e){O.set(this,e)}get $canvas(){return O.get(this)}set $selection(e){N.set(this,e)}get $selection(){return N.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let e=null;if(e=this.selection?this.ownerDocument.querySelector(this.selection):this.closest(this.$getTagNameOf(a)),"object"==typeof(t=e)&&null!==t&&1===t.nodeType){this.$selection=e,this.$onSelectionChange=this.$handleSelectionChange.bind(this),b(e,c,this.$onSelectionChange);const t=e.closest(this.$getTagNameOf(i));if(t){this.$canvas=t;const e=t.querySelector(this.$getTagNameOf(n));e&&(this.$sourceImage=e,this.$image=e.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),b(e.$image,h,this.$onSourceImageLoad),b(e,l,this.$onSourceImageTransform))}this.$render()}var t}disconnectedCallback(){const{$selection:e,$sourceImage:t}=this;e&&this.$onSelectionChange&&(p(e,c,this.$onSelectionChange),this.$onSelectionChange=null),t&&this.$onSourceImageLoad&&(p(t.$image,h,this.$onSourceImageLoad),this.$onSourceImageLoad=null),t&&this.$onSourceImageTransform&&(p(t,l,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(e){this.$render(e.detail)}$handleSourceImageLoad(){const{$image:e,$sourceImage:t}=this,s=e.getAttribute("src"),o=t.getAttribute("src");o&&o!==s&&(e.setAttribute("src",o),e.$ready((()=>{setTimeout((()=>{this.$render()}),50)})))}$handleSourceImageTransform(e){this.$render(void 0,e.detail.matrix)}$render(e,t){const{$canvas:s,$selection:o}=this;e||o.hidden||(e=o),(!e||0===e.x&&0===e.y&&0===e.width&&0===e.height)&&(e={x:0,y:0,width:s.offsetWidth,height:s.offsetHeight});const{x:i,y:n,width:a,height:r}=e,h={},{clientWidth:c,clientHeight:l}=this;let d=c,u=l,m=NaN;switch(this.resize){case j:m=1,d=a,u=r,h.width=a,h.height=r;break;case x:m=r>0?l/r:0,d=a*m,h.width=d;break;case R:m=a>0?c/a:0,u=r*m,h.height=u;break;default:c>0?m=a>0?c/a:0:l>0&&(m=r>0?l/r:0)}this.$scale=m,this.$setStyles(h),this.$sourceImage&&this.$transformImageByOffset(null!=t?t:this.$sourceImage.$getTransform(),-i,-n)}$transformImageByOffset(e,t,s){const{$image:o,$scale:i,$sourceImage:n}=this;if(n&&o&&i>=0){const[n,a,r,h,c,l]=e,d=(t*h-r*s)/(n*h-r*a),u=(s*n-a*t)/(n*h-r*a),m=n*d+r*u+c,g=a*d+h*u+l;o.$ready((e=>{this.$setStyles.call(o,{width:e.naturalWidth*i,height:e.naturalHeight*i})})),o.$setTransform(n,a,r,h,m*i,g*i)}}}W.$name=r,W.$version="2.0.0",e.RESIZE_BOTH=j,e.RESIZE_HORIZONTAL=x,e.RESIZE_NONE=M,e.RESIZE_VERTICAL=R,e.default=W,Object.defineProperty(e,"__esModule",{value:!0})}));
