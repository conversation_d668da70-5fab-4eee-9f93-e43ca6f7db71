{"name": "@cropper/element-viewer", "version": "2.0.1", "description": "A custom viewer element for the Cropper.", "main": "dist/element-viewer.raw.js", "module": "dist/element-viewer.esm.raw.js", "types": "dist/element-viewer.d.ts", "unpkg": "dist/element-viewer.js", "jsdelivr": "dist/element-viewer.js", "files": ["dist"], "exports": {".": {"import": {"types": "./dist/element-viewer.d.ts", "node": {"production": "./dist/element-viewer.esm.min.js", "development": "./dist/element-viewer.esm.js", "default": "./dist/element-viewer.esm.raw.js"}, "default": "./dist/element-viewer.esm.raw.js"}, "require": {"types": "./dist/element-viewer.d.ts", "node": {"production": "./dist/element-viewer.min.js", "development": "./dist/element-viewer.js", "default": "./dist/element-viewer.raw.js"}, "default": "./dist/element-viewer.raw.js"}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "scripts": {"api-extractor": "api-extractor run --local --verbose", "build": "npm run tsc && npm run api-extractor && npm run rollup", "clean": "del-cli dist .temp", "release": "npm run clean && npm run build", "rollup": "rollup -c ../../rollup.config.js", "tsc": "tsc --outDir ./.temp --declaration --emitDeclarationOnly"}, "repository": {"type": "git", "url": "https://github.com/fengyuanchen/cropperjs.git", "directory": "packages/element-viewer"}, "keywords": ["viewer", "cropper", "cropper.js", "cropper-element", "custom-element", "web-component"], "author": "<PERSON> (https://chenfengyuan.com/)", "license": "MIT", "bugs": "https://github.com/fengyuanchen/cropperjs/issues", "homepage": "https://github.com/fengyuanchen/cropperjs/tree/main/packages/element-viewer/#readme", "dependencies": {"@cropper/element": "^2.0.1", "@cropper/element-canvas": "^2.0.1", "@cropper/element-image": "^2.0.1", "@cropper/element-selection": "^2.0.1", "@cropper/utils": "^2.0.1"}, "publishConfig": {"access": "public"}, "gitHead": "6b8e5ff146012939d08b13fae360a1a6ad2dda29"}