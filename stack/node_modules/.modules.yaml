hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.4':
    '@adobe/css-tools': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@7.2.1':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/icons@5.6.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/icons': private
  '@ant-design/react-slick@1.1.2(react@18.3.1)':
    '@ant-design/react-slick': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.3':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.3)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.28.3':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.28.3':
    '@babel/helpers': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.3)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.3)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.3)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-explicit-resource-management': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.3)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-react-constant-elements@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-constant-elements': private
  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-development': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-pure-annotations': private
  '@babel/plugin-transform-regenerator@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.28.3(@babel/core@7.28.3)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.3)':
    '@babel/preset-modules': private
  '@babel/preset-react@7.27.1(@babel/core@7.28.3)':
    '@babel/preset-react': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.28.3)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.28.3':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/is-prop-valid@1.2.2':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.8.1':
    '@emotion/memoize': private
  '@emotion/unitless@0.8.1':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.8.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.57.1':
    '@eslint/js': public
  '@fast-csv/format@4.3.5':
    '@fast-csv/format': private
  '@fast-csv/parse@4.3.6':
    '@fast-csv/parse': private
  '@fortawesome/fontawesome-free@6.7.2':
    '@fortawesome/fontawesome-free': private
  '@happy-dom/global-registrator@14.12.3':
    '@happy-dom/global-registrator': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@icons/material@0.2.4(react@18.3.1)':
    '@icons/material': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5))':
    '@jest/core': private
  '@jest/create-cache-key-function@30.0.5':
    '@jest/create-cache-key-function': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@28.1.3':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/pattern@30.0.1':
    '@jest/pattern': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.11':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  '@monaco-editor/loader@1.5.0':
    '@monaco-editor/loader': private
  '@monaco-editor/react@4.7.0(monaco-editor@0.45.0)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@monaco-editor/react': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@puppeteer/browsers@2.3.0':
    '@puppeteer/browsers': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/trigger': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@restart/hooks@0.3.27(react@18.3.1)':
    '@restart/hooks': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/plugin-commonjs@22.0.2(rollup@2.79.2)':
    '@rollup/plugin-commonjs': private
  '@rollup/plugin-json@4.1.0(rollup@2.79.2)':
    '@rollup/plugin-json': private
  '@rollup/plugin-node-resolve@14.1.0(rollup@2.79.2)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@5.0.7(rollup@2.79.2)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-typescript@8.5.0(rollup@2.79.2)(tslib@2.8.1)(typescript@4.9.5)':
    '@rollup/plugin-typescript': private
  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.50.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.50.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.50.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.50.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.50.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.50.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.50.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.50.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.50.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.50.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.50.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.50.0':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.50.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.50.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.50.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.50.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.50.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-openharmony-arm64@4.50.0':
    '@rollup/rollup-openharmony-arm64': private
  '@rollup/rollup-win32-arm64-msvc@4.50.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.50.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.50.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sinclair/typebox@0.24.51':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@stylelint/postcss-css-in-js@0.37.3(postcss-syntax@0.36.2(postcss-html@0.36.0(postcss-syntax@0.36.2(postcss@7.0.39))(postcss@7.0.39))(postcss-less@3.1.4)(postcss-scss@2.1.1)(postcss@7.0.39))(postcss@7.0.39)':
    '@stylelint/postcss-css-in-js': private
  '@stylelint/postcss-markdown@0.36.2(postcss-syntax@0.36.2(postcss-html@0.36.0(postcss-syntax@0.36.2(postcss@7.0.39))(postcss@7.0.39))(postcss-less@3.1.4)(postcss-scss@2.1.1)(postcss@7.0.39))(postcss@7.0.39)':
    '@stylelint/postcss-markdown': private
  '@svgr/babel-plugin-add-jsx-attribute@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-plugin-add-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.28.3)':
    '@svgr/babel-plugin-remove-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.28.3)':
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  '@svgr/babel-plugin-replace-jsx-attribute-value@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  '@svgr/babel-plugin-svg-dynamic-title@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-plugin-svg-dynamic-title': private
  '@svgr/babel-plugin-svg-em-dimensions@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-plugin-svg-em-dimensions': private
  '@svgr/babel-plugin-transform-react-native-svg@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-plugin-transform-react-native-svg': private
  '@svgr/babel-plugin-transform-svg-component@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-plugin-transform-svg-component': private
  '@svgr/babel-preset@6.5.1(@babel/core@7.28.3)':
    '@svgr/babel-preset': private
  '@svgr/core@6.5.1':
    '@svgr/core': private
  '@svgr/hast-util-to-babel-ast@6.5.1':
    '@svgr/hast-util-to-babel-ast': private
  '@svgr/plugin-jsx@6.5.1(@svgr/core@6.5.1)':
    '@svgr/plugin-jsx': private
  '@svgr/plugin-svgo@6.5.1(@svgr/core@6.5.1)':
    '@svgr/plugin-svgo': private
  '@svgr/rollup@6.5.1':
    '@svgr/rollup': private
  '@swc/core-darwin-arm64@1.13.5':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.13.5':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.13.5':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.13.5':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.13.5':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.13.5':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.13.5':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.13.5':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.13.5':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.13.5':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.13.5':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/jest@0.2.39(@swc/core@1.13.5)':
    '@swc/jest': private
  '@swc/types@0.1.24':
    '@swc/types': private
  '@testing-library/dom@8.20.1':
    '@testing-library/dom': private
  '@testing-library/jest-dom@5.17.0':
    '@testing-library/jest-dom': private
  '@testing-library/react@13.4.0(@types/react@18.3.24)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@testing-library/react': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/async@2.4.2':
    '@types/async': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/codemirror@5.60.10':
    '@types/codemirror': private
  '@types/doctrine@0.0.5':
    '@types/doctrine': private
  '@types/echarts@4.9.22':
    '@types/echarts': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': public
  '@types/eslint@9.6.1':
    '@types/eslint': public
  '@types/estree@0.0.39':
    '@types/estree': private
  '@types/file-saver@2.0.7':
    '@types/file-saver': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/history@4.7.11':
    '@types/history': private
  '@types/hoist-non-react-statics@3.3.7(@types/react@18.3.24)':
    '@types/hoist-non-react-statics': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@28.1.8':
    '@types/jest': private
  '@types/jquery@3.5.33':
    '@types/jquery': private
  '@types/jsdom@20.0.1':
    '@types/jsdom': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/markdown-it@12.2.3':
    '@types/markdown-it': private
  '@types/mdast@3.0.15':
    '@types/mdast': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/minimist@1.2.5':
    '@types/minimist': private
  '@types/mkdirp@1.0.2':
    '@types/mkdirp': private
  '@types/node@14.18.63':
    '@types/node': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/papaparse@5.3.16':
    '@types/papaparse': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prettier@2.7.3':
    '@types/prettier': public
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/react-color@3.0.13(@types/react@18.3.24)':
    '@types/react-color': private
  '@types/react-dom@18.3.7(@types/react@18.3.24)':
    '@types/react-dom': private
  '@types/react-is@18.3.1':
    '@types/react-is': private
  '@types/react-onclickoutside@6.7.10':
    '@types/react-onclickoutside': private
  '@types/react-router-dom@5.3.3':
    '@types/react-router-dom': private
  '@types/react-router@5.1.20':
    '@types/react-router': private
  '@types/react-test-renderer@18.3.1':
    '@types/react-test-renderer': private
  '@types/react-transition-group@4.4.3':
    '@types/react-transition-group': private
  '@types/react@18.3.24':
    '@types/react': private
  '@types/reactcss@1.2.13(@types/react@18.3.24)':
    '@types/reactcss': private
  '@types/resolve@1.17.1':
    '@types/resolve': private
  '@types/semver@7.7.1':
    '@types/semver': private
  '@types/sizzle@2.3.10':
    '@types/sizzle': private
  '@types/sortablejs@1.15.8':
    '@types/sortablejs': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/stylis@4.2.5':
    '@types/stylis': private
  '@types/tern@0.23.9':
    '@types/tern': private
  '@types/testing-library__jest-dom@5.14.9':
    '@types/testing-library__jest-dom': private
  '@types/tinymce@4.6.9':
    '@types/tinymce': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/unist@2.0.11':
    '@types/unist': private
  '@types/warning@3.0.3':
    '@types/warning': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@types/zrender@5.0.0':
    '@types/zrender': private
  '@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitejs/plugin-react@4.7.0(vite@5.4.19(sass@1.92.0)(sugarss@2.0.0)(terser@5.44.0))':
    '@vitejs/plugin-react': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abab@2.0.6:
    abab: private
  abbrev@1.1.1:
    abbrev: private
  acorn-globals@7.0.1:
    acorn-globals: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  adler-32@1.3.1:
    adler-32: private
  agent-base@6.0.2:
    agent-base: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  animate.css@4.1.1:
    animate.css: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  antd@5.27.2(date-fns@2.30.0)(moment@2.30.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    antd: private
  anymatch@3.1.3:
    anymatch: private
  aproba@2.1.0:
    aproba: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.1.3:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-find-index@1.0.2:
    array-find-index: private
  array-union@2.1.0:
    array-union: private
  arrify@1.0.1:
    arrify: private
  asap@2.0.6:
    asap: private
  ast-types@0.13.4:
    ast-types: private
  astral-regex@2.0.0:
    astral-regex: private
  async@1.5.2:
    async: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  attr-accept@2.2.2:
    attr-accept: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios@0.25.0:
    axios: private
  b4a@1.6.7:
    b4a: private
  babel-jest@29.7.0(@babel/core@7.28.3):
    babel-jest: private
  babel-plugin-import@1.13.8:
    babel-plugin-import: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.3):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.3):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.3):
    babel-plugin-polyfill-regenerator: private
  babel-preset-current-node-syntax@1.2.0(@babel/core@7.28.3):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.28.3):
    babel-preset-jest: private
  bail@1.0.5:
    bail: private
  balanced-match@2.0.0:
    balanced-match: private
  bare-events@2.6.1:
    bare-events: private
  bare-fs@4.2.3:
    bare-fs: private
  bare-os@3.6.2:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.7.0(bare-events@2.6.1):
    bare-stream: private
  base16@1.0.0:
    base16: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bce-sdk-js@0.2.9:
    bce-sdk-js: private
  big-integer@1.6.52:
    big-integer: private
  binary-extensions@2.3.0:
    binary-extensions: private
  binary@0.3.0:
    binary: private
  bl@4.1.0:
    bl: private
  bluebird@3.4.7:
    bluebird: private
  blueimp-canvastoblob@2.1.0:
    blueimp-canvastoblob: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.4:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer-indexof-polyfill@1.0.2:
    buffer-indexof-polyfill: private
  buffer@5.7.1:
    buffer: private
  buffers@0.1.1:
    buffers: private
  builtin-modules@3.3.0:
    builtin-modules: private
  builtins@2.0.1:
    builtins: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-keys@6.2.2:
    camelcase-keys: private
  camelcase@6.3.0:
    camelcase: private
  camelize@1.0.1:
    camelize: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001739:
    caniuse-lite: private
  canvas@2.11.2:
    canvas: private
  cfb@1.2.2:
    cfb: private
  chainsaw@0.1.0:
    chainsaw: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chokidar@3.6.0:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  chromium-bidi@0.6.3(devtools-protocol@0.0.1312386):
    chromium-bidi: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  classnames@2.3.2:
    classnames: private
  cliui@8.0.1:
    cliui: private
  clone-regexp@2.2.0:
    clone-regexp: private
  clsx@2.1.1:
    clsx: private
  co@4.6.0:
    co: private
  codepage@1.15.0:
    codepage: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  colord@2.9.3:
    colord: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@13.1.0:
    commander: private
  commenting@1.1.0:
    commenting: private
  commondir@1.0.1:
    commondir: private
  compress-commons@4.1.2:
    compress-commons: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  concat-with-sourcemaps@1.1.0:
    concat-with-sourcemaps: private
  concurrently@7.6.0:
    concurrently: private
  console-control-strings@1.1.0:
    console-control-strings: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-to-clipboard@3.3.1:
    copy-to-clipboard: private
  core-js-compat@3.45.1:
    core-js-compat: private
  core-js@3.45.1:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@9.0.0(typescript@4.9.5):
    cosmiconfig: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  create-jest@29.7.0(@types/node@14.18.63)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cropperjs@1.6.2:
    cropperjs: private
  cross-env@7.0.3:
    cross-env: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-declaration-sorter@6.4.1(postcss@8.5.6):
    css-declaration-sorter: private
  css-select@4.3.0:
    css-select: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  css-tree@1.1.3:
    css-tree: private
  css-what@6.2.2:
    css-what: private
  css.escape@1.5.1:
    css.escape: private
  css@3.0.0:
    css: private
  cssesc@3.0.0:
    cssesc: private
  cssfontparser@1.2.1:
    cssfontparser: private
  cssnano-preset-default@5.2.14(postcss@8.5.6):
    cssnano-preset-default: private
  cssnano-utils@3.1.0(postcss@8.5.6):
    cssnano-utils: private
  cssnano@5.1.15(postcss@8.5.6):
    cssnano: private
  csso@4.2.0:
    csso: private
  cssom@0.5.0:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-urls@3.0.2:
    data-urls: private
  date-fns@2.30.0:
    date-fns: private
  dayjs@1.11.18:
    dayjs: private
  debug@4.4.1:
    debug: private
  decamelize-keys@1.1.1:
    decamelize-keys: private
  decamelize@1.2.0:
    decamelize: private
  decimal.js@10.6.0:
    decimal.js: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decompress-response@4.2.1:
    decompress-response: private
  dedent@1.7.0:
    dedent: private
  deep-equal@2.2.3:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  dependency-graph@0.11.0:
    dependency-graph: private
  dequal@2.0.3:
    dequal: private
  detect-libc@1.0.3:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  devtools-protocol@0.0.1312386:
    devtools-protocol: private
  diff-sequences@28.1.1:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dom-accessibility-api@0.5.16:
    dom-accessibility-api: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@0.2.2:
    dom-serializer: private
  domelementtype@1.3.1:
    domelementtype: private
  domexception@4.0.0:
    domexception: private
  domhandler@2.4.2:
    domhandler: private
  domutils@1.7.0:
    domutils: private
  downshift@6.1.12(react@18.3.1):
    downshift: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer2@0.1.4:
    duplexer2: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  echarts-stat@1.2.0:
    echarts-stat: private
  echarts-wordcloud@2.1.0(echarts@5.5.1):
    echarts-wordcloud: private
  echarts@5.5.1:
    echarts: private
  electron-to-chromium@1.5.214:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@2.1.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-get-iterator@1.1.3:
    es-get-iterator: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es6-promise@4.2.8:
    es6-promise: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-plugin-react-hooks@4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  eslint-plugin-react-refresh@0.4.20(eslint@8.57.1):
    eslint-plugin-react-refresh: public
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  eslint@8.57.1:
    eslint: public
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  exceljs@4.4.0:
    exceljs: private
  execa@4.1.0:
    execa: private
  execall@2.0.0:
    execall: private
  exit@0.1.2:
    exit: private
  expect@28.1.3:
    expect: private
  extend@3.0.2:
    extend: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-csv@4.3.6:
    fast-csv: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.1.0:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fbemitter@3.0.0:
    fbemitter: private
  fbjs-css-vars@1.0.2:
    fbjs-css-vars: private
  fbjs@3.0.5:
    fbjs: private
  fd-slicer@1.1.0:
    fd-slicer: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-saver@2.0.5:
    file-saver: private
  file-selector@0.4.0:
    file-selector: private
  file64@1.0.5:
    file64: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  flux@4.0.4(react@18.3.1):
    flux: private
  follow-redirects@1.15.11:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.4:
    form-data: private
  frac@1.1.2:
    frac: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs-walk@0.0.2:
    fs-walk: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  fstream@1.0.12:
    fstream: private
  function-bind@1.1.2:
    function-bind: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gauge@3.0.2:
    gauge: private
  generic-names@4.0.0:
    generic-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stdin@9.0.0:
    get-stdin: private
  get-stream@5.2.0:
    get-stream: private
  get-uri@6.0.5:
    get-uri: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.2.3:
    glob: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  globals@13.24.0:
    globals: private
  globby@12.2.0:
    globby: private
  globjoin@0.1.4:
    globjoin: private
  gonzales-pe@4.3.0:
    gonzales-pe: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  handlebars@4.7.8:
    handlebars: private
  happy-dom@14.12.3:
    happy-dom: private
  hard-rejection@2.1.0:
    hard-rejection: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  history@4.10.1:
    history: private
  hls.js@1.1.3:
    hls.js: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  hotkeys-js@3.13.15:
    hotkeys-js: private
  html-encoding-sniffer@3.0.0:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  html-tags@3.3.1:
    html-tags: private
  htmlparser2@3.10.1:
    htmlparser2: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@1.1.1:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  icss-replace-symbols@1.1.0:
    icss-replace-symbols: private
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immediate@3.0.6:
    immediate: private
  immutability-helper@3.1.1:
    immutability-helper: private
  immutable@4.3.7:
    immutable: private
  import-cwd@3.0.0:
    import-cwd: private
  import-fresh@3.3.1:
    import-fresh: private
  import-from@3.0.0:
    import-from: private
  import-lazy@4.0.0:
    import-lazy: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  ip-address@10.0.1:
    ip-address: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-builtin-module@3.2.1:
    is-builtin-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-reference@1.2.1:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@2.1.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@0.0.1:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.2.0:
    istanbul-reports: private
  jackspeak@4.1.1:
    jackspeak: private
  jest-canvas-mock@2.5.2:
    jest-canvas-mock: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@14.18.63)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    jest-cli: private
  jest-config@29.7.0(@types/node@14.18.63)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    jest-config: private
  jest-diff@28.1.3:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-jsdom@29.7.0(canvas@2.11.2):
    jest-environment-jsdom: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@28.0.2:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@28.1.3:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@26.6.2:
    jest-worker: private
  jest@29.7.0(@types/node@14.18.63)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    jest: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbarcode@3.12.1:
    jsbarcode: private
  jsdom@20.0.3(canvas@2.11.2):
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.2.0:
    jsonfile: private
  jszip@3.10.1:
    jszip: private
  keycode@2.2.1:
    keycode: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@3.0.3:
    kleur: private
  klona@2.0.6:
    klona: private
  known-css-properties@0.21.0:
    known-css-properties: private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lie@3.3.0:
    lie: private
  lilconfig@2.1.0:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@3.0.3:
    linkify-it: private
  listenercount@1.0.1:
    listenercount: private
  load-json-file@4.0.0:
    load-json-file: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@3.3.1:
    loader-utils: private
  locate-path@5.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.curry@4.1.1:
    lodash.curry: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.escaperegexp@4.1.2:
    lodash.escaperegexp: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.flow@3.5.0:
    lodash.flow: private
  lodash.groupby@4.6.0:
    lodash.groupby: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isfunction@3.0.9:
    lodash.isfunction: private
  lodash.isnil@4.0.0:
    lodash.isnil: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isundefined@3.0.1:
    lodash.isundefined: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  longest-streak@2.0.4:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.25.9:
    magic-string: private
  make-cancellable-promise@1.3.2:
    make-cancellable-promise: private
  make-dir@3.1.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  make-event-props@1.6.2:
    make-event-props: private
  makeerror@1.0.12:
    makeerror: private
  map-obj@4.3.0:
    map-obj: private
  markdown-it-html5-media@0.7.1:
    markdown-it-html5-media: private
  markdown-it@12.3.2:
    markdown-it: private
  marked@16.2.1:
    marked: private
  match-sorter@6.4.0:
    match-sorter: private
  material-colors@1.2.6:
    material-colors: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mathml-tag-names@2.1.3:
    mathml-tag-names: private
  mdast-util-from-markdown@0.8.5:
    mdast-util-from-markdown: private
  mdast-util-to-markdown@0.6.5:
    mdast-util-to-markdown: private
  mdast-util-to-string@2.0.0:
    mdast-util-to-string: private
  mdn-data@2.0.14:
    mdn-data: private
  mdurl@1.0.1:
    mdurl: private
  meow@9.0.0:
    meow: private
  merge-refs@1.3.0(@types/react@18.3.24):
    merge-refs: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromark@2.11.4:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@2.1.0:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  mini-create-react-context@0.4.1(prop-types@15.8.1)(react@18.3.1):
    mini-create-react-context: private
  mini-css-extract-plugin@2.9.4(webpack@5.101.3(@swc/core@1.13.5)):
    mini-css-extract-plugin: private
  minimatch@3.1.2:
    minimatch: private
  minimist-options@4.1.0:
    minimist-options: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp@1.0.4:
    mkdirp: private
  mobx-react-lite@4.1.0(mobx@4.15.7)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    mobx-react-lite: private
  mobx-react@6.3.1(mobx@4.15.7)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    mobx-react: private
  mobx-state-tree@3.17.3(mobx@4.15.7):
    mobx-state-tree: private
  mobx@4.15.7:
    mobx: private
  moment-timezone@0.5.48:
    moment-timezone: private
  moment@2.30.1:
    moment: private
  monaco-editor@0.45.0:
    monaco-editor: private
  moo-color@1.0.3:
    moo-color: private
  mpegts.js@1.8.0:
    mpegts.js: private
  mri@1.2.0:
    mri: private
  ms@2.0.0:
    ms: private
  nan@2.23.0:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  netmask@2.0.2:
    netmask: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-fetch@2.7.0:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  nopt@5.0.0:
    nopt: private
  normalize-package-data@3.0.3:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-selector@0.2.0:
    normalize-selector: private
  normalize-url@6.1.0:
    normalize-url: private
  npm-run-path@4.0.1:
    npm-run-path: private
  npmlog@5.0.1:
    npmlog: private
  nth-check@2.1.1:
    nth-check: private
  num2fraction@1.2.2:
    num2fraction: private
  numfmt@2.5.2:
    numfmt: private
  nwsapi@2.2.21:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  office-viewer@0.3.14(echarts@5.5.1):
    office-viewer: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-queue@6.6.2:
    p-queue: private
  p-timeout@3.2.0:
    p-timeout: private
  p-try@2.2.0:
    p-try: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-name-regex@2.0.6:
    package-name-regex: private
  packages/app:
    elcube-frontend-debugger: private
  packages/ui/amis:
    amis: private
  packages/ui/amis-core:
    amis-core: private
  packages/ui/amis-formula:
    amis-formula: private
  packages/ui/amis-ui:
    amis-ui: private
  pako@1.0.11:
    pako: private
  papaparse@5.5.3:
    papaparse: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  parse-json@5.2.0:
    parse-json: private
  parse5@7.3.0:
    parse5: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-to-regexp@6.2.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  path2d@0.2.2:
    path2d: private
  pdfjs-dist@4.3.136:
    pdfjs-dist: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@3.0.1:
    picomatch: private
  pify@5.0.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-calc@8.2.4(postcss@8.5.6):
    postcss-calc: private
  postcss-cli@9.1.0(postcss@8.5.6)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    postcss-cli: private
  postcss-colormin@5.3.1(postcss@8.5.6):
    postcss-colormin: private
  postcss-convert-values@5.1.3(postcss@8.5.6):
    postcss-convert-values: private
  postcss-custom-properties@12.1.11(postcss@8.5.6):
    postcss-custom-properties: private
  postcss-discard-comments@5.1.2(postcss@8.5.6):
    postcss-discard-comments: private
  postcss-discard-duplicates@5.1.0(postcss@8.5.6):
    postcss-discard-duplicates: private
  postcss-discard-empty@5.1.1(postcss@8.5.6):
    postcss-discard-empty: private
  postcss-discard-overridden@5.1.0(postcss@8.5.6):
    postcss-discard-overridden: private
  postcss-html@0.36.0(postcss-syntax@0.36.2(postcss@7.0.39))(postcss@7.0.39):
    postcss-html: private
  postcss-import@14.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-less@3.1.4:
    postcss-less: private
  postcss-load-config@3.1.4(postcss@8.5.6)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    postcss-load-config: private
  postcss-media-query-parser@0.2.3:
    postcss-media-query-parser: private
  postcss-merge-longhand@5.1.7(postcss@8.5.6):
    postcss-merge-longhand: private
  postcss-merge-rules@5.1.4(postcss@8.5.6):
    postcss-merge-rules: private
  postcss-minify-font-values@5.1.0(postcss@8.5.6):
    postcss-minify-font-values: private
  postcss-minify-gradients@5.1.1(postcss@8.5.6):
    postcss-minify-gradients: private
  postcss-minify-params@5.1.4(postcss@8.5.6):
    postcss-minify-params: private
  postcss-minify-selectors@5.2.1(postcss@8.5.6):
    postcss-minify-selectors: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: private
  postcss-modules@4.3.1(postcss@8.5.6):
    postcss-modules: private
  postcss-normalize-charset@5.1.0(postcss@8.5.6):
    postcss-normalize-charset: private
  postcss-normalize-display-values@5.1.0(postcss@8.5.6):
    postcss-normalize-display-values: private
  postcss-normalize-positions@5.1.1(postcss@8.5.6):
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@5.1.1(postcss@8.5.6):
    postcss-normalize-repeat-style: private
  postcss-normalize-string@5.1.0(postcss@8.5.6):
    postcss-normalize-string: private
  postcss-normalize-timing-functions@5.1.0(postcss@8.5.6):
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@5.1.1(postcss@8.5.6):
    postcss-normalize-unicode: private
  postcss-normalize-url@5.1.0(postcss@8.5.6):
    postcss-normalize-url: private
  postcss-normalize-whitespace@5.1.1(postcss@8.5.6):
    postcss-normalize-whitespace: private
  postcss-ordered-values@5.1.3(postcss@8.5.6):
    postcss-ordered-values: private
  postcss-reduce-initial@5.1.2(postcss@8.5.6):
    postcss-reduce-initial: private
  postcss-reduce-transforms@5.1.0(postcss@8.5.6):
    postcss-reduce-transforms: private
  postcss-reporter@7.1.0(postcss@8.5.6):
    postcss-reporter: private
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: private
  postcss-safe-parser@4.0.2:
    postcss-safe-parser: private
  postcss-sass@0.4.4:
    postcss-sass: private
  postcss-scss@2.1.1:
    postcss-scss: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-svgo@5.1.0(postcss@8.5.6):
    postcss-svgo: private
  postcss-syntax@0.36.2(postcss-html@0.36.0(postcss-syntax@0.36.2(postcss@7.0.39))(postcss@7.0.39))(postcss-less@3.1.4)(postcss-scss@2.1.1)(postcss@7.0.39):
    postcss-syntax: private
  postcss-unique-selectors@5.1.1(postcss@8.5.6):
    postcss-unique-selectors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier@2.8.8:
    prettier: public
  pretty-format@27.5.1:
    pretty-format: private
  pretty-hrtime@1.0.3:
    pretty-hrtime: private
  pretty-quick@3.3.1(prettier@2.8.8):
    pretty-quick: private
  prismjs@1.30.0:
    prismjs: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promise.series@0.2.0:
    promise.series: private
  promise@7.3.1:
    promise: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  puppeteer-core@22.15.0:
    puppeteer-core: private
  puppeteer@22.15.0(typescript@4.9.5):
    puppeteer: private
  pure-color@1.3.0:
    pure-color: private
  pure-rand@6.1.0:
    pure-rand: private
  q@1.5.1:
    q: private
  qrcode-react-next@1.0.0(react@18.3.1):
    qrcode-react-next: private
  qs@6.9.7:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@4.0.1:
    quick-lru: private
  randombytes@2.1.0:
    randombytes: private
  rc-cascader@3.34.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-field-form: private
  rc-image@7.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-image: private
  rc-input-number@7.4.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input-number: private
  rc-input@1.8.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input: private
  rc-mentions@2.20.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-menu: private
  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-motion: private
  rc-notification@5.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-pagination: private
  rc-picker@4.11.3(date-fns@2.30.0)(dayjs@1.11.18)(moment@2.30.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-picker: private
  rc-progress@3.4.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-progress: private
  rc-rate@2.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-segmented: private
  rc-select@14.16.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-select: private
  rc-slider@11.1.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-slider: private
  rc-steps@6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-steps: private
  rc-switch@4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-switch: private
  rc-table@7.51.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-table: private
  rc-tabs@15.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tabs: private
  rc-textarea@1.10.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree: private
  rc-upload@4.9.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-upload: private
  rc-util@5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-virtual-list: private
  react-base16-styling@0.6.0:
    react-base16-styling: private
  react-color@2.19.3(react@18.3.1):
    react-color: private
  react-cropper@2.3.3(react@18.3.1):
    react-cropper: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-draggable@4.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-draggable: private
  react-dropzone@11.7.1(react@18.3.1):
    react-dropzone: private
  react-hook-form@7.39.0(react@18.3.1):
    react-hook-form: private
  react-intersection-observer@9.5.2(react@18.3.1):
    react-intersection-observer: private
  react-is@19.1.1:
    react-is: private
  react-json-view@1.21.3(@types/react@18.3.24)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-json-view: private
  react-lifecycles-compat@3.0.4:
    react-lifecycles-compat: private
  react-overlays@5.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-overlays: private
  react-pdf@9.0.0(@types/react@18.3.24)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-pdf: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-router-dom: private
  react-router@5.2.1(react@18.3.1):
    react-router: private
  react-shallow-renderer@16.15.0(react@18.3.1):
    react-shallow-renderer: private
  react-test-renderer@18.3.1(react@18.3.1):
    react-test-renderer: private
  react-textarea-autosize@8.3.3(@types/react@18.3.24)(react@18.3.1):
    react-textarea-autosize: private
  react-transition-group@4.4.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  react-visibility-sensor@5.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-visibility-sensor: private
  react@18.3.1:
    react: private
  reactcss@1.2.3(react@18.3.1):
    reactcss: private
  read-cache@1.0.0:
    read-cache: private
  read-pkg-up@7.0.1:
    read-pkg-up: private
  read-pkg@3.0.0:
    read-pkg: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  redent@3.0.0:
    redent: private
  redux@4.2.1:
    redux: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regexp.prototype.flags@1.5.0:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  remark-parse@9.0.0:
    remark-parse: private
  remark-stringify@9.0.1:
    remark-stringify: private
  remark@13.0.0:
    remark: private
  remove-accents@0.5.0:
    remove-accents: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pathname@3.0.0:
    resolve-pathname: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup-plugin-auto-external@2.0.0(rollup@2.79.2):
    rollup-plugin-auto-external: private
  rollup-plugin-babel@4.4.0(@babel/core@7.28.3)(rollup@2.79.2):
    rollup-plugin-babel: private
  rollup-plugin-license@2.9.1(rollup@2.79.2):
    rollup-plugin-license: private
  rollup-plugin-postcss@4.0.2(postcss@8.5.6)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)):
    rollup-plugin-postcss: private
  rollup-plugin-scss@3.0.0:
    rollup-plugin-scss: private
  rollup-plugin-terser@7.0.2(rollup@2.79.2):
    rollup-plugin-terser: private
  rollup-pluginutils@2.8.2:
    rollup-pluginutils: private
  rollup@2.79.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safe-identifier@0.4.2:
    safe-identifier: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-resolve@1.0.0:
    safe-resolve: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass-loader@12.6.0(sass@1.92.0)(webpack@5.101.3(@swc/core@1.13.5)):
    sass-loader: private
  sass@1.92.0:
    sass: private
  saxes@5.0.1:
    saxes: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@7.7.2:
    semver: private
  serialize-javascript@4.0.0:
    serialize-javascript: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  setimmediate@1.0.5:
    setimmediate: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@3.1.1:
    simple-get: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@4.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  smooth-signature@1.1.0:
    smooth-signature: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.7:
    socks: private
  sortablejs@1.15.0:
    sortablejs: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-resolve@0.6.0:
    source-map-resolve: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  spawn-command@0.0.2:
    spawn-command: private
  spdx-compare@1.0.0:
    spdx-compare: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-expression-validate@2.0.0:
    spdx-expression-validate: private
  spdx-license-ids@3.0.22:
    spdx-license-ids: private
  spdx-ranges@2.1.1:
    spdx-ranges: private
  spdx-satisfies@5.0.1:
    spdx-satisfies: private
  specificity@0.4.1:
    specificity: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssf@0.11.2:
    ssf: private
  stable@0.1.8:
    stable: private
  stack-utils@2.0.6:
    stack-utils: private
  state-local@1.0.7:
    state-local: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamx@2.22.1:
    streamx: private
  string-convert@0.2.1:
    string-convert: private
  string-hash@1.1.3:
    string-hash: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-inject@0.3.0:
    style-inject: private
  style-loader@3.3.4(webpack@5.101.3(@swc/core@1.13.5)):
    style-loader: private
  style-search@0.1.0:
    style-search: private
  styled-components@6.1.19(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    styled-components: private
  stylehacks@5.1.1(postcss@8.5.6):
    stylehacks: private
  stylelint@13.13.1:
    stylelint: private
  stylis@4.3.2:
    stylis: private
  sugarss@2.0.0:
    sugarss: private
  supports-color@8.1.1:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-parser@2.0.4:
    svg-parser: private
  svg-tags@1.0.0:
    svg-tags: private
  svgo@2.8.0:
    svgo: private
  symbol-tree@3.2.4:
    symbol-tree: private
  table@6.9.0:
    table: private
  tapable@2.2.3:
    tapable: private
  tar-fs@3.1.0:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@6.2.1:
    tar: private
  terser-webpack-plugin@5.3.14(@swc/core@1.13.5)(webpack@5.101.3(@swc/core@1.13.5)):
    terser-webpack-plugin: private
  terser@5.44.0:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  text-decoder@1.2.3:
    text-decoder: private
  text-table@0.2.0:
    text-table: private
  thenby@1.3.4:
    thenby: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  through@2.3.8:
    through: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinycolor2@1.6.0:
    tinycolor2: private
  tmp@0.2.5:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@3.0.0:
    tr46: private
  traverse@0.3.9:
    traverse: private
  tree-kill@1.2.2:
    tree-kill: private
  trim-newlines@3.0.1:
    trim-newlines: private
  trough@1.0.5:
    trough: private
  ts-api-utils@1.4.3(typescript@5.9.2):
    ts-api-utils: private
  ts-jest@29.4.1(@babel/core@7.28.3)(@jest/transform@29.7.0)(@jest/types@30.0.5)(babel-jest@29.7.0(@babel/core@7.28.3))(jest-util@29.7.0)(jest@29.7.0(@types/node@14.18.63)(ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5)))(typescript@4.9.5):
    ts-jest: private
  ts-json-schema-generator@2.4.0:
    ts-json-schema-generator: private
  ts-loader@9.5.4(typescript@4.9.5)(webpack@5.101.3(@swc/core@1.13.5)):
    ts-loader: private
  ts-node@10.9.2(@swc/core@1.13.5)(@types/node@14.18.63)(typescript@4.9.5):
    ts-node: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typescript@5.9.2:
    typescript: private
  ua-parser-js@1.0.41:
    ua-parser-js: private
  uc.micro@1.0.6:
    uc.micro: private
  uglify-js@3.19.3:
    uglify-js: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  uncontrollable@7.2.1(react@18.3.1):
    uncontrollable: private
  underscore@1.13.7:
    underscore: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unified@9.2.2:
    unified: private
  unist-util-find-all-after@3.0.2:
    unist-util-find-all-after: private
  unist-util-is@4.1.0:
    unist-util-is: private
  unist-util-stringify-position@2.0.3:
    unist-util-stringify-position: private
  universalify@2.0.1:
    universalify: private
  unzipper@0.10.14:
    unzipper: private
  update-browserslist-db@1.1.3(browserslist@4.25.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  urlpattern-polyfill@10.0.0:
    urlpattern-polyfill: private
  use-composed-ref@1.4.0(@types/react@18.3.24)(react@18.3.1):
    use-composed-ref: private
  use-isomorphic-layout-effect@1.2.1(@types/react@18.3.24)(react@18.3.1):
    use-isomorphic-layout-effect: private
  use-latest@1.3.0(@types/react@18.3.24)(react@18.3.1):
    use-latest: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-compile-cache@2.4.0:
    v8-compile-cache: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  value-equal@1.0.1:
    value-equal: private
  vfile-message@2.0.4:
    vfile-message: private
  vfile@4.2.1:
    vfile: private
  video-react@0.15.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    video-react: private
  vite@5.4.19(sass@1.92.0)(sugarss@2.0.0)(terser@5.44.0):
    vite: private
  w3c-xmlserializer@4.0.0:
    w3c-xmlserializer: private
  walker@1.0.8:
    walker: private
  warning@4.0.3:
    warning: private
  watchpack@2.4.4:
    watchpack: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.101.3(@swc/core@1.13.5):
    webpack: private
  webworkify-webpack@https://codeload.github.com/xqq/webworkify-webpack/tar.gz/24d1e719b4a6cac37a518b2bb10fe124527ef4ef:
    webworkify-webpack: private
  whatwg-encoding@2.0.0:
    whatwg-encoding: private
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: private
  whatwg-url@11.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wide-align@1.1.5:
    wide-align: private
  wmf@1.0.2:
    wmf: private
  word-wrap@1.2.5:
    word-wrap: private
  word@0.3.0:
    word: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  xlsx@0.18.5:
    xlsx: private
  xml-formatter@3.6.6:
    xml-formatter: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xml-parser-xo@4.1.4:
    xml-parser-xo: private
  xmlchars@2.2.0:
    xmlchars: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@4.1.1:
    zip-stream: private
  zod@3.23.8:
    zod: private
  zrender@5.6.0:
    zrender: private
  zwitch@1.0.5:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.3
pendingBuilds: []
prunedAt: Thu, 04 Sep 2025 08:47:11 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.50.0'
  - '@rollup/rollup-android-arm64@4.50.0'
  - '@rollup/rollup-darwin-x64@4.50.0'
  - '@rollup/rollup-freebsd-arm64@4.50.0'
  - '@rollup/rollup-freebsd-x64@4.50.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.50.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.50.0'
  - '@rollup/rollup-linux-arm64-gnu@4.50.0'
  - '@rollup/rollup-linux-arm64-musl@4.50.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.50.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.50.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.50.0'
  - '@rollup/rollup-linux-riscv64-musl@4.50.0'
  - '@rollup/rollup-linux-s390x-gnu@4.50.0'
  - '@rollup/rollup-linux-x64-gnu@4.50.0'
  - '@rollup/rollup-linux-x64-musl@4.50.0'
  - '@rollup/rollup-openharmony-arm64@4.50.0'
  - '@rollup/rollup-win32-arm64-msvc@4.50.0'
  - '@rollup/rollup-win32-ia32-msvc@4.50.0'
  - '@rollup/rollup-win32-x64-msvc@4.50.0'
  - '@swc/core-darwin-x64@1.13.5'
  - '@swc/core-linux-arm-gnueabihf@1.13.5'
  - '@swc/core-linux-arm64-gnu@1.13.5'
  - '@swc/core-linux-arm64-musl@1.13.5'
  - '@swc/core-linux-x64-gnu@1.13.5'
  - '@swc/core-linux-x64-musl@1.13.5'
  - '@swc/core-win32-arm64-msvc@1.13.5'
  - '@swc/core-win32-ia32-msvc@1.13.5'
  - '@swc/core-win32-x64-msvc@1.13.5'
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
