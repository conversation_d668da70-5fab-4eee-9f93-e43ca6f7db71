hoistPattern:
  - '*'
hoistedDependencies:
  packages/app:
    elcube-frontend-debugger: private
  packages/ui/amis:
    amis: private
  packages/ui/amis-core:
    amis-core: private
  packages/ui/amis-formula:
    amis-formula: private
  packages/ui/amis-ui:
    amis-ui: private
  packages/ui/office-viewer:
    office-viewer: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.3
pendingBuilds: []
prunedAt: Thu, 04 Sep 2025 08:47:11 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.50.0'
  - '@rollup/rollup-android-arm64@4.50.0'
  - '@rollup/rollup-darwin-x64@4.50.0'
  - '@rollup/rollup-freebsd-arm64@4.50.0'
  - '@rollup/rollup-freebsd-x64@4.50.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.50.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.50.0'
  - '@rollup/rollup-linux-arm64-gnu@4.50.0'
  - '@rollup/rollup-linux-arm64-musl@4.50.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.50.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.50.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.50.0'
  - '@rollup/rollup-linux-riscv64-musl@4.50.0'
  - '@rollup/rollup-linux-s390x-gnu@4.50.0'
  - '@rollup/rollup-linux-x64-gnu@4.50.0'
  - '@rollup/rollup-linux-x64-musl@4.50.0'
  - '@rollup/rollup-openharmony-arm64@4.50.0'
  - '@rollup/rollup-win32-arm64-msvc@4.50.0'
  - '@rollup/rollup-win32-ia32-msvc@4.50.0'
  - '@rollup/rollup-win32-x64-msvc@4.50.0'
  - '@swc/core-darwin-x64@1.13.5'
  - '@swc/core-linux-arm-gnueabihf@1.13.5'
  - '@swc/core-linux-arm64-gnu@1.13.5'
  - '@swc/core-linux-arm64-musl@1.13.5'
  - '@swc/core-linux-x64-gnu@1.13.5'
  - '@swc/core-linux-x64-musl@1.13.5'
  - '@swc/core-win32-arm64-msvc@1.13.5'
  - '@swc/core-win32-ia32-msvc@1.13.5'
  - '@swc/core-win32-x64-msvc@1.13.5'
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
