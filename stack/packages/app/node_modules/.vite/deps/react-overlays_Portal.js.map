{"version": 3, "sources": ["../../../../../node_modules/.pnpm/react-overlays@5.1.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-overlays/esm/Portal.js", "../../../../../node_modules/.pnpm/react-overlays@5.1.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-overlays/esm/useWaitForDOMRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport ReactDOM from 'react-dom';\nimport React from 'react';\nimport useWaitForDOMRef from './useWaitForDOMRef';\nvar propTypes = {\n  /**\n   * A DOM element, Ref to an element, or function that returns either. The `container` will have the Portal children\n   * appended to it.\n   */\n  container: PropTypes.any,\n  onRendered: PropTypes.func\n};\n\n/**\n * @public\n */\nvar Portal = function Portal(_ref) {\n  var container = _ref.container,\n      children = _ref.children,\n      onRendered = _ref.onRendered;\n  var resolvedContainer = useWaitForDOMRef(container, onRendered);\n  return resolvedContainer ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/ReactDOM.createPortal(children, resolvedContainer)) : null;\n};\n\nPortal.displayName = 'Portal';\nPortal.propTypes = propTypes;\nexport default Portal;", "import ownerDocument from 'dom-helpers/ownerDocument';\nimport { useState, useEffect } from 'react';\nexport var resolveContainerRef = function resolveContainerRef(ref) {\n  var _ref;\n\n  if (typeof document === 'undefined') return null;\n  if (ref == null) return ownerDocument().body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if ((_ref = ref) != null && _ref.nodeType) return ref || null;\n  return null;\n};\nexport default function useWaitForDOMRef(ref, onResolved) {\n  var _useState = useState(function () {\n    return resolveContainerRef(ref);\n  }),\n      resolvedRef = _useState[0],\n      setRef = _useState[1];\n\n  if (!resolvedRef) {\n    var earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n\n  useEffect(function () {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  useEffect(function () {\n    var nextRef = resolveContainerRef(ref);\n\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,wBAAsB;AACtB,uBAAqB;AACrB,IAAAA,gBAAkB;;;ACDlB,mBAAoC;AAC7B,IAAI,sBAAsB,SAASC,qBAAoB,KAAK;AACjE,MAAI;AAEJ,MAAI,OAAO,aAAa,YAAa,QAAO;AAC5C,MAAI,OAAO,KAAM,QAAO,cAAc,EAAE;AACxC,MAAI,OAAO,QAAQ,WAAY,OAAM,IAAI;AACzC,MAAI,OAAO,aAAa,IAAK,OAAM,IAAI;AACvC,OAAK,OAAO,QAAQ,QAAQ,KAAK,SAAU,QAAO,OAAO;AACzD,SAAO;AACT;AACe,SAAR,iBAAkC,KAAK,YAAY;AACxD,MAAI,gBAAY,uBAAS,WAAY;AACnC,WAAO,oBAAoB,GAAG;AAAA,EAChC,CAAC,GACG,cAAc,UAAU,CAAC,GACzB,SAAS,UAAU,CAAC;AAExB,MAAI,CAAC,aAAa;AAChB,QAAI,WAAW,oBAAoB,GAAG;AACtC,QAAI,SAAU,QAAO,QAAQ;AAAA,EAC/B;AAEA,8BAAU,WAAY;AACpB,QAAI,cAAc,aAAa;AAC7B,iBAAW,WAAW;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,YAAY,WAAW,CAAC;AAC5B,8BAAU,WAAY;AACpB,QAAI,UAAU,oBAAoB,GAAG;AAErC,QAAI,YAAY,aAAa;AAC3B,aAAO,OAAO;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,KAAK,WAAW,CAAC;AACrB,SAAO;AACT;;;ADjCA,IAAI,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,WAAW,kBAAAC,QAAU;AAAA,EACrB,YAAY,kBAAAA,QAAU;AACxB;AAKA,IAAI,SAAS,SAASC,QAAO,MAAM;AACjC,MAAI,YAAY,KAAK,WACjB,WAAW,KAAK,UAChB,aAAa,KAAK;AACtB,MAAI,oBAAoB,iBAAiB,WAAW,UAAU;AAC9D,SAAO,oBAAiC,cAAAC,QAAM,cAAc,cAAAA,QAAM,UAAU,MAAmB,iBAAAC,QAAS,aAAa,UAAU,iBAAiB,CAAC,IAAI;AACvJ;AAEA,OAAO,cAAc;AACrB,OAAO,YAAY;AACnB,IAAO,iBAAQ;", "names": ["import_react", "resolveContainerRef", "PropTypes", "Portal", "React", "ReactDOM"]}