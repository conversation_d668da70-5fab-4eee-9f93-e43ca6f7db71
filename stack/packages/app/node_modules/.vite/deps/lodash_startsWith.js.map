{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/startsWith.js"], "sourcesContent": ["var baseClamp = require('./_baseClamp'),\n    baseToString = require('./_baseToString'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/**\n * Checks if `string` starts with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=0] The position to search from.\n * @returns {boolean} Returns `true` if `string` starts with `target`,\n *  else `false`.\n * @example\n *\n * _.startsWith('abc', 'a');\n * // => true\n *\n * _.startsWith('abc', 'b');\n * // => false\n *\n * _.startsWith('abc', 'b', 1);\n * // => true\n */\nfunction startsWith(string, target, position) {\n  string = toString(string);\n  position = position == null\n    ? 0\n    : baseClamp(toInteger(position), 0, string.length);\n\n  target = baseToString(target);\n  return string.slice(position, position + target.length) == target;\n}\n\nmodule.exports = startsWith;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,eAAe;AADnB,QAEI,YAAY;AAFhB,QAGI,WAAW;AAyBf,aAAS,WAAW,QAAQ,QAAQ,UAAU;AAC5C,eAAS,SAAS,MAAM;AACxB,iBAAW,YAAY,OACnB,IACA,UAAU,UAAU,QAAQ,GAAG,GAAG,OAAO,MAAM;AAEnD,eAAS,aAAa,MAAM;AAC5B,aAAO,OAAO,MAAM,UAAU,WAAW,OAAO,MAAM,KAAK;AAAA,IAC7D;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}