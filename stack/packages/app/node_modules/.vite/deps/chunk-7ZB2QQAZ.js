import {
  require_flatten
} from "./chunk-3LRRGZUN.js";
import {
  require_overRest,
  require_setToString
} from "./chunk-JLXDD2GJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_flatRest.js
var require_flatRest = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_flatRest.js"(exports, module) {
    var flatten = require_flatten();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function flatRest(func) {
      return setToString(overRest(func, void 0, flatten), func + "");
    }
    module.exports = flatRest;
  }
});

export {
  require_flatRest
};
//# sourceMappingURL=chunk-7ZB2QQAZ.js.map
