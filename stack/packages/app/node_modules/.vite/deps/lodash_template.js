import {
  require_escape
} from "./chunk-ZOD72EII.js";
import {
  require_baseValues
} from "./chunk-DGY6WLAD.js";
import "./chunk-7667RCYF.js";
import {
  require_createAssigner
} from "./chunk-G43AXPMY.js";
import {
  require_baseRest
} from "./chunk-VZDPLNRX.js";
import {
  require_isIterateeCall
} from "./chunk-OTX6QH7U.js";
import {
  require_isPlainObject
} from "./chunk-O7BYROIV.js";
import {
  require_copyObject,
  require_keysIn
} from "./chunk-KVXYJQ4O.js";
import "./chunk-6RHF6BK4.js";
import {
  require_apply
} from "./chunk-JLXDD2GJ.js";
import "./chunk-Q72BQYBD.js";
import "./chunk-5EO5MQJI.js";
import "./chunk-SL5LTBZ2.js";
import {
  require_keys
} from "./chunk-TAUPVCQB.js";
import "./chunk-AJQMZXLQ.js";
import "./chunk-BTFDFZI2.js";
import "./chunk-4MTN4377.js";
import "./chunk-TOPLOUEN.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-NBE5XRJS.js";
import "./chunk-4GUL7IQK.js";
import {
  require_toString
} from "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-MUOIXU4T.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-4HM7XD2G.js";
import "./chunk-3CCHXQR4.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-CXIFQW4L.js";
import "./chunk-JQ6QUUYU.js";
import {
  require_eq
} from "./chunk-3MWHQ5U6.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import {
  require_isObjectLike
} from "./chunk-WLFKI2V4.js";
import {
  require_baseGetTag
} from "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/assignInWith.js
var require_assignInWith = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/assignInWith.js"(exports, module) {
    var copyObject = require_copyObject();
    var createAssigner = require_createAssigner();
    var keysIn = require_keysIn();
    var assignInWith = createAssigner(function(object, source, srcIndex, customizer) {
      copyObject(source, keysIn(source), object, customizer);
    });
    module.exports = assignInWith;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isError.js
var require_isError = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isError.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var isPlainObject = require_isPlainObject();
    var domExcTag = "[object DOMException]";
    var errorTag = "[object Error]";
    function isError(value) {
      if (!isObjectLike(value)) {
        return false;
      }
      var tag = baseGetTag(value);
      return tag == errorTag || tag == domExcTag || typeof value.message == "string" && typeof value.name == "string" && !isPlainObject(value);
    }
    module.exports = isError;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/attempt.js
var require_attempt = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/attempt.js"(exports, module) {
    var apply = require_apply();
    var baseRest = require_baseRest();
    var isError = require_isError();
    var attempt = baseRest(function(func, args) {
      try {
        return apply(func, void 0, args);
      } catch (e) {
        return isError(e) ? e : new Error(e);
      }
    });
    module.exports = attempt;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_customDefaultsAssignIn.js
var require_customDefaultsAssignIn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_customDefaultsAssignIn.js"(exports, module) {
    var eq = require_eq();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function customDefaultsAssignIn(objValue, srcValue, key, object) {
      if (objValue === void 0 || eq(objValue, objectProto[key]) && !hasOwnProperty.call(object, key)) {
        return srcValue;
      }
      return objValue;
    }
    module.exports = customDefaultsAssignIn;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_escapeStringChar.js
var require_escapeStringChar = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_escapeStringChar.js"(exports, module) {
    var stringEscapes = {
      "\\": "\\",
      "'": "'",
      "\n": "n",
      "\r": "r",
      "\u2028": "u2028",
      "\u2029": "u2029"
    };
    function escapeStringChar(chr) {
      return "\\" + stringEscapes[chr];
    }
    module.exports = escapeStringChar;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_reInterpolate.js
var require_reInterpolate = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_reInterpolate.js"(exports, module) {
    var reInterpolate = /<%=([\s\S]+?)%>/g;
    module.exports = reInterpolate;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_reEscape.js
var require_reEscape = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_reEscape.js"(exports, module) {
    var reEscape = /<%-([\s\S]+?)%>/g;
    module.exports = reEscape;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_reEvaluate.js
var require_reEvaluate = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_reEvaluate.js"(exports, module) {
    var reEvaluate = /<%([\s\S]+?)%>/g;
    module.exports = reEvaluate;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/templateSettings.js
var require_templateSettings = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/templateSettings.js"(exports, module) {
    var escape = require_escape();
    var reEscape = require_reEscape();
    var reEvaluate = require_reEvaluate();
    var reInterpolate = require_reInterpolate();
    var templateSettings = {
      /**
       * Used to detect `data` property values to be HTML-escaped.
       *
       * @memberOf _.templateSettings
       * @type {RegExp}
       */
      "escape": reEscape,
      /**
       * Used to detect code to be evaluated.
       *
       * @memberOf _.templateSettings
       * @type {RegExp}
       */
      "evaluate": reEvaluate,
      /**
       * Used to detect `data` property values to inject.
       *
       * @memberOf _.templateSettings
       * @type {RegExp}
       */
      "interpolate": reInterpolate,
      /**
       * Used to reference the data object in the template text.
       *
       * @memberOf _.templateSettings
       * @type {string}
       */
      "variable": "",
      /**
       * Used to import variables into the compiled template.
       *
       * @memberOf _.templateSettings
       * @type {Object}
       */
      "imports": {
        /**
         * A reference to the `lodash` function.
         *
         * @memberOf _.templateSettings.imports
         * @type {Function}
         */
        "_": { "escape": escape }
      }
    };
    module.exports = templateSettings;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/template.js
var require_template = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/template.js"(exports, module) {
    var assignInWith = require_assignInWith();
    var attempt = require_attempt();
    var baseValues = require_baseValues();
    var customDefaultsAssignIn = require_customDefaultsAssignIn();
    var escapeStringChar = require_escapeStringChar();
    var isError = require_isError();
    var isIterateeCall = require_isIterateeCall();
    var keys = require_keys();
    var reInterpolate = require_reInterpolate();
    var templateSettings = require_templateSettings();
    var toString = require_toString();
    var INVALID_TEMPL_VAR_ERROR_TEXT = "Invalid `variable` option passed into `_.template`";
    var reEmptyStringLeading = /\b__p \+= '';/g;
    var reEmptyStringMiddle = /\b(__p \+=) '' \+/g;
    var reEmptyStringTrailing = /(__e\(.*?\)|\b__t\)) \+\n'';/g;
    var reForbiddenIdentifierChars = /[()=,{}\[\]\/\s]/;
    var reEsTemplate = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;
    var reNoMatch = /($^)/;
    var reUnescapedString = /['\n\r\u2028\u2029\\]/g;
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function template(string, options, guard) {
      var settings = templateSettings.imports._.templateSettings || templateSettings;
      if (guard && isIterateeCall(string, options, guard)) {
        options = void 0;
      }
      string = toString(string);
      options = assignInWith({}, options, settings, customDefaultsAssignIn);
      var imports = assignInWith({}, options.imports, settings.imports, customDefaultsAssignIn), importsKeys = keys(imports), importsValues = baseValues(imports, importsKeys);
      var isEscaping, isEvaluating, index = 0, interpolate = options.interpolate || reNoMatch, source = "__p += '";
      var reDelimiters = RegExp(
        (options.escape || reNoMatch).source + "|" + interpolate.source + "|" + (interpolate === reInterpolate ? reEsTemplate : reNoMatch).source + "|" + (options.evaluate || reNoMatch).source + "|$",
        "g"
      );
      var sourceURL = hasOwnProperty.call(options, "sourceURL") ? "//# sourceURL=" + (options.sourceURL + "").replace(/\s/g, " ") + "\n" : "";
      string.replace(reDelimiters, function(match, escapeValue, interpolateValue, esTemplateValue, evaluateValue, offset) {
        interpolateValue || (interpolateValue = esTemplateValue);
        source += string.slice(index, offset).replace(reUnescapedString, escapeStringChar);
        if (escapeValue) {
          isEscaping = true;
          source += "' +\n__e(" + escapeValue + ") +\n'";
        }
        if (evaluateValue) {
          isEvaluating = true;
          source += "';\n" + evaluateValue + ";\n__p += '";
        }
        if (interpolateValue) {
          source += "' +\n((__t = (" + interpolateValue + ")) == null ? '' : __t) +\n'";
        }
        index = offset + match.length;
        return match;
      });
      source += "';\n";
      var variable = hasOwnProperty.call(options, "variable") && options.variable;
      if (!variable) {
        source = "with (obj) {\n" + source + "\n}\n";
      } else if (reForbiddenIdentifierChars.test(variable)) {
        throw new Error(INVALID_TEMPL_VAR_ERROR_TEXT);
      }
      source = (isEvaluating ? source.replace(reEmptyStringLeading, "") : source).replace(reEmptyStringMiddle, "$1").replace(reEmptyStringTrailing, "$1;");
      source = "function(" + (variable || "obj") + ") {\n" + (variable ? "" : "obj || (obj = {});\n") + "var __t, __p = ''" + (isEscaping ? ", __e = _.escape" : "") + (isEvaluating ? ", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n" : ";\n") + source + "return __p\n}";
      var result = attempt(function() {
        return Function(importsKeys, sourceURL + "return " + source).apply(void 0, importsValues);
      });
      result.source = source;
      if (isError(result)) {
        throw result;
      }
      return result;
    }
    module.exports = template;
  }
});
export default require_template();
//# sourceMappingURL=lodash_template.js.map
