import {
  require_baseIteratee
} from "./chunk-JYHWN6MQ.js";
import {
  require_baseFindIndex
} from "./chunk-VQMXUB7P.js";
import {
  require_toInteger
} from "./chunk-3M5FHVBU.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/findIndex.js
var require_findIndex = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/findIndex.js"(exports, module) {
    var baseFindIndex = require_baseFindIndex();
    var baseIteratee = require_baseIteratee();
    var toInteger = require_toInteger();
    var nativeMax = Math.max;
    function findIndex(array, predicate, fromIndex) {
      var length = array == null ? 0 : array.length;
      if (!length) {
        return -1;
      }
      var index = fromIndex == null ? 0 : toInteger(fromIndex);
      if (index < 0) {
        index = nativeMax(length + index, 0);
      }
      return baseFindIndex(array, baseIteratee(predicate, 3), index);
    }
    module.exports = findIndex;
  }
});

export {
  require_findIndex
};
//# sourceMappingURL=chunk-4TJKFX5K.js.map
