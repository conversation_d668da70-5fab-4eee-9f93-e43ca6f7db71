import {
  require_baseFor
} from "./chunk-DJAUCU6Q.js";
import {
  require_keys
} from "./chunk-TAUPVCQB.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseForOwn.js
var require_baseForOwn = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseForOwn.js"(exports, module) {
    var baseFor = require_baseFor();
    var keys = require_keys();
    function baseForOwn(object, iteratee) {
      return object && baseFor(object, iteratee, keys);
    }
    module.exports = baseForOwn;
  }
});

export {
  require_baseForOwn
};
//# sourceMappingURL=chunk-OC6REVJJ.js.map
