import {
  require_baseEach
} from "./chunk-7LSMNYBW.js";
import {
  require_isArrayLike
} from "./chunk-4GUL7IQK.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseMap.js
var require_baseMap = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseMap.js"(exports, module) {
    var baseEach = require_baseEach();
    var isArrayLike = require_isArrayLike();
    function baseMap(collection, iteratee) {
      var index = -1, result = isArrayLike(collection) ? Array(collection.length) : [];
      baseEach(collection, function(value, key, collection2) {
        result[++index] = iteratee(value, key, collection2);
      });
      return result;
    }
    module.exports = baseMap;
  }
});

export {
  require_baseMap
};
//# sourceMappingURL=chunk-PAMSZVOR.js.map
