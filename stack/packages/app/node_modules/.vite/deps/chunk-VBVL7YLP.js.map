{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dom-helpers@5.2.1/node_modules/dom-helpers/esm/ownerDocument.js"], "sourcesContent": ["/**\n * Returns the owner document of a given element.\n * \n * @param node the element\n */\nexport default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}"], "mappings": ";AAKe,SAAR,cAA+B,MAAM;AAC1C,SAAO,QAAQ,KAAK,iBAAiB;AACvC;", "names": []}