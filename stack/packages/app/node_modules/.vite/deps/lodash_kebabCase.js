import {
  require_createCompounder
} from "./chunk-NX4AHR4E.js";
import "./chunk-7667RCYF.js";
import "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/kebabCase.js
var require_kebabCase = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/kebabCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var kebabCase = createCompounder(function(result, word, index) {
      return result + (index ? "-" : "") + word.toLowerCase();
    });
    module.exports = kebabCase;
  }
});
export default require_kebabCase();
//# sourceMappingURL=lodash_kebabCase.js.map
