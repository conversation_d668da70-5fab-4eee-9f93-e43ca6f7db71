{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseValues.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap');\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function(key) {\n    return object[key];\n  });\n}\n\nmodule.exports = baseValues;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAYf,aAAS,WAAW,QAAQ,OAAO;AACjC,aAAO,SAAS,OAAO,SAAS,KAAK;AACnC,eAAO,OAAO,GAAG;AAAA,MACnB,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}