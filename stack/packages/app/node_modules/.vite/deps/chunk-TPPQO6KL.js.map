{"version": 3, "sources": ["../../../../../node_modules/.pnpm/mobx@4.15.7/node_modules/mobx/lib/mobx.module.js"], "sourcesContent": ["/** MobX - (c) <PERSON> 2015 - 2020 - MIT Licensed */\nvar OBFUSCATED_ERROR = \"An invariant failed, however the error is obfuscated because this is an production build.\";\nvar EMPTY_ARRAY = [];\nObject.freeze(EMPTY_ARRAY);\nvar EMPTY_OBJECT = {};\nObject.freeze(EMPTY_OBJECT);\nvar mockGlobal = {};\nfunction getGlobal() {\n    if (typeof window !== \"undefined\") {\n        return window;\n    }\n    if (typeof global !== \"undefined\") {\n        return global;\n    }\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    return mockGlobal;\n}\nfunction getNextId() {\n    return ++globalState.mobxGuid;\n}\nfunction fail(message) {\n    invariant(false, message);\n    throw \"X\"; // unreachable\n}\nfunction invariant(check, message) {\n    if (!check)\n        throw new Error(\"[mobx] \" + (message || OBFUSCATED_ERROR));\n}\n/**\n * Prints a deprecation message, but only one time.\n * Returns false if the deprecated message was already printed before\n */\nvar deprecatedMessages = [];\nfunction deprecated(msg, thing) {\n    if (process.env.NODE_ENV === \"production\")\n        return false;\n    if (thing) {\n        return deprecated(\"'\" + msg + \"', use '\" + thing + \"' instead.\");\n    }\n    if (deprecatedMessages.indexOf(msg) !== -1)\n        return false;\n    deprecatedMessages.push(msg);\n    console.error(\"[mobx] Deprecated: \" + msg);\n    return true;\n}\n/**\n * Makes sure that the provided function is invoked at most once.\n */\nfunction once(func) {\n    var invoked = false;\n    return function () {\n        if (invoked)\n            return;\n        invoked = true;\n        return func.apply(this, arguments);\n    };\n}\nvar noop = function () { };\nfunction unique(list) {\n    var res = [];\n    list.forEach(function (item) {\n        if (res.indexOf(item) === -1)\n            res.push(item);\n    });\n    return res;\n}\nfunction isObject(value) {\n    return value !== null && typeof value === \"object\";\n}\nfunction isPlainObject(value) {\n    if (value === null || typeof value !== \"object\")\n        return false;\n    var proto = Object.getPrototypeOf(value);\n    return proto === Object.prototype || proto === null;\n}\nfunction convertToMap(dataStructure) {\n    if (isES6Map(dataStructure) || isObservableMap(dataStructure)) {\n        return dataStructure;\n    }\n    else if (Array.isArray(dataStructure)) {\n        return new Map(dataStructure);\n    }\n    else if (isPlainObject(dataStructure)) {\n        var map = new Map();\n        for (var key in dataStructure) {\n            map.set(key, dataStructure[key]);\n        }\n        return map;\n    }\n    else {\n        return fail(\"Cannot convert to map from '\" + dataStructure + \"'\");\n    }\n}\nfunction makeNonEnumerable(object, propNames) {\n    for (var i = 0; i < propNames.length; i++) {\n        addHiddenProp(object, propNames[i], object[propNames[i]]);\n    }\n}\nfunction addHiddenProp(object, propName, value) {\n    Object.defineProperty(object, propName, {\n        enumerable: false,\n        writable: true,\n        configurable: true,\n        value: value\n    });\n}\nfunction addHiddenFinalProp(object, propName, value) {\n    Object.defineProperty(object, propName, {\n        enumerable: false,\n        writable: false,\n        configurable: true,\n        value: value\n    });\n}\nfunction isPropertyConfigurable(object, prop) {\n    var descriptor = Object.getOwnPropertyDescriptor(object, prop);\n    return !descriptor || (descriptor.configurable !== false && descriptor.writable !== false);\n}\nfunction assertPropertyConfigurable(object, prop) {\n    if (process.env.NODE_ENV !== \"production\" && !isPropertyConfigurable(object, prop))\n        fail(\"Cannot make property '\" + prop + \"' observable, it is not configurable and writable in the target object\");\n}\nfunction createInstanceofPredicate(name, clazz) {\n    var propName = \"isMobX\" + name;\n    clazz.prototype[propName] = true;\n    return function (x) {\n        return isObject(x) && x[propName] === true;\n    };\n}\nfunction areBothNaN(a, b) {\n    return typeof a === \"number\" && typeof b === \"number\" && isNaN(a) && isNaN(b);\n}\n/**\n * Returns whether the argument is an array, disregarding observability.\n */\nfunction isArrayLike(x) {\n    return Array.isArray(x) || isObservableArray(x);\n}\nfunction isES6Map(thing) {\n    if (getGlobal().Map !== undefined && thing instanceof getGlobal().Map)\n        return true;\n    return false;\n}\nfunction isES6Set(thing) {\n    return thing instanceof Set;\n}\n// use Array.from in Mobx 5\nfunction iteratorToArray(it) {\n    var res = [];\n    while (true) {\n        var r = it.next();\n        if (r.done)\n            break;\n        res.push(r.value);\n    }\n    return res;\n}\nfunction primitiveSymbol() {\n    // es-disable-next-line\n    return (typeof Symbol === \"function\" && Symbol.toPrimitive) || \"@@toPrimitive\";\n}\nfunction toPrimitive(value) {\n    return value === null ? null : typeof value === \"object\" ? \"\" + value : value;\n}\n// Use \"for of\" in V5\nfunction forOf(iter, callback) {\n    var next = iter.next();\n    while (!next.done) {\n        callback(next.value);\n        next = iter.next();\n    }\n}\n\nfunction iteratorSymbol() {\n    return (typeof Symbol === \"function\" && Symbol.iterator) || \"@@iterator\";\n}\nfunction declareIterator(prototType, iteratorFactory) {\n    addHiddenFinalProp(prototType, iteratorSymbol(), iteratorFactory);\n}\nfunction makeIterable(iterator) {\n    iterator[iteratorSymbol()] = getSelf;\n    return iterator;\n}\nfunction toStringTagSymbol() {\n    return (typeof Symbol === \"function\" && Symbol.toStringTag) || \"@@toStringTag\";\n}\nfunction getSelf() {\n    return this;\n}\n\n/**\n * Anything that can be used to _store_ state is an Atom in mobx. Atoms have two important jobs\n *\n * 1) detect when they are being _used_ and report this (using reportObserved). This allows mobx to make the connection between running functions and the data they used\n * 2) they should notify mobx whenever they have _changed_. This way mobx can re-run any functions (derivations) that are using this atom.\n */\nvar Atom = /** @class */ (function () {\n    /**\n     * Create a new atom. For debugging purposes it is recommended to give it a name.\n     * The onBecomeObserved and onBecomeUnobserved callbacks can be used for resource management.\n     */\n    function Atom(name) {\n        if (name === void 0) { name = \"Atom@\" + getNextId(); }\n        this.name = name;\n        this.isPendingUnobservation = false; // for effective unobserving. BaseAtom has true, for extra optimization, so its onBecomeUnobserved never gets called, because it's not needed\n        this.isBeingObserved = false;\n        this.observers = [];\n        this.observersIndexes = {};\n        this.diffValue = 0;\n        this.lastAccessedBy = 0;\n        this.lowestObserverState = IDerivationState.NOT_TRACKING;\n    }\n    Atom.prototype.onBecomeUnobserved = function () {\n        // noop\n    };\n    Atom.prototype.onBecomeObserved = function () {\n        /* noop */\n    };\n    /**\n     * Invoke this method to notify mobx that your atom has been used somehow.\n     * Returns true if there is currently a reactive context.\n     */\n    Atom.prototype.reportObserved = function () {\n        return reportObserved(this);\n    };\n    /**\n     * Invoke this method _after_ this method has changed to signal mobx that all its observers should invalidate.\n     */\n    Atom.prototype.reportChanged = function () {\n        startBatch();\n        propagateChanged(this);\n        endBatch();\n    };\n    Atom.prototype.toString = function () {\n        return this.name;\n    };\n    return Atom;\n}());\nvar isAtom = createInstanceofPredicate(\"Atom\", Atom);\nfunction createAtom(name, onBecomeObservedHandler, onBecomeUnobservedHandler) {\n    if (onBecomeObservedHandler === void 0) { onBecomeObservedHandler = noop; }\n    if (onBecomeUnobservedHandler === void 0) { onBecomeUnobservedHandler = noop; }\n    var atom = new Atom(name);\n    onBecomeObserved(atom, onBecomeObservedHandler);\n    onBecomeUnobserved(atom, onBecomeUnobservedHandler);\n    return atom;\n}\n\nfunction identityComparer(a, b) {\n    return a === b;\n}\nfunction structuralComparer(a, b) {\n    return deepEqual(a, b);\n}\nfunction shallowComparer(a, b) {\n    return deepEqual(a, b, 1);\n}\nfunction defaultComparer(a, b) {\n    return areBothNaN(a, b) || identityComparer(a, b);\n}\nvar comparer = {\n    identity: identityComparer,\n    structural: structuralComparer,\n    default: defaultComparer,\n    shallow: shallowComparer\n};\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nfunction __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\n\nvar enumerableDescriptorCache = {};\nvar nonEnumerableDescriptorCache = {};\nfunction createPropertyInitializerDescriptor(prop, enumerable) {\n    var cache = enumerable ? enumerableDescriptorCache : nonEnumerableDescriptorCache;\n    return (cache[prop] ||\n        (cache[prop] = {\n            configurable: true,\n            enumerable: enumerable,\n            get: function () {\n                initializeInstance(this);\n                return this[prop];\n            },\n            set: function (value) {\n                initializeInstance(this);\n                this[prop] = value;\n            }\n        }));\n}\nfunction initializeInstance(target) {\n    if (target.__mobxDidRunLazyInitializers === true)\n        return;\n    var decorators = target.__mobxDecorators;\n    if (decorators) {\n        addHiddenProp(target, \"__mobxDidRunLazyInitializers\", true);\n        for (var key in decorators) {\n            var d = decorators[key];\n            d.propertyCreator(target, d.prop, d.descriptor, d.decoratorTarget, d.decoratorArguments);\n        }\n    }\n}\nfunction createPropDecorator(propertyInitiallyEnumerable, propertyCreator) {\n    return function decoratorFactory() {\n        var decoratorArguments;\n        var decorator = function decorate(target, prop, descriptor, applyImmediately\n        // This is a special parameter to signal the direct application of a decorator, allow extendObservable to skip the entire type decoration part,\n        // as the instance to apply the decorator to equals the target\n        ) {\n            if (applyImmediately === true) {\n                propertyCreator(target, prop, descriptor, target, decoratorArguments);\n                return null;\n            }\n            if (process.env.NODE_ENV !== \"production\" && !quacksLikeADecorator(arguments))\n                fail(\"This function is a decorator, but it wasn't invoked like a decorator\");\n            if (!Object.prototype.hasOwnProperty.call(target, \"__mobxDecorators\")) {\n                var inheritedDecorators = target.__mobxDecorators;\n                addHiddenProp(target, \"__mobxDecorators\", __assign({}, inheritedDecorators));\n            }\n            target.__mobxDecorators[prop] = {\n                prop: prop,\n                propertyCreator: propertyCreator,\n                descriptor: descriptor,\n                decoratorTarget: target,\n                decoratorArguments: decoratorArguments\n            };\n            return createPropertyInitializerDescriptor(prop, propertyInitiallyEnumerable);\n        };\n        if (quacksLikeADecorator(arguments)) {\n            // @decorator\n            decoratorArguments = EMPTY_ARRAY;\n            return decorator.apply(null, arguments);\n        }\n        else {\n            // @decorator(args)\n            decoratorArguments = Array.prototype.slice.call(arguments);\n            return decorator;\n        }\n    };\n}\nfunction quacksLikeADecorator(args) {\n    return (((args.length === 2 || args.length === 3) && typeof args[1] === \"string\") ||\n        (args.length === 4 && args[3] === true));\n}\n\nfunction deepEnhancer(v, _, name) {\n    // it is an observable already, done\n    if (isObservable(v))\n        return v;\n    // something that can be converted and mutated?\n    if (Array.isArray(v))\n        return observable.array(v, { name: name });\n    if (isPlainObject(v))\n        return observable.object(v, undefined, { name: name });\n    if (isES6Map(v))\n        return observable.map(v, { name: name });\n    if (isES6Set(v))\n        return observable.set(v, { name: name });\n    return v;\n}\nfunction shallowEnhancer(v, _, name) {\n    if (v === undefined || v === null)\n        return v;\n    if (isObservableObject(v) || isObservableArray(v) || isObservableMap(v) || isObservableSet(v))\n        return v;\n    if (Array.isArray(v))\n        return observable.array(v, { name: name, deep: false });\n    if (isPlainObject(v))\n        return observable.object(v, undefined, { name: name, deep: false });\n    if (isES6Map(v))\n        return observable.map(v, { name: name, deep: false });\n    if (isES6Set(v))\n        return observable.set(v, { name: name, deep: false });\n    return fail(process.env.NODE_ENV !== \"production\" &&\n        \"The shallow modifier / decorator can only used in combination with arrays, objects, maps and sets\");\n}\nfunction referenceEnhancer(newValue) {\n    // never turn into an observable\n    return newValue;\n}\nfunction refStructEnhancer(v, oldValue, name) {\n    if (process.env.NODE_ENV !== \"production\" && isObservable(v))\n        throw \"observable.struct should not be used with observable values\";\n    if (deepEqual(v, oldValue))\n        return oldValue;\n    return v;\n}\n\nfunction createDecoratorForEnhancer(enhancer) {\n    invariant(enhancer);\n    var decorator = createPropDecorator(true, function (target, propertyName, descriptor, _decoratorTarget, decoratorArgs) {\n        if (process.env.NODE_ENV !== \"production\") {\n            invariant(!descriptor || !descriptor.get, \"@observable cannot be used on getter (property \\\"\" + propertyName + \"\\\"), use @computed instead.\");\n        }\n        var initialValue = descriptor\n            ? descriptor.initializer\n                ? descriptor.initializer.call(target)\n                : descriptor.value\n            : undefined;\n        defineObservableProperty(target, propertyName, initialValue, enhancer);\n    });\n    var res = \n    // Extra process checks, as this happens during module initialization\n    typeof process !== \"undefined\" && process.env && process.env.NODE_ENV !== \"production\"\n        ? function observableDecorator() {\n            // This wrapper function is just to detect illegal decorator invocations, deprecate in a next version\n            // and simply return the created prop decorator\n            if (arguments.length < 2)\n                return fail(\"Incorrect decorator invocation. @observable decorator doesn't expect any arguments\");\n            return decorator.apply(null, arguments);\n        }\n        : decorator;\n    res.enhancer = enhancer;\n    return res;\n}\n\n// Predefined bags of create observable options, to avoid allocating temporarily option objects\n// in the majority of cases\nvar defaultCreateObservableOptions = {\n    deep: true,\n    name: undefined,\n    defaultDecorator: undefined\n};\nvar shallowCreateObservableOptions = {\n    deep: false,\n    name: undefined,\n    defaultDecorator: undefined\n};\nObject.freeze(defaultCreateObservableOptions);\nObject.freeze(shallowCreateObservableOptions);\nfunction assertValidOption(key) {\n    if (!/^(deep|name|equals|defaultDecorator)$/.test(key))\n        fail(\"invalid option for (extend)observable: \" + key);\n}\nfunction asCreateObservableOptions(thing) {\n    if (thing === null || thing === undefined)\n        return defaultCreateObservableOptions;\n    if (typeof thing === \"string\")\n        return { name: thing, deep: true };\n    if (process.env.NODE_ENV !== \"production\") {\n        if (typeof thing !== \"object\")\n            return fail(\"expected options object\");\n        Object.keys(thing).forEach(assertValidOption);\n    }\n    return thing;\n}\nfunction getEnhancerFromOptions(options) {\n    return options.defaultDecorator\n        ? options.defaultDecorator.enhancer\n        : options.deep === false\n            ? referenceEnhancer\n            : deepEnhancer;\n}\nvar deepDecorator = createDecoratorForEnhancer(deepEnhancer);\nvar shallowDecorator = createDecoratorForEnhancer(shallowEnhancer);\nvar refDecorator = createDecoratorForEnhancer(referenceEnhancer);\nvar refStructDecorator = createDecoratorForEnhancer(refStructEnhancer);\n/**\n * Turns an object, array or function into a reactive structure.\n * @param v the value which should become observable.\n */\nfunction createObservable(v, arg2, arg3) {\n    // @observable someProp;\n    if (typeof arguments[1] === \"string\") {\n        return deepDecorator.apply(null, arguments);\n    }\n    // it is an observable already, done\n    if (isObservable(v))\n        return v;\n    // something that can be converted and mutated?\n    var res = isPlainObject(v)\n        ? observable.object(v, arg2, arg3)\n        : Array.isArray(v)\n            ? observable.array(v, arg2)\n            : isES6Map(v)\n                ? observable.map(v, arg2)\n                : isES6Set(v)\n                    ? observable.set(v, arg2)\n                    : v;\n    // this value could be converted to a new observable data structure, return it\n    if (res !== v)\n        return res;\n    // otherwise, just box it\n    fail(process.env.NODE_ENV !== \"production\" &&\n        \"The provided value could not be converted into an observable. If you want just create an observable reference to the object use 'observable.box(value)'\");\n}\nvar observableFactories = {\n    box: function (value, options) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"box\");\n        var o = asCreateObservableOptions(options);\n        return new ObservableValue(value, getEnhancerFromOptions(o), o.name, true, o.equals);\n    },\n    shallowBox: function (value, name) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"shallowBox\");\n        deprecated(\"observable.shallowBox\", \"observable.box(value, { deep: false })\");\n        return observable.box(value, { name: name, deep: false });\n    },\n    array: function (initialValues, options) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"array\");\n        var o = asCreateObservableOptions(options);\n        return new ObservableArray(initialValues, getEnhancerFromOptions(o), o.name);\n    },\n    shallowArray: function (initialValues, name) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"shallowArray\");\n        deprecated(\"observable.shallowArray\", \"observable.array(values, { deep: false })\");\n        return observable.array(initialValues, { name: name, deep: false });\n    },\n    map: function (initialValues, options) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"map\");\n        var o = asCreateObservableOptions(options);\n        return new ObservableMap(initialValues, getEnhancerFromOptions(o), o.name);\n    },\n    shallowMap: function (initialValues, name) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"shallowMap\");\n        deprecated(\"observable.shallowMap\", \"observable.map(values, { deep: false })\");\n        return observable.map(initialValues, { name: name, deep: false });\n    },\n    set: function (initialValues, options) {\n        if (arguments.length > 2)\n            incorrectlyUsedAsDecorator(\"set\");\n        var o = asCreateObservableOptions(options);\n        return new ObservableSet(initialValues, getEnhancerFromOptions(o), o.name);\n    },\n    object: function (props, decorators, options) {\n        if (typeof arguments[1] === \"string\")\n            incorrectlyUsedAsDecorator(\"object\");\n        var o = asCreateObservableOptions(options);\n        return extendObservable({}, props, decorators, o);\n    },\n    shallowObject: function (props, name) {\n        if (typeof arguments[1] === \"string\")\n            incorrectlyUsedAsDecorator(\"shallowObject\");\n        deprecated(\"observable.shallowObject\", \"observable.object(values, {}, { deep: false })\");\n        return observable.object(props, {}, { name: name, deep: false });\n    },\n    ref: refDecorator,\n    shallow: shallowDecorator,\n    deep: deepDecorator,\n    struct: refStructDecorator\n};\nvar observable = createObservable;\n// weird trick to keep our typings nicely with our funcs, and still extend the observable function\nObject.keys(observableFactories).forEach(function (name) { return (observable[name] = observableFactories[name]); });\nfunction incorrectlyUsedAsDecorator(methodName) {\n    fail(\n    // process.env.NODE_ENV !== \"production\" &&\n    \"Expected one or two arguments to observable.\" + methodName + \". Did you accidentally try to use observable.\" + methodName + \" as decorator?\");\n}\n\nvar computedDecorator = createPropDecorator(false, function (instance, propertyName, descriptor, decoratorTarget, decoratorArgs) {\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(descriptor && descriptor.get, \"Trying to declare a computed value for unspecified getter '\" + propertyName + \"'\");\n    }\n    var get = descriptor.get, set = descriptor.set; // initialValue is the descriptor for get / set props\n    // Optimization: faster on decorator target or instance? Assuming target\n    // Optimization: find out if declaring on instance isn't just faster. (also makes the property descriptor simpler). But, more memory usage..\n    // Forcing instance now, fixes hot reloadig issues on React Native:\n    var options = decoratorArgs[0] || {};\n    defineComputedProperty(instance, propertyName, __assign({ get: get, set: set }, options));\n});\nvar computedStructDecorator = computedDecorator({ equals: comparer.structural });\n/**\n * Decorator for class properties: @computed get value() { return expr; }.\n * For legacy purposes also invokable as ES5 observable created: `computed(() => expr)`;\n */\nvar computed = function computed(arg1, arg2, arg3) {\n    if (typeof arg2 === \"string\") {\n        // @computed\n        return computedDecorator.apply(null, arguments);\n    }\n    if (arg1 !== null && typeof arg1 === \"object\" && arguments.length === 1) {\n        // @computed({ options })\n        return computedDecorator.apply(null, arguments);\n    }\n    // computed(expr, options?)\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(typeof arg1 === \"function\", \"First argument to `computed` should be an expression.\");\n        invariant(arguments.length < 3, \"Computed takes one or two arguments if used as function\");\n    }\n    var opts = typeof arg2 === \"object\" ? arg2 : {};\n    opts.get = arg1;\n    opts.set = typeof arg2 === \"function\" ? arg2 : opts.set;\n    opts.name = opts.name || arg1.name || \"\"; /* for generated name */\n    return new ComputedValue(opts);\n};\ncomputed.struct = computedStructDecorator;\n\nvar IDerivationState;\n(function (IDerivationState) {\n    // before being run or (outside batch and not being observed)\n    // at this point derivation is not holding any data about dependency tree\n    IDerivationState[IDerivationState[\"NOT_TRACKING\"] = -1] = \"NOT_TRACKING\";\n    // no shallow dependency changed since last computation\n    // won't recalculate derivation\n    // this is what makes mobx fast\n    IDerivationState[IDerivationState[\"UP_TO_DATE\"] = 0] = \"UP_TO_DATE\";\n    // some deep dependency changed, but don't know if shallow dependency changed\n    // will require to check first if UP_TO_DATE or POSSIBLY_STALE\n    // currently only ComputedValue will propagate POSSIBLY_STALE\n    //\n    // having this state is second big optimization:\n    // don't have to recompute on every dependency change, but only when it's needed\n    IDerivationState[IDerivationState[\"POSSIBLY_STALE\"] = 1] = \"POSSIBLY_STALE\";\n    // A shallow dependency has changed since last computation and the derivation\n    // will need to recompute when it's needed next.\n    IDerivationState[IDerivationState[\"STALE\"] = 2] = \"STALE\";\n})(IDerivationState || (IDerivationState = {}));\nvar TraceMode;\n(function (TraceMode) {\n    TraceMode[TraceMode[\"NONE\"] = 0] = \"NONE\";\n    TraceMode[TraceMode[\"LOG\"] = 1] = \"LOG\";\n    TraceMode[TraceMode[\"BREAK\"] = 2] = \"BREAK\";\n})(TraceMode || (TraceMode = {}));\nvar CaughtException = /** @class */ (function () {\n    function CaughtException(cause) {\n        this.cause = cause;\n        // Empty\n    }\n    return CaughtException;\n}());\nfunction isCaughtException(e) {\n    return e instanceof CaughtException;\n}\n/**\n * Finds out whether any dependency of the derivation has actually changed.\n * If dependenciesState is 1 then it will recalculate dependencies,\n * if any dependency changed it will propagate it by changing dependenciesState to 2.\n *\n * By iterating over the dependencies in the same order that they were reported and\n * stopping on the first change, all the recalculations are only called for ComputedValues\n * that will be tracked by derivation. That is because we assume that if the first x\n * dependencies of the derivation doesn't change then the derivation should run the same way\n * up until accessing x-th dependency.\n */\nfunction shouldCompute(derivation) {\n    switch (derivation.dependenciesState) {\n        case IDerivationState.UP_TO_DATE:\n            return false;\n        case IDerivationState.NOT_TRACKING:\n        case IDerivationState.STALE:\n            return true;\n        case IDerivationState.POSSIBLY_STALE: {\n            // state propagation can occur outside of action/reactive context #2195\n            var prevAllowStateReads = allowStateReadsStart(true);\n            var prevUntracked = untrackedStart(); // no need for those computeds to be reported, they will be picked up in trackDerivedFunction.\n            var obs = derivation.observing, l = obs.length;\n            for (var i = 0; i < l; i++) {\n                var obj = obs[i];\n                if (isComputedValue(obj)) {\n                    if (globalState.disableErrorBoundaries) {\n                        obj.get();\n                    }\n                    else {\n                        try {\n                            obj.get();\n                        }\n                        catch (e) {\n                            // we are not interested in the value *or* exception at this moment, but if there is one, notify all\n                            untrackedEnd(prevUntracked);\n                            allowStateReadsEnd(prevAllowStateReads);\n                            return true;\n                        }\n                    }\n                    // if ComputedValue `obj` actually changed it will be computed and propagated to its observers.\n                    // and `derivation` is an observer of `obj`\n                    // invariantShouldCompute(derivation)\n                    if (derivation.dependenciesState === IDerivationState.STALE) {\n                        untrackedEnd(prevUntracked);\n                        allowStateReadsEnd(prevAllowStateReads);\n                        return true;\n                    }\n                }\n            }\n            changeDependenciesStateTo0(derivation);\n            untrackedEnd(prevUntracked);\n            allowStateReadsEnd(prevAllowStateReads);\n            return false;\n        }\n    }\n}\n// function invariantShouldCompute(derivation: IDerivation) {\n//     const newDepState = (derivation as any).dependenciesState\n//     if (\n//         process.env.NODE_ENV === \"production\" &&\n//         (newDepState === IDerivationState.POSSIBLY_STALE ||\n//             newDepState === IDerivationState.NOT_TRACKING)\n//     )\n//         fail(\"Illegal dependency state\")\n// }\nfunction isComputingDerivation() {\n    return globalState.trackingDerivation !== null; // filter out actions inside computations\n}\nfunction checkIfStateModificationsAreAllowed(atom) {\n    var hasObservers = atom.observers.length > 0;\n    // Should never be possible to change an observed observable from inside computed, see #798\n    if (globalState.computationDepth > 0 && hasObservers)\n        fail(process.env.NODE_ENV !== \"production\" &&\n            \"Computed values are not allowed to cause side effects by changing observables that are already being observed. Tried to modify: \" + atom.name);\n    // Should not be possible to change observed state outside strict mode, except during initialization, see #563\n    if (!globalState.allowStateChanges && (hasObservers || globalState.enforceActions === \"strict\"))\n        fail(process.env.NODE_ENV !== \"production\" &&\n            (globalState.enforceActions\n                ? \"Since strict-mode is enabled, changing observed observable values outside actions is not allowed. Please wrap the code in an `action` if this change is intended. Tried to modify: \"\n                : \"Side effects like changing state are not allowed at this point. Are you trying to modify state from, for example, the render function of a React component? Tried to modify: \") +\n                atom.name);\n}\nfunction checkIfStateReadsAreAllowed(observable) {\n    if (process.env.NODE_ENV !== \"production\" &&\n        !globalState.allowStateReads &&\n        globalState.observableRequiresReaction) {\n        console.warn(\"[mobx] Observable \" + observable.name + \" being read outside a reactive context\");\n    }\n}\n/**\n * Executes the provided function `f` and tracks which observables are being accessed.\n * The tracking information is stored on the `derivation` object and the derivation is registered\n * as observer of any of the accessed observables.\n */\nfunction trackDerivedFunction(derivation, f, context) {\n    var prevAllowStateReads = allowStateReadsStart(true);\n    // pre allocate array allocation + room for variation in deps\n    // array will be trimmed by bindDependencies\n    changeDependenciesStateTo0(derivation);\n    derivation.newObserving = new Array(derivation.observing.length + 100);\n    derivation.unboundDepsCount = 0;\n    derivation.runId = ++globalState.runId;\n    var prevTracking = globalState.trackingDerivation;\n    globalState.trackingDerivation = derivation;\n    var result;\n    if (globalState.disableErrorBoundaries === true) {\n        result = f.call(context);\n    }\n    else {\n        try {\n            result = f.call(context);\n        }\n        catch (e) {\n            result = new CaughtException(e);\n        }\n    }\n    globalState.trackingDerivation = prevTracking;\n    bindDependencies(derivation);\n    if (derivation.observing.length === 0) {\n        warnAboutDerivationWithoutDependencies(derivation);\n    }\n    allowStateReadsEnd(prevAllowStateReads);\n    return result;\n}\nfunction warnAboutDerivationWithoutDependencies(derivation) {\n    if (process.env.NODE_ENV === \"production\")\n        return;\n    if (globalState.reactionRequiresObservable || derivation.requiresObservable) {\n        console.warn(\"[mobx] Derivation \" + derivation.name + \" is created/updated without reading any observable value\");\n    }\n}\n/**\n * diffs newObserving with observing.\n * update observing to be newObserving with unique observables\n * notify observers that become observed/unobserved\n */\nfunction bindDependencies(derivation) {\n    // invariant(derivation.dependenciesState !== IDerivationState.NOT_TRACKING, \"INTERNAL ERROR bindDependencies expects derivation.dependenciesState !== -1\");\n    var prevObserving = derivation.observing;\n    var observing = (derivation.observing = derivation.newObserving);\n    var lowestNewObservingDerivationState = IDerivationState.UP_TO_DATE;\n    // Go through all new observables and check diffValue: (this list can contain duplicates):\n    //   0: first occurrence, change to 1 and keep it\n    //   1: extra occurrence, drop it\n    var i0 = 0, l = derivation.unboundDepsCount;\n    for (var i = 0; i < l; i++) {\n        var dep = observing[i];\n        if (dep.diffValue === 0) {\n            dep.diffValue = 1;\n            if (i0 !== i)\n                observing[i0] = dep;\n            i0++;\n        }\n        // Upcast is 'safe' here, because if dep is IObservable, `dependenciesState` will be undefined,\n        // not hitting the condition\n        if (dep.dependenciesState > lowestNewObservingDerivationState) {\n            lowestNewObservingDerivationState = dep.dependenciesState;\n        }\n    }\n    observing.length = i0;\n    derivation.newObserving = null; // newObserving shouldn't be needed outside tracking (statement moved down to work around FF bug, see #614)\n    // Go through all old observables and check diffValue: (it is unique after last bindDependencies)\n    //   0: it's not in new observables, unobserve it\n    //   1: it keeps being observed, don't want to notify it. change to 0\n    l = prevObserving.length;\n    while (l--) {\n        var dep = prevObserving[l];\n        if (dep.diffValue === 0) {\n            removeObserver(dep, derivation);\n        }\n        dep.diffValue = 0;\n    }\n    // Go through all new observables and check diffValue: (now it should be unique)\n    //   0: it was set to 0 in last loop. don't need to do anything.\n    //   1: it wasn't observed, let's observe it. set back to 0\n    while (i0--) {\n        var dep = observing[i0];\n        if (dep.diffValue === 1) {\n            dep.diffValue = 0;\n            addObserver(dep, derivation);\n        }\n    }\n    // Some new observed derivations may become stale during this derivation computation\n    // so they have had no chance to propagate staleness (#916)\n    if (lowestNewObservingDerivationState !== IDerivationState.UP_TO_DATE) {\n        derivation.dependenciesState = lowestNewObservingDerivationState;\n        derivation.onBecomeStale();\n    }\n}\nfunction clearObserving(derivation) {\n    // invariant(globalState.inBatch > 0, \"INTERNAL ERROR clearObserving should be called only inside batch\");\n    var obs = derivation.observing;\n    derivation.observing = [];\n    var i = obs.length;\n    while (i--)\n        removeObserver(obs[i], derivation);\n    derivation.dependenciesState = IDerivationState.NOT_TRACKING;\n}\nfunction untracked(action) {\n    var prev = untrackedStart();\n    var res = action();\n    untrackedEnd(prev);\n    return res;\n}\nfunction untrackedStart() {\n    var prev = globalState.trackingDerivation;\n    globalState.trackingDerivation = null;\n    return prev;\n}\nfunction untrackedEnd(prev) {\n    globalState.trackingDerivation = prev;\n}\nfunction allowStateReadsStart(allowStateReads) {\n    var prev = globalState.allowStateReads;\n    globalState.allowStateReads = allowStateReads;\n    return prev;\n}\nfunction allowStateReadsEnd(prev) {\n    globalState.allowStateReads = prev;\n}\n/**\n * needed to keep `lowestObserverState` correct. when changing from (2 or 1) to 0\n *\n */\nfunction changeDependenciesStateTo0(derivation) {\n    if (derivation.dependenciesState === IDerivationState.UP_TO_DATE)\n        return;\n    derivation.dependenciesState = IDerivationState.UP_TO_DATE;\n    var obs = derivation.observing;\n    var i = obs.length;\n    while (i--)\n        obs[i].lowestObserverState = IDerivationState.UP_TO_DATE;\n}\n\n// we don't use globalState for these in order to avoid possible issues with multiple\n// mobx versions\nvar currentActionId = 0;\nvar nextActionId = 1;\nvar functionNameDescriptor = Object.getOwnPropertyDescriptor(function () { }, \"name\");\nvar isFunctionNameConfigurable = functionNameDescriptor && functionNameDescriptor.configurable;\nfunction createAction(actionName, fn) {\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(typeof fn === \"function\", \"`action` can only be invoked on functions\");\n        if (typeof actionName !== \"string\" || !actionName)\n            fail(\"actions should have valid names, got: '\" + actionName + \"'\");\n    }\n    var res = function () {\n        return executeAction(actionName, fn, this, arguments);\n    };\n    if (process.env.NODE_ENV !== \"production\") {\n        if (isFunctionNameConfigurable) {\n            Object.defineProperty(res, \"name\", { value: actionName });\n        }\n    }\n    res.isMobxAction = true;\n    return res;\n}\nfunction executeAction(actionName, fn, scope, args) {\n    var runInfo = _startAction(actionName, scope, args);\n    try {\n        return fn.apply(scope, args);\n    }\n    catch (err) {\n        runInfo.error = err;\n        throw err;\n    }\n    finally {\n        _endAction(runInfo);\n    }\n}\nfunction _startAction(actionName, scope, args) {\n    var notifySpy = isSpyEnabled() && !!actionName;\n    var startTime = 0;\n    if (notifySpy) {\n        startTime = Date.now();\n        var l = (args && args.length) || 0;\n        var flattendArgs = new Array(l);\n        if (l > 0)\n            for (var i = 0; i < l; i++)\n                flattendArgs[i] = args[i];\n        spyReportStart({\n            type: \"action\",\n            name: actionName,\n            object: scope,\n            arguments: flattendArgs\n        });\n    }\n    var prevDerivation = untrackedStart();\n    startBatch();\n    var prevAllowStateChanges = allowStateChangesStart(true);\n    var prevAllowStateReads = allowStateReadsStart(true);\n    var runInfo = {\n        prevDerivation: prevDerivation,\n        prevAllowStateChanges: prevAllowStateChanges,\n        prevAllowStateReads: prevAllowStateReads,\n        notifySpy: notifySpy,\n        startTime: startTime,\n        actionId: nextActionId++,\n        parentActionId: currentActionId\n    };\n    currentActionId = runInfo.actionId;\n    return runInfo;\n}\nfunction _endAction(runInfo) {\n    if (currentActionId !== runInfo.actionId) {\n        fail(\"invalid action stack. did you forget to finish an action?\");\n    }\n    currentActionId = runInfo.parentActionId;\n    if (runInfo.error !== undefined) {\n        globalState.suppressReactionErrors = true;\n    }\n    allowStateChangesEnd(runInfo.prevAllowStateChanges);\n    allowStateReadsEnd(runInfo.prevAllowStateReads);\n    endBatch();\n    untrackedEnd(runInfo.prevDerivation);\n    if (runInfo.notifySpy) {\n        spyReportEnd({ time: Date.now() - runInfo.startTime });\n    }\n    globalState.suppressReactionErrors = false;\n}\nfunction allowStateChanges(allowStateChanges, func) {\n    var prev = allowStateChangesStart(allowStateChanges);\n    var res;\n    try {\n        res = func();\n    }\n    finally {\n        allowStateChangesEnd(prev);\n    }\n    return res;\n}\nfunction allowStateChangesStart(allowStateChanges) {\n    var prev = globalState.allowStateChanges;\n    globalState.allowStateChanges = allowStateChanges;\n    return prev;\n}\nfunction allowStateChangesEnd(prev) {\n    globalState.allowStateChanges = prev;\n}\nfunction allowStateChangesInsideComputed(func) {\n    var prev = globalState.computationDepth;\n    globalState.computationDepth = 0;\n    var res;\n    try {\n        res = func();\n    }\n    finally {\n        globalState.computationDepth = prev;\n    }\n    return res;\n}\n\nvar ObservableValue = /** @class */ (function (_super) {\n    __extends(ObservableValue, _super);\n    function ObservableValue(value, enhancer, name, notifySpy, equals) {\n        if (name === void 0) { name = \"ObservableValue@\" + getNextId(); }\n        if (notifySpy === void 0) { notifySpy = true; }\n        if (equals === void 0) { equals = comparer.default; }\n        var _this = _super.call(this, name) || this;\n        _this.enhancer = enhancer;\n        _this.name = name;\n        _this.equals = equals;\n        _this.hasUnreportedChange = false;\n        _this.value = enhancer(value, undefined, name);\n        if (notifySpy && isSpyEnabled()) {\n            // only notify spy if this is a stand-alone observable\n            spyReport({ type: \"create\", name: _this.name, newValue: \"\" + _this.value });\n        }\n        return _this;\n    }\n    ObservableValue.prototype.dehanceValue = function (value) {\n        if (this.dehancer !== undefined)\n            return this.dehancer(value);\n        return value;\n    };\n    ObservableValue.prototype.set = function (newValue) {\n        var oldValue = this.value;\n        newValue = this.prepareNewValue(newValue);\n        if (newValue !== globalState.UNCHANGED) {\n            var notifySpy = isSpyEnabled();\n            if (notifySpy) {\n                spyReportStart({\n                    type: \"update\",\n                    name: this.name,\n                    newValue: newValue,\n                    oldValue: oldValue\n                });\n            }\n            this.setNewValue(newValue);\n            if (notifySpy)\n                spyReportEnd();\n        }\n    };\n    ObservableValue.prototype.prepareNewValue = function (newValue) {\n        checkIfStateModificationsAreAllowed(this);\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                object: this,\n                type: \"update\",\n                newValue: newValue\n            });\n            if (!change)\n                return globalState.UNCHANGED;\n            newValue = change.newValue;\n        }\n        // apply modifier\n        newValue = this.enhancer(newValue, this.value, this.name);\n        return this.equals(this.value, newValue) ? globalState.UNCHANGED : newValue;\n    };\n    ObservableValue.prototype.setNewValue = function (newValue) {\n        var oldValue = this.value;\n        this.value = newValue;\n        this.reportChanged();\n        if (hasListeners(this)) {\n            notifyListeners(this, {\n                type: \"update\",\n                object: this,\n                newValue: newValue,\n                oldValue: oldValue\n            });\n        }\n    };\n    ObservableValue.prototype.get = function () {\n        this.reportObserved();\n        return this.dehanceValue(this.value);\n    };\n    ObservableValue.prototype.intercept = function (handler) {\n        return registerInterceptor(this, handler);\n    };\n    ObservableValue.prototype.observe = function (listener, fireImmediately) {\n        if (fireImmediately)\n            listener({\n                object: this,\n                type: \"update\",\n                newValue: this.value,\n                oldValue: undefined\n            });\n        return registerListener(this, listener);\n    };\n    ObservableValue.prototype.toJSON = function () {\n        return this.get();\n    };\n    ObservableValue.prototype.toString = function () {\n        return this.name + \"[\" + this.value + \"]\";\n    };\n    ObservableValue.prototype.valueOf = function () {\n        return toPrimitive(this.get());\n    };\n    return ObservableValue;\n}(Atom));\nObservableValue.prototype[primitiveSymbol()] = ObservableValue.prototype.valueOf;\nvar isObservableValue = createInstanceofPredicate(\"ObservableValue\", ObservableValue);\n\n/**\n * A node in the state dependency root that observes other nodes, and can be observed itself.\n *\n * ComputedValue will remember the result of the computation for the duration of the batch, or\n * while being observed.\n *\n * During this time it will recompute only when one of its direct dependencies changed,\n * but only when it is being accessed with `ComputedValue.get()`.\n *\n * Implementation description:\n * 1. First time it's being accessed it will compute and remember result\n *    give back remembered result until 2. happens\n * 2. First time any deep dependency change, propagate POSSIBLY_STALE to all observers, wait for 3.\n * 3. When it's being accessed, recompute if any shallow dependency changed.\n *    if result changed: propagate STALE to all observers, that were POSSIBLY_STALE from the last step.\n *    go to step 2. either way\n *\n * If at any point it's outside batch and it isn't observed: reset everything and go to 1.\n */\nvar ComputedValue = /** @class */ (function () {\n    /**\n     * Create a new computed value based on a function expression.\n     *\n     * The `name` property is for debug purposes only.\n     *\n     * The `equals` property specifies the comparer function to use to determine if a newly produced\n     * value differs from the previous value. Two comparers are provided in the library; `defaultComparer`\n     * compares based on identity comparison (===), and `structualComparer` deeply compares the structure.\n     * Structural comparison can be convenient if you always produce a new aggregated object and\n     * don't want to notify observers if it is structurally the same.\n     * This is useful for working with vectors, mouse coordinates etc.\n     */\n    function ComputedValue(options) {\n        this.dependenciesState = IDerivationState.NOT_TRACKING;\n        this.observing = []; // nodes we are looking at. Our value depends on these nodes\n        this.newObserving = null; // during tracking it's an array with new observed observers\n        this.isBeingObserved = false;\n        this.isPendingUnobservation = false;\n        this.observers = [];\n        this.observersIndexes = {};\n        this.diffValue = 0;\n        this.runId = 0;\n        this.lastAccessedBy = 0;\n        this.lowestObserverState = IDerivationState.UP_TO_DATE;\n        this.unboundDepsCount = 0;\n        this.__mapid = \"#\" + getNextId();\n        this.value = new CaughtException(null);\n        this.isComputing = false; // to check for cycles\n        this.isRunningSetter = false;\n        this.isTracing = TraceMode.NONE;\n        invariant(options.get, \"missing option for computed: get\");\n        this.derivation = options.get;\n        this.name = options.name || \"ComputedValue@\" + getNextId();\n        if (options.set)\n            this.setter = createAction(this.name + \"-setter\", options.set);\n        this.equals =\n            options.equals ||\n                (options.compareStructural || options.struct\n                    ? comparer.structural\n                    : comparer.default);\n        this.scope = options.context;\n        this.requiresReaction = !!options.requiresReaction;\n        this.keepAlive = !!options.keepAlive;\n    }\n    ComputedValue.prototype.onBecomeStale = function () {\n        propagateMaybeChanged(this);\n    };\n    ComputedValue.prototype.onBecomeUnobserved = function () { };\n    ComputedValue.prototype.onBecomeObserved = function () { };\n    /**\n     * Returns the current value of this computed value.\n     * Will evaluate its computation first if needed.\n     */\n    ComputedValue.prototype.get = function () {\n        if (this.isComputing)\n            fail(\"Cycle detected in computation \" + this.name + \": \" + this.derivation);\n        if (globalState.inBatch === 0 && this.observers.length === 0 && !this.keepAlive) {\n            if (shouldCompute(this)) {\n                this.warnAboutUntrackedRead();\n                startBatch(); // See perf test 'computed memoization'\n                this.value = this.computeValue(false);\n                endBatch();\n            }\n        }\n        else {\n            reportObserved(this);\n            if (shouldCompute(this))\n                if (this.trackAndCompute())\n                    propagateChangeConfirmed(this);\n        }\n        var result = this.value;\n        if (isCaughtException(result))\n            throw result.cause;\n        return result;\n    };\n    ComputedValue.prototype.peek = function () {\n        var res = this.computeValue(false);\n        if (isCaughtException(res))\n            throw res.cause;\n        return res;\n    };\n    ComputedValue.prototype.set = function (value) {\n        if (this.setter) {\n            invariant(!this.isRunningSetter, \"The setter of computed value '\" + this.name + \"' is trying to update itself. Did you intend to update an _observable_ value, instead of the computed property?\");\n            this.isRunningSetter = true;\n            try {\n                this.setter.call(this.scope, value);\n            }\n            finally {\n                this.isRunningSetter = false;\n            }\n        }\n        else\n            invariant(false, process.env.NODE_ENV !== \"production\" &&\n                \"[ComputedValue '\" + this.name + \"'] It is not possible to assign a new value to a computed value.\");\n    };\n    ComputedValue.prototype.trackAndCompute = function () {\n        if (isSpyEnabled()) {\n            spyReport({\n                object: this.scope,\n                type: \"compute\",\n                name: this.name\n            });\n        }\n        var oldValue = this.value;\n        var wasSuspended = \n        /* see #1208 */ this.dependenciesState === IDerivationState.NOT_TRACKING;\n        var newValue = this.computeValue(true);\n        var changed = wasSuspended ||\n            isCaughtException(oldValue) ||\n            isCaughtException(newValue) ||\n            !this.equals(oldValue, newValue);\n        if (changed) {\n            this.value = newValue;\n        }\n        return changed;\n    };\n    ComputedValue.prototype.computeValue = function (track) {\n        this.isComputing = true;\n        globalState.computationDepth++;\n        var res;\n        if (track) {\n            res = trackDerivedFunction(this, this.derivation, this.scope);\n        }\n        else {\n            if (globalState.disableErrorBoundaries === true) {\n                res = this.derivation.call(this.scope);\n            }\n            else {\n                try {\n                    res = this.derivation.call(this.scope);\n                }\n                catch (e) {\n                    res = new CaughtException(e);\n                }\n            }\n        }\n        globalState.computationDepth--;\n        this.isComputing = false;\n        return res;\n    };\n    ComputedValue.prototype.suspend = function () {\n        if (!this.keepAlive) {\n            clearObserving(this);\n            this.value = undefined; // don't hold on to computed value!\n        }\n    };\n    ComputedValue.prototype.observe = function (listener, fireImmediately) {\n        var _this = this;\n        var firstTime = true;\n        var prevValue = undefined;\n        return autorun(function () {\n            var newValue = _this.get();\n            if (!firstTime || fireImmediately) {\n                var prevU = untrackedStart();\n                listener({\n                    type: \"update\",\n                    object: _this,\n                    newValue: newValue,\n                    oldValue: prevValue\n                });\n                untrackedEnd(prevU);\n            }\n            firstTime = false;\n            prevValue = newValue;\n        });\n    };\n    ComputedValue.prototype.warnAboutUntrackedRead = function () {\n        if (process.env.NODE_ENV === \"production\")\n            return;\n        if (this.requiresReaction === true) {\n            fail(\"[mobx] Computed value \" + this.name + \" is read outside a reactive context\");\n        }\n        if (this.isTracing !== TraceMode.NONE) {\n            console.log(\"[mobx.trace] '\" + this.name + \"' is being read outside a reactive context. Doing a full recompute\");\n        }\n        if (globalState.computedRequiresReaction) {\n            console.warn(\"[mobx] Computed value \" + this.name + \" is being read outside a reactive context. Doing a full recompute\");\n        }\n    };\n    ComputedValue.prototype.toJSON = function () {\n        return this.get();\n    };\n    ComputedValue.prototype.toString = function () {\n        return this.name + \"[\" + this.derivation.toString() + \"]\";\n    };\n    ComputedValue.prototype.valueOf = function () {\n        return toPrimitive(this.get());\n    };\n    return ComputedValue;\n}());\nComputedValue.prototype[primitiveSymbol()] = ComputedValue.prototype.valueOf;\nvar isComputedValue = createInstanceofPredicate(\"ComputedValue\", ComputedValue);\n\n/**\n * These values will persist if global state is reset\n */\nvar persistentKeys = [\n    \"mobxGuid\",\n    \"spyListeners\",\n    \"enforceActions\",\n    \"computedRequiresReaction\",\n    \"reactionRequiresObservable\",\n    \"observableRequiresReaction\",\n    \"allowStateReads\",\n    \"disableErrorBoundaries\",\n    \"runId\",\n    \"UNCHANGED\"\n];\nvar MobXGlobals = /** @class */ (function () {\n    function MobXGlobals() {\n        /**\n         * MobXGlobals version.\n         * MobX compatiblity with other versions loaded in memory as long as this version matches.\n         * It indicates that the global state still stores similar information\n         *\n         * N.B: this version is unrelated to the package version of MobX, and is only the version of the\n         * internal state storage of MobX, and can be the same across many different package versions\n         */\n        this.version = 5;\n        /**\n         * globally unique token to signal unchanged\n         */\n        this.UNCHANGED = {};\n        /**\n         * Currently running derivation\n         */\n        this.trackingDerivation = null;\n        /**\n         * Are we running a computation currently? (not a reaction)\n         */\n        this.computationDepth = 0;\n        /**\n         * Each time a derivation is tracked, it is assigned a unique run-id\n         */\n        this.runId = 0;\n        /**\n         * 'guid' for general purpose. Will be persisted amongst resets.\n         */\n        this.mobxGuid = 0;\n        /**\n         * Are we in a batch block? (and how many of them)\n         */\n        this.inBatch = 0;\n        /**\n         * Observables that don't have observers anymore, and are about to be\n         * suspended, unless somebody else accesses it in the same batch\n         *\n         * @type {IObservable[]}\n         */\n        this.pendingUnobservations = [];\n        /**\n         * List of scheduled, not yet executed, reactions.\n         */\n        this.pendingReactions = [];\n        /**\n         * Are we currently processing reactions?\n         */\n        this.isRunningReactions = false;\n        /**\n         * Is it allowed to change observables at this point?\n         * In general, MobX doesn't allow that when running computations and React.render.\n         * To ensure that those functions stay pure.\n         */\n        this.allowStateChanges = true;\n        /**\n         * Is it allowed to read observables at this point?\n         * Used to hold the state needed for `observableRequiresReaction`\n         */\n        this.allowStateReads = true;\n        /**\n         * If strict mode is enabled, state changes are by default not allowed\n         */\n        this.enforceActions = false;\n        /**\n         * Spy callbacks\n         */\n        this.spyListeners = [];\n        /**\n         * Globally attached error handlers that react specifically to errors in reactions\n         */\n        this.globalReactionErrorHandlers = [];\n        /**\n         * Warn if computed values are accessed outside a reactive context\n         */\n        this.computedRequiresReaction = false;\n        /**\n         * (Experimental)\n         * Warn if you try to create to derivation / reactive context without accessing any observable.\n         */\n        this.reactionRequiresObservable = false;\n        /**\n         * (Experimental)\n         * Warn if observables are accessed outside a reactive context\n         */\n        this.observableRequiresReaction = false;\n        /**\n         * Allows overwriting of computed properties, useful in tests but not prod as it can cause\n         * memory leaks. See https://github.com/mobxjs/mobx/issues/1867\n         */\n        this.computedConfigurable = false;\n        /*\n         * Don't catch and rethrow exceptions. This is useful for inspecting the state of\n         * the stack when an exception occurs while debugging.\n         */\n        this.disableErrorBoundaries = false;\n        /*\n         * If true, we are already handling an exception in an action. Any errors in reactions should be supressed, as\n         * they are not the cause, see: https://github.com/mobxjs/mobx/issues/1836\n         */\n        this.suppressReactionErrors = false;\n    }\n    return MobXGlobals;\n}());\nvar canMergeGlobalState = true;\nvar isolateCalled = false;\nvar globalState = (function () {\n    var global = getGlobal();\n    if (global.__mobxInstanceCount > 0 && !global.__mobxGlobals)\n        canMergeGlobalState = false;\n    if (global.__mobxGlobals && global.__mobxGlobals.version !== new MobXGlobals().version)\n        canMergeGlobalState = false;\n    if (!canMergeGlobalState) {\n        setTimeout(function () {\n            if (!isolateCalled) {\n                fail(\"There are multiple, different versions of MobX active. Make sure MobX is loaded only once or use `configure({ isolateGlobalState: true })`\");\n            }\n        }, 1);\n        return new MobXGlobals();\n    }\n    else if (global.__mobxGlobals) {\n        global.__mobxInstanceCount += 1;\n        if (!global.__mobxGlobals.UNCHANGED)\n            global.__mobxGlobals.UNCHANGED = {}; // make merge backward compatible\n        return global.__mobxGlobals;\n    }\n    else {\n        global.__mobxInstanceCount = 1;\n        return (global.__mobxGlobals = new MobXGlobals());\n    }\n})();\nfunction isolateGlobalState() {\n    if (globalState.pendingReactions.length ||\n        globalState.inBatch ||\n        globalState.isRunningReactions)\n        fail(\"isolateGlobalState should be called before MobX is running any reactions\");\n    isolateCalled = true;\n    if (canMergeGlobalState) {\n        if (--getGlobal().__mobxInstanceCount === 0)\n            getGlobal().__mobxGlobals = undefined;\n        globalState = new MobXGlobals();\n    }\n}\nfunction getGlobalState() {\n    return globalState;\n}\n/**\n * For testing purposes only; this will break the internal state of existing observables,\n * but can be used to get back at a stable state after throwing errors\n */\nfunction resetGlobalState() {\n    var defaultGlobals = new MobXGlobals();\n    for (var key in defaultGlobals)\n        if (persistentKeys.indexOf(key) === -1)\n            globalState[key] = defaultGlobals[key];\n    globalState.allowStateChanges = !globalState.enforceActions;\n}\n\nfunction hasObservers(observable) {\n    return observable.observers && observable.observers.length > 0;\n}\nfunction getObservers(observable) {\n    return observable.observers;\n}\n// function invariantObservers(observable: IObservable) {\n//     const list = observable.observers\n//     const map = observable.observersIndexes\n//     const l = list.length\n//     for (let i = 0; i < l; i++) {\n//         const id = list[i].__mapid\n//         if (i) {\n//             invariant(map[id] === i, \"INTERNAL ERROR maps derivation.__mapid to index in list\") // for performance\n//         } else {\n//             invariant(!(id in map), \"INTERNAL ERROR observer on index 0 shouldn't be held in map.\") // for performance\n//         }\n//     }\n//     invariant(\n//         list.length === 0 || Object.keys(map).length === list.length - 1,\n//         \"INTERNAL ERROR there is no junk in map\"\n//     )\n// }\nfunction addObserver(observable, node) {\n    // invariant(node.dependenciesState !== -1, \"INTERNAL ERROR, can add only dependenciesState !== -1\");\n    // invariant(observable._observers.indexOf(node) === -1, \"INTERNAL ERROR add already added node\");\n    // invariantObservers(observable);\n    var l = observable.observers.length;\n    if (l) {\n        // because object assignment is relatively expensive, let's not store data about index 0.\n        observable.observersIndexes[node.__mapid] = l;\n    }\n    observable.observers[l] = node;\n    if (observable.lowestObserverState > node.dependenciesState)\n        observable.lowestObserverState = node.dependenciesState;\n    // invariantObservers(observable);\n    // invariant(observable._observers.indexOf(node) !== -1, \"INTERNAL ERROR didn't add node\");\n}\nfunction removeObserver(observable, node) {\n    // invariant(globalState.inBatch > 0, \"INTERNAL ERROR, remove should be called only inside batch\");\n    // invariant(observable._observers.indexOf(node) !== -1, \"INTERNAL ERROR remove already removed node\");\n    // invariantObservers(observable);\n    if (observable.observers.length === 1) {\n        // deleting last observer\n        observable.observers.length = 0;\n        queueForUnobservation(observable);\n    }\n    else {\n        // deleting from _observersIndexes is straight forward, to delete from _observers, let's swap `node` with last element\n        var list = observable.observers;\n        var map = observable.observersIndexes;\n        var filler = list.pop(); // get last element, which should fill the place of `node`, so the array doesn't have holes\n        if (filler !== node) {\n            // otherwise node was the last element, which already got removed from array\n            var index = map[node.__mapid] || 0; // getting index of `node`. this is the only place we actually use map.\n            if (index) {\n                // map store all indexes but 0, see comment in `addObserver`\n                map[filler.__mapid] = index;\n            }\n            else {\n                delete map[filler.__mapid];\n            }\n            list[index] = filler;\n        }\n        delete map[node.__mapid];\n    }\n    // invariantObservers(observable);\n    // invariant(observable._observers.indexOf(node) === -1, \"INTERNAL ERROR remove already removed node2\");\n}\nfunction queueForUnobservation(observable) {\n    if (observable.isPendingUnobservation === false) {\n        // invariant(observable._observers.length === 0, \"INTERNAL ERROR, should only queue for unobservation unobserved observables\");\n        observable.isPendingUnobservation = true;\n        globalState.pendingUnobservations.push(observable);\n    }\n}\n/**\n * Batch starts a transaction, at least for purposes of memoizing ComputedValues when nothing else does.\n * During a batch `onBecomeUnobserved` will be called at most once per observable.\n * Avoids unnecessary recalculations.\n */\nfunction startBatch() {\n    globalState.inBatch++;\n}\nfunction endBatch() {\n    if (--globalState.inBatch === 0) {\n        runReactions();\n        // the batch is actually about to finish, all unobserving should happen here.\n        var list = globalState.pendingUnobservations;\n        for (var i = 0; i < list.length; i++) {\n            var observable = list[i];\n            observable.isPendingUnobservation = false;\n            if (observable.observers.length === 0) {\n                if (observable.isBeingObserved) {\n                    // if this observable had reactive observers, trigger the hooks\n                    observable.isBeingObserved = false;\n                    observable.onBecomeUnobserved();\n                }\n                if (observable instanceof ComputedValue) {\n                    // computed values are automatically teared down when the last observer leaves\n                    // this process happens recursively, this computed might be the last observable of another, etc..\n                    observable.suspend();\n                }\n            }\n        }\n        globalState.pendingUnobservations = [];\n    }\n}\nfunction reportObserved(observable) {\n    checkIfStateReadsAreAllowed(observable);\n    var derivation = globalState.trackingDerivation;\n    if (derivation !== null) {\n        /**\n         * Simple optimization, give each derivation run an unique id (runId)\n         * Check if last time this observable was accessed the same runId is used\n         * if this is the case, the relation is already known\n         */\n        if (derivation.runId !== observable.lastAccessedBy) {\n            observable.lastAccessedBy = derivation.runId;\n            derivation.newObserving[derivation.unboundDepsCount++] = observable;\n            if (!observable.isBeingObserved) {\n                observable.isBeingObserved = true;\n                observable.onBecomeObserved();\n            }\n        }\n        return true;\n    }\n    else if (observable.observers.length === 0 && globalState.inBatch > 0) {\n        queueForUnobservation(observable);\n    }\n    return false;\n}\n// function invariantLOS(observable: IObservable, msg: string) {\n//     // it's expensive so better not run it in produciton. but temporarily helpful for testing\n//     const min = getObservers(observable).reduce((a, b) => Math.min(a, b.dependenciesState), 2)\n//     if (min >= observable.lowestObserverState) return // <- the only assumption about `lowestObserverState`\n//     throw new Error(\n//         \"lowestObserverState is wrong for \" +\n//             msg +\n//             \" because \" +\n//             min +\n//             \" < \" +\n//             observable.lowestObserverState\n//     )\n// }\n/**\n * NOTE: current propagation mechanism will in case of self reruning autoruns behave unexpectedly\n * It will propagate changes to observers from previous run\n * It's hard or maybe impossible (with reasonable perf) to get it right with current approach\n * Hopefully self reruning autoruns aren't a feature people should depend on\n * Also most basic use cases should be ok\n */\n// Called by Atom when its value changes\nfunction propagateChanged(observable) {\n    // invariantLOS(observable, \"changed start\");\n    if (observable.lowestObserverState === IDerivationState.STALE)\n        return;\n    observable.lowestObserverState = IDerivationState.STALE;\n    var observers = observable.observers;\n    var i = observers.length;\n    while (i--) {\n        var d = observers[i];\n        if (d.dependenciesState === IDerivationState.UP_TO_DATE) {\n            if (d.isTracing !== TraceMode.NONE) {\n                logTraceInfo(d, observable);\n            }\n            d.onBecomeStale();\n        }\n        d.dependenciesState = IDerivationState.STALE;\n    }\n    // invariantLOS(observable, \"changed end\");\n}\n// Called by ComputedValue when it recalculate and its value changed\nfunction propagateChangeConfirmed(observable) {\n    // invariantLOS(observable, \"confirmed start\");\n    if (observable.lowestObserverState === IDerivationState.STALE)\n        return;\n    observable.lowestObserverState = IDerivationState.STALE;\n    var observers = observable.observers;\n    var i = observers.length;\n    while (i--) {\n        var d = observers[i];\n        if (d.dependenciesState === IDerivationState.POSSIBLY_STALE)\n            d.dependenciesState = IDerivationState.STALE;\n        else if (d.dependenciesState === IDerivationState.UP_TO_DATE // this happens during computing of `d`, just keep lowestObserverState up to date.\n        )\n            observable.lowestObserverState = IDerivationState.UP_TO_DATE;\n    }\n    // invariantLOS(observable, \"confirmed end\");\n}\n// Used by computed when its dependency changed, but we don't wan't to immediately recompute.\nfunction propagateMaybeChanged(observable) {\n    // invariantLOS(observable, \"maybe start\");\n    if (observable.lowestObserverState !== IDerivationState.UP_TO_DATE)\n        return;\n    observable.lowestObserverState = IDerivationState.POSSIBLY_STALE;\n    var observers = observable.observers;\n    var i = observers.length;\n    while (i--) {\n        var d = observers[i];\n        if (d.dependenciesState === IDerivationState.UP_TO_DATE) {\n            d.dependenciesState = IDerivationState.POSSIBLY_STALE;\n            if (d.isTracing !== TraceMode.NONE) {\n                logTraceInfo(d, observable);\n            }\n            d.onBecomeStale();\n        }\n    }\n    // invariantLOS(observable, \"maybe end\");\n}\nfunction logTraceInfo(derivation, observable) {\n    console.log(\"[mobx.trace] '\" + derivation.name + \"' is invalidated due to a change in: '\" + observable.name + \"'\");\n    if (derivation.isTracing === TraceMode.BREAK) {\n        var lines = [];\n        printDepTree(getDependencyTree(derivation), lines, 1);\n        // prettier-ignore\n        new Function(\"debugger;\\n/*\\nTracing '\" + derivation.name + \"'\\n\\nYou are entering this break point because derivation '\" + derivation.name + \"' is being traced and '\" + observable.name + \"' is now forcing it to update.\\nJust follow the stacktrace you should now see in the devtools to see precisely what piece of your code is causing this update\\nThe stackframe you are looking for is at least ~6-8 stack-frames up.\\n\\n\" + (derivation instanceof ComputedValue ? derivation.derivation.toString().replace(/[*]\\//g, \"/\") : \"\") + \"\\n\\nThe dependencies for this derivation are:\\n\\n\" + lines.join(\"\\n\") + \"\\n*/\\n    \")();\n    }\n}\nfunction printDepTree(tree, lines, depth) {\n    if (lines.length >= 1000) {\n        lines.push(\"(and many more)\");\n        return;\n    }\n    lines.push(\"\" + new Array(depth).join(\"\\t\") + tree.name); // MWE: not the fastest, but the easiest way :)\n    if (tree.dependencies)\n        tree.dependencies.forEach(function (child) { return printDepTree(child, lines, depth + 1); });\n}\n\nvar Reaction = /** @class */ (function () {\n    function Reaction(name, onInvalidate, errorHandler, requiresObservable) {\n        if (name === void 0) { name = \"Reaction@\" + getNextId(); }\n        if (requiresObservable === void 0) { requiresObservable = false; }\n        this.name = name;\n        this.onInvalidate = onInvalidate;\n        this.errorHandler = errorHandler;\n        this.requiresObservable = requiresObservable;\n        this.observing = []; // nodes we are looking at. Our value depends on these nodes\n        this.newObserving = [];\n        this.dependenciesState = IDerivationState.NOT_TRACKING;\n        this.diffValue = 0;\n        this.runId = 0;\n        this.unboundDepsCount = 0;\n        this.__mapid = \"#\" + getNextId();\n        this.isDisposed = false;\n        this._isScheduled = false;\n        this._isTrackPending = false;\n        this._isRunning = false;\n        this.isTracing = TraceMode.NONE;\n    }\n    Reaction.prototype.onBecomeStale = function () {\n        this.schedule();\n    };\n    Reaction.prototype.schedule = function () {\n        if (!this._isScheduled) {\n            this._isScheduled = true;\n            globalState.pendingReactions.push(this);\n            runReactions();\n        }\n    };\n    Reaction.prototype.isScheduled = function () {\n        return this._isScheduled;\n    };\n    /**\n     * internal, use schedule() if you intend to kick off a reaction\n     */\n    Reaction.prototype.runReaction = function () {\n        if (!this.isDisposed) {\n            startBatch();\n            this._isScheduled = false;\n            if (shouldCompute(this)) {\n                this._isTrackPending = true;\n                try {\n                    this.onInvalidate();\n                    if (this._isTrackPending && isSpyEnabled()) {\n                        // onInvalidate didn't trigger track right away..\n                        spyReport({\n                            name: this.name,\n                            type: \"scheduled-reaction\"\n                        });\n                    }\n                }\n                catch (e) {\n                    this.reportExceptionInDerivation(e);\n                }\n            }\n            endBatch();\n        }\n    };\n    Reaction.prototype.track = function (fn) {\n        startBatch();\n        var notify = isSpyEnabled();\n        var startTime;\n        if (notify) {\n            startTime = Date.now();\n            spyReportStart({\n                name: this.name,\n                type: \"reaction\"\n            });\n        }\n        this._isRunning = true;\n        var result = trackDerivedFunction(this, fn, undefined);\n        this._isRunning = false;\n        this._isTrackPending = false;\n        if (this.isDisposed) {\n            // disposed during last run. Clean up everything that was bound after the dispose call.\n            clearObserving(this);\n        }\n        if (isCaughtException(result))\n            this.reportExceptionInDerivation(result.cause);\n        if (notify) {\n            spyReportEnd({\n                time: Date.now() - startTime\n            });\n        }\n        endBatch();\n    };\n    Reaction.prototype.reportExceptionInDerivation = function (error) {\n        var _this = this;\n        if (this.errorHandler) {\n            this.errorHandler(error, this);\n            return;\n        }\n        if (globalState.disableErrorBoundaries)\n            throw error;\n        var message = \"[mobx] Encountered an uncaught exception that was thrown by a reaction or observer component, in: '\" + this + \"'\";\n        if (globalState.suppressReactionErrors) {\n            console.warn(\"[mobx] (error in reaction '\" + this.name + \"' suppressed, fix error of causing action below)\"); // prettier-ignore\n        }\n        else {\n            console.error(message, error);\n            /** If debugging brought you here, please, read the above message :-). Tnx! */\n        }\n        if (isSpyEnabled()) {\n            spyReport({\n                type: \"error\",\n                name: this.name,\n                message: message,\n                error: \"\" + error\n            });\n        }\n        globalState.globalReactionErrorHandlers.forEach(function (f) { return f(error, _this); });\n    };\n    Reaction.prototype.dispose = function () {\n        if (!this.isDisposed) {\n            this.isDisposed = true;\n            if (!this._isRunning) {\n                // if disposed while running, clean up later. Maybe not optimal, but rare case\n                startBatch();\n                clearObserving(this);\n                endBatch();\n            }\n        }\n    };\n    Reaction.prototype.getDisposer = function () {\n        var r = this.dispose.bind(this);\n        r.$mobx = this;\n        return r;\n    };\n    Reaction.prototype.toString = function () {\n        return \"Reaction[\" + this.name + \"]\";\n    };\n    Reaction.prototype.trace = function (enterBreakPoint) {\n        if (enterBreakPoint === void 0) { enterBreakPoint = false; }\n        trace(this, enterBreakPoint);\n    };\n    return Reaction;\n}());\nfunction onReactionError(handler) {\n    globalState.globalReactionErrorHandlers.push(handler);\n    return function () {\n        var idx = globalState.globalReactionErrorHandlers.indexOf(handler);\n        if (idx >= 0)\n            globalState.globalReactionErrorHandlers.splice(idx, 1);\n    };\n}\n/**\n * Magic number alert!\n * Defines within how many times a reaction is allowed to re-trigger itself\n * until it is assumed that this is gonna be a never ending loop...\n */\nvar MAX_REACTION_ITERATIONS = 100;\nvar reactionScheduler = function (f) { return f(); };\nfunction runReactions() {\n    // Trampolining, if runReactions are already running, new reactions will be picked up\n    if (globalState.inBatch > 0 || globalState.isRunningReactions)\n        return;\n    reactionScheduler(runReactionsHelper);\n}\nfunction runReactionsHelper() {\n    globalState.isRunningReactions = true;\n    var allReactions = globalState.pendingReactions;\n    var iterations = 0;\n    // While running reactions, new reactions might be triggered.\n    // Hence we work with two variables and check whether\n    // we converge to no remaining reactions after a while.\n    while (allReactions.length > 0) {\n        if (++iterations === MAX_REACTION_ITERATIONS) {\n            console.error(\"Reaction doesn't converge to a stable state after \" + MAX_REACTION_ITERATIONS + \" iterations.\" +\n                (\" Probably there is a cycle in the reactive function: \" + allReactions[0]));\n            allReactions.splice(0); // clear reactions\n        }\n        var remainingReactions = allReactions.splice(0);\n        for (var i = 0, l = remainingReactions.length; i < l; i++)\n            remainingReactions[i].runReaction();\n    }\n    globalState.isRunningReactions = false;\n}\nvar isReaction = createInstanceofPredicate(\"Reaction\", Reaction);\nfunction setReactionScheduler(fn) {\n    var baseScheduler = reactionScheduler;\n    reactionScheduler = function (f) { return fn(function () { return baseScheduler(f); }); };\n}\n\nfunction isSpyEnabled() {\n    return !!globalState.spyListeners.length;\n}\nfunction spyReport(event) {\n    if (!globalState.spyListeners.length)\n        return;\n    var listeners = globalState.spyListeners;\n    for (var i = 0, l = listeners.length; i < l; i++)\n        listeners[i](event);\n}\nfunction spyReportStart(event) {\n    var change = __assign(__assign({}, event), { spyReportStart: true });\n    spyReport(change);\n}\nvar END_EVENT = { spyReportEnd: true };\nfunction spyReportEnd(change) {\n    if (change)\n        spyReport(__assign(__assign({}, change), { spyReportEnd: true }));\n    else\n        spyReport(END_EVENT);\n}\nfunction spy(listener) {\n    globalState.spyListeners.push(listener);\n    return once(function () {\n        globalState.spyListeners = globalState.spyListeners.filter(function (l) { return l !== listener; });\n    });\n}\n\nfunction dontReassignFields() {\n    fail(process.env.NODE_ENV !== \"production\" && \"@action fields are not reassignable\");\n}\nfunction namedActionDecorator(name) {\n    return function (target, prop, descriptor) {\n        if (descriptor) {\n            if (process.env.NODE_ENV !== \"production\" && descriptor.get !== undefined) {\n                return fail(\"@action cannot be used with getters\");\n            }\n            // babel / typescript\n            // @action method() { }\n            if (descriptor.value) {\n                // typescript\n                return {\n                    value: createAction(name, descriptor.value),\n                    enumerable: false,\n                    configurable: true,\n                    writable: true // for typescript, this must be writable, otherwise it cannot inherit :/ (see inheritable actions test)\n                };\n            }\n            // babel only: @action method = () => {}\n            var initializer_1 = descriptor.initializer;\n            return {\n                enumerable: false,\n                configurable: true,\n                writable: true,\n                initializer: function () {\n                    // N.B: we can't immediately invoke initializer; this would be wrong\n                    return createAction(name, initializer_1.call(this));\n                }\n            };\n        }\n        // bound instance methods\n        return actionFieldDecorator(name).apply(this, arguments);\n    };\n}\nfunction actionFieldDecorator(name) {\n    // Simple property that writes on first invocation to the current instance\n    return function (target, prop, descriptor) {\n        Object.defineProperty(target, prop, {\n            configurable: true,\n            enumerable: false,\n            get: function () {\n                return undefined;\n            },\n            set: function (value) {\n                addHiddenProp(this, prop, action(name, value));\n            }\n        });\n    };\n}\nfunction boundActionDecorator(target, propertyName, descriptor, applyToInstance) {\n    if (applyToInstance === true) {\n        defineBoundAction(target, propertyName, descriptor.value);\n        return null;\n    }\n    if (descriptor) {\n        // if (descriptor.value)\n        // Typescript / Babel: @action.bound method() { }\n        // also: babel @action.bound method = () => {}\n        return {\n            configurable: true,\n            enumerable: false,\n            get: function () {\n                defineBoundAction(this, propertyName, descriptor.value || descriptor.initializer.call(this));\n                return this[propertyName];\n            },\n            set: dontReassignFields\n        };\n    }\n    // field decorator Typescript @action.bound method = () => {}\n    return {\n        enumerable: false,\n        configurable: true,\n        set: function (v) {\n            defineBoundAction(this, propertyName, v);\n        },\n        get: function () {\n            return undefined;\n        }\n    };\n}\n\nvar action = function action(arg1, arg2, arg3, arg4) {\n    // action(fn() {})\n    if (arguments.length === 1 && typeof arg1 === \"function\")\n        return createAction(arg1.name || \"<unnamed action>\", arg1);\n    // action(\"name\", fn() {})\n    if (arguments.length === 2 && typeof arg2 === \"function\")\n        return createAction(arg1, arg2);\n    // @action(\"name\") fn() {}\n    if (arguments.length === 1 && typeof arg1 === \"string\")\n        return namedActionDecorator(arg1);\n    // @action fn() {}\n    if (arg4 === true) {\n        // apply to instance immediately\n        arg1[arg2] = createAction(arg1.name || arg2, arg3.value);\n    }\n    else {\n        return namedActionDecorator(arg2).apply(null, arguments);\n    }\n};\naction.bound = boundActionDecorator;\nfunction runInAction(arg1, arg2) {\n    // TODO: deprecate?\n    var actionName = typeof arg1 === \"string\" ? arg1 : arg1.name || \"<unnamed action>\";\n    var fn = typeof arg1 === \"function\" ? arg1 : arg2;\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(typeof fn === \"function\" && fn.length === 0, \"`runInAction` expects a function without arguments\");\n        if (typeof actionName !== \"string\" || !actionName)\n            fail(\"actions should have valid names, got: '\" + actionName + \"'\");\n    }\n    return executeAction(actionName, fn, this, undefined);\n}\nfunction isAction(thing) {\n    return typeof thing === \"function\" && thing.isMobxAction === true;\n}\nfunction defineBoundAction(target, propertyName, fn) {\n    addHiddenProp(target, propertyName, createAction(propertyName, fn.bind(target)));\n}\n\n/**\n * Creates a named reactive view and keeps it alive, so that the view is always\n * updated if one of the dependencies changes, even when the view is not further used by something else.\n * @param view The reactive view\n * @returns disposer function, which can be used to stop the view from being updated in the future.\n */\nfunction autorun(view, opts) {\n    if (opts === void 0) { opts = EMPTY_OBJECT; }\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(typeof view === \"function\", \"Autorun expects a function as first argument\");\n        invariant(isAction(view) === false, \"Autorun does not accept actions since actions are untrackable\");\n    }\n    var name = (opts && opts.name) || view.name || \"Autorun@\" + getNextId();\n    var runSync = !opts.scheduler && !opts.delay;\n    var reaction;\n    if (runSync) {\n        // normal autorun\n        reaction = new Reaction(name, function () {\n            this.track(reactionRunner);\n        }, opts.onError, opts.requiresObservable);\n    }\n    else {\n        var scheduler_1 = createSchedulerFromOptions(opts);\n        // debounced autorun\n        var isScheduled_1 = false;\n        reaction = new Reaction(name, function () {\n            if (!isScheduled_1) {\n                isScheduled_1 = true;\n                scheduler_1(function () {\n                    isScheduled_1 = false;\n                    if (!reaction.isDisposed)\n                        reaction.track(reactionRunner);\n                });\n            }\n        }, opts.onError, opts.requiresObservable);\n    }\n    function reactionRunner() {\n        view(reaction);\n    }\n    reaction.schedule();\n    return reaction.getDisposer();\n}\nvar run = function (f) { return f(); };\nfunction createSchedulerFromOptions(opts) {\n    return opts.scheduler\n        ? opts.scheduler\n        : opts.delay\n            ? function (f) { return setTimeout(f, opts.delay); }\n            : run;\n}\nfunction reaction(expression, effect, opts) {\n    if (opts === void 0) { opts = EMPTY_OBJECT; }\n    if (typeof opts === \"boolean\") {\n        opts = { fireImmediately: opts };\n        deprecated(\"Using fireImmediately as argument is deprecated. Use '{ fireImmediately: true }' instead\");\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(typeof expression === \"function\", \"First argument to reaction should be a function\");\n        invariant(typeof opts === \"object\", \"Third argument of reactions should be an object\");\n    }\n    var name = opts.name || \"Reaction@\" + getNextId();\n    var effectAction = action(name, opts.onError ? wrapErrorHandler(opts.onError, effect) : effect);\n    var runSync = !opts.scheduler && !opts.delay;\n    var scheduler = createSchedulerFromOptions(opts);\n    var firstTime = true;\n    var isScheduled = false;\n    var value;\n    var equals = opts.compareStructural\n        ? comparer.structural\n        : opts.equals || comparer.default;\n    var r = new Reaction(name, function () {\n        if (firstTime || runSync) {\n            reactionRunner();\n        }\n        else if (!isScheduled) {\n            isScheduled = true;\n            scheduler(reactionRunner);\n        }\n    }, opts.onError, opts.requiresObservable);\n    function reactionRunner() {\n        isScheduled = false; // Q: move into reaction runner?\n        if (r.isDisposed)\n            return;\n        var changed = false;\n        r.track(function () {\n            var nextValue = expression(r);\n            changed = firstTime || !equals(value, nextValue);\n            value = nextValue;\n        });\n        if (firstTime && opts.fireImmediately)\n            effectAction(value, r);\n        if (!firstTime && changed === true)\n            effectAction(value, r);\n        if (firstTime)\n            firstTime = false;\n    }\n    r.schedule();\n    return r.getDisposer();\n}\nfunction wrapErrorHandler(errorHandler, baseFn) {\n    return function () {\n        try {\n            return baseFn.apply(this, arguments);\n        }\n        catch (e) {\n            errorHandler.call(this, e);\n        }\n    };\n}\n\nfunction onBecomeObserved(thing, arg2, arg3) {\n    return interceptHook(\"onBecomeObserved\", thing, arg2, arg3);\n}\nfunction onBecomeUnobserved(thing, arg2, arg3) {\n    return interceptHook(\"onBecomeUnobserved\", thing, arg2, arg3);\n}\nfunction interceptHook(hook, thing, arg2, arg3) {\n    var atom = typeof arg3 === \"function\" ? getAtom(thing, arg2) : getAtom(thing);\n    var cb = typeof arg3 === \"function\" ? arg3 : arg2;\n    var orig = atom[hook];\n    if (typeof orig !== \"function\")\n        return fail(process.env.NODE_ENV !== \"production\" && \"Not an atom that can be (un)observed\");\n    atom[hook] = function () {\n        orig.call(this);\n        cb.call(this);\n    };\n    return function () {\n        atom[hook] = orig;\n    };\n}\n\nfunction configure(options) {\n    var enforceActions = options.enforceActions, computedRequiresReaction = options.computedRequiresReaction, computedConfigurable = options.computedConfigurable, disableErrorBoundaries = options.disableErrorBoundaries, arrayBuffer = options.arrayBuffer, reactionScheduler = options.reactionScheduler, reactionRequiresObservable = options.reactionRequiresObservable, observableRequiresReaction = options.observableRequiresReaction;\n    if (options.isolateGlobalState === true) {\n        isolateGlobalState();\n    }\n    if (enforceActions !== undefined) {\n        if (typeof enforceActions === \"boolean\" || enforceActions === \"strict\")\n            deprecated(\"Deprecated value for 'enforceActions', use 'false' => '\\\"never\\\"', 'true' => '\\\"observed\\\"', '\\\"strict\\\"' => \\\"'always'\\\" instead\");\n        var ea = void 0;\n        switch (enforceActions) {\n            case true:\n            case \"observed\":\n                ea = true;\n                break;\n            case false:\n            case \"never\":\n                ea = false;\n                break;\n            case \"strict\":\n            case \"always\":\n                ea = \"strict\";\n                break;\n            default:\n                fail(\"Invalid value for 'enforceActions': '\" + enforceActions + \"', expected 'never', 'always' or 'observed'\");\n        }\n        globalState.enforceActions = ea;\n        globalState.allowStateChanges = ea === true || ea === \"strict\" ? false : true;\n    }\n    if (computedRequiresReaction !== undefined) {\n        globalState.computedRequiresReaction = !!computedRequiresReaction;\n    }\n    if (reactionRequiresObservable !== undefined) {\n        globalState.reactionRequiresObservable = !!reactionRequiresObservable;\n    }\n    if (observableRequiresReaction !== undefined) {\n        globalState.observableRequiresReaction = !!observableRequiresReaction;\n        globalState.allowStateReads = !globalState.observableRequiresReaction;\n    }\n    if (computedConfigurable !== undefined) {\n        globalState.computedConfigurable = !!computedConfigurable;\n    }\n    if (disableErrorBoundaries !== undefined) {\n        if (disableErrorBoundaries === true)\n            console.warn(\"WARNING: Debug feature only. MobX will NOT recover from errors if this is on.\");\n        globalState.disableErrorBoundaries = !!disableErrorBoundaries;\n    }\n    if (typeof arrayBuffer === \"number\") {\n        reserveArrayBuffer(arrayBuffer);\n    }\n    if (reactionScheduler) {\n        setReactionScheduler(reactionScheduler);\n    }\n}\n\nfunction decorate(thing, decorators) {\n    if (process.env.NODE_ENV !== \"production\" && !isPlainObject(decorators))\n        fail(\"Decorators should be a key value map\");\n    var target = typeof thing === \"function\" ? thing.prototype : thing;\n    var _loop_1 = function (prop) {\n        var propertyDecorators = decorators[prop];\n        if (!Array.isArray(propertyDecorators)) {\n            propertyDecorators = [propertyDecorators];\n        }\n        // prettier-ignore\n        if (process.env.NODE_ENV !== \"production\" && !propertyDecorators.every(function (decorator) { return typeof decorator === \"function\"; }))\n            fail(\"Decorate: expected a decorator function or array of decorator functions for '\" + prop + \"'\");\n        var descriptor = Object.getOwnPropertyDescriptor(target, prop);\n        var newDescriptor = propertyDecorators.reduce(function (accDescriptor, decorator) { return decorator(target, prop, accDescriptor); }, descriptor);\n        if (newDescriptor)\n            Object.defineProperty(target, prop, newDescriptor);\n    };\n    for (var prop in decorators) {\n        _loop_1(prop);\n    }\n    return thing;\n}\n\nfunction extendShallowObservable(target, properties, decorators) {\n    deprecated(\"'extendShallowObservable' is deprecated, use 'extendObservable(target, props, { deep: false })' instead\");\n    return extendObservable(target, properties, decorators, shallowCreateObservableOptions);\n}\nfunction extendObservable(target, properties, decorators, options) {\n    if (process.env.NODE_ENV !== \"production\") {\n        invariant(arguments.length >= 2 && arguments.length <= 4, \"'extendObservable' expected 2-4 arguments\");\n        invariant(typeof target === \"object\", \"'extendObservable' expects an object as first argument\");\n        invariant(!isObservableMap(target), \"'extendObservable' should not be used on maps, use map.merge instead\");\n        invariant(!isObservable(properties), \"Extending an object with another observable (object) is not supported. Please construct an explicit propertymap, using `toJS` if need. See issue #540\");\n        if (decorators)\n            for (var key in decorators)\n                if (!(key in properties))\n                    fail(\"Trying to declare a decorator for unspecified property '\" + key + \"'\");\n    }\n    options = asCreateObservableOptions(options);\n    var defaultDecorator = options.defaultDecorator || (options.deep === false ? refDecorator : deepDecorator);\n    initializeInstance(target);\n    asObservableObject(target, options.name, defaultDecorator.enhancer); // make sure object is observable, even without initial props\n    startBatch();\n    try {\n        var keys = Object.getOwnPropertyNames(properties);\n        for (var i = 0, l = keys.length; i < l; i++) {\n            var key = keys[i];\n            var descriptor = Object.getOwnPropertyDescriptor(properties, key);\n            if (process.env.NODE_ENV !== \"production\") {\n                if (isComputed(descriptor.value))\n                    fail(\"Passing a 'computed' as initial property value is no longer supported by extendObservable. Use a getter or decorator instead\");\n            }\n            var decorator = decorators && key in decorators\n                ? decorators[key]\n                : descriptor.get\n                    ? computedDecorator\n                    : defaultDecorator;\n            if (process.env.NODE_ENV !== \"production\" && typeof decorator !== \"function\")\n                return fail(\"Not a valid decorator for '\" + key + \"', got: \" + decorator);\n            var resultDescriptor = decorator(target, key, descriptor, true);\n            if (resultDescriptor // otherwise, assume already applied, due to `applyToInstance`\n            )\n                Object.defineProperty(target, key, resultDescriptor);\n        }\n    }\n    finally {\n        endBatch();\n    }\n    return target;\n}\n\nfunction getDependencyTree(thing, property) {\n    return nodeToDependencyTree(getAtom(thing, property));\n}\nfunction nodeToDependencyTree(node) {\n    var result = {\n        name: node.name\n    };\n    if (node.observing && node.observing.length > 0)\n        result.dependencies = unique(node.observing).map(nodeToDependencyTree);\n    return result;\n}\nfunction getObserverTree(thing, property) {\n    return nodeToObserverTree(getAtom(thing, property));\n}\nfunction nodeToObserverTree(node) {\n    var result = {\n        name: node.name\n    };\n    if (hasObservers(node))\n        result.observers = getObservers(node).map(nodeToObserverTree);\n    return result;\n}\n\nvar generatorId = 0;\nfunction FlowCancellationError() {\n    this.message = \"FLOW_CANCELLED\";\n}\nFlowCancellationError.prototype = Object.create(Error.prototype);\nfunction isFlowCancellationError(error) {\n    return error instanceof FlowCancellationError;\n}\nfunction flow(generator) {\n    if (arguments.length !== 1)\n        fail(!!process.env.NODE_ENV && \"Flow expects one 1 argument and cannot be used as decorator\");\n    var name = generator.name || \"<unnamed flow>\";\n    // Implementation based on https://github.com/tj/co/blob/master/index.js\n    return function () {\n        var ctx = this;\n        var args = arguments;\n        var runId = ++generatorId;\n        var gen = action(name + \" - runid: \" + runId + \" - init\", generator).apply(ctx, args);\n        var rejector;\n        var pendingPromise = undefined;\n        var res = new Promise(function (resolve, reject) {\n            var stepId = 0;\n            rejector = reject;\n            function onFulfilled(res) {\n                pendingPromise = undefined;\n                var ret;\n                try {\n                    ret = action(name + \" - runid: \" + runId + \" - yield \" + stepId++, gen.next).call(gen, res);\n                }\n                catch (e) {\n                    return reject(e);\n                }\n                next(ret);\n            }\n            function onRejected(err) {\n                pendingPromise = undefined;\n                var ret;\n                try {\n                    ret = action(name + \" - runid: \" + runId + \" - yield \" + stepId++, gen.throw).call(gen, err);\n                }\n                catch (e) {\n                    return reject(e);\n                }\n                next(ret);\n            }\n            function next(ret) {\n                if (ret && typeof ret.then === \"function\") {\n                    // an async iterator\n                    ret.then(next, reject);\n                    return;\n                }\n                if (ret.done)\n                    return resolve(ret.value);\n                pendingPromise = Promise.resolve(ret.value);\n                return pendingPromise.then(onFulfilled, onRejected);\n            }\n            onFulfilled(undefined); // kick off the process\n        });\n        res.cancel = action(name + \" - runid: \" + runId + \" - cancel\", function () {\n            try {\n                if (pendingPromise)\n                    cancelPromise(pendingPromise);\n                // Finally block can return (or yield) stuff..\n                var res_1 = gen.return(undefined);\n                // eat anything that promise would do, it's cancelled!\n                var yieldedPromise = Promise.resolve(res_1.value);\n                yieldedPromise.then(noop, noop);\n                cancelPromise(yieldedPromise); // maybe it can be cancelled :)\n                // reject our original promise\n                rejector(new FlowCancellationError());\n            }\n            catch (e) {\n                rejector(e); // there could be a throwing finally block\n            }\n        });\n        return res;\n    };\n}\nfunction cancelPromise(promise) {\n    if (typeof promise.cancel === \"function\")\n        promise.cancel();\n}\n\nfunction interceptReads(thing, propOrHandler, handler) {\n    var target;\n    if (isObservableMap(thing) || isObservableArray(thing) || isObservableValue(thing)) {\n        target = getAdministration(thing);\n    }\n    else if (isObservableObject(thing)) {\n        if (typeof propOrHandler !== \"string\")\n            return fail(process.env.NODE_ENV !== \"production\" &&\n                \"InterceptReads can only be used with a specific property, not with an object in general\");\n        target = getAdministration(thing, propOrHandler);\n    }\n    else {\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"Expected observable map, object or array as first array\");\n    }\n    if (target.dehancer !== undefined)\n        return fail(process.env.NODE_ENV !== \"production\" && \"An intercept reader was already established\");\n    target.dehancer = typeof propOrHandler === \"function\" ? propOrHandler : handler;\n    return function () {\n        target.dehancer = undefined;\n    };\n}\n\nfunction intercept(thing, propOrHandler, handler) {\n    if (typeof handler === \"function\")\n        return interceptProperty(thing, propOrHandler, handler);\n    else\n        return interceptInterceptable(thing, propOrHandler);\n}\nfunction interceptInterceptable(thing, handler) {\n    return getAdministration(thing).intercept(handler);\n}\nfunction interceptProperty(thing, property, handler) {\n    return getAdministration(thing, property).intercept(handler);\n}\n\nfunction _isComputed(value, property) {\n    if (value === null || value === undefined)\n        return false;\n    if (property !== undefined) {\n        if (isObservableObject(value) === false)\n            return false;\n        if (!value.$mobx.values[property])\n            return false;\n        var atom = getAtom(value, property);\n        return isComputedValue(atom);\n    }\n    return isComputedValue(value);\n}\nfunction isComputed(value) {\n    if (arguments.length > 1)\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"isComputed expects only 1 argument. Use isObservableProp to inspect the observability of a property\");\n    return _isComputed(value);\n}\nfunction isComputedProp(value, propName) {\n    if (typeof propName !== \"string\")\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"isComputed expected a property name as second argument\");\n    return _isComputed(value, propName);\n}\n\nfunction _isObservable(value, property) {\n    if (value === null || value === undefined)\n        return false;\n    if (property !== undefined) {\n        if (process.env.NODE_ENV !== \"production\" &&\n            (isObservableMap(value) || isObservableArray(value)))\n            return fail(\"isObservable(object, propertyName) is not supported for arrays and maps. Use map.has or array.length instead.\");\n        if (isObservableObject(value)) {\n            var o = value.$mobx;\n            return o.values && !!o.values[property];\n        }\n        return false;\n    }\n    // For first check, see #701\n    return (isObservableObject(value) ||\n        !!value.$mobx ||\n        isAtom(value) ||\n        isReaction(value) ||\n        isComputedValue(value));\n}\nfunction isObservable(value) {\n    if (arguments.length !== 1)\n        fail(process.env.NODE_ENV !== \"production\" &&\n            \"isObservable expects only 1 argument. Use isObservableProp to inspect the observability of a property\");\n    return _isObservable(value);\n}\nfunction isObservableProp(value, propName) {\n    if (typeof propName !== \"string\")\n        return fail(process.env.NODE_ENV !== \"production\" && \"expected a property name as second argument\");\n    return _isObservable(value, propName);\n}\n\nfunction keys(obj) {\n    if (isObservableObject(obj)) {\n        return obj.$mobx.getKeys();\n    }\n    if (isObservableMap(obj)) {\n        return iteratorToArray(obj.keys());\n    }\n    if (isObservableSet(obj)) {\n        return iteratorToArray(obj.keys());\n    }\n    if (isObservableArray(obj)) {\n        return obj.map(function (_, index) { return index; });\n    }\n    return fail(process.env.NODE_ENV !== \"production\" &&\n        \"'keys()' can only be used on observable objects, arrays, sets and maps\");\n}\nfunction values(obj) {\n    if (isObservableObject(obj)) {\n        return keys(obj).map(function (key) { return obj[key]; });\n    }\n    if (isObservableMap(obj)) {\n        return keys(obj).map(function (key) { return obj.get(key); });\n    }\n    if (isObservableSet(obj)) {\n        return iteratorToArray(obj.values());\n    }\n    if (isObservableArray(obj)) {\n        return obj.slice();\n    }\n    return fail(process.env.NODE_ENV !== \"production\" &&\n        \"'values()' can only be used on observable objects, arrays, sets and maps\");\n}\nfunction entries(obj) {\n    if (isObservableObject(obj)) {\n        return keys(obj).map(function (key) { return [key, obj[key]]; });\n    }\n    if (isObservableMap(obj)) {\n        return keys(obj).map(function (key) { return [key, obj.get(key)]; });\n    }\n    if (isObservableSet(obj)) {\n        return iteratorToArray(obj.entries());\n    }\n    if (isObservableArray(obj)) {\n        return obj.map(function (key, index) { return [index, key]; });\n    }\n    return fail(process.env.NODE_ENV !== \"production\" &&\n        \"'entries()' can only be used on observable objects, arrays and maps\");\n}\nfunction set(obj, key, value) {\n    if (arguments.length === 2 && !isObservableSet(obj)) {\n        startBatch();\n        var values_1 = key;\n        try {\n            for (var key_1 in values_1)\n                set(obj, key_1, values_1[key_1]);\n        }\n        finally {\n            endBatch();\n        }\n        return;\n    }\n    if (isObservableObject(obj)) {\n        var adm = obj.$mobx;\n        var existingObservable = adm.values[key];\n        if (existingObservable) {\n            adm.write(obj, key, value);\n        }\n        else {\n            defineObservableProperty(obj, key, value, adm.defaultEnhancer);\n        }\n    }\n    else if (isObservableMap(obj)) {\n        obj.set(key, value);\n    }\n    else if (isObservableSet(obj)) {\n        obj.add(key);\n    }\n    else if (isObservableArray(obj)) {\n        if (typeof key !== \"number\")\n            key = parseInt(key, 10);\n        invariant(key >= 0, \"Not a valid index: '\" + key + \"'\");\n        startBatch();\n        if (key >= obj.length)\n            obj.length = key + 1;\n        obj[key] = value;\n        endBatch();\n    }\n    else {\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"'set()' can only be used on observable objects, arrays and maps\");\n    }\n}\nfunction remove(obj, key) {\n    if (isObservableObject(obj)) {\n        obj.$mobx.remove(key);\n    }\n    else if (isObservableMap(obj)) {\n        obj.delete(key);\n    }\n    else if (isObservableSet(obj)) {\n        obj.delete(key);\n    }\n    else if (isObservableArray(obj)) {\n        if (typeof key !== \"number\")\n            key = parseInt(key, 10);\n        invariant(key >= 0, \"Not a valid index: '\" + key + \"'\");\n        obj.splice(key, 1);\n    }\n    else {\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"'remove()' can only be used on observable objects, arrays and maps\");\n    }\n}\nfunction has(obj, key) {\n    if (isObservableObject(obj)) {\n        // return keys(obj).indexOf(key) >= 0\n        var adm = getAdministration(obj);\n        adm.getKeys(); // make sure we get notified of key changes, but for performance, use the values map to look up existence\n        return !!adm.values[key];\n    }\n    else if (isObservableMap(obj)) {\n        return obj.has(key);\n    }\n    else if (isObservableSet(obj)) {\n        return obj.has(key);\n    }\n    else if (isObservableArray(obj)) {\n        return key >= 0 && key < obj.length;\n    }\n    else {\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"'has()' can only be used on observable objects, arrays and maps\");\n    }\n}\nfunction get(obj, key) {\n    if (!has(obj, key))\n        return undefined;\n    if (isObservableObject(obj)) {\n        return obj[key];\n    }\n    else if (isObservableMap(obj)) {\n        return obj.get(key);\n    }\n    else if (isObservableArray(obj)) {\n        return obj[key];\n    }\n    else {\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"'get()' can only be used on observable objects, arrays and maps\");\n    }\n}\n\nfunction observe(thing, propOrCb, cbOrFire, fireImmediately) {\n    if (typeof cbOrFire === \"function\")\n        return observeObservableProperty(thing, propOrCb, cbOrFire, fireImmediately);\n    else\n        return observeObservable(thing, propOrCb, cbOrFire);\n}\nfunction observeObservable(thing, listener, fireImmediately) {\n    return getAdministration(thing).observe(listener, fireImmediately);\n}\nfunction observeObservableProperty(thing, property, listener, fireImmediately) {\n    return getAdministration(thing, property).observe(listener, fireImmediately);\n}\n\nvar defaultOptions = {\n    detectCycles: true,\n    exportMapsAsObjects: true,\n    recurseEverything: false\n};\nfunction cache(map, key, value, options) {\n    if (options.detectCycles)\n        map.set(key, value);\n    return value;\n}\nfunction toJSHelper(source, options, __alreadySeen) {\n    if (!options.recurseEverything && !isObservable(source))\n        return source;\n    if (typeof source !== \"object\")\n        return source;\n    // Directly return null if source is null\n    if (source === null)\n        return null;\n    // Directly return the Date object itself if contained in the observable\n    if (source instanceof Date)\n        return source;\n    if (isObservableValue(source))\n        return toJSHelper(source.get(), options, __alreadySeen);\n    // make sure we track the keys of the object\n    if (isObservable(source))\n        keys(source);\n    var detectCycles = options.detectCycles === true;\n    if (detectCycles && source !== null && __alreadySeen.has(source)) {\n        return __alreadySeen.get(source);\n    }\n    if (isObservableArray(source) || Array.isArray(source)) {\n        var res_1 = cache(__alreadySeen, source, [], options);\n        var toAdd = source.map(function (value) { return toJSHelper(value, options, __alreadySeen); });\n        res_1.length = toAdd.length;\n        for (var i = 0, l = toAdd.length; i < l; i++)\n            res_1[i] = toAdd[i];\n        return res_1;\n    }\n    if (isObservableSet(source) || Object.getPrototypeOf(source) === Set.prototype) {\n        if (options.exportMapsAsObjects === false) {\n            var res_2 = cache(__alreadySeen, source, new Set(), options);\n            source.forEach(function (value) {\n                res_2.add(toJSHelper(value, options, __alreadySeen));\n            });\n            return res_2;\n        }\n        else {\n            var res_3 = cache(__alreadySeen, source, [], options);\n            source.forEach(function (value) {\n                res_3.push(toJSHelper(value, options, __alreadySeen));\n            });\n            return res_3;\n        }\n    }\n    if (isObservableMap(source) || Object.getPrototypeOf(source) === Map.prototype) {\n        if (options.exportMapsAsObjects === false) {\n            var res_4 = cache(__alreadySeen, source, new Map(), options);\n            source.forEach(function (value, key) {\n                res_4.set(key, toJSHelper(value, options, __alreadySeen));\n            });\n            return res_4;\n        }\n        else {\n            var res_5 = cache(__alreadySeen, source, {}, options);\n            source.forEach(function (value, key) {\n                res_5[key] = toJSHelper(value, options, __alreadySeen);\n            });\n            return res_5;\n        }\n    }\n    // Fallback to the situation that source is an ObservableObject or a plain object\n    var res = cache(__alreadySeen, source, {}, options);\n    for (var key in source) {\n        res[key] = toJSHelper(source[key], options, __alreadySeen);\n    }\n    return res;\n}\nfunction toJS(source, options) {\n    // backward compatibility\n    if (typeof options === \"boolean\")\n        options = { detectCycles: options };\n    if (!options)\n        options = defaultOptions;\n    options.detectCycles =\n        options.detectCycles === undefined\n            ? options.recurseEverything === true\n            : options.detectCycles === true;\n    var __alreadySeen;\n    if (options.detectCycles)\n        __alreadySeen = new Map();\n    return toJSHelper(source, options, __alreadySeen);\n}\n\nfunction trace() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var enterBreakPoint = false;\n    if (typeof args[args.length - 1] === \"boolean\")\n        enterBreakPoint = args.pop();\n    var derivation = getAtomFromArgs(args);\n    if (!derivation) {\n        return fail(process.env.NODE_ENV !== \"production\" &&\n            \"'trace(break?)' can only be used inside a tracked computed value or a Reaction. Consider passing in the computed value or reaction explicitly\");\n    }\n    if (derivation.isTracing === TraceMode.NONE) {\n        console.log(\"[mobx.trace] '\" + derivation.name + \"' tracing enabled\");\n    }\n    derivation.isTracing = enterBreakPoint ? TraceMode.BREAK : TraceMode.LOG;\n}\nfunction getAtomFromArgs(args) {\n    switch (args.length) {\n        case 0:\n            return globalState.trackingDerivation;\n        case 1:\n            return getAtom(args[0]);\n        case 2:\n            return getAtom(args[0], args[1]);\n    }\n}\n\n/**\n * During a transaction no views are updated until the end of the transaction.\n * The transaction will be run synchronously nonetheless.\n *\n * @param action a function that updates some reactive state\n * @returns any value that was returned by the 'action' parameter.\n */\nfunction transaction(action, thisArg) {\n    if (thisArg === void 0) { thisArg = undefined; }\n    startBatch();\n    try {\n        return action.apply(thisArg);\n    }\n    finally {\n        endBatch();\n    }\n}\n\nfunction when(predicate, arg1, arg2) {\n    if (arguments.length === 1 || (arg1 && typeof arg1 === \"object\"))\n        return whenPromise(predicate, arg1);\n    return _when(predicate, arg1, arg2 || {});\n}\nfunction _when(predicate, effect, opts) {\n    var timeoutHandle;\n    if (typeof opts.timeout === \"number\") {\n        timeoutHandle = setTimeout(function () {\n            if (!disposer.$mobx.isDisposed) {\n                disposer();\n                var error = new Error(\"WHEN_TIMEOUT\");\n                if (opts.onError)\n                    opts.onError(error);\n                else\n                    throw error;\n            }\n        }, opts.timeout);\n    }\n    opts.name = opts.name || \"When@\" + getNextId();\n    var effectAction = createAction(opts.name + \"-effect\", effect);\n    var disposer = autorun(function (r) {\n        if (predicate()) {\n            r.dispose();\n            if (timeoutHandle)\n                clearTimeout(timeoutHandle);\n            effectAction();\n        }\n    }, opts);\n    return disposer;\n}\nfunction whenPromise(predicate, opts) {\n    if (process.env.NODE_ENV !== \"production\" && opts && opts.onError)\n        return fail(\"the options 'onError' and 'promise' cannot be combined\");\n    var cancel;\n    var res = new Promise(function (resolve, reject) {\n        var disposer = _when(predicate, resolve, __assign(__assign({}, opts), { onError: reject }));\n        cancel = function () {\n            disposer();\n            reject(\"WHEN_CANCELLED\");\n        };\n    });\n    res.cancel = cancel;\n    return res;\n}\n\nfunction hasInterceptors(interceptable) {\n    return interceptable.interceptors !== undefined && interceptable.interceptors.length > 0;\n}\nfunction registerInterceptor(interceptable, handler) {\n    var interceptors = interceptable.interceptors || (interceptable.interceptors = []);\n    interceptors.push(handler);\n    return once(function () {\n        var idx = interceptors.indexOf(handler);\n        if (idx !== -1)\n            interceptors.splice(idx, 1);\n    });\n}\nfunction interceptChange(interceptable, change) {\n    var prevU = untrackedStart();\n    try {\n        var interceptors = interceptable.interceptors;\n        if (interceptors)\n            for (var i = 0, l = interceptors.length; i < l; i++) {\n                change = interceptors[i](change);\n                invariant(!change || change.type, \"Intercept handlers should return nothing or a change object\");\n                if (!change)\n                    break;\n            }\n        return change;\n    }\n    finally {\n        untrackedEnd(prevU);\n    }\n}\n\nfunction hasListeners(listenable) {\n    return listenable.changeListeners !== undefined && listenable.changeListeners.length > 0;\n}\nfunction registerListener(listenable, handler) {\n    var listeners = listenable.changeListeners || (listenable.changeListeners = []);\n    listeners.push(handler);\n    return once(function () {\n        var idx = listeners.indexOf(handler);\n        if (idx !== -1)\n            listeners.splice(idx, 1);\n    });\n}\nfunction notifyListeners(listenable, change) {\n    var prevU = untrackedStart();\n    var listeners = listenable.changeListeners;\n    if (!listeners)\n        return;\n    listeners = listeners.slice();\n    for (var i = 0, l = listeners.length; i < l; i++) {\n        listeners[i](change);\n    }\n    untrackedEnd(prevU);\n}\n\nvar MAX_SPLICE_SIZE = 10000; // See e.g. https://github.com/mobxjs/mobx/issues/859\n// Detects bug in safari 9.1.1 (or iOS 9 safari mobile). See #364\nvar safariPrototypeSetterInheritanceBug = (function () {\n    var v = false;\n    var p = {};\n    Object.defineProperty(p, \"0\", {\n        set: function () {\n            v = true;\n        }\n    });\n    Object.create(p)[\"0\"] = 1;\n    return v === false;\n})();\n/**\n * This array buffer contains two lists of properties, so that all arrays\n * can recycle their property definitions, which significantly improves performance of creating\n * properties on the fly.\n */\nvar OBSERVABLE_ARRAY_BUFFER_SIZE = 0;\n// Typescript workaround to make sure ObservableArray extends Array\nvar StubArray = /** @class */ (function () {\n    function StubArray() {\n    }\n    return StubArray;\n}());\nfunction inherit(ctor, proto) {\n    if (typeof Object[\"setPrototypeOf\"] !== \"undefined\") {\n        Object[\"setPrototypeOf\"](ctor.prototype, proto);\n    }\n    else if (typeof ctor.prototype.__proto__ !== \"undefined\") {\n        ctor.prototype.__proto__ = proto;\n    }\n    else {\n        ctor[\"prototype\"] = proto;\n    }\n}\ninherit(StubArray, Array.prototype);\n// Weex freeze Array.prototype\n// Make them writeable and configurable in prototype chain\n// https://github.com/alibaba/weex/pull/1529\nif (Object.isFrozen(Array)) {\n    [\n        \"constructor\",\n        \"push\",\n        \"shift\",\n        \"concat\",\n        \"pop\",\n        \"unshift\",\n        \"replace\",\n        \"find\",\n        \"findIndex\",\n        \"splice\",\n        \"reverse\",\n        \"sort\"\n    ].forEach(function (key) {\n        Object.defineProperty(StubArray.prototype, key, {\n            configurable: true,\n            writable: true,\n            value: Array.prototype[key]\n        });\n    });\n}\nvar ObservableArrayAdministration = /** @class */ (function () {\n    function ObservableArrayAdministration(name, enhancer, array, owned) {\n        this.array = array;\n        this.owned = owned;\n        this.values = [];\n        this.lastKnownLength = 0;\n        this.atom = new Atom(name || \"ObservableArray@\" + getNextId());\n        this.enhancer = function (newV, oldV) { return enhancer(newV, oldV, name + \"[..]\"); };\n    }\n    ObservableArrayAdministration.prototype.dehanceValue = function (value) {\n        if (this.dehancer !== undefined)\n            return this.dehancer(value);\n        return value;\n    };\n    ObservableArrayAdministration.prototype.dehanceValues = function (values) {\n        if (this.dehancer !== undefined && values.length > 0)\n            return values.map(this.dehancer);\n        return values;\n    };\n    ObservableArrayAdministration.prototype.intercept = function (handler) {\n        return registerInterceptor(this, handler);\n    };\n    ObservableArrayAdministration.prototype.observe = function (listener, fireImmediately) {\n        if (fireImmediately === void 0) { fireImmediately = false; }\n        if (fireImmediately) {\n            listener({\n                object: this.array,\n                type: \"splice\",\n                index: 0,\n                added: this.values.slice(),\n                addedCount: this.values.length,\n                removed: [],\n                removedCount: 0\n            });\n        }\n        return registerListener(this, listener);\n    };\n    ObservableArrayAdministration.prototype.getArrayLength = function () {\n        this.atom.reportObserved();\n        return this.values.length;\n    };\n    ObservableArrayAdministration.prototype.setArrayLength = function (newLength) {\n        if (typeof newLength !== \"number\" || newLength < 0)\n            throw new Error(\"[mobx.array] Out of range: \" + newLength);\n        var currentLength = this.values.length;\n        if (newLength === currentLength)\n            return;\n        else if (newLength > currentLength) {\n            var newItems = new Array(newLength - currentLength);\n            for (var i = 0; i < newLength - currentLength; i++)\n                newItems[i] = undefined; // No Array.fill everywhere...\n            this.spliceWithArray(currentLength, 0, newItems);\n        }\n        else\n            this.spliceWithArray(newLength, currentLength - newLength);\n    };\n    // adds / removes the necessary numeric properties to this object\n    ObservableArrayAdministration.prototype.updateArrayLength = function (oldLength, delta) {\n        if (oldLength !== this.lastKnownLength)\n            throw new Error(\"[mobx] Modification exception: the internal structure of an observable array was changed. Did you use peek() to change it?\");\n        this.lastKnownLength += delta;\n        if (delta > 0 && oldLength + delta + 1 > OBSERVABLE_ARRAY_BUFFER_SIZE)\n            reserveArrayBuffer(oldLength + delta + 1);\n    };\n    ObservableArrayAdministration.prototype.spliceWithArray = function (index, deleteCount, newItems) {\n        var _this = this;\n        checkIfStateModificationsAreAllowed(this.atom);\n        var length = this.values.length;\n        if (index === undefined)\n            index = 0;\n        else if (index > length)\n            index = length;\n        else if (index < 0)\n            index = Math.max(0, length + index);\n        if (arguments.length === 1)\n            deleteCount = length - index;\n        else if (deleteCount === undefined || deleteCount === null)\n            deleteCount = 0;\n        else\n            deleteCount = Math.max(0, Math.min(deleteCount, length - index));\n        if (newItems === undefined)\n            newItems = EMPTY_ARRAY;\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                object: this.array,\n                type: \"splice\",\n                index: index,\n                removedCount: deleteCount,\n                added: newItems\n            });\n            if (!change)\n                return EMPTY_ARRAY;\n            deleteCount = change.removedCount;\n            newItems = change.added;\n        }\n        newItems =\n            newItems.length === 0 ? newItems : newItems.map(function (v) { return _this.enhancer(v, undefined); });\n        var lengthDelta = newItems.length - deleteCount;\n        this.updateArrayLength(length, lengthDelta); // create or remove new entries\n        var res = this.spliceItemsIntoValues(index, deleteCount, newItems);\n        if (deleteCount !== 0 || newItems.length !== 0)\n            this.notifyArraySplice(index, newItems, res);\n        return this.dehanceValues(res);\n    };\n    ObservableArrayAdministration.prototype.spliceItemsIntoValues = function (index, deleteCount, newItems) {\n        var _a;\n        if (newItems.length < MAX_SPLICE_SIZE) {\n            return (_a = this.values).splice.apply(_a, __spread([index, deleteCount], newItems));\n        }\n        else {\n            var res = this.values.slice(index, index + deleteCount);\n            this.values = this.values\n                .slice(0, index)\n                .concat(newItems, this.values.slice(index + deleteCount));\n            return res;\n        }\n    };\n    ObservableArrayAdministration.prototype.notifyArrayChildUpdate = function (index, newValue, oldValue) {\n        var notifySpy = !this.owned && isSpyEnabled();\n        var notify = hasListeners(this);\n        var change = notify || notifySpy\n            ? {\n                object: this.array,\n                type: \"update\",\n                index: index,\n                newValue: newValue,\n                oldValue: oldValue\n            }\n            : null;\n        if (notifySpy)\n            spyReportStart(__assign(__assign({}, change), { name: this.atom.name }));\n        this.atom.reportChanged();\n        if (notify)\n            notifyListeners(this, change);\n        if (notifySpy)\n            spyReportEnd();\n    };\n    ObservableArrayAdministration.prototype.notifyArraySplice = function (index, added, removed) {\n        var notifySpy = !this.owned && isSpyEnabled();\n        var notify = hasListeners(this);\n        var change = notify || notifySpy\n            ? {\n                object: this.array,\n                type: \"splice\",\n                index: index,\n                removed: removed,\n                added: added,\n                removedCount: removed.length,\n                addedCount: added.length\n            }\n            : null;\n        if (notifySpy)\n            spyReportStart(__assign(__assign({}, change), { name: this.atom.name }));\n        this.atom.reportChanged();\n        // conform: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/observe\n        if (notify)\n            notifyListeners(this, change);\n        if (notifySpy)\n            spyReportEnd();\n    };\n    return ObservableArrayAdministration;\n}());\nvar ObservableArray = /** @class */ (function (_super) {\n    __extends(ObservableArray, _super);\n    function ObservableArray(initialValues, enhancer, name, owned) {\n        if (name === void 0) { name = \"ObservableArray@\" + getNextId(); }\n        if (owned === void 0) { owned = false; }\n        var _this = _super.call(this) || this;\n        var adm = new ObservableArrayAdministration(name, enhancer, _this, owned);\n        addHiddenFinalProp(_this, \"$mobx\", adm);\n        if (initialValues && initialValues.length) {\n            var prev = allowStateChangesStart(true);\n            _this.spliceWithArray(0, 0, initialValues);\n            allowStateChangesEnd(prev);\n        }\n        if (safariPrototypeSetterInheritanceBug) {\n            // Seems that Safari won't use numeric prototype setter untill any * numeric property is\n            // defined on the instance. After that it works fine, even if this property is deleted.\n            Object.defineProperty(adm.array, \"0\", ENTRY_0);\n        }\n        return _this;\n    }\n    ObservableArray.prototype.intercept = function (handler) {\n        return this.$mobx.intercept(handler);\n    };\n    ObservableArray.prototype.observe = function (listener, fireImmediately) {\n        if (fireImmediately === void 0) { fireImmediately = false; }\n        return this.$mobx.observe(listener, fireImmediately);\n    };\n    ObservableArray.prototype.clear = function () {\n        return this.splice(0);\n    };\n    ObservableArray.prototype.concat = function () {\n        var arrays = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            arrays[_i] = arguments[_i];\n        }\n        this.$mobx.atom.reportObserved();\n        return Array.prototype.concat.apply(this.peek(), arrays.map(function (a) { return (isObservableArray(a) ? a.peek() : a); }));\n    };\n    ObservableArray.prototype.replace = function (newItems) {\n        return this.$mobx.spliceWithArray(0, this.$mobx.values.length, newItems);\n    };\n    /**\n     * Converts this array back to a (shallow) javascript structure.\n     * For a deep clone use mobx.toJS\n     */\n    ObservableArray.prototype.toJS = function () {\n        return this.slice();\n    };\n    ObservableArray.prototype.toJSON = function () {\n        // Used by JSON.stringify\n        return this.toJS();\n    };\n    ObservableArray.prototype.peek = function () {\n        this.$mobx.atom.reportObserved();\n        return this.$mobx.dehanceValues(this.$mobx.values);\n    };\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find\n    ObservableArray.prototype.find = function (predicate, thisArg, fromIndex) {\n        if (fromIndex === void 0) { fromIndex = 0; }\n        if (arguments.length === 3)\n            deprecated(\"The array.find fromIndex argument to find will not be supported anymore in the next major\");\n        var idx = this.findIndex.apply(this, arguments);\n        return idx === -1 ? undefined : this.get(idx);\n    };\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/findIndex\n    ObservableArray.prototype.findIndex = function (predicate, thisArg, fromIndex) {\n        if (fromIndex === void 0) { fromIndex = 0; }\n        if (arguments.length === 3)\n            deprecated(\"The array.findIndex fromIndex argument to find will not be supported anymore in the next major\");\n        var items = this.peek(), l = items.length;\n        for (var i = fromIndex; i < l; i++)\n            if (predicate.call(thisArg, items[i], i, this))\n                return i;\n        return -1;\n    };\n    /*\n     * functions that do alter the internal structure of the array, (based on lib.es6.d.ts)\n     * since these functions alter the inner structure of the array, the have side effects.\n     * Because the have side effects, they should not be used in computed function,\n     * and for that reason the do not call dependencyState.notifyObserved\n     */\n    ObservableArray.prototype.splice = function (index, deleteCount) {\n        var newItems = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            newItems[_i - 2] = arguments[_i];\n        }\n        switch (arguments.length) {\n            case 0:\n                return [];\n            case 1:\n                return this.$mobx.spliceWithArray(index);\n            case 2:\n                return this.$mobx.spliceWithArray(index, deleteCount);\n        }\n        return this.$mobx.spliceWithArray(index, deleteCount, newItems);\n    };\n    ObservableArray.prototype.spliceWithArray = function (index, deleteCount, newItems) {\n        return this.$mobx.spliceWithArray(index, deleteCount, newItems);\n    };\n    ObservableArray.prototype.push = function () {\n        var items = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            items[_i] = arguments[_i];\n        }\n        var adm = this.$mobx;\n        adm.spliceWithArray(adm.values.length, 0, items);\n        return adm.values.length;\n    };\n    ObservableArray.prototype.pop = function () {\n        return this.splice(Math.max(this.$mobx.values.length - 1, 0), 1)[0];\n    };\n    ObservableArray.prototype.shift = function () {\n        return this.splice(0, 1)[0];\n    };\n    ObservableArray.prototype.unshift = function () {\n        var items = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            items[_i] = arguments[_i];\n        }\n        var adm = this.$mobx;\n        adm.spliceWithArray(0, 0, items);\n        return adm.values.length;\n    };\n    ObservableArray.prototype.reverse = function () {\n        // reverse by default mutates in place before returning the result\n        // which makes it both a 'derivation' and a 'mutation'.\n        // so we deviate from the default and just make it an dervitation\n        var clone = this.slice();\n        return clone.reverse.apply(clone, arguments);\n    };\n    ObservableArray.prototype.sort = function (compareFn) {\n        // sort by default mutates in place before returning the result\n        // which goes against all good practices. Let's not change the array in place!\n        var clone = this.slice();\n        return clone.sort.apply(clone, arguments);\n    };\n    ObservableArray.prototype.remove = function (value) {\n        var idx = this.$mobx.dehanceValues(this.$mobx.values).indexOf(value);\n        if (idx > -1) {\n            this.splice(idx, 1);\n            return true;\n        }\n        return false;\n    };\n    ObservableArray.prototype.move = function (fromIndex, toIndex) {\n        deprecated(\"observableArray.move is deprecated, use .slice() & .replace() instead\");\n        function checkIndex(index) {\n            if (index < 0) {\n                throw new Error(\"[mobx.array] Index out of bounds: \" + index + \" is negative\");\n            }\n            var length = this.$mobx.values.length;\n            if (index >= length) {\n                throw new Error(\"[mobx.array] Index out of bounds: \" + index + \" is not smaller than \" + length);\n            }\n        }\n        checkIndex.call(this, fromIndex);\n        checkIndex.call(this, toIndex);\n        if (fromIndex === toIndex) {\n            return;\n        }\n        var oldItems = this.$mobx.values;\n        var newItems;\n        if (fromIndex < toIndex) {\n            newItems = __spread(oldItems.slice(0, fromIndex), oldItems.slice(fromIndex + 1, toIndex + 1), [\n                oldItems[fromIndex]\n            ], oldItems.slice(toIndex + 1));\n        }\n        else {\n            // toIndex < fromIndex\n            newItems = __spread(oldItems.slice(0, toIndex), [\n                oldItems[fromIndex]\n            ], oldItems.slice(toIndex, fromIndex), oldItems.slice(fromIndex + 1));\n        }\n        this.replace(newItems);\n    };\n    // See #734, in case property accessors are unreliable...\n    ObservableArray.prototype.get = function (index) {\n        var impl = this.$mobx;\n        if (impl) {\n            if (index < impl.values.length) {\n                impl.atom.reportObserved();\n                return impl.dehanceValue(impl.values[index]);\n            }\n            console.warn(\"[mobx.array] Attempt to read an array index (\" + index + \") that is out of bounds (\" + impl.values.length + \"). Please check length first. Out of bound indices will not be tracked by MobX\");\n        }\n        return undefined;\n    };\n    // See #734, in case property accessors are unreliable...\n    ObservableArray.prototype.set = function (index, newValue) {\n        var adm = this.$mobx;\n        var values = adm.values;\n        if (index < values.length) {\n            // update at index in range\n            checkIfStateModificationsAreAllowed(adm.atom);\n            var oldValue = values[index];\n            if (hasInterceptors(adm)) {\n                var change = interceptChange(adm, {\n                    type: \"update\",\n                    object: this,\n                    index: index,\n                    newValue: newValue\n                });\n                if (!change)\n                    return;\n                newValue = change.newValue;\n            }\n            newValue = adm.enhancer(newValue, oldValue);\n            var changed = newValue !== oldValue;\n            if (changed) {\n                values[index] = newValue;\n                adm.notifyArrayChildUpdate(index, newValue, oldValue);\n            }\n        }\n        else if (index === values.length) {\n            // add a new item\n            adm.spliceWithArray(index, 0, [newValue]);\n        }\n        else {\n            // out of bounds\n            throw new Error(\"[mobx.array] Index out of bounds, \" + index + \" is larger than \" + values.length);\n        }\n    };\n    return ObservableArray;\n}(StubArray));\ndeclareIterator(ObservableArray.prototype, function () {\n    this.$mobx.atom.reportObserved();\n    var self = this;\n    var nextIndex = 0;\n    return makeIterable({\n        next: function () {\n            return nextIndex < self.length\n                ? { value: self[nextIndex++], done: false }\n                : { done: true, value: undefined };\n        }\n    });\n});\nObject.defineProperty(ObservableArray.prototype, \"length\", {\n    enumerable: false,\n    configurable: true,\n    get: function () {\n        return this.$mobx.getArrayLength();\n    },\n    set: function (newLength) {\n        this.$mobx.setArrayLength(newLength);\n    }\n});\naddHiddenProp(ObservableArray.prototype, toStringTagSymbol(), \"Array\");\n[\"indexOf\", \"join\", \"lastIndexOf\", \"slice\", \"toString\", \"toLocaleString\"].forEach(function (funcName) {\n    var baseFunc = Array.prototype[funcName];\n    invariant(typeof baseFunc === \"function\", \"Base function not defined on Array prototype: '\" + funcName + \"'\");\n    addHiddenProp(ObservableArray.prototype, funcName, function () {\n        return baseFunc.apply(this.peek(), arguments);\n    });\n});\n[\n    \"every\",\n    \"filter\",\n    //\"find\", // implemented individually (IE support)\n    //\"findIndex\", // implemented individually (IE support)\n    //\"flatMap\", // not supported\n    \"forEach\",\n    \"map\",\n    \"some\"\n].forEach(function (funcName) {\n    var baseFunc = Array.prototype[funcName];\n    invariant(typeof baseFunc === \"function\", \"Base function not defined on Array prototype: '\" + funcName + \"'\");\n    addHiddenProp(ObservableArray.prototype, funcName, function (callback, thisArg) {\n        var _this = this;\n        var adm = this.$mobx;\n        adm.atom.reportObserved();\n        var dehancedValues = adm.dehanceValues(adm.values);\n        return dehancedValues[funcName](function (element, index) {\n            return callback.call(thisArg, element, index, _this);\n        }, thisArg);\n    });\n});\n[\"reduce\", \"reduceRight\"].forEach(function (funcName) {\n    addHiddenProp(ObservableArray.prototype, funcName, function () {\n        var _this = this;\n        var adm = this.$mobx;\n        adm.atom.reportObserved();\n        // #2432 - reduce behavior depends on arguments.length\n        var callback = arguments[0];\n        arguments[0] = function (accumulator, currentValue, index) {\n            currentValue = adm.dehanceValue(currentValue);\n            return callback(accumulator, currentValue, index, _this);\n        };\n        return adm.values[funcName].apply(adm.values, arguments);\n    });\n});\n/**\n * We don't want those to show up in `for (const key in ar)` ...\n */\nmakeNonEnumerable(ObservableArray.prototype, [\n    \"constructor\",\n    \"intercept\",\n    \"observe\",\n    \"clear\",\n    \"concat\",\n    \"get\",\n    \"replace\",\n    \"toJS\",\n    \"toJSON\",\n    \"peek\",\n    \"find\",\n    \"findIndex\",\n    \"splice\",\n    \"spliceWithArray\",\n    \"push\",\n    \"pop\",\n    \"set\",\n    \"shift\",\n    \"unshift\",\n    \"reverse\",\n    \"sort\",\n    \"remove\",\n    \"move\",\n    \"toString\",\n    \"toLocaleString\"\n]);\n// See #364\nvar ENTRY_0 = createArrayEntryDescriptor(0);\nfunction createArrayEntryDescriptor(index) {\n    return {\n        enumerable: false,\n        configurable: false,\n        get: function () {\n            return this.get(index);\n        },\n        set: function (value) {\n            this.set(index, value);\n        }\n    };\n}\nfunction createArrayBufferItem(index) {\n    Object.defineProperty(ObservableArray.prototype, \"\" + index, createArrayEntryDescriptor(index));\n}\nfunction reserveArrayBuffer(max) {\n    for (var index = OBSERVABLE_ARRAY_BUFFER_SIZE; index < max; index++)\n        createArrayBufferItem(index);\n    OBSERVABLE_ARRAY_BUFFER_SIZE = max;\n}\nreserveArrayBuffer(1000);\nvar isObservableArrayAdministration = createInstanceofPredicate(\"ObservableArrayAdministration\", ObservableArrayAdministration);\nfunction isObservableArray(thing) {\n    return isObject(thing) && isObservableArrayAdministration(thing.$mobx);\n}\n\nvar ObservableMapMarker = {};\nvar ObservableMap = /** @class */ (function () {\n    function ObservableMap(initialData, enhancer, name) {\n        if (enhancer === void 0) { enhancer = deepEnhancer; }\n        if (name === void 0) { name = \"ObservableMap@\" + getNextId(); }\n        this.enhancer = enhancer;\n        this.name = name;\n        this.$mobx = ObservableMapMarker;\n        this._keysAtom = createAtom(this.name + \".keys()\");\n        if (typeof Map !== \"function\") {\n            throw new Error(\"mobx.map requires Map polyfill for the current browser. Check babel-polyfill or core-js/es6/map.js\");\n        }\n        this._data = new Map();\n        this._hasMap = new Map();\n        this.merge(initialData);\n    }\n    ObservableMap.prototype._has = function (key) {\n        return this._data.has(key);\n    };\n    ObservableMap.prototype.has = function (key) {\n        var _this = this;\n        if (!globalState.trackingDerivation)\n            return this._has(key);\n        var entry = this._hasMap.get(key);\n        if (!entry) {\n            // todo: replace with atom (breaking change)\n            var newEntry = (entry = new ObservableValue(this._has(key), referenceEnhancer, this.name + \".\" + stringifyKey(key) + \"?\", false));\n            this._hasMap.set(key, newEntry);\n            onBecomeUnobserved(newEntry, function () { return _this._hasMap.delete(key); });\n        }\n        return entry.get();\n    };\n    ObservableMap.prototype.set = function (key, value) {\n        var hasKey = this._has(key);\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                type: hasKey ? \"update\" : \"add\",\n                object: this,\n                newValue: value,\n                name: key\n            });\n            if (!change)\n                return this;\n            value = change.newValue;\n        }\n        if (hasKey) {\n            this._updateValue(key, value);\n        }\n        else {\n            this._addValue(key, value);\n        }\n        return this;\n    };\n    ObservableMap.prototype.delete = function (key) {\n        var _this = this;\n        checkIfStateModificationsAreAllowed(this._keysAtom);\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                type: \"delete\",\n                object: this,\n                name: key\n            });\n            if (!change)\n                return false;\n        }\n        if (this._has(key)) {\n            var notifySpy = isSpyEnabled();\n            var notify = hasListeners(this);\n            var change = notify || notifySpy\n                ? {\n                    type: \"delete\",\n                    object: this,\n                    oldValue: this._data.get(key).value,\n                    name: key\n                }\n                : null;\n            if (notifySpy)\n                spyReportStart(__assign(__assign({}, change), { name: this.name, key: key }));\n            transaction(function () {\n                _this._keysAtom.reportChanged();\n                _this._updateHasMapEntry(key, false);\n                var observable = _this._data.get(key);\n                observable.setNewValue(undefined);\n                _this._data.delete(key);\n            });\n            if (notify)\n                notifyListeners(this, change);\n            if (notifySpy)\n                spyReportEnd();\n            return true;\n        }\n        return false;\n    };\n    ObservableMap.prototype._updateHasMapEntry = function (key, value) {\n        var entry = this._hasMap.get(key);\n        if (entry) {\n            entry.setNewValue(value);\n        }\n    };\n    ObservableMap.prototype._updateValue = function (key, newValue) {\n        var observable = this._data.get(key);\n        newValue = observable.prepareNewValue(newValue);\n        if (newValue !== globalState.UNCHANGED) {\n            var notifySpy = isSpyEnabled();\n            var notify = hasListeners(this);\n            var change = notify || notifySpy\n                ? {\n                    type: \"update\",\n                    object: this,\n                    oldValue: observable.value,\n                    name: key,\n                    newValue: newValue\n                }\n                : null;\n            if (notifySpy)\n                spyReportStart(__assign(__assign({}, change), { name: this.name, key: key }));\n            observable.setNewValue(newValue);\n            if (notify)\n                notifyListeners(this, change);\n            if (notifySpy)\n                spyReportEnd();\n        }\n    };\n    ObservableMap.prototype._addValue = function (key, newValue) {\n        var _this = this;\n        checkIfStateModificationsAreAllowed(this._keysAtom);\n        transaction(function () {\n            var observable = new ObservableValue(newValue, _this.enhancer, _this.name + \".\" + stringifyKey(key), false);\n            _this._data.set(key, observable);\n            newValue = observable.value; // value might have been changed\n            _this._updateHasMapEntry(key, true);\n            _this._keysAtom.reportChanged();\n        });\n        var notifySpy = isSpyEnabled();\n        var notify = hasListeners(this);\n        var change = notify || notifySpy\n            ? {\n                type: \"add\",\n                object: this,\n                name: key,\n                newValue: newValue\n            }\n            : null;\n        if (notifySpy)\n            spyReportStart(__assign(__assign({}, change), { name: this.name, key: key }));\n        if (notify)\n            notifyListeners(this, change);\n        if (notifySpy)\n            spyReportEnd();\n    };\n    ObservableMap.prototype.get = function (key) {\n        if (this.has(key))\n            return this.dehanceValue(this._data.get(key).get());\n        return this.dehanceValue(undefined);\n    };\n    ObservableMap.prototype.dehanceValue = function (value) {\n        if (this.dehancer !== undefined) {\n            return this.dehancer(value);\n        }\n        return value;\n    };\n    ObservableMap.prototype.keys = function () {\n        this._keysAtom.reportObserved();\n        return this._data.keys();\n    };\n    ObservableMap.prototype.values = function () {\n        var self = this;\n        var keys = this.keys();\n        return makeIterable({\n            next: function () {\n                var _a = keys.next(), done = _a.done, value = _a.value;\n                return {\n                    done: done,\n                    value: done ? undefined : self.get(value)\n                };\n            }\n        });\n    };\n    ObservableMap.prototype.entries = function () {\n        var self = this;\n        var keys = this.keys();\n        return makeIterable({\n            next: function () {\n                var _a = keys.next(), done = _a.done, value = _a.value;\n                return {\n                    done: done,\n                    value: done ? undefined : [value, self.get(value)]\n                };\n            }\n        });\n    };\n    ObservableMap.prototype.forEach = function (callback, thisArg) {\n        var _this = this;\n        this._keysAtom.reportObserved();\n        this._data.forEach(function (_, key) { return callback.call(thisArg, _this.get(key), key, _this); });\n    };\n    /** Merge another object into this object, returns this. */\n    ObservableMap.prototype.merge = function (other) {\n        var _this = this;\n        if (isObservableMap(other)) {\n            other = other.toJS();\n        }\n        transaction(function () {\n            var prev = allowStateChangesStart(true);\n            try {\n                if (isPlainObject(other))\n                    Object.keys(other).forEach(function (key) { return _this.set(key, other[key]); });\n                else if (Array.isArray(other))\n                    other.forEach(function (_a) {\n                        var _b = __read(_a, 2), key = _b[0], value = _b[1];\n                        return _this.set(key, value);\n                    });\n                else if (isES6Map(other)) {\n                    if (other.constructor !== Map)\n                        fail(\"Cannot initialize from classes that inherit from Map: \" + other.constructor.name); // prettier-ignore\n                    else\n                        other.forEach(function (value, key) { return _this.set(key, value); });\n                }\n                else if (other !== null && other !== undefined)\n                    fail(\"Cannot initialize map from \" + other);\n            }\n            finally {\n                allowStateChangesEnd(prev);\n            }\n        });\n        return this;\n    };\n    ObservableMap.prototype.clear = function () {\n        var _this = this;\n        transaction(function () {\n            untracked(function () {\n                // Note we are concurrently reading/deleting the same keys\n                // forEach handles this properly\n                _this._data.forEach(function (_, key) { return _this.delete(key); });\n            });\n        });\n    };\n    ObservableMap.prototype.replace = function (values) {\n        var _this = this;\n        // Implementation requirements:\n        // - respect ordering of replacement map\n        // - allow interceptors to run and potentially prevent individual operations\n        // - don't recreate observables that already exist in original map (so we don't destroy existing subscriptions)\n        // - don't _keysAtom.reportChanged if the keys of resulting map are indentical (order matters!)\n        // - note that result map may differ from replacement map due to the interceptors\n        transaction(function () {\n            // Convert to map so we can do quick key lookups\n            var replacementMap = convertToMap(values);\n            var orderedData = new Map();\n            // Used for optimization\n            var keysReportChangedCalled = false;\n            // Delete keys that don't exist in replacement map\n            // if the key deletion is prevented by interceptor\n            // add entry at the beginning of the result map\n            forOf(_this._data.keys(), function (key) {\n                // Concurrently iterating/deleting keys\n                // iterator should handle this correctly\n                if (!replacementMap.has(key)) {\n                    var deleted = _this.delete(key);\n                    // Was the key removed?\n                    if (deleted) {\n                        // _keysAtom.reportChanged() was already called\n                        keysReportChangedCalled = true;\n                    }\n                    else {\n                        // Delete prevented by interceptor\n                        var value = _this._data.get(key);\n                        orderedData.set(key, value);\n                    }\n                }\n            });\n            // Merge entries\n            forOf(replacementMap.entries(), function (_a) {\n                var _b = __read(_a, 2), key = _b[0], value = _b[1];\n                // We will want to know whether a new key is added\n                var keyExisted = _this._data.has(key);\n                // Add or update value\n                _this.set(key, value);\n                // The addition could have been prevent by interceptor\n                if (_this._data.has(key)) {\n                    // The update could have been prevented by interceptor\n                    // and also we want to preserve existing values\n                    // so use value from _data map (instead of replacement map)\n                    var value_1 = _this._data.get(key);\n                    orderedData.set(key, value_1);\n                    // Was a new key added?\n                    if (!keyExisted) {\n                        // _keysAtom.reportChanged() was already called\n                        keysReportChangedCalled = true;\n                    }\n                }\n            });\n            // Check for possible key order change\n            if (!keysReportChangedCalled) {\n                if (_this._data.size !== orderedData.size) {\n                    // If size differs, keys are definitely modified\n                    _this._keysAtom.reportChanged();\n                }\n                else {\n                    var iter1 = _this._data.keys();\n                    var iter2 = orderedData.keys();\n                    var next1 = iter1.next();\n                    var next2 = iter2.next();\n                    while (!next1.done) {\n                        if (next1.value !== next2.value) {\n                            _this._keysAtom.reportChanged();\n                            break;\n                        }\n                        next1 = iter1.next();\n                        next2 = iter2.next();\n                    }\n                }\n            }\n            // Use correctly ordered map\n            _this._data = orderedData;\n        });\n        return this;\n    };\n    Object.defineProperty(ObservableMap.prototype, \"size\", {\n        get: function () {\n            this._keysAtom.reportObserved();\n            return this._data.size;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    /**\n     * Returns a plain object that represents this map.\n     * Note that all the keys being stringified.\n     * If there are duplicating keys after converting them to strings, behaviour is undetermined.\n     */\n    ObservableMap.prototype.toPOJO = function () {\n        var _this = this;\n        var res = {};\n        this.forEach(function (_, key) {\n            return (res[typeof key === \"symbol\" ? key : stringifyKey(key)] = _this.get(key));\n        });\n        return res;\n    };\n    /**\n     * Returns a shallow non observable object clone of this map.\n     * Note that the values migth still be observable. For a deep clone use mobx.toJS.\n     */\n    ObservableMap.prototype.toJS = function () {\n        return new Map(this);\n    };\n    ObservableMap.prototype.toJSON = function () {\n        // Used by JSON.stringify\n        return this.toPOJO();\n    };\n    ObservableMap.prototype.toString = function () {\n        var _this = this;\n        return (this.name +\n            \"[{ \" +\n            iteratorToArray(this.keys())\n                .map(function (key) { return stringifyKey(key) + \": \" + (\"\" + _this.get(key)); })\n                .join(\", \") +\n            \" }]\");\n    };\n    /**\n     * Observes this object. Triggers for the events 'add', 'update' and 'delete'.\n     * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/observe\n     * for callback details\n     */\n    ObservableMap.prototype.observe = function (listener, fireImmediately) {\n        process.env.NODE_ENV !== \"production\" &&\n            invariant(fireImmediately !== true, \"`observe` doesn't support fireImmediately=true in combination with maps.\");\n        return registerListener(this, listener);\n    };\n    ObservableMap.prototype.intercept = function (handler) {\n        return registerInterceptor(this, handler);\n    };\n    return ObservableMap;\n}());\nfunction stringifyKey(key) {\n    if (key && key.toString)\n        return key.toString();\n    else\n        return new String(key).toString();\n}\ndeclareIterator(ObservableMap.prototype, function () {\n    return this.entries();\n});\naddHiddenFinalProp(ObservableMap.prototype, toStringTagSymbol(), \"Map\");\n/* 'var' fixes small-build issue */\nvar isObservableMap = createInstanceofPredicate(\"ObservableMap\", ObservableMap);\n\nvar ObservableSetMarker = {};\nvar ObservableSet = /** @class */ (function () {\n    function ObservableSet(initialData, enhancer, name) {\n        if (enhancer === void 0) { enhancer = deepEnhancer; }\n        if (name === void 0) { name = \"ObservableSet@\" + getNextId(); }\n        this.name = name;\n        this.$mobx = ObservableSetMarker;\n        this._data = new Set();\n        this._atom = createAtom(this.name);\n        if (typeof Set !== \"function\") {\n            throw new Error(\"mobx.set requires Set polyfill for the current browser. Check babel-polyfill or core-js/es6/set.js\");\n        }\n        this.enhancer = function (newV, oldV) { return enhancer(newV, oldV, name); };\n        if (initialData) {\n            this.replace(initialData);\n        }\n    }\n    ObservableSet.prototype.dehanceValue = function (value) {\n        if (this.dehancer !== undefined) {\n            return this.dehancer(value);\n        }\n        return value;\n    };\n    ObservableSet.prototype.clear = function () {\n        var _this = this;\n        transaction(function () {\n            untracked(function () {\n                _this._data.forEach(function (value) {\n                    _this.delete(value);\n                });\n            });\n        });\n    };\n    ObservableSet.prototype.forEach = function (callbackFn, thisArg) {\n        var _this = this;\n        this._atom.reportObserved();\n        this._data.forEach(function (value) {\n            callbackFn.call(thisArg, value, value, _this);\n        });\n    };\n    Object.defineProperty(ObservableSet.prototype, \"size\", {\n        get: function () {\n            this._atom.reportObserved();\n            return this._data.size;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    ObservableSet.prototype.add = function (value) {\n        var _this = this;\n        checkIfStateModificationsAreAllowed(this._atom);\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                type: \"add\",\n                object: this,\n                newValue: value\n            });\n            if (!change)\n                return this;\n            // TODO: ideally, value = change.value would be done here, so that values can be\n            // changed by interceptor. Same applies for other Set and Map api's.\n        }\n        if (!this.has(value)) {\n            transaction(function () {\n                _this._data.add(_this.enhancer(value, undefined));\n                _this._atom.reportChanged();\n            });\n            var notifySpy = isSpyEnabled();\n            var notify = hasListeners(this);\n            var change = notify || notifySpy\n                ? {\n                    type: \"add\",\n                    object: this,\n                    newValue: value\n                }\n                : null;\n            if (notifySpy && process.env.NODE_ENV !== \"production\")\n                spyReportStart(change);\n            if (notify)\n                notifyListeners(this, change);\n            if (notifySpy && process.env.NODE_ENV !== \"production\")\n                spyReportEnd();\n        }\n        return this;\n    };\n    ObservableSet.prototype.delete = function (value) {\n        var _this = this;\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                type: \"delete\",\n                object: this,\n                oldValue: value\n            });\n            if (!change)\n                return false;\n        }\n        if (this.has(value)) {\n            var notifySpy = isSpyEnabled();\n            var notify = hasListeners(this);\n            var change = notify || notifySpy\n                ? {\n                    type: \"delete\",\n                    object: this,\n                    oldValue: value\n                }\n                : null;\n            if (notifySpy && process.env.NODE_ENV !== \"production\")\n                spyReportStart(__assign(__assign({}, change), { name: this.name }));\n            transaction(function () {\n                _this._atom.reportChanged();\n                _this._data.delete(value);\n            });\n            if (notify)\n                notifyListeners(this, change);\n            if (notifySpy && process.env.NODE_ENV !== \"production\")\n                spyReportEnd();\n            return true;\n        }\n        return false;\n    };\n    ObservableSet.prototype.has = function (value) {\n        this._atom.reportObserved();\n        return this._data.has(this.dehanceValue(value));\n    };\n    ObservableSet.prototype.entries = function () {\n        var nextIndex = 0;\n        var keys = iteratorToArray(this.keys());\n        var values = iteratorToArray(this.values());\n        return makeIterable({\n            next: function () {\n                var index = nextIndex;\n                nextIndex += 1;\n                return index < values.length\n                    ? { value: [keys[index], values[index]], done: false }\n                    : { done: true };\n            }\n        });\n    };\n    ObservableSet.prototype.keys = function () {\n        return this.values();\n    };\n    ObservableSet.prototype.values = function () {\n        this._atom.reportObserved();\n        var self = this;\n        var nextIndex = 0;\n        var observableValues;\n        if (this._data.values !== undefined) {\n            observableValues = iteratorToArray(this._data.values());\n        }\n        else {\n            // There is no values function in IE11\n            observableValues = [];\n            this._data.forEach(function (e) { return observableValues.push(e); });\n        }\n        return makeIterable({\n            next: function () {\n                return nextIndex < observableValues.length\n                    ? { value: self.dehanceValue(observableValues[nextIndex++]), done: false }\n                    : { done: true };\n            }\n        });\n    };\n    ObservableSet.prototype.replace = function (other) {\n        var _this = this;\n        if (isObservableSet(other)) {\n            other = other.toJS();\n        }\n        transaction(function () {\n            var prev = allowStateChangesStart(true);\n            try {\n                if (Array.isArray(other)) {\n                    _this.clear();\n                    other.forEach(function (value) { return _this.add(value); });\n                }\n                else if (isES6Set(other)) {\n                    _this.clear();\n                    other.forEach(function (value) { return _this.add(value); });\n                }\n                else if (other !== null && other !== undefined) {\n                    fail(\"Cannot initialize set from \" + other);\n                }\n            }\n            finally {\n                allowStateChangesEnd(prev);\n            }\n        });\n        return this;\n    };\n    ObservableSet.prototype.observe = function (listener, fireImmediately) {\n        // TODO 'fireImmediately' can be true?\n        process.env.NODE_ENV !== \"production\" &&\n            invariant(fireImmediately !== true, \"`observe` doesn't support fireImmediately=true in combination with sets.\");\n        return registerListener(this, listener);\n    };\n    ObservableSet.prototype.intercept = function (handler) {\n        return registerInterceptor(this, handler);\n    };\n    ObservableSet.prototype.toJS = function () {\n        return new Set(this);\n    };\n    ObservableSet.prototype.toString = function () {\n        return this.name + \"[ \" + iteratorToArray(this.keys()).join(\", \") + \" ]\";\n    };\n    return ObservableSet;\n}());\ndeclareIterator(ObservableSet.prototype, function () {\n    return this.values();\n});\naddHiddenFinalProp(ObservableSet.prototype, toStringTagSymbol(), \"Set\");\nvar isObservableSet = createInstanceofPredicate(\"ObservableSet\", ObservableSet);\n\nvar ObservableObjectAdministration = /** @class */ (function () {\n    function ObservableObjectAdministration(target, name, defaultEnhancer) {\n        this.target = target;\n        this.name = name;\n        this.defaultEnhancer = defaultEnhancer;\n        this.values = {};\n    }\n    ObservableObjectAdministration.prototype.read = function (owner, key) {\n        if (process.env.NODE_ENV === \"production\" && this.target !== owner) {\n            this.illegalAccess(owner, key);\n            if (!this.values[key])\n                return undefined;\n        }\n        return this.values[key].get();\n    };\n    ObservableObjectAdministration.prototype.write = function (owner, key, newValue) {\n        var instance = this.target;\n        if (process.env.NODE_ENV === \"production\" && instance !== owner) {\n            this.illegalAccess(owner, key);\n        }\n        var observable = this.values[key];\n        if (observable instanceof ComputedValue) {\n            observable.set(newValue);\n            return;\n        }\n        // intercept\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                type: \"update\",\n                object: instance,\n                name: key,\n                newValue: newValue\n            });\n            if (!change)\n                return;\n            newValue = change.newValue;\n        }\n        newValue = observable.prepareNewValue(newValue);\n        // notify spy & observers\n        if (newValue !== globalState.UNCHANGED) {\n            var notify = hasListeners(this);\n            var notifySpy = isSpyEnabled();\n            var change = notify || notifySpy\n                ? {\n                    type: \"update\",\n                    object: instance,\n                    oldValue: observable.value,\n                    name: key,\n                    newValue: newValue\n                }\n                : null;\n            if (notifySpy)\n                spyReportStart(__assign(__assign({}, change), { name: this.name, key: key }));\n            observable.setNewValue(newValue);\n            if (notify)\n                notifyListeners(this, change);\n            if (notifySpy)\n                spyReportEnd();\n        }\n    };\n    ObservableObjectAdministration.prototype.remove = function (key) {\n        if (!this.values[key])\n            return;\n        var target = this.target;\n        if (hasInterceptors(this)) {\n            var change = interceptChange(this, {\n                object: target,\n                name: key,\n                type: \"remove\"\n            });\n            if (!change)\n                return;\n        }\n        try {\n            startBatch();\n            var notify = hasListeners(this);\n            var notifySpy = isSpyEnabled();\n            var oldValue = this.values[key].get();\n            if (this.keys)\n                this.keys.remove(key);\n            delete this.values[key];\n            delete this.target[key];\n            var change = notify || notifySpy\n                ? {\n                    type: \"remove\",\n                    object: target,\n                    oldValue: oldValue,\n                    name: key\n                }\n                : null;\n            if (notifySpy)\n                spyReportStart(__assign(__assign({}, change), { name: this.name, key: key }));\n            if (notify)\n                notifyListeners(this, change);\n            if (notifySpy)\n                spyReportEnd();\n        }\n        finally {\n            endBatch();\n        }\n    };\n    ObservableObjectAdministration.prototype.illegalAccess = function (owner, propName) {\n        /**\n         * This happens if a property is accessed through the prototype chain, but the property was\n         * declared directly as own property on the prototype.\n         *\n         * E.g.:\n         * class A {\n         * }\n         * extendObservable(A.prototype, { x: 1 })\n         *\n         * classB extens A {\n         * }\n         * console.log(new B().x)\n         *\n         * It is unclear whether the property should be considered 'static' or inherited.\n         * Either use `console.log(A.x)`\n         * or: decorate(A, { x: observable })\n         *\n         * When using decorate, the property will always be redeclared as own property on the actual instance\n         */\n        console.warn(\"Property '\" + propName + \"' of '\" + owner + \"' was accessed through the prototype chain. Use 'decorate' instead to declare the prop or access it statically through it's owner\");\n    };\n    /**\n     * Observes this object. Triggers for the events 'add', 'update' and 'delete'.\n     * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/observe\n     * for callback details\n     */\n    ObservableObjectAdministration.prototype.observe = function (callback, fireImmediately) {\n        process.env.NODE_ENV !== \"production\" &&\n            invariant(fireImmediately !== true, \"`observe` doesn't support the fire immediately property for observable objects.\");\n        return registerListener(this, callback);\n    };\n    ObservableObjectAdministration.prototype.intercept = function (handler) {\n        return registerInterceptor(this, handler);\n    };\n    ObservableObjectAdministration.prototype.getKeys = function () {\n        var _this = this;\n        if (this.keys === undefined) {\n            this.keys = (new ObservableArray(Object.keys(this.values).filter(function (key) { return _this.values[key] instanceof ObservableValue; }), referenceEnhancer, \"keys(\" + this.name + \")\", true));\n        }\n        return this.keys.slice();\n    };\n    return ObservableObjectAdministration;\n}());\nfunction asObservableObject(target, name, defaultEnhancer) {\n    if (name === void 0) { name = \"\"; }\n    if (defaultEnhancer === void 0) { defaultEnhancer = deepEnhancer; }\n    var adm = target.$mobx;\n    if (adm)\n        return adm;\n    process.env.NODE_ENV !== \"production\" &&\n        invariant(Object.isExtensible(target), \"Cannot make the designated object observable; it is not extensible\");\n    if (!isPlainObject(target))\n        name = (target.constructor.name || \"ObservableObject\") + \"@\" + getNextId();\n    if (!name)\n        name = \"ObservableObject@\" + getNextId();\n    adm = new ObservableObjectAdministration(target, name, defaultEnhancer);\n    addHiddenFinalProp(target, \"$mobx\", adm);\n    return adm;\n}\nfunction defineObservableProperty(target, propName, newValue, enhancer) {\n    var adm = asObservableObject(target);\n    assertPropertyConfigurable(target, propName);\n    if (hasInterceptors(adm)) {\n        var change = interceptChange(adm, {\n            object: target,\n            name: propName,\n            type: \"add\",\n            newValue: newValue\n        });\n        if (!change)\n            return;\n        newValue = change.newValue;\n    }\n    var observable = (adm.values[propName] = new ObservableValue(newValue, enhancer, adm.name + \".\" + propName, false));\n    newValue = observable.value; // observableValue might have changed it\n    Object.defineProperty(target, propName, generateObservablePropConfig(propName));\n    if (adm.keys)\n        adm.keys.push(propName);\n    notifyPropertyAddition(adm, target, propName, newValue);\n}\nfunction defineComputedProperty(target, // which objects holds the observable and provides `this` context?\npropName, options) {\n    var adm = asObservableObject(target);\n    options.name = adm.name + \".\" + propName;\n    options.context = target;\n    adm.values[propName] = new ComputedValue(options);\n    Object.defineProperty(target, propName, generateComputedPropConfig(propName));\n}\nvar observablePropertyConfigs = Object.create(null);\nvar computedPropertyConfigs = Object.create(null);\nfunction generateObservablePropConfig(propName) {\n    return (observablePropertyConfigs[propName] ||\n        (observablePropertyConfigs[propName] = {\n            configurable: true,\n            enumerable: true,\n            get: function () {\n                return this.$mobx.read(this, propName);\n            },\n            set: function (v) {\n                this.$mobx.write(this, propName, v);\n            }\n        }));\n}\nfunction getAdministrationForComputedPropOwner(owner) {\n    var adm = owner.$mobx;\n    if (!adm) {\n        // because computed props are declared on proty,\n        // the current instance might not have been initialized yet\n        initializeInstance(owner);\n        return owner.$mobx;\n    }\n    return adm;\n}\nfunction generateComputedPropConfig(propName) {\n    return (computedPropertyConfigs[propName] ||\n        (computedPropertyConfigs[propName] = {\n            configurable: globalState.computedConfigurable,\n            enumerable: false,\n            get: function () {\n                return getAdministrationForComputedPropOwner(this).read(this, propName);\n            },\n            set: function (v) {\n                getAdministrationForComputedPropOwner(this).write(this, propName, v);\n            }\n        }));\n}\nfunction notifyPropertyAddition(adm, object, key, newValue) {\n    var notify = hasListeners(adm);\n    var notifySpy = isSpyEnabled();\n    var change = notify || notifySpy\n        ? {\n            type: \"add\",\n            object: object,\n            name: key,\n            newValue: newValue\n        }\n        : null;\n    if (notifySpy)\n        spyReportStart(__assign(__assign({}, change), { name: adm.name, key: key }));\n    if (notify)\n        notifyListeners(adm, change);\n    if (notifySpy)\n        spyReportEnd();\n}\nvar isObservableObjectAdministration = createInstanceofPredicate(\"ObservableObjectAdministration\", ObservableObjectAdministration);\nfunction isObservableObject(thing) {\n    if (isObject(thing)) {\n        // Initializers run lazily when transpiling to babel, so make sure they are run...\n        initializeInstance(thing);\n        return isObservableObjectAdministration(thing.$mobx);\n    }\n    return false;\n}\n\nfunction getAtom(thing, property) {\n    if (typeof thing === \"object\" && thing !== null) {\n        if (isObservableArray(thing)) {\n            if (property !== undefined)\n                fail(process.env.NODE_ENV !== \"production\" &&\n                    \"It is not possible to get index atoms from arrays\");\n            return thing.$mobx.atom;\n        }\n        if (isObservableSet(thing)) {\n            return thing.$mobx;\n        }\n        if (isObservableMap(thing)) {\n            var anyThing = thing;\n            if (property === undefined)\n                return anyThing._keysAtom;\n            var observable = anyThing._data.get(property) || anyThing._hasMap.get(property);\n            if (!observable)\n                fail(process.env.NODE_ENV !== \"production\" &&\n                    \"the entry '\" + property + \"' does not exist in the observable map '\" + getDebugName(thing) + \"'\");\n            return observable;\n        }\n        // Initializers run lazily when transpiling to babel, so make sure they are run...\n        initializeInstance(thing);\n        if (property && !thing.$mobx)\n            thing[property]; // See #1072\n        if (isObservableObject(thing)) {\n            if (!property)\n                return fail(process.env.NODE_ENV !== \"production\" && \"please specify a property\");\n            var observable = thing.$mobx.values[property];\n            if (!observable)\n                fail(process.env.NODE_ENV !== \"production\" &&\n                    \"no observable property '\" + property + \"' found on the observable object '\" + getDebugName(thing) + \"'\");\n            return observable;\n        }\n        if (isAtom(thing) || isComputedValue(thing) || isReaction(thing)) {\n            return thing;\n        }\n    }\n    else if (typeof thing === \"function\") {\n        if (isReaction(thing.$mobx)) {\n            // disposer function\n            return thing.$mobx;\n        }\n    }\n    return fail(process.env.NODE_ENV !== \"production\" && \"Cannot obtain atom from \" + thing);\n}\nfunction getAdministration(thing, property) {\n    if (!thing)\n        fail(\"Expecting some object\");\n    if (property !== undefined)\n        return getAdministration(getAtom(thing, property));\n    if (isAtom(thing) || isComputedValue(thing) || isReaction(thing))\n        return thing;\n    if (isObservableMap(thing) || isObservableSet(thing))\n        return thing;\n    // Initializers run lazily when transpiling to babel, so make sure they are run...\n    initializeInstance(thing);\n    if (thing.$mobx)\n        return thing.$mobx;\n    fail(process.env.NODE_ENV !== \"production\" && \"Cannot obtain administration from \" + thing);\n}\nfunction getDebugName(thing, property) {\n    var named;\n    if (property !== undefined)\n        named = getAtom(thing, property);\n    else if (isObservableObject(thing) || isObservableMap(thing) || isObservableSet(thing))\n        named = getAdministration(thing);\n    else\n        named = getAtom(thing); // valid for arrays as well\n    return named.name;\n}\n\nvar toString = Object.prototype.toString;\nfunction deepEqual(a, b, depth) {\n    if (depth === void 0) { depth = -1; }\n    return eq(a, b, depth);\n}\n// Copied from https://github.com/jashkenas/underscore/blob/5c237a7c682fb68fd5378203f0bf22dce1624854/underscore.js#L1186-L1289\n// Internal recursive comparison function for `isEqual`.\nfunction eq(a, b, depth, aStack, bStack) {\n    // Identical objects are equal. `0 === -0`, but they aren't identical.\n    // See the [Harmony `egal` proposal](http://wiki.ecmascript.org/doku.php?id=harmony:egal).\n    if (a === b)\n        return a !== 0 || 1 / a === 1 / b;\n    // `null` or `undefined` only equal to itself (strict comparison).\n    if (a == null || b == null)\n        return false;\n    // `NaN`s are equivalent, but non-reflexive.\n    if (a !== a)\n        return b !== b;\n    // Exhaust primitive checks\n    var type = typeof a;\n    if (type !== \"function\" && type !== \"object\" && typeof b != \"object\")\n        return false;\n    // Unwrap any wrapped objects.\n    a = unwrap(a);\n    b = unwrap(b);\n    // Compare `[[Class]]` names.\n    var className = toString.call(a);\n    if (className !== toString.call(b))\n        return false;\n    switch (className) {\n        // Strings, numbers, regular expressions, dates, and booleans are compared by value.\n        case \"[object RegExp]\":\n        // RegExps are coerced to strings for comparison (Note: '' + /a/i === '/a/i')\n        case \"[object String]\":\n            // Primitives and their corresponding object wrappers are equivalent; thus, `\"5\"` is\n            // equivalent to `new String(\"5\")`.\n            return \"\" + a === \"\" + b;\n        case \"[object Number]\":\n            // `NaN`s are equivalent, but non-reflexive.\n            // Object(NaN) is equivalent to NaN.\n            if (+a !== +a)\n                return +b !== +b;\n            // An `egal` comparison is performed for other numeric values.\n            return +a === 0 ? 1 / +a === 1 / b : +a === +b;\n        case \"[object Date]\":\n        case \"[object Boolean]\":\n            // Coerce dates and booleans to numeric primitive values. Dates are compared by their\n            // millisecond representations. Note that invalid dates with millisecond representations\n            // of `NaN` are not equivalent.\n            return +a === +b;\n        case \"[object Symbol]\":\n            return (\n            // eslint-disable-next-line\n            typeof Symbol !== \"undefined\" && Symbol.valueOf.call(a) === Symbol.valueOf.call(b));\n    }\n    var areArrays = className === \"[object Array]\";\n    if (!areArrays) {\n        if (typeof a != \"object\" || typeof b != \"object\")\n            return false;\n        // Objects with different constructors are not equivalent, but `Object`s or `Array`s\n        // from different frames are.\n        var aCtor = a.constructor, bCtor = b.constructor;\n        if (aCtor !== bCtor &&\n            !(typeof aCtor === \"function\" &&\n                aCtor instanceof aCtor &&\n                typeof bCtor === \"function\" &&\n                bCtor instanceof bCtor) &&\n            (\"constructor\" in a && \"constructor\" in b)) {\n            return false;\n        }\n    }\n    if (depth === 0) {\n        return false;\n    }\n    else if (depth < 0) {\n        depth = -1;\n    }\n    // Assume equality for cyclic structures. The algorithm for detecting cyclic\n    // structures is adapted from ES 5.1 section 15.12.3, abstract operation `JO`.\n    // Initializing stack of traversed objects.\n    // It's done here since we only need them for objects and arrays comparison.\n    aStack = aStack || [];\n    bStack = bStack || [];\n    var length = aStack.length;\n    while (length--) {\n        // Linear search. Performance is inversely proportional to the number of\n        // unique nested structures.\n        if (aStack[length] === a)\n            return bStack[length] === b;\n    }\n    // Add the first object to the stack of traversed objects.\n    aStack.push(a);\n    bStack.push(b);\n    // Recursively compare objects and arrays.\n    if (areArrays) {\n        // Compare array lengths to determine if a deep comparison is necessary.\n        length = a.length;\n        if (length !== b.length)\n            return false;\n        // Deep compare the contents, ignoring non-numeric properties.\n        while (length--) {\n            if (!eq(a[length], b[length], depth - 1, aStack, bStack))\n                return false;\n        }\n    }\n    else {\n        // Deep compare objects.\n        var keys = Object.keys(a);\n        var key = void 0;\n        length = keys.length;\n        // Ensure that both objects contain the same number of properties before comparing deep equality.\n        if (Object.keys(b).length !== length)\n            return false;\n        while (length--) {\n            // Deep compare each member\n            key = keys[length];\n            if (!(has$1(b, key) && eq(a[key], b[key], depth - 1, aStack, bStack)))\n                return false;\n        }\n    }\n    // Remove the first object from the stack of traversed objects.\n    aStack.pop();\n    bStack.pop();\n    return true;\n}\nfunction unwrap(a) {\n    if (isObservableArray(a))\n        return a.peek();\n    if (isES6Map(a) || isObservableMap(a))\n        return iteratorToArray(a.entries());\n    if (isES6Set(a) || isObservableSet(a))\n        return iteratorToArray(a.entries());\n    return a;\n}\nfunction has$1(a, key) {\n    return Object.prototype.hasOwnProperty.call(a, key);\n}\n\n/**\n * (c) Michel Weststrate 2015 - 2019\n * MIT Licensed\n *\n * Welcome to the mobx sources! To get an global overview of how MobX internally works,\n * this is a good place to start:\n * https://medium.com/@mweststrate/becoming-fully-reactive-an-in-depth-explanation-of-mobservable-55995262a254#.xvbh6qd74\n *\n * Source folders:\n * ===============\n *\n * - api/     Most of the public static methods exposed by the module can be found here.\n * - core/    Implementation of the MobX algorithm; atoms, derivations, reactions, dependency trees, optimizations. Cool stuff can be found here.\n * - types/   All the magic that is need to have observable objects, arrays and values is in this folder. Including the modifiers like `asFlat`.\n * - utils/   Utility stuff.\n *\n */\ntry {\n    // define process.env if needed\n    // if this is not a production build in the first place\n    // (in which case the expression below would be substituted with 'production')\n    // tslint:disable-next-line\n    process.env.NODE_ENV;\n}\ncatch (e) {\n    var g = getGlobal();\n    if (typeof process === \"undefined\")\n        g.process = {};\n    g.process.env = {};\n}\n(function () {\n    function testCodeMinification() { }\n    if (testCodeMinification.name !== \"testCodeMinification\" &&\n        process.env.NODE_ENV !== \"production\" &&\n        typeof process !== 'undefined' && process.env.IGNORE_MOBX_MINIFY_WARNING !== \"true\") {\n        // trick so it doesn't get replaced\n        var varName = [\"process\", \"env\", \"NODE_ENV\"].join(\".\");\n        console.warn(\"[mobx] you are running a minified build, but '\" + varName + \"' was not set to 'production' in your bundler. This results in an unnecessarily large and slow bundle\");\n    }\n})();\n// forward compatibility with mobx, so that packages can easily support mobx 4 & 5\nvar $mobx = \"$mobx\";\nif (typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__ === \"object\") {\n    // See: https://github.com/andykog/mobx-devtools/\n    __MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({\n        spy: spy,\n        extras: {\n            getDebugName: getDebugName\n        },\n        $mobx: $mobx\n    });\n}\n// TODO: remove in some future build\nif (process.env.NODE_ENV !== \"production\" &&\n    typeof module !== \"undefined\" &&\n    typeof module.exports !== \"undefined\") {\n    var warnedAboutDefaultExport_1 = false;\n    Object.defineProperty(module.exports, \"default\", {\n        enumerable: false,\n        get: function () {\n            if (!warnedAboutDefaultExport_1) {\n                warnedAboutDefaultExport_1 = true;\n                console.warn(\"The MobX package does not have a default export. Use 'import { thing } from \\\"mobx\\\"' (recommended) or 'import * as mobx from \\\"mobx\\\"' instead.\\\"\");\n            }\n            return undefined;\n        }\n    });\n    [\n        \"extras\",\n        \"Atom\",\n        \"BaseAtom\",\n        \"asFlat\",\n        \"asMap\",\n        \"asReference\",\n        \"asStructure\",\n        \"autorunAsync\",\n        \"createTranformer\",\n        \"expr\",\n        \"isModifierDescriptor\",\n        \"isStrictModeEnabled\",\n        \"map\",\n        \"useStrict\",\n        \"whyRun\"\n    ].forEach(function (prop) {\n        Object.defineProperty(module.exports, prop, {\n            enumerable: false,\n            get: function () {\n                fail(\"'\" + prop + \"' is no longer part of the public MobX api. Please consult the changelog to find out where this functionality went\");\n            },\n            set: function () { }\n        });\n    });\n}\n\nexport { $mobx, FlowCancellationError, IDerivationState, ObservableMap, ObservableSet, Reaction, allowStateChanges as _allowStateChanges, allowStateChangesInsideComputed as _allowStateChangesInsideComputed, allowStateReadsEnd as _allowStateReadsEnd, allowStateReadsStart as _allowStateReadsStart, _endAction, getAdministration as _getAdministration, getGlobalState as _getGlobalState, interceptReads as _interceptReads, isComputingDerivation as _isComputingDerivation, resetGlobalState as _resetGlobalState, _startAction, action, autorun, comparer, computed, configure, createAtom, decorate, entries, extendObservable, extendShallowObservable, flow, get, getAtom, getDebugName, getDependencyTree, getObserverTree, has, intercept, isAction, isArrayLike, isObservableValue as isBoxedObservable, isComputed, isComputedProp, isFlowCancellationError, isObservable, isObservableArray, isObservableMap, isObservableObject, isObservableProp, isObservableSet, keys, observable, observe, onBecomeObserved, onBecomeUnobserved, onReactionError, reaction, remove, runInAction, set, spy, toJS, trace, transaction, untracked, values, when };\n"], "mappings": ";AACA,IAAI,mBAAmB;AACvB,IAAI,cAAc,CAAC;AACnB,OAAO,OAAO,WAAW;AACzB,IAAI,eAAe,CAAC;AACpB,OAAO,OAAO,YAAY;AAC1B,IAAI,aAAa,CAAC;AAClB,SAAS,YAAY;AACjB,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY;AACjB,SAAO,EAAE,YAAY;AACzB;AACA,SAAS,KAAK,SAAS;AACnB,YAAU,OAAO,OAAO;AACxB,QAAM;AACV;AACA,SAAS,UAAU,OAAO,SAAS;AAC/B,MAAI,CAAC;AACD,UAAM,IAAI,MAAM,aAAa,WAAW,iBAAiB;AACjE;AAKA,IAAI,qBAAqB,CAAC;AAC1B,SAAS,WAAW,KAAK,OAAO;AAC5B,MAAI;AACA,WAAO;AACX,MAAI,OAAO;AACP,WAAO,WAAW,MAAM,MAAM,aAAa,QAAQ,YAAY;AAAA,EACnE;AACA,MAAI,mBAAmB,QAAQ,GAAG,MAAM;AACpC,WAAO;AACX,qBAAmB,KAAK,GAAG;AAC3B,UAAQ,MAAM,wBAAwB,GAAG;AACzC,SAAO;AACX;AAIA,SAAS,KAAK,MAAM;AAChB,MAAI,UAAU;AACd,SAAO,WAAY;AACf,QAAI;AACA;AACJ,cAAU;AACV,WAAO,KAAK,MAAM,MAAM,SAAS;AAAA,EACrC;AACJ;AACA,IAAI,OAAO,WAAY;AAAE;AACzB,SAAS,OAAO,MAAM;AAClB,MAAI,MAAM,CAAC;AACX,OAAK,QAAQ,SAAU,MAAM;AACzB,QAAI,IAAI,QAAQ,IAAI,MAAM;AACtB,UAAI,KAAK,IAAI;AAAA,EACrB,CAAC;AACD,SAAO;AACX;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC9C;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,UAAU,QAAQ,OAAO,UAAU;AACnC,WAAO;AACX,MAAI,QAAQ,OAAO,eAAe,KAAK;AACvC,SAAO,UAAU,OAAO,aAAa,UAAU;AACnD;AACA,SAAS,aAAa,eAAe;AACjC,MAAI,SAAS,aAAa,KAAK,gBAAgB,aAAa,GAAG;AAC3D,WAAO;AAAA,EACX,WACS,MAAM,QAAQ,aAAa,GAAG;AACnC,WAAO,IAAI,IAAI,aAAa;AAAA,EAChC,WACS,cAAc,aAAa,GAAG;AACnC,QAAI,MAAM,oBAAI,IAAI;AAClB,aAAS,OAAO,eAAe;AAC3B,UAAI,IAAI,KAAK,cAAc,GAAG,CAAC;AAAA,IACnC;AACA,WAAO;AAAA,EACX,OACK;AACD,WAAO,KAAK,iCAAiC,gBAAgB,GAAG;AAAA,EACpE;AACJ;AACA,SAAS,kBAAkB,QAAQ,WAAW;AAC1C,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,kBAAc,QAAQ,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC,CAAC,CAAC;AAAA,EAC5D;AACJ;AACA,SAAS,cAAc,QAAQ,UAAU,OAAO;AAC5C,SAAO,eAAe,QAAQ,UAAU;AAAA,IACpC,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,IACd;AAAA,EACJ,CAAC;AACL;AACA,SAAS,mBAAmB,QAAQ,UAAU,OAAO;AACjD,SAAO,eAAe,QAAQ,UAAU;AAAA,IACpC,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,IACd;AAAA,EACJ,CAAC;AACL;AACA,SAAS,uBAAuB,QAAQ,MAAM;AAC1C,MAAI,aAAa,OAAO,yBAAyB,QAAQ,IAAI;AAC7D,SAAO,CAAC,cAAe,WAAW,iBAAiB,SAAS,WAAW,aAAa;AACxF;AACA,SAAS,2BAA2B,QAAQ,MAAM;AAC9C,MAA6C,CAAC,uBAAuB,QAAQ,IAAI;AAC7E,SAAK,2BAA2B,OAAO,wEAAwE;AACvH;AACA,SAAS,0BAA0B,MAAM,OAAO;AAC5C,MAAI,WAAW,WAAW;AAC1B,QAAM,UAAU,QAAQ,IAAI;AAC5B,SAAO,SAAU,GAAG;AAChB,WAAO,SAAS,CAAC,KAAK,EAAE,QAAQ,MAAM;AAAA,EAC1C;AACJ;AACA,SAAS,WAAW,GAAG,GAAG;AACtB,SAAO,OAAO,MAAM,YAAY,OAAO,MAAM,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC;AAChF;AAIA,SAAS,YAAY,GAAG;AACpB,SAAO,MAAM,QAAQ,CAAC,KAAK,kBAAkB,CAAC;AAClD;AACA,SAAS,SAAS,OAAO;AACrB,MAAI,UAAU,EAAE,QAAQ,UAAa,iBAAiB,UAAU,EAAE;AAC9D,WAAO;AACX,SAAO;AACX;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,iBAAiB;AAC5B;AAEA,SAAS,gBAAgB,IAAI;AACzB,MAAI,MAAM,CAAC;AACX,SAAO,MAAM;AACT,QAAI,IAAI,GAAG,KAAK;AAChB,QAAI,EAAE;AACF;AACJ,QAAI,KAAK,EAAE,KAAK;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,kBAAkB;AAEvB,SAAQ,OAAO,WAAW,cAAc,OAAO,eAAgB;AACnE;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,UAAU,OAAO,OAAO,OAAO,UAAU,WAAW,KAAK,QAAQ;AAC5E;AAEA,SAAS,MAAM,MAAM,UAAU;AAC3B,MAAI,OAAO,KAAK,KAAK;AACrB,SAAO,CAAC,KAAK,MAAM;AACf,aAAS,KAAK,KAAK;AACnB,WAAO,KAAK,KAAK;AAAA,EACrB;AACJ;AAEA,SAAS,iBAAiB;AACtB,SAAQ,OAAO,WAAW,cAAc,OAAO,YAAa;AAChE;AACA,SAAS,gBAAgB,YAAY,iBAAiB;AAClD,qBAAmB,YAAY,eAAe,GAAG,eAAe;AACpE;AACA,SAAS,aAAa,UAAU;AAC5B,WAAS,eAAe,CAAC,IAAI;AAC7B,SAAO;AACX;AACA,SAAS,oBAAoB;AACzB,SAAQ,OAAO,WAAW,cAAc,OAAO,eAAgB;AACnE;AACA,SAAS,UAAU;AACf,SAAO;AACX;AAQA,IAAI;AAAA;AAAA,EAAsB,WAAY;AAKlC,aAASA,MAAK,MAAM;AAChB,UAAI,SAAS,QAAQ;AAAE,eAAO,UAAU,UAAU;AAAA,MAAG;AACrD,WAAK,OAAO;AACZ,WAAK,yBAAyB;AAC9B,WAAK,kBAAkB;AACvB,WAAK,YAAY,CAAC;AAClB,WAAK,mBAAmB,CAAC;AACzB,WAAK,YAAY;AACjB,WAAK,iBAAiB;AACtB,WAAK,sBAAsB,iBAAiB;AAAA,IAChD;AACA,IAAAA,MAAK,UAAU,qBAAqB,WAAY;AAAA,IAEhD;AACA,IAAAA,MAAK,UAAU,mBAAmB,WAAY;AAAA,IAE9C;AAKA,IAAAA,MAAK,UAAU,iBAAiB,WAAY;AACxC,aAAO,eAAe,IAAI;AAAA,IAC9B;AAIA,IAAAA,MAAK,UAAU,gBAAgB,WAAY;AACvC,iBAAW;AACX,uBAAiB,IAAI;AACrB,eAAS;AAAA,IACb;AACA,IAAAA,MAAK,UAAU,WAAW,WAAY;AAClC,aAAO,KAAK;AAAA,IAChB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI,SAAS,0BAA0B,QAAQ,IAAI;AACnD,SAAS,WAAW,MAAM,yBAAyB,2BAA2B;AAC1E,MAAI,4BAA4B,QAAQ;AAAE,8BAA0B;AAAA,EAAM;AAC1E,MAAI,8BAA8B,QAAQ;AAAE,gCAA4B;AAAA,EAAM;AAC9E,MAAI,OAAO,IAAI,KAAK,IAAI;AACxB,mBAAiB,MAAM,uBAAuB;AAC9C,qBAAmB,MAAM,yBAAyB;AAClD,SAAO;AACX;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,SAAO,MAAM;AACjB;AACA,SAAS,mBAAmB,GAAG,GAAG;AAC9B,SAAO,UAAU,GAAG,CAAC;AACzB;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,SAAO,UAAU,GAAG,GAAG,CAAC;AAC5B;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,SAAO,WAAW,GAAG,CAAC,KAAK,iBAAiB,GAAG,CAAC;AACpD;AACA,IAAI,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACb;AAkBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAIA,GAAE,eAAe,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AAC7E,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAEA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI,EAAG,OAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAEA,SAAS,WAAW;AAChB,WAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ;AAC3C,SAAK,GAAG,OAAO,OAAO,UAAU,CAAC,CAAC,CAAC;AACvC,SAAO;AACX;AAEA,IAAI,4BAA4B,CAAC;AACjC,IAAI,+BAA+B,CAAC;AACpC,SAAS,oCAAoC,MAAM,YAAY;AAC3D,MAAIC,SAAQ,aAAa,4BAA4B;AACrD,SAAQA,OAAM,IAAI,MACbA,OAAM,IAAI,IAAI;AAAA,IACX,cAAc;AAAA,IACd;AAAA,IACA,KAAK,WAAY;AACb,yBAAmB,IAAI;AACvB,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,IACA,KAAK,SAAU,OAAO;AAClB,yBAAmB,IAAI;AACvB,WAAK,IAAI,IAAI;AAAA,IACjB;AAAA,EACJ;AACR;AACA,SAAS,mBAAmB,QAAQ;AAChC,MAAI,OAAO,iCAAiC;AACxC;AACJ,MAAI,aAAa,OAAO;AACxB,MAAI,YAAY;AACZ,kBAAc,QAAQ,gCAAgC,IAAI;AAC1D,aAAS,OAAO,YAAY;AACxB,UAAI,IAAI,WAAW,GAAG;AACtB,QAAE,gBAAgB,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,kBAAkB;AAAA,IAC3F;AAAA,EACJ;AACJ;AACA,SAAS,oBAAoB,6BAA6B,iBAAiB;AACvE,SAAO,SAAS,mBAAmB;AAC/B,QAAI;AACJ,QAAI,YAAY,SAASC,UAAS,QAAQ,MAAM,YAAY,kBAG1D;AACE,UAAI,qBAAqB,MAAM;AAC3B,wBAAgB,QAAQ,MAAM,YAAY,QAAQ,kBAAkB;AACpE,eAAO;AAAA,MACX;AACA,UAA6C,CAAC,qBAAqB,SAAS;AACxE,aAAK,sEAAsE;AAC/E,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,kBAAkB,GAAG;AACnE,YAAI,sBAAsB,OAAO;AACjC,sBAAc,QAAQ,oBAAoB,SAAS,CAAC,GAAG,mBAAmB,CAAC;AAAA,MAC/E;AACA,aAAO,iBAAiB,IAAI,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB;AAAA,MACJ;AACA,aAAO,oCAAoC,MAAM,2BAA2B;AAAA,IAChF;AACA,QAAI,qBAAqB,SAAS,GAAG;AAEjC,2BAAqB;AACrB,aAAO,UAAU,MAAM,MAAM,SAAS;AAAA,IAC1C,OACK;AAED,2BAAqB,MAAM,UAAU,MAAM,KAAK,SAAS;AACzD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,qBAAqB,MAAM;AAChC,UAAU,KAAK,WAAW,KAAK,KAAK,WAAW,MAAM,OAAO,KAAK,CAAC,MAAM,YACnE,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM;AAC1C;AAEA,SAAS,aAAa,GAAG,GAAG,MAAM;AAE9B,MAAI,aAAa,CAAC;AACd,WAAO;AAEX,MAAI,MAAM,QAAQ,CAAC;AACf,WAAO,WAAW,MAAM,GAAG,EAAE,KAAW,CAAC;AAC7C,MAAI,cAAc,CAAC;AACf,WAAO,WAAW,OAAO,GAAG,QAAW,EAAE,KAAW,CAAC;AACzD,MAAI,SAAS,CAAC;AACV,WAAO,WAAW,IAAI,GAAG,EAAE,KAAW,CAAC;AAC3C,MAAI,SAAS,CAAC;AACV,WAAO,WAAW,IAAI,GAAG,EAAE,KAAW,CAAC;AAC3C,SAAO;AACX;AACA,SAAS,gBAAgB,GAAG,GAAG,MAAM;AACjC,MAAI,MAAM,UAAa,MAAM;AACzB,WAAO;AACX,MAAI,mBAAmB,CAAC,KAAK,kBAAkB,CAAC,KAAK,gBAAgB,CAAC,KAAK,gBAAgB,CAAC;AACxF,WAAO;AACX,MAAI,MAAM,QAAQ,CAAC;AACf,WAAO,WAAW,MAAM,GAAG,EAAE,MAAY,MAAM,MAAM,CAAC;AAC1D,MAAI,cAAc,CAAC;AACf,WAAO,WAAW,OAAO,GAAG,QAAW,EAAE,MAAY,MAAM,MAAM,CAAC;AACtE,MAAI,SAAS,CAAC;AACV,WAAO,WAAW,IAAI,GAAG,EAAE,MAAY,MAAM,MAAM,CAAC;AACxD,MAAI,SAAS,CAAC;AACV,WAAO,WAAW,IAAI,GAAG,EAAE,MAAY,MAAM,MAAM,CAAC;AACxD,SAAO,KACH,mGAAmG;AAC3G;AACA,SAAS,kBAAkB,UAAU;AAEjC,SAAO;AACX;AACA,SAAS,kBAAkB,GAAG,UAAU,MAAM;AAC1C,MAA6C,aAAa,CAAC;AACvD,UAAM;AACV,MAAI,UAAU,GAAG,QAAQ;AACrB,WAAO;AACX,SAAO;AACX;AAEA,SAAS,2BAA2B,UAAU;AAC1C,YAAU,QAAQ;AAClB,MAAI,YAAY,oBAAoB,MAAM,SAAU,QAAQ,cAAc,YAAY,kBAAkB,eAAe;AACnH,QAAI,MAAuC;AACvC,gBAAU,CAAC,cAAc,CAAC,WAAW,KAAK,qDAAsD,eAAe,4BAA6B;AAAA,IAChJ;AACA,QAAI,eAAe,aACb,WAAW,cACP,WAAW,YAAY,KAAK,MAAM,IAClC,WAAW,QACf;AACN,6BAAyB,QAAQ,cAAc,cAAc,QAAQ;AAAA,EACzE,CAAC;AACD,MAAI;AAAA;AAAA,IAEJ,OAAO,YAAY,eAAe,QAAQ,OAAO,OAC3C,SAAS,sBAAsB;AAG7B,UAAI,UAAU,SAAS;AACnB,eAAO,KAAK,oFAAoF;AACpG,aAAO,UAAU,MAAM,MAAM,SAAS;AAAA,IAC1C,IACE;AAAA;AACN,MAAI,WAAW;AACf,SAAO;AACX;AAIA,IAAI,iCAAiC;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,kBAAkB;AACtB;AACA,IAAI,iCAAiC;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,kBAAkB;AACtB;AACA,OAAO,OAAO,8BAA8B;AAC5C,OAAO,OAAO,8BAA8B;AAC5C,SAAS,kBAAkB,KAAK;AAC5B,MAAI,CAAC,wCAAwC,KAAK,GAAG;AACjD,SAAK,4CAA4C,GAAG;AAC5D;AACA,SAAS,0BAA0B,OAAO;AACtC,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,MAAI,OAAO,UAAU;AACjB,WAAO,EAAE,MAAM,OAAO,MAAM,KAAK;AACrC,MAAI,MAAuC;AACvC,QAAI,OAAO,UAAU;AACjB,aAAO,KAAK,yBAAyB;AACzC,WAAO,KAAK,KAAK,EAAE,QAAQ,iBAAiB;AAAA,EAChD;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,SAAS;AACrC,SAAO,QAAQ,mBACT,QAAQ,iBAAiB,WACzB,QAAQ,SAAS,QACb,oBACA;AACd;AACA,IAAI,gBAAgB,2BAA2B,YAAY;AAC3D,IAAI,mBAAmB,2BAA2B,eAAe;AACjE,IAAI,eAAe,2BAA2B,iBAAiB;AAC/D,IAAI,qBAAqB,2BAA2B,iBAAiB;AAKrE,SAAS,iBAAiB,GAAG,MAAM,MAAM;AAErC,MAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AAClC,WAAO,cAAc,MAAM,MAAM,SAAS;AAAA,EAC9C;AAEA,MAAI,aAAa,CAAC;AACd,WAAO;AAEX,MAAI,MAAM,cAAc,CAAC,IACnB,WAAW,OAAO,GAAG,MAAM,IAAI,IAC/B,MAAM,QAAQ,CAAC,IACX,WAAW,MAAM,GAAG,IAAI,IACxB,SAAS,CAAC,IACN,WAAW,IAAI,GAAG,IAAI,IACtB,SAAS,CAAC,IACN,WAAW,IAAI,GAAG,IAAI,IACtB;AAElB,MAAI,QAAQ;AACR,WAAO;AAEX,OACI,yJAAyJ;AACjK;AACA,IAAI,sBAAsB;AAAA,EACtB,KAAK,SAAU,OAAO,SAAS;AAC3B,QAAI,UAAU,SAAS;AACnB,iCAA2B,KAAK;AACpC,QAAI,IAAI,0BAA0B,OAAO;AACzC,WAAO,IAAI,gBAAgB,OAAO,uBAAuB,CAAC,GAAG,EAAE,MAAM,MAAM,EAAE,MAAM;AAAA,EACvF;AAAA,EACA,YAAY,SAAU,OAAO,MAAM;AAC/B,QAAI,UAAU,SAAS;AACnB,iCAA2B,YAAY;AAC3C,eAAW,yBAAyB,wCAAwC;AAC5E,WAAO,WAAW,IAAI,OAAO,EAAE,MAAY,MAAM,MAAM,CAAC;AAAA,EAC5D;AAAA,EACA,OAAO,SAAU,eAAe,SAAS;AACrC,QAAI,UAAU,SAAS;AACnB,iCAA2B,OAAO;AACtC,QAAI,IAAI,0BAA0B,OAAO;AACzC,WAAO,IAAI,gBAAgB,eAAe,uBAAuB,CAAC,GAAG,EAAE,IAAI;AAAA,EAC/E;AAAA,EACA,cAAc,SAAU,eAAe,MAAM;AACzC,QAAI,UAAU,SAAS;AACnB,iCAA2B,cAAc;AAC7C,eAAW,2BAA2B,2CAA2C;AACjF,WAAO,WAAW,MAAM,eAAe,EAAE,MAAY,MAAM,MAAM,CAAC;AAAA,EACtE;AAAA,EACA,KAAK,SAAU,eAAe,SAAS;AACnC,QAAI,UAAU,SAAS;AACnB,iCAA2B,KAAK;AACpC,QAAI,IAAI,0BAA0B,OAAO;AACzC,WAAO,IAAI,cAAc,eAAe,uBAAuB,CAAC,GAAG,EAAE,IAAI;AAAA,EAC7E;AAAA,EACA,YAAY,SAAU,eAAe,MAAM;AACvC,QAAI,UAAU,SAAS;AACnB,iCAA2B,YAAY;AAC3C,eAAW,yBAAyB,yCAAyC;AAC7E,WAAO,WAAW,IAAI,eAAe,EAAE,MAAY,MAAM,MAAM,CAAC;AAAA,EACpE;AAAA,EACA,KAAK,SAAU,eAAe,SAAS;AACnC,QAAI,UAAU,SAAS;AACnB,iCAA2B,KAAK;AACpC,QAAI,IAAI,0BAA0B,OAAO;AACzC,WAAO,IAAI,cAAc,eAAe,uBAAuB,CAAC,GAAG,EAAE,IAAI;AAAA,EAC7E;AAAA,EACA,QAAQ,SAAU,OAAO,YAAY,SAAS;AAC1C,QAAI,OAAO,UAAU,CAAC,MAAM;AACxB,iCAA2B,QAAQ;AACvC,QAAI,IAAI,0BAA0B,OAAO;AACzC,WAAO,iBAAiB,CAAC,GAAG,OAAO,YAAY,CAAC;AAAA,EACpD;AAAA,EACA,eAAe,SAAU,OAAO,MAAM;AAClC,QAAI,OAAO,UAAU,CAAC,MAAM;AACxB,iCAA2B,eAAe;AAC9C,eAAW,4BAA4B,gDAAgD;AACvF,WAAO,WAAW,OAAO,OAAO,CAAC,GAAG,EAAE,MAAY,MAAM,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AACZ;AACA,IAAI,aAAa;AAEjB,OAAO,KAAK,mBAAmB,EAAE,QAAQ,SAAU,MAAM;AAAE,SAAQ,WAAW,IAAI,IAAI,oBAAoB,IAAI;AAAI,CAAC;AACnH,SAAS,2BAA2B,YAAY;AAC5C;AAAA;AAAA,IAEA,iDAAiD,aAAa,kDAAkD,aAAa;AAAA,EAAgB;AACjJ;AAEA,IAAI,oBAAoB,oBAAoB,OAAO,SAAU,UAAU,cAAc,YAAY,iBAAiB,eAAe;AAC7H,MAAI,MAAuC;AACvC,cAAU,cAAc,WAAW,KAAK,gEAAgE,eAAe,GAAG;AAAA,EAC9H;AACA,MAAIC,OAAM,WAAW,KAAKC,OAAM,WAAW;AAI3C,MAAI,UAAU,cAAc,CAAC,KAAK,CAAC;AACnC,yBAAuB,UAAU,cAAc,SAAS,EAAE,KAAKD,MAAK,KAAKC,KAAI,GAAG,OAAO,CAAC;AAC5F,CAAC;AACD,IAAI,0BAA0B,kBAAkB,EAAE,QAAQ,SAAS,WAAW,CAAC;AAK/E,IAAI,WAAW,SAASC,UAAS,MAAM,MAAM,MAAM;AAC/C,MAAI,OAAO,SAAS,UAAU;AAE1B,WAAO,kBAAkB,MAAM,MAAM,SAAS;AAAA,EAClD;AACA,MAAI,SAAS,QAAQ,OAAO,SAAS,YAAY,UAAU,WAAW,GAAG;AAErE,WAAO,kBAAkB,MAAM,MAAM,SAAS;AAAA,EAClD;AAEA,MAAI,MAAuC;AACvC,cAAU,OAAO,SAAS,YAAY,uDAAuD;AAC7F,cAAU,UAAU,SAAS,GAAG,yDAAyD;AAAA,EAC7F;AACA,MAAI,OAAO,OAAO,SAAS,WAAW,OAAO,CAAC;AAC9C,OAAK,MAAM;AACX,OAAK,MAAM,OAAO,SAAS,aAAa,OAAO,KAAK;AACpD,OAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ;AACtC,SAAO,IAAI,cAAc,IAAI;AACjC;AACA,SAAS,SAAS;AAElB,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAGzB,EAAAA,kBAAiBA,kBAAiB,cAAc,IAAI,EAAE,IAAI;AAI1D,EAAAA,kBAAiBA,kBAAiB,YAAY,IAAI,CAAC,IAAI;AAOvD,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAG3D,EAAAA,kBAAiBA,kBAAiB,OAAO,IAAI,CAAC,IAAI;AACtD,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAI;AAAA,CACH,SAAUC,YAAW;AAClB,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,KAAK,IAAI,CAAC,IAAI;AAClC,EAAAA,WAAUA,WAAU,OAAO,IAAI,CAAC,IAAI;AACxC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA;AAAA,EAAiC,2BAAY;AAC7C,aAASC,iBAAgB,OAAO;AAC5B,WAAK,QAAQ;AAAA,IAEjB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,SAAS,kBAAkB,GAAG;AAC1B,SAAO,aAAa;AACxB;AAYA,SAAS,cAAc,YAAY;AAC/B,UAAQ,WAAW,mBAAmB;AAAA,IAClC,KAAK,iBAAiB;AAClB,aAAO;AAAA,IACX,KAAK,iBAAiB;AAAA,IACtB,KAAK,iBAAiB;AAClB,aAAO;AAAA,IACX,KAAK,iBAAiB,gBAAgB;AAElC,UAAI,sBAAsB,qBAAqB,IAAI;AACnD,UAAI,gBAAgB,eAAe;AACnC,UAAI,MAAM,WAAW,WAAW,IAAI,IAAI;AACxC,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAI,MAAM,IAAI,CAAC;AACf,YAAI,gBAAgB,GAAG,GAAG;AACtB,cAAI,YAAY,wBAAwB;AACpC,gBAAI,IAAI;AAAA,UACZ,OACK;AACD,gBAAI;AACA,kBAAI,IAAI;AAAA,YACZ,SACO,GAAG;AAEN,2BAAa,aAAa;AAC1B,iCAAmB,mBAAmB;AACtC,qBAAO;AAAA,YACX;AAAA,UACJ;AAIA,cAAI,WAAW,sBAAsB,iBAAiB,OAAO;AACzD,yBAAa,aAAa;AAC1B,+BAAmB,mBAAmB;AACtC,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,iCAA2B,UAAU;AACrC,mBAAa,aAAa;AAC1B,yBAAmB,mBAAmB;AACtC,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAUA,SAAS,wBAAwB;AAC7B,SAAO,YAAY,uBAAuB;AAC9C;AACA,SAAS,oCAAoC,MAAM;AAC/C,MAAIC,gBAAe,KAAK,UAAU,SAAS;AAE3C,MAAI,YAAY,mBAAmB,KAAKA;AACpC,SACI,qIAAqI,KAAK,IAAI;AAEtJ,MAAI,CAAC,YAAY,sBAAsBA,iBAAgB,YAAY,mBAAmB;AAClF,UACK,YAAY,iBACP,wLACA,mLACF,KAAK,IAAI;AACzB;AACA,SAAS,4BAA4BC,aAAY;AAC7C,MACI,CAAC,YAAY,mBACb,YAAY,4BAA4B;AACxC,YAAQ,KAAK,uBAAuBA,YAAW,OAAO,wCAAwC;AAAA,EAClG;AACJ;AAMA,SAAS,qBAAqB,YAAY,GAAG,SAAS;AAClD,MAAI,sBAAsB,qBAAqB,IAAI;AAGnD,6BAA2B,UAAU;AACrC,aAAW,eAAe,IAAI,MAAM,WAAW,UAAU,SAAS,GAAG;AACrE,aAAW,mBAAmB;AAC9B,aAAW,QAAQ,EAAE,YAAY;AACjC,MAAI,eAAe,YAAY;AAC/B,cAAY,qBAAqB;AACjC,MAAI;AACJ,MAAI,YAAY,2BAA2B,MAAM;AAC7C,aAAS,EAAE,KAAK,OAAO;AAAA,EAC3B,OACK;AACD,QAAI;AACA,eAAS,EAAE,KAAK,OAAO;AAAA,IAC3B,SACO,GAAG;AACN,eAAS,IAAI,gBAAgB,CAAC;AAAA,IAClC;AAAA,EACJ;AACA,cAAY,qBAAqB;AACjC,mBAAiB,UAAU;AAC3B,MAAI,WAAW,UAAU,WAAW,GAAG;AACnC,2CAAuC,UAAU;AAAA,EACrD;AACA,qBAAmB,mBAAmB;AACtC,SAAO;AACX;AACA,SAAS,uCAAuC,YAAY;AACxD,MAAI;AACA;AACJ,MAAI,YAAY,8BAA8B,WAAW,oBAAoB;AACzE,YAAQ,KAAK,uBAAuB,WAAW,OAAO,0DAA0D;AAAA,EACpH;AACJ;AAMA,SAAS,iBAAiB,YAAY;AAElC,MAAI,gBAAgB,WAAW;AAC/B,MAAI,YAAa,WAAW,YAAY,WAAW;AACnD,MAAI,oCAAoC,iBAAiB;AAIzD,MAAI,KAAK,GAAG,IAAI,WAAW;AAC3B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAI,MAAM,UAAU,CAAC;AACrB,QAAI,IAAI,cAAc,GAAG;AACrB,UAAI,YAAY;AAChB,UAAI,OAAO;AACP,kBAAU,EAAE,IAAI;AACpB;AAAA,IACJ;AAGA,QAAI,IAAI,oBAAoB,mCAAmC;AAC3D,0CAAoC,IAAI;AAAA,IAC5C;AAAA,EACJ;AACA,YAAU,SAAS;AACnB,aAAW,eAAe;AAI1B,MAAI,cAAc;AAClB,SAAO,KAAK;AACR,QAAI,MAAM,cAAc,CAAC;AACzB,QAAI,IAAI,cAAc,GAAG;AACrB,qBAAe,KAAK,UAAU;AAAA,IAClC;AACA,QAAI,YAAY;AAAA,EACpB;AAIA,SAAO,MAAM;AACT,QAAI,MAAM,UAAU,EAAE;AACtB,QAAI,IAAI,cAAc,GAAG;AACrB,UAAI,YAAY;AAChB,kBAAY,KAAK,UAAU;AAAA,IAC/B;AAAA,EACJ;AAGA,MAAI,sCAAsC,iBAAiB,YAAY;AACnE,eAAW,oBAAoB;AAC/B,eAAW,cAAc;AAAA,EAC7B;AACJ;AACA,SAAS,eAAe,YAAY;AAEhC,MAAI,MAAM,WAAW;AACrB,aAAW,YAAY,CAAC;AACxB,MAAI,IAAI,IAAI;AACZ,SAAO;AACH,mBAAe,IAAI,CAAC,GAAG,UAAU;AACrC,aAAW,oBAAoB,iBAAiB;AACpD;AACA,SAAS,UAAUC,SAAQ;AACvB,MAAI,OAAO,eAAe;AAC1B,MAAI,MAAMA,QAAO;AACjB,eAAa,IAAI;AACjB,SAAO;AACX;AACA,SAAS,iBAAiB;AACtB,MAAI,OAAO,YAAY;AACvB,cAAY,qBAAqB;AACjC,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,cAAY,qBAAqB;AACrC;AACA,SAAS,qBAAqB,iBAAiB;AAC3C,MAAI,OAAO,YAAY;AACvB,cAAY,kBAAkB;AAC9B,SAAO;AACX;AACA,SAAS,mBAAmB,MAAM;AAC9B,cAAY,kBAAkB;AAClC;AAKA,SAAS,2BAA2B,YAAY;AAC5C,MAAI,WAAW,sBAAsB,iBAAiB;AAClD;AACJ,aAAW,oBAAoB,iBAAiB;AAChD,MAAI,MAAM,WAAW;AACrB,MAAI,IAAI,IAAI;AACZ,SAAO;AACH,QAAI,CAAC,EAAE,sBAAsB,iBAAiB;AACtD;AAIA,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,yBAAyB,OAAO,yBAAyB,WAAY;AAAE,GAAG,MAAM;AACpF,IAAI,6BAA6B,0BAA0B,uBAAuB;AAClF,SAAS,aAAa,YAAY,IAAI;AAClC,MAAI,MAAuC;AACvC,cAAU,OAAO,OAAO,YAAY,2CAA2C;AAC/E,QAAI,OAAO,eAAe,YAAY,CAAC;AACnC,WAAK,4CAA4C,aAAa,GAAG;AAAA,EACzE;AACA,MAAI,MAAM,WAAY;AAClB,WAAO,cAAc,YAAY,IAAI,MAAM,SAAS;AAAA,EACxD;AACA,MAAI,MAAuC;AACvC,QAAI,4BAA4B;AAC5B,aAAO,eAAe,KAAK,QAAQ,EAAE,OAAO,WAAW,CAAC;AAAA,IAC5D;AAAA,EACJ;AACA,MAAI,eAAe;AACnB,SAAO;AACX;AACA,SAAS,cAAc,YAAY,IAAI,OAAO,MAAM;AAChD,MAAI,UAAU,aAAa,YAAY,OAAO,IAAI;AAClD,MAAI;AACA,WAAO,GAAG,MAAM,OAAO,IAAI;AAAA,EAC/B,SACO,KAAK;AACR,YAAQ,QAAQ;AAChB,UAAM;AAAA,EACV,UACA;AACI,eAAW,OAAO;AAAA,EACtB;AACJ;AACA,SAAS,aAAa,YAAY,OAAO,MAAM;AAC3C,MAAI,YAAY,aAAa,KAAK,CAAC,CAAC;AACpC,MAAI,YAAY;AAChB,MAAI,WAAW;AACX,gBAAY,KAAK,IAAI;AACrB,QAAI,IAAK,QAAQ,KAAK,UAAW;AACjC,QAAI,eAAe,IAAI,MAAM,CAAC;AAC9B,QAAI,IAAI;AACJ,eAAS,IAAI,GAAG,IAAI,GAAG;AACnB,qBAAa,CAAC,IAAI,KAAK,CAAC;AAChC,mBAAe;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,IACf,CAAC;AAAA,EACL;AACA,MAAI,iBAAiB,eAAe;AACpC,aAAW;AACX,MAAI,wBAAwB,uBAAuB,IAAI;AACvD,MAAI,sBAAsB,qBAAqB,IAAI;AACnD,MAAI,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,EACpB;AACA,oBAAkB,QAAQ;AAC1B,SAAO;AACX;AACA,SAAS,WAAW,SAAS;AACzB,MAAI,oBAAoB,QAAQ,UAAU;AACtC,SAAK,2DAA2D;AAAA,EACpE;AACA,oBAAkB,QAAQ;AAC1B,MAAI,QAAQ,UAAU,QAAW;AAC7B,gBAAY,yBAAyB;AAAA,EACzC;AACA,uBAAqB,QAAQ,qBAAqB;AAClD,qBAAmB,QAAQ,mBAAmB;AAC9C,WAAS;AACT,eAAa,QAAQ,cAAc;AACnC,MAAI,QAAQ,WAAW;AACnB,iBAAa,EAAE,MAAM,KAAK,IAAI,IAAI,QAAQ,UAAU,CAAC;AAAA,EACzD;AACA,cAAY,yBAAyB;AACzC;AACA,SAAS,kBAAkBC,oBAAmB,MAAM;AAChD,MAAI,OAAO,uBAAuBA,kBAAiB;AACnD,MAAI;AACJ,MAAI;AACA,UAAM,KAAK;AAAA,EACf,UACA;AACI,yBAAqB,IAAI;AAAA,EAC7B;AACA,SAAO;AACX;AACA,SAAS,uBAAuBA,oBAAmB;AAC/C,MAAI,OAAO,YAAY;AACvB,cAAY,oBAAoBA;AAChC,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM;AAChC,cAAY,oBAAoB;AACpC;AACA,SAAS,gCAAgC,MAAM;AAC3C,MAAI,OAAO,YAAY;AACvB,cAAY,mBAAmB;AAC/B,MAAI;AACJ,MAAI;AACA,UAAM,KAAK;AAAA,EACf,UACA;AACI,gBAAY,mBAAmB;AAAA,EACnC;AACA,SAAO;AACX;AAEA,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACnD,cAAUC,kBAAiB,MAAM;AACjC,aAASA,iBAAgB,OAAO,UAAU,MAAM,WAAW,QAAQ;AAC/D,UAAI,SAAS,QAAQ;AAAE,eAAO,qBAAqB,UAAU;AAAA,MAAG;AAChE,UAAI,cAAc,QAAQ;AAAE,oBAAY;AAAA,MAAM;AAC9C,UAAI,WAAW,QAAQ;AAAE,iBAAS,SAAS;AAAA,MAAS;AACpD,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,WAAW;AACjB,YAAM,OAAO;AACb,YAAM,SAAS;AACf,YAAM,sBAAsB;AAC5B,YAAM,QAAQ,SAAS,OAAO,QAAW,IAAI;AAC7C,UAAI,aAAa,aAAa,GAAG;AAE7B,kBAAU,EAAE,MAAM,UAAU,MAAM,MAAM,MAAM,UAAU,KAAK,MAAM,MAAM,CAAC;AAAA,MAC9E;AACA,aAAO;AAAA,IACX;AACA,IAAAA,iBAAgB,UAAU,eAAe,SAAU,OAAO;AACtD,UAAI,KAAK,aAAa;AAClB,eAAO,KAAK,SAAS,KAAK;AAC9B,aAAO;AAAA,IACX;AACA,IAAAA,iBAAgB,UAAU,MAAM,SAAU,UAAU;AAChD,UAAI,WAAW,KAAK;AACpB,iBAAW,KAAK,gBAAgB,QAAQ;AACxC,UAAI,aAAa,YAAY,WAAW;AACpC,YAAI,YAAY,aAAa;AAC7B,YAAI,WAAW;AACX,yBAAe;AAAA,YACX,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,YACX;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AACA,aAAK,YAAY,QAAQ;AACzB,YAAI;AACA,uBAAa;AAAA,MACrB;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,kBAAkB,SAAU,UAAU;AAC5D,0CAAoC,IAAI;AACxC,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN;AAAA,QACJ,CAAC;AACD,YAAI,CAAC;AACD,iBAAO,YAAY;AACvB,mBAAW,OAAO;AAAA,MACtB;AAEA,iBAAW,KAAK,SAAS,UAAU,KAAK,OAAO,KAAK,IAAI;AACxD,aAAO,KAAK,OAAO,KAAK,OAAO,QAAQ,IAAI,YAAY,YAAY;AAAA,IACvE;AACA,IAAAA,iBAAgB,UAAU,cAAc,SAAU,UAAU;AACxD,UAAI,WAAW,KAAK;AACpB,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,UAAI,aAAa,IAAI,GAAG;AACpB,wBAAgB,MAAM;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,MAAM,WAAY;AACxC,WAAK,eAAe;AACpB,aAAO,KAAK,aAAa,KAAK,KAAK;AAAA,IACvC;AACA,IAAAA,iBAAgB,UAAU,YAAY,SAAU,SAAS;AACrD,aAAO,oBAAoB,MAAM,OAAO;AAAA,IAC5C;AACA,IAAAA,iBAAgB,UAAU,UAAU,SAAU,UAAU,iBAAiB;AACrE,UAAI;AACA,iBAAS;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU,KAAK;AAAA,UACf,UAAU;AAAA,QACd,CAAC;AACL,aAAO,iBAAiB,MAAM,QAAQ;AAAA,IAC1C;AACA,IAAAA,iBAAgB,UAAU,SAAS,WAAY;AAC3C,aAAO,KAAK,IAAI;AAAA,IACpB;AACA,IAAAA,iBAAgB,UAAU,WAAW,WAAY;AAC7C,aAAO,KAAK,OAAO,MAAM,KAAK,QAAQ;AAAA,IAC1C;AACA,IAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,aAAO,YAAY,KAAK,IAAI,CAAC;AAAA,IACjC;AACA,WAAOA;AAAA,EACX,EAAE,IAAI;AAAA;AACN,gBAAgB,UAAU,gBAAgB,CAAC,IAAI,gBAAgB,UAAU;AACzE,IAAI,oBAAoB,0BAA0B,mBAAmB,eAAe;AAqBpF,IAAI;AAAA;AAAA,EAA+B,WAAY;AAa3C,aAASC,eAAc,SAAS;AAC5B,WAAK,oBAAoB,iBAAiB;AAC1C,WAAK,YAAY,CAAC;AAClB,WAAK,eAAe;AACpB,WAAK,kBAAkB;AACvB,WAAK,yBAAyB;AAC9B,WAAK,YAAY,CAAC;AAClB,WAAK,mBAAmB,CAAC;AACzB,WAAK,YAAY;AACjB,WAAK,QAAQ;AACb,WAAK,iBAAiB;AACtB,WAAK,sBAAsB,iBAAiB;AAC5C,WAAK,mBAAmB;AACxB,WAAK,UAAU,MAAM,UAAU;AAC/B,WAAK,QAAQ,IAAI,gBAAgB,IAAI;AACrC,WAAK,cAAc;AACnB,WAAK,kBAAkB;AACvB,WAAK,YAAY,UAAU;AAC3B,gBAAU,QAAQ,KAAK,kCAAkC;AACzD,WAAK,aAAa,QAAQ;AAC1B,WAAK,OAAO,QAAQ,QAAQ,mBAAmB,UAAU;AACzD,UAAI,QAAQ;AACR,aAAK,SAAS,aAAa,KAAK,OAAO,WAAW,QAAQ,GAAG;AACjE,WAAK,SACD,QAAQ,WACH,QAAQ,qBAAqB,QAAQ,SAChC,SAAS,aACT,SAAS;AACvB,WAAK,QAAQ,QAAQ;AACrB,WAAK,mBAAmB,CAAC,CAAC,QAAQ;AAClC,WAAK,YAAY,CAAC,CAAC,QAAQ;AAAA,IAC/B;AACA,IAAAA,eAAc,UAAU,gBAAgB,WAAY;AAChD,4BAAsB,IAAI;AAAA,IAC9B;AACA,IAAAA,eAAc,UAAU,qBAAqB,WAAY;AAAA,IAAE;AAC3D,IAAAA,eAAc,UAAU,mBAAmB,WAAY;AAAA,IAAE;AAKzD,IAAAA,eAAc,UAAU,MAAM,WAAY;AACtC,UAAI,KAAK;AACL,aAAK,mCAAmC,KAAK,OAAO,OAAO,KAAK,UAAU;AAC9E,UAAI,YAAY,YAAY,KAAK,KAAK,UAAU,WAAW,KAAK,CAAC,KAAK,WAAW;AAC7E,YAAI,cAAc,IAAI,GAAG;AACrB,eAAK,uBAAuB;AAC5B,qBAAW;AACX,eAAK,QAAQ,KAAK,aAAa,KAAK;AACpC,mBAAS;AAAA,QACb;AAAA,MACJ,OACK;AACD,uBAAe,IAAI;AACnB,YAAI,cAAc,IAAI;AAClB,cAAI,KAAK,gBAAgB;AACrB,qCAAyB,IAAI;AAAA;AAAA,MACzC;AACA,UAAI,SAAS,KAAK;AAClB,UAAI,kBAAkB,MAAM;AACxB,cAAM,OAAO;AACjB,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,OAAO,WAAY;AACvC,UAAI,MAAM,KAAK,aAAa,KAAK;AACjC,UAAI,kBAAkB,GAAG;AACrB,cAAM,IAAI;AACd,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,MAAM,SAAU,OAAO;AAC3C,UAAI,KAAK,QAAQ;AACb,kBAAU,CAAC,KAAK,iBAAiB,mCAAmC,KAAK,OAAO,iHAAiH;AACjM,aAAK,kBAAkB;AACvB,YAAI;AACA,eAAK,OAAO,KAAK,KAAK,OAAO,KAAK;AAAA,QACtC,UACA;AACI,eAAK,kBAAkB;AAAA,QAC3B;AAAA,MACJ;AAEI,kBAAU,OACN,qBAAqB,KAAK,OAAO,kEAAkE;AAAA,IAC/G;AACA,IAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,UAAI,aAAa,GAAG;AAChB,kBAAU;AAAA,UACN,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,MAAM,KAAK;AAAA,QACf,CAAC;AAAA,MACL;AACA,UAAI,WAAW,KAAK;AACpB,UAAI;AAAA;AAAA,QACY,KAAK,sBAAsB,iBAAiB;AAAA;AAC5D,UAAI,WAAW,KAAK,aAAa,IAAI;AACrC,UAAI,UAAU,gBACV,kBAAkB,QAAQ,KAC1B,kBAAkB,QAAQ,KAC1B,CAAC,KAAK,OAAO,UAAU,QAAQ;AACnC,UAAI,SAAS;AACT,aAAK,QAAQ;AAAA,MACjB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,OAAO;AACpD,WAAK,cAAc;AACnB,kBAAY;AACZ,UAAI;AACJ,UAAI,OAAO;AACP,cAAM,qBAAqB,MAAM,KAAK,YAAY,KAAK,KAAK;AAAA,MAChE,OACK;AACD,YAAI,YAAY,2BAA2B,MAAM;AAC7C,gBAAM,KAAK,WAAW,KAAK,KAAK,KAAK;AAAA,QACzC,OACK;AACD,cAAI;AACA,kBAAM,KAAK,WAAW,KAAK,KAAK,KAAK;AAAA,UACzC,SACO,GAAG;AACN,kBAAM,IAAI,gBAAgB,CAAC;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AACA,kBAAY;AACZ,WAAK,cAAc;AACnB,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,UAAI,CAAC,KAAK,WAAW;AACjB,uBAAe,IAAI;AACnB,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,UAAU,SAAU,UAAU,iBAAiB;AACnE,UAAI,QAAQ;AACZ,UAAI,YAAY;AAChB,UAAI,YAAY;AAChB,aAAO,QAAQ,WAAY;AACvB,YAAI,WAAW,MAAM,IAAI;AACzB,YAAI,CAAC,aAAa,iBAAiB;AAC/B,cAAI,QAAQ,eAAe;AAC3B,mBAAS;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,YACR;AAAA,YACA,UAAU;AAAA,UACd,CAAC;AACD,uBAAa,KAAK;AAAA,QACtB;AACA,oBAAY;AACZ,oBAAY;AAAA,MAChB,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,yBAAyB,WAAY;AACzD,UAAI;AACA;AACJ,UAAI,KAAK,qBAAqB,MAAM;AAChC,aAAK,2BAA2B,KAAK,OAAO,qCAAqC;AAAA,MACrF;AACA,UAAI,KAAK,cAAc,UAAU,MAAM;AACnC,gBAAQ,IAAI,mBAAmB,KAAK,OAAO,oEAAoE;AAAA,MACnH;AACA,UAAI,YAAY,0BAA0B;AACtC,gBAAQ,KAAK,2BAA2B,KAAK,OAAO,mEAAmE;AAAA,MAC3H;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,SAAS,WAAY;AACzC,aAAO,KAAK,IAAI;AAAA,IACpB;AACA,IAAAA,eAAc,UAAU,WAAW,WAAY;AAC3C,aAAO,KAAK,OAAO,MAAM,KAAK,WAAW,SAAS,IAAI;AAAA,IAC1D;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,aAAO,YAAY,KAAK,IAAI,CAAC;AAAA,IACjC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,cAAc,UAAU,gBAAgB,CAAC,IAAI,cAAc,UAAU;AACrE,IAAI,kBAAkB,0BAA0B,iBAAiB,aAAa;AAK9E,IAAI,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAI;AAAA;AAAA,EAA6B,2BAAY;AACzC,aAASC,eAAc;AASnB,WAAK,UAAU;AAIf,WAAK,YAAY,CAAC;AAIlB,WAAK,qBAAqB;AAI1B,WAAK,mBAAmB;AAIxB,WAAK,QAAQ;AAIb,WAAK,WAAW;AAIhB,WAAK,UAAU;AAOf,WAAK,wBAAwB,CAAC;AAI9B,WAAK,mBAAmB,CAAC;AAIzB,WAAK,qBAAqB;AAM1B,WAAK,oBAAoB;AAKzB,WAAK,kBAAkB;AAIvB,WAAK,iBAAiB;AAItB,WAAK,eAAe,CAAC;AAIrB,WAAK,8BAA8B,CAAC;AAIpC,WAAK,2BAA2B;AAKhC,WAAK,6BAA6B;AAKlC,WAAK,6BAA6B;AAKlC,WAAK,uBAAuB;AAK5B,WAAK,yBAAyB;AAK9B,WAAK,yBAAyB;AAAA,IAClC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI,sBAAsB;AAC1B,IAAI,gBAAgB;AACpB,IAAI,cAAe,WAAY;AAC3B,MAAIC,UAAS,UAAU;AACvB,MAAIA,QAAO,sBAAsB,KAAK,CAACA,QAAO;AAC1C,0BAAsB;AAC1B,MAAIA,QAAO,iBAAiBA,QAAO,cAAc,YAAY,IAAI,YAAY,EAAE;AAC3E,0BAAsB;AAC1B,MAAI,CAAC,qBAAqB;AACtB,eAAW,WAAY;AACnB,UAAI,CAAC,eAAe;AAChB,aAAK,4IAA4I;AAAA,MACrJ;AAAA,IACJ,GAAG,CAAC;AACJ,WAAO,IAAI,YAAY;AAAA,EAC3B,WACSA,QAAO,eAAe;AAC3B,IAAAA,QAAO,uBAAuB;AAC9B,QAAI,CAACA,QAAO,cAAc;AACtB,MAAAA,QAAO,cAAc,YAAY,CAAC;AACtC,WAAOA,QAAO;AAAA,EAClB,OACK;AACD,IAAAA,QAAO,sBAAsB;AAC7B,WAAQA,QAAO,gBAAgB,IAAI,YAAY;AAAA,EACnD;AACJ,EAAG;AACH,SAAS,qBAAqB;AAC1B,MAAI,YAAY,iBAAiB,UAC7B,YAAY,WACZ,YAAY;AACZ,SAAK,0EAA0E;AACnF,kBAAgB;AAChB,MAAI,qBAAqB;AACrB,QAAI,EAAE,UAAU,EAAE,wBAAwB;AACtC,gBAAU,EAAE,gBAAgB;AAChC,kBAAc,IAAI,YAAY;AAAA,EAClC;AACJ;AACA,SAAS,iBAAiB;AACtB,SAAO;AACX;AAKA,SAAS,mBAAmB;AACxB,MAAI,iBAAiB,IAAI,YAAY;AACrC,WAAS,OAAO;AACZ,QAAI,eAAe,QAAQ,GAAG,MAAM;AAChC,kBAAY,GAAG,IAAI,eAAe,GAAG;AAC7C,cAAY,oBAAoB,CAAC,YAAY;AACjD;AAEA,SAAS,aAAaN,aAAY;AAC9B,SAAOA,YAAW,aAAaA,YAAW,UAAU,SAAS;AACjE;AACA,SAAS,aAAaA,aAAY;AAC9B,SAAOA,YAAW;AACtB;AAkBA,SAAS,YAAYA,aAAY,MAAM;AAInC,MAAI,IAAIA,YAAW,UAAU;AAC7B,MAAI,GAAG;AAEH,IAAAA,YAAW,iBAAiB,KAAK,OAAO,IAAI;AAAA,EAChD;AACA,EAAAA,YAAW,UAAU,CAAC,IAAI;AAC1B,MAAIA,YAAW,sBAAsB,KAAK;AACtC,IAAAA,YAAW,sBAAsB,KAAK;AAG9C;AACA,SAAS,eAAeA,aAAY,MAAM;AAItC,MAAIA,YAAW,UAAU,WAAW,GAAG;AAEnC,IAAAA,YAAW,UAAU,SAAS;AAC9B,0BAAsBA,WAAU;AAAA,EACpC,OACK;AAED,QAAI,OAAOA,YAAW;AACtB,QAAI,MAAMA,YAAW;AACrB,QAAI,SAAS,KAAK,IAAI;AACtB,QAAI,WAAW,MAAM;AAEjB,UAAI,QAAQ,IAAI,KAAK,OAAO,KAAK;AACjC,UAAI,OAAO;AAEP,YAAI,OAAO,OAAO,IAAI;AAAA,MAC1B,OACK;AACD,eAAO,IAAI,OAAO,OAAO;AAAA,MAC7B;AACA,WAAK,KAAK,IAAI;AAAA,IAClB;AACA,WAAO,IAAI,KAAK,OAAO;AAAA,EAC3B;AAGJ;AACA,SAAS,sBAAsBA,aAAY;AACvC,MAAIA,YAAW,2BAA2B,OAAO;AAE7C,IAAAA,YAAW,yBAAyB;AACpC,gBAAY,sBAAsB,KAAKA,WAAU;AAAA,EACrD;AACJ;AAMA,SAAS,aAAa;AAClB,cAAY;AAChB;AACA,SAAS,WAAW;AAChB,MAAI,EAAE,YAAY,YAAY,GAAG;AAC7B,iBAAa;AAEb,QAAI,OAAO,YAAY;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAIA,cAAa,KAAK,CAAC;AACvB,MAAAA,YAAW,yBAAyB;AACpC,UAAIA,YAAW,UAAU,WAAW,GAAG;AACnC,YAAIA,YAAW,iBAAiB;AAE5B,UAAAA,YAAW,kBAAkB;AAC7B,UAAAA,YAAW,mBAAmB;AAAA,QAClC;AACA,YAAIA,uBAAsB,eAAe;AAGrC,UAAAA,YAAW,QAAQ;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AACA,gBAAY,wBAAwB,CAAC;AAAA,EACzC;AACJ;AACA,SAAS,eAAeA,aAAY;AAChC,8BAA4BA,WAAU;AACtC,MAAI,aAAa,YAAY;AAC7B,MAAI,eAAe,MAAM;AAMrB,QAAI,WAAW,UAAUA,YAAW,gBAAgB;AAChD,MAAAA,YAAW,iBAAiB,WAAW;AACvC,iBAAW,aAAa,WAAW,kBAAkB,IAAIA;AACzD,UAAI,CAACA,YAAW,iBAAiB;AAC7B,QAAAA,YAAW,kBAAkB;AAC7B,QAAAA,YAAW,iBAAiB;AAAA,MAChC;AAAA,IACJ;AACA,WAAO;AAAA,EACX,WACSA,YAAW,UAAU,WAAW,KAAK,YAAY,UAAU,GAAG;AACnE,0BAAsBA,WAAU;AAAA,EACpC;AACA,SAAO;AACX;AAsBA,SAAS,iBAAiBA,aAAY;AAElC,MAAIA,YAAW,wBAAwB,iBAAiB;AACpD;AACJ,EAAAA,YAAW,sBAAsB,iBAAiB;AAClD,MAAI,YAAYA,YAAW;AAC3B,MAAI,IAAI,UAAU;AAClB,SAAO,KAAK;AACR,QAAI,IAAI,UAAU,CAAC;AACnB,QAAI,EAAE,sBAAsB,iBAAiB,YAAY;AACrD,UAAI,EAAE,cAAc,UAAU,MAAM;AAChC,qBAAa,GAAGA,WAAU;AAAA,MAC9B;AACA,QAAE,cAAc;AAAA,IACpB;AACA,MAAE,oBAAoB,iBAAiB;AAAA,EAC3C;AAEJ;AAEA,SAAS,yBAAyBA,aAAY;AAE1C,MAAIA,YAAW,wBAAwB,iBAAiB;AACpD;AACJ,EAAAA,YAAW,sBAAsB,iBAAiB;AAClD,MAAI,YAAYA,YAAW;AAC3B,MAAI,IAAI,UAAU;AAClB,SAAO,KAAK;AACR,QAAI,IAAI,UAAU,CAAC;AACnB,QAAI,EAAE,sBAAsB,iBAAiB;AACzC,QAAE,oBAAoB,iBAAiB;AAAA,aAClC,EAAE,sBAAsB,iBAAiB;AAE9C,MAAAA,YAAW,sBAAsB,iBAAiB;AAAA,EAC1D;AAEJ;AAEA,SAAS,sBAAsBA,aAAY;AAEvC,MAAIA,YAAW,wBAAwB,iBAAiB;AACpD;AACJ,EAAAA,YAAW,sBAAsB,iBAAiB;AAClD,MAAI,YAAYA,YAAW;AAC3B,MAAI,IAAI,UAAU;AAClB,SAAO,KAAK;AACR,QAAI,IAAI,UAAU,CAAC;AACnB,QAAI,EAAE,sBAAsB,iBAAiB,YAAY;AACrD,QAAE,oBAAoB,iBAAiB;AACvC,UAAI,EAAE,cAAc,UAAU,MAAM;AAChC,qBAAa,GAAGA,WAAU;AAAA,MAC9B;AACA,QAAE,cAAc;AAAA,IACpB;AAAA,EACJ;AAEJ;AACA,SAAS,aAAa,YAAYA,aAAY;AAC1C,UAAQ,IAAI,mBAAmB,WAAW,OAAO,2CAA2CA,YAAW,OAAO,GAAG;AACjH,MAAI,WAAW,cAAc,UAAU,OAAO;AAC1C,QAAI,QAAQ,CAAC;AACb,iBAAa,kBAAkB,UAAU,GAAG,OAAO,CAAC;AAEpD,QAAI,SAAS,6BAA6B,WAAW,OAAO,gEAAgE,WAAW,OAAO,4BAA4BA,YAAW,OAAO,6OAA6O,sBAAsB,gBAAgB,WAAW,WAAW,SAAS,EAAE,QAAQ,UAAU,GAAG,IAAI,MAAM,sDAAsD,MAAM,KAAK,IAAI,IAAI,YAAY,EAAE;AAAA,EAC1mB;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,OAAO;AACtC,MAAI,MAAM,UAAU,KAAM;AACtB,UAAM,KAAK,iBAAiB;AAC5B;AAAA,EACJ;AACA,QAAM,KAAK,KAAK,IAAI,MAAM,KAAK,EAAE,KAAK,GAAI,IAAI,KAAK,IAAI;AACvD,MAAI,KAAK;AACL,SAAK,aAAa,QAAQ,SAAU,OAAO;AAAE,aAAO,aAAa,OAAO,OAAO,QAAQ,CAAC;AAAA,IAAG,CAAC;AACpG;AAEA,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASO,UAAS,MAAM,cAAc,cAAc,oBAAoB;AACpE,UAAI,SAAS,QAAQ;AAAE,eAAO,cAAc,UAAU;AAAA,MAAG;AACzD,UAAI,uBAAuB,QAAQ;AAAE,6BAAqB;AAAA,MAAO;AACjE,WAAK,OAAO;AACZ,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,qBAAqB;AAC1B,WAAK,YAAY,CAAC;AAClB,WAAK,eAAe,CAAC;AACrB,WAAK,oBAAoB,iBAAiB;AAC1C,WAAK,YAAY;AACjB,WAAK,QAAQ;AACb,WAAK,mBAAmB;AACxB,WAAK,UAAU,MAAM,UAAU;AAC/B,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAClB,WAAK,YAAY,UAAU;AAAA,IAC/B;AACA,IAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,WAAK,SAAS;AAAA,IAClB;AACA,IAAAA,UAAS,UAAU,WAAW,WAAY;AACtC,UAAI,CAAC,KAAK,cAAc;AACpB,aAAK,eAAe;AACpB,oBAAY,iBAAiB,KAAK,IAAI;AACtC,qBAAa;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,cAAc,WAAY;AACzC,aAAO,KAAK;AAAA,IAChB;AAIA,IAAAA,UAAS,UAAU,cAAc,WAAY;AACzC,UAAI,CAAC,KAAK,YAAY;AAClB,mBAAW;AACX,aAAK,eAAe;AACpB,YAAI,cAAc,IAAI,GAAG;AACrB,eAAK,kBAAkB;AACvB,cAAI;AACA,iBAAK,aAAa;AAClB,gBAAI,KAAK,mBAAmB,aAAa,GAAG;AAExC,wBAAU;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX,MAAM;AAAA,cACV,CAAC;AAAA,YACL;AAAA,UACJ,SACO,GAAG;AACN,iBAAK,4BAA4B,CAAC;AAAA,UACtC;AAAA,QACJ;AACA,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,QAAQ,SAAU,IAAI;AACrC,iBAAW;AACX,UAAI,SAAS,aAAa;AAC1B,UAAI;AACJ,UAAI,QAAQ;AACR,oBAAY,KAAK,IAAI;AACrB,uBAAe;AAAA,UACX,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AACA,WAAK,aAAa;AAClB,UAAI,SAAS,qBAAqB,MAAM,IAAI,MAAS;AACrD,WAAK,aAAa;AAClB,WAAK,kBAAkB;AACvB,UAAI,KAAK,YAAY;AAEjB,uBAAe,IAAI;AAAA,MACvB;AACA,UAAI,kBAAkB,MAAM;AACxB,aAAK,4BAA4B,OAAO,KAAK;AACjD,UAAI,QAAQ;AACR,qBAAa;AAAA,UACT,MAAM,KAAK,IAAI,IAAI;AAAA,QACvB,CAAC;AAAA,MACL;AACA,eAAS;AAAA,IACb;AACA,IAAAA,UAAS,UAAU,8BAA8B,SAAU,OAAO;AAC9D,UAAI,QAAQ;AACZ,UAAI,KAAK,cAAc;AACnB,aAAK,aAAa,OAAO,IAAI;AAC7B;AAAA,MACJ;AACA,UAAI,YAAY;AACZ,cAAM;AACV,UAAI,UAAU,wGAAwG,OAAO;AAC7H,UAAI,YAAY,wBAAwB;AACpC,gBAAQ,KAAK,gCAAgC,KAAK,OAAO,kDAAkD;AAAA,MAC/G,OACK;AACD,gBAAQ,MAAM,SAAS,KAAK;AAAA,MAEhC;AACA,UAAI,aAAa,GAAG;AAChB,kBAAU;AAAA,UACN,MAAM;AAAA,UACN,MAAM,KAAK;AAAA,UACX;AAAA,UACA,OAAO,KAAK;AAAA,QAChB,CAAC;AAAA,MACL;AACA,kBAAY,4BAA4B,QAAQ,SAAU,GAAG;AAAE,eAAO,EAAE,OAAO,KAAK;AAAA,MAAG,CAAC;AAAA,IAC5F;AACA,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,UAAI,CAAC,KAAK,YAAY;AAClB,aAAK,aAAa;AAClB,YAAI,CAAC,KAAK,YAAY;AAElB,qBAAW;AACX,yBAAe,IAAI;AACnB,mBAAS;AAAA,QACb;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,cAAc,WAAY;AACzC,UAAI,IAAI,KAAK,QAAQ,KAAK,IAAI;AAC9B,QAAE,QAAQ;AACV,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,WAAW,WAAY;AACtC,aAAO,cAAc,KAAK,OAAO;AAAA,IACrC;AACA,IAAAA,UAAS,UAAU,QAAQ,SAAU,iBAAiB;AAClD,UAAI,oBAAoB,QAAQ;AAAE,0BAAkB;AAAA,MAAO;AAC3D,YAAM,MAAM,eAAe;AAAA,IAC/B;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,SAAS,gBAAgB,SAAS;AAC9B,cAAY,4BAA4B,KAAK,OAAO;AACpD,SAAO,WAAY;AACf,QAAI,MAAM,YAAY,4BAA4B,QAAQ,OAAO;AACjE,QAAI,OAAO;AACP,kBAAY,4BAA4B,OAAO,KAAK,CAAC;AAAA,EAC7D;AACJ;AAMA,IAAI,0BAA0B;AAC9B,IAAI,oBAAoB,SAAU,GAAG;AAAE,SAAO,EAAE;AAAG;AACnD,SAAS,eAAe;AAEpB,MAAI,YAAY,UAAU,KAAK,YAAY;AACvC;AACJ,oBAAkB,kBAAkB;AACxC;AACA,SAAS,qBAAqB;AAC1B,cAAY,qBAAqB;AACjC,MAAI,eAAe,YAAY;AAC/B,MAAI,aAAa;AAIjB,SAAO,aAAa,SAAS,GAAG;AAC5B,QAAI,EAAE,eAAe,yBAAyB;AAC1C,cAAQ,MAAM,uDAAuD,0BAA0B,kBAC1F,0DAA0D,aAAa,CAAC,EAAE;AAC/E,mBAAa,OAAO,CAAC;AAAA,IACzB;AACA,QAAI,qBAAqB,aAAa,OAAO,CAAC;AAC9C,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG;AAClD,yBAAmB,CAAC,EAAE,YAAY;AAAA,EAC1C;AACA,cAAY,qBAAqB;AACrC;AACA,IAAI,aAAa,0BAA0B,YAAY,QAAQ;AAC/D,SAAS,qBAAqB,IAAI;AAC9B,MAAI,gBAAgB;AACpB,sBAAoB,SAAU,GAAG;AAAE,WAAO,GAAG,WAAY;AAAE,aAAO,cAAc,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAC5F;AAEA,SAAS,eAAe;AACpB,SAAO,CAAC,CAAC,YAAY,aAAa;AACtC;AACA,SAAS,UAAU,OAAO;AACtB,MAAI,CAAC,YAAY,aAAa;AAC1B;AACJ,MAAI,YAAY,YAAY;AAC5B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG;AACzC,cAAU,CAAC,EAAE,KAAK;AAC1B;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,gBAAgB,KAAK,CAAC;AACnE,YAAU,MAAM;AACpB;AACA,IAAI,YAAY,EAAE,cAAc,KAAK;AACrC,SAAS,aAAa,QAAQ;AAC1B,MAAI;AACA,cAAU,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,cAAc,KAAK,CAAC,CAAC;AAAA;AAEhE,cAAU,SAAS;AAC3B;AACA,SAAS,IAAI,UAAU;AACnB,cAAY,aAAa,KAAK,QAAQ;AACtC,SAAO,KAAK,WAAY;AACpB,gBAAY,eAAe,YAAY,aAAa,OAAO,SAAU,GAAG;AAAE,aAAO,MAAM;AAAA,IAAU,CAAC;AAAA,EACtG,CAAC;AACL;AAEA,SAAS,qBAAqB;AAC1B,OAA8C,qCAAqC;AACvF;AACA,SAAS,qBAAqB,MAAM;AAChC,SAAO,SAAU,QAAQ,MAAM,YAAY;AACvC,QAAI,YAAY;AACZ,UAA6C,WAAW,QAAQ,QAAW;AACvE,eAAO,KAAK,qCAAqC;AAAA,MACrD;AAGA,UAAI,WAAW,OAAO;AAElB,eAAO;AAAA,UACH,OAAO,aAAa,MAAM,WAAW,KAAK;AAAA,UAC1C,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA;AAAA,QACd;AAAA,MACJ;AAEA,UAAI,gBAAgB,WAAW;AAC/B,aAAO;AAAA,QACH,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,UAAU;AAAA,QACV,aAAa,WAAY;AAErB,iBAAO,aAAa,MAAM,cAAc,KAAK,IAAI,CAAC;AAAA,QACtD;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,qBAAqB,IAAI,EAAE,MAAM,MAAM,SAAS;AAAA,EAC3D;AACJ;AACA,SAAS,qBAAqB,MAAM;AAEhC,SAAO,SAAU,QAAQ,MAAM,YAAY;AACvC,WAAO,eAAe,QAAQ,MAAM;AAAA,MAChC,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,eAAO;AAAA,MACX;AAAA,MACA,KAAK,SAAU,OAAO;AAClB,sBAAc,MAAM,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,MACjD;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,qBAAqB,QAAQ,cAAc,YAAY,iBAAiB;AAC7E,MAAI,oBAAoB,MAAM;AAC1B,sBAAkB,QAAQ,cAAc,WAAW,KAAK;AACxD,WAAO;AAAA,EACX;AACA,MAAI,YAAY;AAIZ,WAAO;AAAA,MACH,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,0BAAkB,MAAM,cAAc,WAAW,SAAS,WAAW,YAAY,KAAK,IAAI,CAAC;AAC3F,eAAO,KAAK,YAAY;AAAA,MAC5B;AAAA,MACA,KAAK;AAAA,IACT;AAAA,EACJ;AAEA,SAAO;AAAA,IACH,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,KAAK,SAAU,GAAG;AACd,wBAAkB,MAAM,cAAc,CAAC;AAAA,IAC3C;AAAA,IACA,KAAK,WAAY;AACb,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAI,SAAS,SAASN,QAAO,MAAM,MAAM,MAAM,MAAM;AAEjD,MAAI,UAAU,WAAW,KAAK,OAAO,SAAS;AAC1C,WAAO,aAAa,KAAK,QAAQ,oBAAoB,IAAI;AAE7D,MAAI,UAAU,WAAW,KAAK,OAAO,SAAS;AAC1C,WAAO,aAAa,MAAM,IAAI;AAElC,MAAI,UAAU,WAAW,KAAK,OAAO,SAAS;AAC1C,WAAO,qBAAqB,IAAI;AAEpC,MAAI,SAAS,MAAM;AAEf,SAAK,IAAI,IAAI,aAAa,KAAK,QAAQ,MAAM,KAAK,KAAK;AAAA,EAC3D,OACK;AACD,WAAO,qBAAqB,IAAI,EAAE,MAAM,MAAM,SAAS;AAAA,EAC3D;AACJ;AACA,OAAO,QAAQ;AACf,SAAS,YAAY,MAAM,MAAM;AAE7B,MAAI,aAAa,OAAO,SAAS,WAAW,OAAO,KAAK,QAAQ;AAChE,MAAI,KAAK,OAAO,SAAS,aAAa,OAAO;AAC7C,MAAI,MAAuC;AACvC,cAAU,OAAO,OAAO,cAAc,GAAG,WAAW,GAAG,oDAAoD;AAC3G,QAAI,OAAO,eAAe,YAAY,CAAC;AACnC,WAAK,4CAA4C,aAAa,GAAG;AAAA,EACzE;AACA,SAAO,cAAc,YAAY,IAAI,MAAM,MAAS;AACxD;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,cAAc,MAAM,iBAAiB;AACjE;AACA,SAAS,kBAAkB,QAAQ,cAAc,IAAI;AACjD,gBAAc,QAAQ,cAAc,aAAa,cAAc,GAAG,KAAK,MAAM,CAAC,CAAC;AACnF;AAQA,SAAS,QAAQ,MAAM,MAAM;AACzB,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAc;AAC5C,MAAI,MAAuC;AACvC,cAAU,OAAO,SAAS,YAAY,8CAA8C;AACpF,cAAU,SAAS,IAAI,MAAM,OAAO,+DAA+D;AAAA,EACvG;AACA,MAAI,OAAQ,QAAQ,KAAK,QAAS,KAAK,QAAQ,aAAa,UAAU;AACtE,MAAI,UAAU,CAAC,KAAK,aAAa,CAAC,KAAK;AACvC,MAAIO;AACJ,MAAI,SAAS;AAET,IAAAA,YAAW,IAAI,SAAS,MAAM,WAAY;AACtC,WAAK,MAAM,cAAc;AAAA,IAC7B,GAAG,KAAK,SAAS,KAAK,kBAAkB;AAAA,EAC5C,OACK;AACD,QAAI,cAAc,2BAA2B,IAAI;AAEjD,QAAI,gBAAgB;AACpB,IAAAA,YAAW,IAAI,SAAS,MAAM,WAAY;AACtC,UAAI,CAAC,eAAe;AAChB,wBAAgB;AAChB,oBAAY,WAAY;AACpB,0BAAgB;AAChB,cAAI,CAACA,UAAS;AACV,YAAAA,UAAS,MAAM,cAAc;AAAA,QACrC,CAAC;AAAA,MACL;AAAA,IACJ,GAAG,KAAK,SAAS,KAAK,kBAAkB;AAAA,EAC5C;AACA,WAAS,iBAAiB;AACtB,SAAKA,SAAQ;AAAA,EACjB;AACA,EAAAA,UAAS,SAAS;AAClB,SAAOA,UAAS,YAAY;AAChC;AACA,IAAI,MAAM,SAAU,GAAG;AAAE,SAAO,EAAE;AAAG;AACrC,SAAS,2BAA2B,MAAM;AACtC,SAAO,KAAK,YACN,KAAK,YACL,KAAK,QACD,SAAU,GAAG;AAAE,WAAO,WAAW,GAAG,KAAK,KAAK;AAAA,EAAG,IACjD;AACd;AACA,SAAS,SAAS,YAAY,QAAQ,MAAM;AACxC,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAc;AAC5C,MAAI,OAAO,SAAS,WAAW;AAC3B,WAAO,EAAE,iBAAiB,KAAK;AAC/B,eAAW,0FAA0F;AAAA,EACzG;AACA,MAAI,MAAuC;AACvC,cAAU,OAAO,eAAe,YAAY,iDAAiD;AAC7F,cAAU,OAAO,SAAS,UAAU,iDAAiD;AAAA,EACzF;AACA,MAAI,OAAO,KAAK,QAAQ,cAAc,UAAU;AAChD,MAAI,eAAe,OAAO,MAAM,KAAK,UAAU,iBAAiB,KAAK,SAAS,MAAM,IAAI,MAAM;AAC9F,MAAI,UAAU,CAAC,KAAK,aAAa,CAAC,KAAK;AACvC,MAAI,YAAY,2BAA2B,IAAI;AAC/C,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,SAAS,KAAK,oBACZ,SAAS,aACT,KAAK,UAAU,SAAS;AAC9B,MAAI,IAAI,IAAI,SAAS,MAAM,WAAY;AACnC,QAAI,aAAa,SAAS;AACtB,qBAAe;AAAA,IACnB,WACS,CAAC,aAAa;AACnB,oBAAc;AACd,gBAAU,cAAc;AAAA,IAC5B;AAAA,EACJ,GAAG,KAAK,SAAS,KAAK,kBAAkB;AACxC,WAAS,iBAAiB;AACtB,kBAAc;AACd,QAAI,EAAE;AACF;AACJ,QAAI,UAAU;AACd,MAAE,MAAM,WAAY;AAChB,UAAI,YAAY,WAAW,CAAC;AAC5B,gBAAU,aAAa,CAAC,OAAO,OAAO,SAAS;AAC/C,cAAQ;AAAA,IACZ,CAAC;AACD,QAAI,aAAa,KAAK;AAClB,mBAAa,OAAO,CAAC;AACzB,QAAI,CAAC,aAAa,YAAY;AAC1B,mBAAa,OAAO,CAAC;AACzB,QAAI;AACA,kBAAY;AAAA,EACpB;AACA,IAAE,SAAS;AACX,SAAO,EAAE,YAAY;AACzB;AACA,SAAS,iBAAiB,cAAc,QAAQ;AAC5C,SAAO,WAAY;AACf,QAAI;AACA,aAAO,OAAO,MAAM,MAAM,SAAS;AAAA,IACvC,SACO,GAAG;AACN,mBAAa,KAAK,MAAM,CAAC;AAAA,IAC7B;AAAA,EACJ;AACJ;AAEA,SAAS,iBAAiB,OAAO,MAAM,MAAM;AACzC,SAAO,cAAc,oBAAoB,OAAO,MAAM,IAAI;AAC9D;AACA,SAAS,mBAAmB,OAAO,MAAM,MAAM;AAC3C,SAAO,cAAc,sBAAsB,OAAO,MAAM,IAAI;AAChE;AACA,SAAS,cAAc,MAAM,OAAO,MAAM,MAAM;AAC5C,MAAI,OAAO,OAAO,SAAS,aAAa,QAAQ,OAAO,IAAI,IAAI,QAAQ,KAAK;AAC5E,MAAI,KAAK,OAAO,SAAS,aAAa,OAAO;AAC7C,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,OAAO,SAAS;AAChB,WAAO,KAA8C,sCAAsC;AAC/F,OAAK,IAAI,IAAI,WAAY;AACrB,SAAK,KAAK,IAAI;AACd,OAAG,KAAK,IAAI;AAAA,EAChB;AACA,SAAO,WAAY;AACf,SAAK,IAAI,IAAI;AAAA,EACjB;AACJ;AAEA,SAAS,UAAU,SAAS;AACxB,MAAI,iBAAiB,QAAQ,gBAAgB,2BAA2B,QAAQ,0BAA0B,uBAAuB,QAAQ,sBAAsB,yBAAyB,QAAQ,wBAAwB,cAAc,QAAQ,aAAaC,qBAAoB,QAAQ,mBAAmB,6BAA6B,QAAQ,4BAA4B,6BAA6B,QAAQ;AAChZ,MAAI,QAAQ,uBAAuB,MAAM;AACrC,uBAAmB;AAAA,EACvB;AACA,MAAI,mBAAmB,QAAW;AAC9B,QAAI,OAAO,mBAAmB,aAAa,mBAAmB;AAC1D,iBAAW,2HAAmI;AAClJ,QAAI,KAAK;AACT,YAAQ,gBAAgB;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,aAAK;AACL;AAAA,MACJ;AACI,aAAK,0CAA0C,iBAAiB,6CAA6C;AAAA,IACrH;AACA,gBAAY,iBAAiB;AAC7B,gBAAY,oBAAoB,OAAO,QAAQ,OAAO,WAAW,QAAQ;AAAA,EAC7E;AACA,MAAI,6BAA6B,QAAW;AACxC,gBAAY,2BAA2B,CAAC,CAAC;AAAA,EAC7C;AACA,MAAI,+BAA+B,QAAW;AAC1C,gBAAY,6BAA6B,CAAC,CAAC;AAAA,EAC/C;AACA,MAAI,+BAA+B,QAAW;AAC1C,gBAAY,6BAA6B,CAAC,CAAC;AAC3C,gBAAY,kBAAkB,CAAC,YAAY;AAAA,EAC/C;AACA,MAAI,yBAAyB,QAAW;AACpC,gBAAY,uBAAuB,CAAC,CAAC;AAAA,EACzC;AACA,MAAI,2BAA2B,QAAW;AACtC,QAAI,2BAA2B;AAC3B,cAAQ,KAAK,+EAA+E;AAChG,gBAAY,yBAAyB,CAAC,CAAC;AAAA,EAC3C;AACA,MAAI,OAAO,gBAAgB,UAAU;AACjC,uBAAmB,WAAW;AAAA,EAClC;AACA,MAAIA,oBAAmB;AACnB,yBAAqBA,kBAAiB;AAAA,EAC1C;AACJ;AAEA,SAAS,SAAS,OAAO,YAAY;AACjC,MAA6C,CAAC,cAAc,UAAU;AAClE,SAAK,sCAAsC;AAC/C,MAAI,SAAS,OAAO,UAAU,aAAa,MAAM,YAAY;AAC7D,MAAI,UAAU,SAAUC,OAAM;AAC1B,QAAI,qBAAqB,WAAWA,KAAI;AACxC,QAAI,CAAC,MAAM,QAAQ,kBAAkB,GAAG;AACpC,2BAAqB,CAAC,kBAAkB;AAAA,IAC5C;AAEA,QAA6C,CAAC,mBAAmB,MAAM,SAAU,WAAW;AAAE,aAAO,OAAO,cAAc;AAAA,IAAY,CAAC;AACnI,WAAK,kFAAkFA,QAAO,GAAG;AACrG,QAAI,aAAa,OAAO,yBAAyB,QAAQA,KAAI;AAC7D,QAAI,gBAAgB,mBAAmB,OAAO,SAAU,eAAe,WAAW;AAAE,aAAO,UAAU,QAAQA,OAAM,aAAa;AAAA,IAAG,GAAG,UAAU;AAChJ,QAAI;AACA,aAAO,eAAe,QAAQA,OAAM,aAAa;AAAA,EACzD;AACA,WAAS,QAAQ,YAAY;AACzB,YAAQ,IAAI;AAAA,EAChB;AACA,SAAO;AACX;AAEA,SAAS,wBAAwB,QAAQ,YAAY,YAAY;AAC7D,aAAW,yGAAyG;AACpH,SAAO,iBAAiB,QAAQ,YAAY,YAAY,8BAA8B;AAC1F;AACA,SAAS,iBAAiB,QAAQ,YAAY,YAAY,SAAS;AAC/D,MAAI,MAAuC;AACvC,cAAU,UAAU,UAAU,KAAK,UAAU,UAAU,GAAG,2CAA2C;AACrG,cAAU,OAAO,WAAW,UAAU,wDAAwD;AAC9F,cAAU,CAAC,gBAAgB,MAAM,GAAG,sEAAsE;AAC1G,cAAU,CAAC,aAAa,UAAU,GAAG,uJAAuJ;AAC5L,QAAI;AACA,eAAS,OAAO;AACZ,YAAI,EAAE,OAAO;AACT,eAAK,6DAA6D,MAAM,GAAG;AAAA;AAAA,EAC3F;AACA,YAAU,0BAA0B,OAAO;AAC3C,MAAI,mBAAmB,QAAQ,qBAAqB,QAAQ,SAAS,QAAQ,eAAe;AAC5F,qBAAmB,MAAM;AACzB,qBAAmB,QAAQ,QAAQ,MAAM,iBAAiB,QAAQ;AAClE,aAAW;AACX,MAAI;AACA,QAAIC,QAAO,OAAO,oBAAoB,UAAU;AAChD,aAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,IAAI,GAAG,KAAK;AACzC,UAAI,MAAMA,MAAK,CAAC;AAChB,UAAI,aAAa,OAAO,yBAAyB,YAAY,GAAG;AAChE,UAAI,MAAuC;AACvC,YAAI,WAAW,WAAW,KAAK;AAC3B,eAAK,8HAA8H;AAAA,MAC3I;AACA,UAAI,YAAY,cAAc,OAAO,aAC/B,WAAW,GAAG,IACd,WAAW,MACP,oBACA;AACV,UAA6C,OAAO,cAAc;AAC9D,eAAO,KAAK,gCAAgC,MAAM,aAAa,SAAS;AAC5E,UAAI,mBAAmB,UAAU,QAAQ,KAAK,YAAY,IAAI;AAC9D,UAAI;AAEA,eAAO,eAAe,QAAQ,KAAK,gBAAgB;AAAA,IAC3D;AAAA,EACJ,UACA;AACI,aAAS;AAAA,EACb;AACA,SAAO;AACX;AAEA,SAAS,kBAAkB,OAAO,UAAU;AACxC,SAAO,qBAAqB,QAAQ,OAAO,QAAQ,CAAC;AACxD;AACA,SAAS,qBAAqB,MAAM;AAChC,MAAI,SAAS;AAAA,IACT,MAAM,KAAK;AAAA,EACf;AACA,MAAI,KAAK,aAAa,KAAK,UAAU,SAAS;AAC1C,WAAO,eAAe,OAAO,KAAK,SAAS,EAAE,IAAI,oBAAoB;AACzE,SAAO;AACX;AACA,SAAS,gBAAgB,OAAO,UAAU;AACtC,SAAO,mBAAmB,QAAQ,OAAO,QAAQ,CAAC;AACtD;AACA,SAAS,mBAAmB,MAAM;AAC9B,MAAI,SAAS;AAAA,IACT,MAAM,KAAK;AAAA,EACf;AACA,MAAI,aAAa,IAAI;AACjB,WAAO,YAAY,aAAa,IAAI,EAAE,IAAI,kBAAkB;AAChE,SAAO;AACX;AAEA,IAAI,cAAc;AAClB,SAAS,wBAAwB;AAC7B,OAAK,UAAU;AACnB;AACA,sBAAsB,YAAY,OAAO,OAAO,MAAM,SAAS;AAC/D,SAAS,wBAAwB,OAAO;AACpC,SAAO,iBAAiB;AAC5B;AACA,SAAS,KAAK,WAAW;AACrB,MAAI,UAAU,WAAW;AACrB,SAA+B,6DAA6D;AAChG,MAAI,OAAO,UAAU,QAAQ;AAE7B,SAAO,WAAY;AACf,QAAI,MAAM;AACV,QAAI,OAAO;AACX,QAAI,QAAQ,EAAE;AACd,QAAI,MAAM,OAAO,OAAO,eAAe,QAAQ,WAAW,SAAS,EAAE,MAAM,KAAK,IAAI;AACpF,QAAI;AACJ,QAAI,iBAAiB;AACrB,QAAI,MAAM,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC7C,UAAI,SAAS;AACb,iBAAW;AACX,eAAS,YAAYC,MAAK;AACtB,yBAAiB;AACjB,YAAI;AACJ,YAAI;AACA,gBAAM,OAAO,OAAO,eAAe,QAAQ,cAAc,UAAU,IAAI,IAAI,EAAE,KAAK,KAAKA,IAAG;AAAA,QAC9F,SACO,GAAG;AACN,iBAAO,OAAO,CAAC;AAAA,QACnB;AACA,aAAK,GAAG;AAAA,MACZ;AACA,eAAS,WAAW,KAAK;AACrB,yBAAiB;AACjB,YAAI;AACJ,YAAI;AACA,gBAAM,OAAO,OAAO,eAAe,QAAQ,cAAc,UAAU,IAAI,KAAK,EAAE,KAAK,KAAK,GAAG;AAAA,QAC/F,SACO,GAAG;AACN,iBAAO,OAAO,CAAC;AAAA,QACnB;AACA,aAAK,GAAG;AAAA,MACZ;AACA,eAAS,KAAK,KAAK;AACf,YAAI,OAAO,OAAO,IAAI,SAAS,YAAY;AAEvC,cAAI,KAAK,MAAM,MAAM;AACrB;AAAA,QACJ;AACA,YAAI,IAAI;AACJ,iBAAO,QAAQ,IAAI,KAAK;AAC5B,yBAAiB,QAAQ,QAAQ,IAAI,KAAK;AAC1C,eAAO,eAAe,KAAK,aAAa,UAAU;AAAA,MACtD;AACA,kBAAY,MAAS;AAAA,IACzB,CAAC;AACD,QAAI,SAAS,OAAO,OAAO,eAAe,QAAQ,aAAa,WAAY;AACvE,UAAI;AACA,YAAI;AACA,wBAAc,cAAc;AAEhC,YAAI,QAAQ,IAAI,OAAO,MAAS;AAEhC,YAAI,iBAAiB,QAAQ,QAAQ,MAAM,KAAK;AAChD,uBAAe,KAAK,MAAM,IAAI;AAC9B,sBAAc,cAAc;AAE5B,iBAAS,IAAI,sBAAsB,CAAC;AAAA,MACxC,SACO,GAAG;AACN,iBAAS,CAAC;AAAA,MACd;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,cAAc,SAAS;AAC5B,MAAI,OAAO,QAAQ,WAAW;AAC1B,YAAQ,OAAO;AACvB;AAEA,SAAS,eAAe,OAAO,eAAe,SAAS;AACnD,MAAI;AACJ,MAAI,gBAAgB,KAAK,KAAK,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,GAAG;AAChF,aAAS,kBAAkB,KAAK;AAAA,EACpC,WACS,mBAAmB,KAAK,GAAG;AAChC,QAAI,OAAO,kBAAkB;AACzB,aAAO,KACH,yFAAyF;AACjG,aAAS,kBAAkB,OAAO,aAAa;AAAA,EACnD,OACK;AACD,WAAO,KACH,yDAAyD;AAAA,EACjE;AACA,MAAI,OAAO,aAAa;AACpB,WAAO,KAA8C,6CAA6C;AACtG,SAAO,WAAW,OAAO,kBAAkB,aAAa,gBAAgB;AACxE,SAAO,WAAY;AACf,WAAO,WAAW;AAAA,EACtB;AACJ;AAEA,SAAS,UAAU,OAAO,eAAe,SAAS;AAC9C,MAAI,OAAO,YAAY;AACnB,WAAO,kBAAkB,OAAO,eAAe,OAAO;AAAA;AAEtD,WAAO,uBAAuB,OAAO,aAAa;AAC1D;AACA,SAAS,uBAAuB,OAAO,SAAS;AAC5C,SAAO,kBAAkB,KAAK,EAAE,UAAU,OAAO;AACrD;AACA,SAAS,kBAAkB,OAAO,UAAU,SAAS;AACjD,SAAO,kBAAkB,OAAO,QAAQ,EAAE,UAAU,OAAO;AAC/D;AAEA,SAAS,YAAY,OAAO,UAAU;AAClC,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,MAAI,aAAa,QAAW;AACxB,QAAI,mBAAmB,KAAK,MAAM;AAC9B,aAAO;AACX,QAAI,CAAC,MAAM,MAAM,OAAO,QAAQ;AAC5B,aAAO;AACX,QAAI,OAAO,QAAQ,OAAO,QAAQ;AAClC,WAAO,gBAAgB,IAAI;AAAA,EAC/B;AACA,SAAO,gBAAgB,KAAK;AAChC;AACA,SAAS,WAAW,OAAO;AACvB,MAAI,UAAU,SAAS;AACnB,WAAO,KACH,qGAAqG;AAC7G,SAAO,YAAY,KAAK;AAC5B;AACA,SAAS,eAAe,OAAO,UAAU;AACrC,MAAI,OAAO,aAAa;AACpB,WAAO,KACH,wDAAwD;AAChE,SAAO,YAAY,OAAO,QAAQ;AACtC;AAEA,SAAS,cAAc,OAAO,UAAU;AACpC,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,MAAI,aAAa,QAAW;AACxB,QACK,gBAAgB,KAAK,KAAK,kBAAkB,KAAK;AAClD,aAAO,KAAK,+GAA+G;AAC/H,QAAI,mBAAmB,KAAK,GAAG;AAC3B,UAAI,IAAI,MAAM;AACd,aAAO,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,QAAQ;AAAA,IAC1C;AACA,WAAO;AAAA,EACX;AAEA,SAAQ,mBAAmB,KAAK,KAC5B,CAAC,CAAC,MAAM,SACR,OAAO,KAAK,KACZ,WAAW,KAAK,KAChB,gBAAgB,KAAK;AAC7B;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,UAAU,WAAW;AACrB,SACI,uGAAuG;AAC/G,SAAO,cAAc,KAAK;AAC9B;AACA,SAAS,iBAAiB,OAAO,UAAU;AACvC,MAAI,OAAO,aAAa;AACpB,WAAO,KAA8C,6CAA6C;AACtG,SAAO,cAAc,OAAO,QAAQ;AACxC;AAEA,SAAS,KAAK,KAAK;AACf,MAAI,mBAAmB,GAAG,GAAG;AACzB,WAAO,IAAI,MAAM,QAAQ;AAAA,EAC7B;AACA,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,gBAAgB,IAAI,KAAK,CAAC;AAAA,EACrC;AACA,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,gBAAgB,IAAI,KAAK,CAAC;AAAA,EACrC;AACA,MAAI,kBAAkB,GAAG,GAAG;AACxB,WAAO,IAAI,IAAI,SAAU,GAAG,OAAO;AAAE,aAAO;AAAA,IAAO,CAAC;AAAA,EACxD;AACA,SAAO,KACH,wEAAwE;AAChF;AACA,SAAS,OAAO,KAAK;AACjB,MAAI,mBAAmB,GAAG,GAAG;AACzB,WAAO,KAAK,GAAG,EAAE,IAAI,SAAU,KAAK;AAAE,aAAO,IAAI,GAAG;AAAA,IAAG,CAAC;AAAA,EAC5D;AACA,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,KAAK,GAAG,EAAE,IAAI,SAAU,KAAK;AAAE,aAAO,IAAI,IAAI,GAAG;AAAA,IAAG,CAAC;AAAA,EAChE;AACA,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,gBAAgB,IAAI,OAAO,CAAC;AAAA,EACvC;AACA,MAAI,kBAAkB,GAAG,GAAG;AACxB,WAAO,IAAI,MAAM;AAAA,EACrB;AACA,SAAO,KACH,0EAA0E;AAClF;AACA,SAAS,QAAQ,KAAK;AAClB,MAAI,mBAAmB,GAAG,GAAG;AACzB,WAAO,KAAK,GAAG,EAAE,IAAI,SAAU,KAAK;AAAE,aAAO,CAAC,KAAK,IAAI,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EACnE;AACA,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,KAAK,GAAG,EAAE,IAAI,SAAU,KAAK;AAAE,aAAO,CAAC,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EACvE;AACA,MAAI,gBAAgB,GAAG,GAAG;AACtB,WAAO,gBAAgB,IAAI,QAAQ,CAAC;AAAA,EACxC;AACA,MAAI,kBAAkB,GAAG,GAAG;AACxB,WAAO,IAAI,IAAI,SAAU,KAAK,OAAO;AAAE,aAAO,CAAC,OAAO,GAAG;AAAA,IAAG,CAAC;AAAA,EACjE;AACA,SAAO,KACH,qEAAqE;AAC7E;AACA,SAAS,IAAI,KAAK,KAAK,OAAO;AAC1B,MAAI,UAAU,WAAW,KAAK,CAAC,gBAAgB,GAAG,GAAG;AACjD,eAAW;AACX,QAAI,WAAW;AACf,QAAI;AACA,eAAS,SAAS;AACd,YAAI,KAAK,OAAO,SAAS,KAAK,CAAC;AAAA,IACvC,UACA;AACI,eAAS;AAAA,IACb;AACA;AAAA,EACJ;AACA,MAAI,mBAAmB,GAAG,GAAG;AACzB,QAAI,MAAM,IAAI;AACd,QAAI,qBAAqB,IAAI,OAAO,GAAG;AACvC,QAAI,oBAAoB;AACpB,UAAI,MAAM,KAAK,KAAK,KAAK;AAAA,IAC7B,OACK;AACD,+BAAyB,KAAK,KAAK,OAAO,IAAI,eAAe;AAAA,IACjE;AAAA,EACJ,WACS,gBAAgB,GAAG,GAAG;AAC3B,QAAI,IAAI,KAAK,KAAK;AAAA,EACtB,WACS,gBAAgB,GAAG,GAAG;AAC3B,QAAI,IAAI,GAAG;AAAA,EACf,WACS,kBAAkB,GAAG,GAAG;AAC7B,QAAI,OAAO,QAAQ;AACf,YAAM,SAAS,KAAK,EAAE;AAC1B,cAAU,OAAO,GAAG,yBAAyB,MAAM,GAAG;AACtD,eAAW;AACX,QAAI,OAAO,IAAI;AACX,UAAI,SAAS,MAAM;AACvB,QAAI,GAAG,IAAI;AACX,aAAS;AAAA,EACb,OACK;AACD,WAAO,KACH,iEAAiE;AAAA,EACzE;AACJ;AACA,SAAS,OAAO,KAAK,KAAK;AACtB,MAAI,mBAAmB,GAAG,GAAG;AACzB,QAAI,MAAM,OAAO,GAAG;AAAA,EACxB,WACS,gBAAgB,GAAG,GAAG;AAC3B,QAAI,OAAO,GAAG;AAAA,EAClB,WACS,gBAAgB,GAAG,GAAG;AAC3B,QAAI,OAAO,GAAG;AAAA,EAClB,WACS,kBAAkB,GAAG,GAAG;AAC7B,QAAI,OAAO,QAAQ;AACf,YAAM,SAAS,KAAK,EAAE;AAC1B,cAAU,OAAO,GAAG,yBAAyB,MAAM,GAAG;AACtD,QAAI,OAAO,KAAK,CAAC;AAAA,EACrB,OACK;AACD,WAAO,KACH,oEAAoE;AAAA,EAC5E;AACJ;AACA,SAAS,IAAI,KAAK,KAAK;AACnB,MAAI,mBAAmB,GAAG,GAAG;AAEzB,QAAI,MAAM,kBAAkB,GAAG;AAC/B,QAAI,QAAQ;AACZ,WAAO,CAAC,CAAC,IAAI,OAAO,GAAG;AAAA,EAC3B,WACS,gBAAgB,GAAG,GAAG;AAC3B,WAAO,IAAI,IAAI,GAAG;AAAA,EACtB,WACS,gBAAgB,GAAG,GAAG;AAC3B,WAAO,IAAI,IAAI,GAAG;AAAA,EACtB,WACS,kBAAkB,GAAG,GAAG;AAC7B,WAAO,OAAO,KAAK,MAAM,IAAI;AAAA,EACjC,OACK;AACD,WAAO,KACH,iEAAiE;AAAA,EACzE;AACJ;AACA,SAAS,IAAI,KAAK,KAAK;AACnB,MAAI,CAAC,IAAI,KAAK,GAAG;AACb,WAAO;AACX,MAAI,mBAAmB,GAAG,GAAG;AACzB,WAAO,IAAI,GAAG;AAAA,EAClB,WACS,gBAAgB,GAAG,GAAG;AAC3B,WAAO,IAAI,IAAI,GAAG;AAAA,EACtB,WACS,kBAAkB,GAAG,GAAG;AAC7B,WAAO,IAAI,GAAG;AAAA,EAClB,OACK;AACD,WAAO,KACH,iEAAiE;AAAA,EACzE;AACJ;AAEA,SAAS,QAAQ,OAAO,UAAU,UAAU,iBAAiB;AACzD,MAAI,OAAO,aAAa;AACpB,WAAO,0BAA0B,OAAO,UAAU,UAAU,eAAe;AAAA;AAE3E,WAAO,kBAAkB,OAAO,UAAU,QAAQ;AAC1D;AACA,SAAS,kBAAkB,OAAO,UAAU,iBAAiB;AACzD,SAAO,kBAAkB,KAAK,EAAE,QAAQ,UAAU,eAAe;AACrE;AACA,SAAS,0BAA0B,OAAO,UAAU,UAAU,iBAAiB;AAC3E,SAAO,kBAAkB,OAAO,QAAQ,EAAE,QAAQ,UAAU,eAAe;AAC/E;AAEA,IAAI,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;AACA,SAAS,MAAM,KAAK,KAAK,OAAO,SAAS;AACrC,MAAI,QAAQ;AACR,QAAI,IAAI,KAAK,KAAK;AACtB,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,SAAS,eAAe;AAChD,MAAI,CAAC,QAAQ,qBAAqB,CAAC,aAAa,MAAM;AAClD,WAAO;AACX,MAAI,OAAO,WAAW;AAClB,WAAO;AAEX,MAAI,WAAW;AACX,WAAO;AAEX,MAAI,kBAAkB;AAClB,WAAO;AACX,MAAI,kBAAkB,MAAM;AACxB,WAAO,WAAW,OAAO,IAAI,GAAG,SAAS,aAAa;AAE1D,MAAI,aAAa,MAAM;AACnB,SAAK,MAAM;AACf,MAAI,eAAe,QAAQ,iBAAiB;AAC5C,MAAI,gBAAgB,WAAW,QAAQ,cAAc,IAAI,MAAM,GAAG;AAC9D,WAAO,cAAc,IAAI,MAAM;AAAA,EACnC;AACA,MAAI,kBAAkB,MAAM,KAAK,MAAM,QAAQ,MAAM,GAAG;AACpD,QAAI,QAAQ,MAAM,eAAe,QAAQ,CAAC,GAAG,OAAO;AACpD,QAAI,QAAQ,OAAO,IAAI,SAAU,OAAO;AAAE,aAAO,WAAW,OAAO,SAAS,aAAa;AAAA,IAAG,CAAC;AAC7F,UAAM,SAAS,MAAM;AACrB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG;AACrC,YAAM,CAAC,IAAI,MAAM,CAAC;AACtB,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB,MAAM,KAAK,OAAO,eAAe,MAAM,MAAM,IAAI,WAAW;AAC5E,QAAI,QAAQ,wBAAwB,OAAO;AACvC,UAAI,QAAQ,MAAM,eAAe,QAAQ,oBAAI,IAAI,GAAG,OAAO;AAC3D,aAAO,QAAQ,SAAU,OAAO;AAC5B,cAAM,IAAI,WAAW,OAAO,SAAS,aAAa,CAAC;AAAA,MACvD,CAAC;AACD,aAAO;AAAA,IACX,OACK;AACD,UAAI,QAAQ,MAAM,eAAe,QAAQ,CAAC,GAAG,OAAO;AACpD,aAAO,QAAQ,SAAU,OAAO;AAC5B,cAAM,KAAK,WAAW,OAAO,SAAS,aAAa,CAAC;AAAA,MACxD,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,gBAAgB,MAAM,KAAK,OAAO,eAAe,MAAM,MAAM,IAAI,WAAW;AAC5E,QAAI,QAAQ,wBAAwB,OAAO;AACvC,UAAI,QAAQ,MAAM,eAAe,QAAQ,oBAAI,IAAI,GAAG,OAAO;AAC3D,aAAO,QAAQ,SAAU,OAAOC,MAAK;AACjC,cAAM,IAAIA,MAAK,WAAW,OAAO,SAAS,aAAa,CAAC;AAAA,MAC5D,CAAC;AACD,aAAO;AAAA,IACX,OACK;AACD,UAAI,QAAQ,MAAM,eAAe,QAAQ,CAAC,GAAG,OAAO;AACpD,aAAO,QAAQ,SAAU,OAAOA,MAAK;AACjC,cAAMA,IAAG,IAAI,WAAW,OAAO,SAAS,aAAa;AAAA,MACzD,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,MAAI,MAAM,MAAM,eAAe,QAAQ,CAAC,GAAG,OAAO;AAClD,WAAS,OAAO,QAAQ;AACpB,QAAI,GAAG,IAAI,WAAW,OAAO,GAAG,GAAG,SAAS,aAAa;AAAA,EAC7D;AACA,SAAO;AACX;AACA,SAAS,KAAK,QAAQ,SAAS;AAE3B,MAAI,OAAO,YAAY;AACnB,cAAU,EAAE,cAAc,QAAQ;AACtC,MAAI,CAAC;AACD,cAAU;AACd,UAAQ,eACJ,QAAQ,iBAAiB,SACnB,QAAQ,sBAAsB,OAC9B,QAAQ,iBAAiB;AACnC,MAAI;AACJ,MAAI,QAAQ;AACR,oBAAgB,oBAAI,IAAI;AAC5B,SAAO,WAAW,QAAQ,SAAS,aAAa;AACpD;AAEA,SAAS,QAAQ;AACb,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,MAAI,kBAAkB;AACtB,MAAI,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM;AACjC,sBAAkB,KAAK,IAAI;AAC/B,MAAI,aAAa,gBAAgB,IAAI;AACrC,MAAI,CAAC,YAAY;AACb,WAAO,KACH,+IAA+I;AAAA,EACvJ;AACA,MAAI,WAAW,cAAc,UAAU,MAAM;AACzC,YAAQ,IAAI,mBAAmB,WAAW,OAAO,mBAAmB;AAAA,EACxE;AACA,aAAW,YAAY,kBAAkB,UAAU,QAAQ,UAAU;AACzE;AACA,SAAS,gBAAgB,MAAM;AAC3B,UAAQ,KAAK,QAAQ;AAAA,IACjB,KAAK;AACD,aAAO,YAAY;AAAA,IACvB,KAAK;AACD,aAAO,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1B,KAAK;AACD,aAAO,QAAQ,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EACvC;AACJ;AASA,SAAS,YAAYZ,SAAQ,SAAS;AAClC,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAW;AAC/C,aAAW;AACX,MAAI;AACA,WAAOA,QAAO,MAAM,OAAO;AAAA,EAC/B,UACA;AACI,aAAS;AAAA,EACb;AACJ;AAEA,SAAS,KAAK,WAAW,MAAM,MAAM;AACjC,MAAI,UAAU,WAAW,KAAM,QAAQ,OAAO,SAAS;AACnD,WAAO,YAAY,WAAW,IAAI;AACtC,SAAO,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC;AAC5C;AACA,SAAS,MAAM,WAAW,QAAQ,MAAM;AACpC,MAAI;AACJ,MAAI,OAAO,KAAK,YAAY,UAAU;AAClC,oBAAgB,WAAW,WAAY;AACnC,UAAI,CAAC,SAAS,MAAM,YAAY;AAC5B,iBAAS;AACT,YAAI,QAAQ,IAAI,MAAM,cAAc;AACpC,YAAI,KAAK;AACL,eAAK,QAAQ,KAAK;AAAA;AAElB,gBAAM;AAAA,MACd;AAAA,IACJ,GAAG,KAAK,OAAO;AAAA,EACnB;AACA,OAAK,OAAO,KAAK,QAAQ,UAAU,UAAU;AAC7C,MAAI,eAAe,aAAa,KAAK,OAAO,WAAW,MAAM;AAC7D,MAAI,WAAW,QAAQ,SAAU,GAAG;AAChC,QAAI,UAAU,GAAG;AACb,QAAE,QAAQ;AACV,UAAI;AACA,qBAAa,aAAa;AAC9B,mBAAa;AAAA,IACjB;AAAA,EACJ,GAAG,IAAI;AACP,SAAO;AACX;AACA,SAAS,YAAY,WAAW,MAAM;AAClC,MAA6C,QAAQ,KAAK;AACtD,WAAO,KAAK,wDAAwD;AACxE,MAAI;AACJ,MAAI,MAAM,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC7C,QAAI,WAAW,MAAM,WAAW,SAAS,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,SAAS,OAAO,CAAC,CAAC;AAC1F,aAAS,WAAY;AACjB,eAAS;AACT,aAAO,gBAAgB;AAAA,IAC3B;AAAA,EACJ,CAAC;AACD,MAAI,SAAS;AACb,SAAO;AACX;AAEA,SAAS,gBAAgB,eAAe;AACpC,SAAO,cAAc,iBAAiB,UAAa,cAAc,aAAa,SAAS;AAC3F;AACA,SAAS,oBAAoB,eAAe,SAAS;AACjD,MAAI,eAAe,cAAc,iBAAiB,cAAc,eAAe,CAAC;AAChF,eAAa,KAAK,OAAO;AACzB,SAAO,KAAK,WAAY;AACpB,QAAI,MAAM,aAAa,QAAQ,OAAO;AACtC,QAAI,QAAQ;AACR,mBAAa,OAAO,KAAK,CAAC;AAAA,EAClC,CAAC;AACL;AACA,SAAS,gBAAgB,eAAe,QAAQ;AAC5C,MAAI,QAAQ,eAAe;AAC3B,MAAI;AACA,QAAI,eAAe,cAAc;AACjC,QAAI;AACA,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK;AACjD,iBAAS,aAAa,CAAC,EAAE,MAAM;AAC/B,kBAAU,CAAC,UAAU,OAAO,MAAM,6DAA6D;AAC/F,YAAI,CAAC;AACD;AAAA,MACR;AACJ,WAAO;AAAA,EACX,UACA;AACI,iBAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,aAAa,YAAY;AAC9B,SAAO,WAAW,oBAAoB,UAAa,WAAW,gBAAgB,SAAS;AAC3F;AACA,SAAS,iBAAiB,YAAY,SAAS;AAC3C,MAAI,YAAY,WAAW,oBAAoB,WAAW,kBAAkB,CAAC;AAC7E,YAAU,KAAK,OAAO;AACtB,SAAO,KAAK,WAAY;AACpB,QAAI,MAAM,UAAU,QAAQ,OAAO;AACnC,QAAI,QAAQ;AACR,gBAAU,OAAO,KAAK,CAAC;AAAA,EAC/B,CAAC;AACL;AACA,SAAS,gBAAgB,YAAY,QAAQ;AACzC,MAAI,QAAQ,eAAe;AAC3B,MAAI,YAAY,WAAW;AAC3B,MAAI,CAAC;AACD;AACJ,cAAY,UAAU,MAAM;AAC5B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC9C,cAAU,CAAC,EAAE,MAAM;AAAA,EACvB;AACA,eAAa,KAAK;AACtB;AAEA,IAAI,kBAAkB;AAEtB,IAAI,sCAAuC,WAAY;AACnD,MAAI,IAAI;AACR,MAAI,IAAI,CAAC;AACT,SAAO,eAAe,GAAG,KAAK;AAAA,IAC1B,KAAK,WAAY;AACb,UAAI;AAAA,IACR;AAAA,EACJ,CAAC;AACD,SAAO,OAAO,CAAC,EAAE,GAAG,IAAI;AACxB,SAAO,MAAM;AACjB,EAAG;AAMH,IAAI,+BAA+B;AAEnC,IAAI;AAAA;AAAA,EAA2B,2BAAY;AACvC,aAASa,aAAY;AAAA,IACrB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,SAAS,QAAQ,MAAM,OAAO;AAC1B,MAAI,OAAO,OAAO,gBAAgB,MAAM,aAAa;AACjD,WAAO,gBAAgB,EAAE,KAAK,WAAW,KAAK;AAAA,EAClD,WACS,OAAO,KAAK,UAAU,cAAc,aAAa;AACtD,SAAK,UAAU,YAAY;AAAA,EAC/B,OACK;AACD,SAAK,WAAW,IAAI;AAAA,EACxB;AACJ;AACA,QAAQ,WAAW,MAAM,SAAS;AAIlC,IAAI,OAAO,SAAS,KAAK,GAAG;AACxB;AAAA,IACI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,QAAQ,SAAU,KAAK;AACrB,WAAO,eAAe,UAAU,WAAW,KAAK;AAAA,MAC5C,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO,MAAM,UAAU,GAAG;AAAA,IAC9B,CAAC;AAAA,EACL,CAAC;AACL;AACA,IAAI;AAAA;AAAA,EAA+C,WAAY;AAC3D,aAASC,+BAA8B,MAAM,UAAU,OAAO,OAAO;AACjE,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,WAAK,SAAS,CAAC;AACf,WAAK,kBAAkB;AACvB,WAAK,OAAO,IAAI,KAAK,QAAQ,qBAAqB,UAAU,CAAC;AAC7D,WAAK,WAAW,SAAU,MAAM,MAAM;AAAE,eAAO,SAAS,MAAM,MAAM,OAAO,MAAM;AAAA,MAAG;AAAA,IACxF;AACA,IAAAA,+BAA8B,UAAU,eAAe,SAAU,OAAO;AACpE,UAAI,KAAK,aAAa;AAClB,eAAO,KAAK,SAAS,KAAK;AAC9B,aAAO;AAAA,IACX;AACA,IAAAA,+BAA8B,UAAU,gBAAgB,SAAUC,SAAQ;AACtE,UAAI,KAAK,aAAa,UAAaA,QAAO,SAAS;AAC/C,eAAOA,QAAO,IAAI,KAAK,QAAQ;AACnC,aAAOA;AAAA,IACX;AACA,IAAAD,+BAA8B,UAAU,YAAY,SAAU,SAAS;AACnE,aAAO,oBAAoB,MAAM,OAAO;AAAA,IAC5C;AACA,IAAAA,+BAA8B,UAAU,UAAU,SAAU,UAAU,iBAAiB;AACnF,UAAI,oBAAoB,QAAQ;AAAE,0BAAkB;AAAA,MAAO;AAC3D,UAAI,iBAAiB;AACjB,iBAAS;AAAA,UACL,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO,KAAK,OAAO,MAAM;AAAA,UACzB,YAAY,KAAK,OAAO;AAAA,UACxB,SAAS,CAAC;AAAA,UACV,cAAc;AAAA,QAClB,CAAC;AAAA,MACL;AACA,aAAO,iBAAiB,MAAM,QAAQ;AAAA,IAC1C;AACA,IAAAA,+BAA8B,UAAU,iBAAiB,WAAY;AACjE,WAAK,KAAK,eAAe;AACzB,aAAO,KAAK,OAAO;AAAA,IACvB;AACA,IAAAA,+BAA8B,UAAU,iBAAiB,SAAU,WAAW;AAC1E,UAAI,OAAO,cAAc,YAAY,YAAY;AAC7C,cAAM,IAAI,MAAM,gCAAgC,SAAS;AAC7D,UAAI,gBAAgB,KAAK,OAAO;AAChC,UAAI,cAAc;AACd;AAAA,eACK,YAAY,eAAe;AAChC,YAAI,WAAW,IAAI,MAAM,YAAY,aAAa;AAClD,iBAAS,IAAI,GAAG,IAAI,YAAY,eAAe;AAC3C,mBAAS,CAAC,IAAI;AAClB,aAAK,gBAAgB,eAAe,GAAG,QAAQ;AAAA,MACnD;AAEI,aAAK,gBAAgB,WAAW,gBAAgB,SAAS;AAAA,IACjE;AAEA,IAAAA,+BAA8B,UAAU,oBAAoB,SAAU,WAAW,OAAO;AACpF,UAAI,cAAc,KAAK;AACnB,cAAM,IAAI,MAAM,4HAA4H;AAChJ,WAAK,mBAAmB;AACxB,UAAI,QAAQ,KAAK,YAAY,QAAQ,IAAI;AACrC,2BAAmB,YAAY,QAAQ,CAAC;AAAA,IAChD;AACA,IAAAA,+BAA8B,UAAU,kBAAkB,SAAU,OAAO,aAAa,UAAU;AAC9F,UAAI,QAAQ;AACZ,0CAAoC,KAAK,IAAI;AAC7C,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,UAAU;AACV,gBAAQ;AAAA,eACH,QAAQ;AACb,gBAAQ;AAAA,eACH,QAAQ;AACb,gBAAQ,KAAK,IAAI,GAAG,SAAS,KAAK;AACtC,UAAI,UAAU,WAAW;AACrB,sBAAc,SAAS;AAAA,eAClB,gBAAgB,UAAa,gBAAgB;AAClD,sBAAc;AAAA;AAEd,sBAAc,KAAK,IAAI,GAAG,KAAK,IAAI,aAAa,SAAS,KAAK,CAAC;AACnE,UAAI,aAAa;AACb,mBAAW;AACf,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN;AAAA,UACA,cAAc;AAAA,UACd,OAAO;AAAA,QACX,CAAC;AACD,YAAI,CAAC;AACD,iBAAO;AACX,sBAAc,OAAO;AACrB,mBAAW,OAAO;AAAA,MACtB;AACA,iBACI,SAAS,WAAW,IAAI,WAAW,SAAS,IAAI,SAAU,GAAG;AAAE,eAAO,MAAM,SAAS,GAAG,MAAS;AAAA,MAAG,CAAC;AACzG,UAAI,cAAc,SAAS,SAAS;AACpC,WAAK,kBAAkB,QAAQ,WAAW;AAC1C,UAAI,MAAM,KAAK,sBAAsB,OAAO,aAAa,QAAQ;AACjE,UAAI,gBAAgB,KAAK,SAAS,WAAW;AACzC,aAAK,kBAAkB,OAAO,UAAU,GAAG;AAC/C,aAAO,KAAK,cAAc,GAAG;AAAA,IACjC;AACA,IAAAA,+BAA8B,UAAU,wBAAwB,SAAU,OAAO,aAAa,UAAU;AACpG,UAAI;AACJ,UAAI,SAAS,SAAS,iBAAiB;AACnC,gBAAQ,KAAK,KAAK,QAAQ,OAAO,MAAM,IAAI,SAAS,CAAC,OAAO,WAAW,GAAG,QAAQ,CAAC;AAAA,MACvF,OACK;AACD,YAAI,MAAM,KAAK,OAAO,MAAM,OAAO,QAAQ,WAAW;AACtD,aAAK,SAAS,KAAK,OACd,MAAM,GAAG,KAAK,EACd,OAAO,UAAU,KAAK,OAAO,MAAM,QAAQ,WAAW,CAAC;AAC5D,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,+BAA8B,UAAU,yBAAyB,SAAU,OAAO,UAAU,UAAU;AAClG,UAAI,YAAY,CAAC,KAAK,SAAS,aAAa;AAC5C,UAAI,SAAS,aAAa,IAAI;AAC9B,UAAI,SAAS,UAAU,YACjB;AAAA,QACE,QAAQ,KAAK;AAAA,QACb,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACJ,IACE;AACN,UAAI;AACA,uBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,KAAK,KAAK,CAAC,CAAC;AAC3E,WAAK,KAAK,cAAc;AACxB,UAAI;AACA,wBAAgB,MAAM,MAAM;AAChC,UAAI;AACA,qBAAa;AAAA,IACrB;AACA,IAAAA,+BAA8B,UAAU,oBAAoB,SAAU,OAAO,OAAO,SAAS;AACzF,UAAI,YAAY,CAAC,KAAK,SAAS,aAAa;AAC5C,UAAI,SAAS,aAAa,IAAI;AAC9B,UAAI,SAAS,UAAU,YACjB;AAAA,QACE,QAAQ,KAAK;AAAA,QACb,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,QAAQ;AAAA,QACtB,YAAY,MAAM;AAAA,MACtB,IACE;AACN,UAAI;AACA,uBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,KAAK,KAAK,CAAC,CAAC;AAC3E,WAAK,KAAK,cAAc;AAExB,UAAI;AACA,wBAAgB,MAAM,MAAM;AAChC,UAAI;AACA,qBAAa;AAAA,IACrB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACnD,cAAUE,kBAAiB,MAAM;AACjC,aAASA,iBAAgB,eAAe,UAAU,MAAM,OAAO;AAC3D,UAAI,SAAS,QAAQ;AAAE,eAAO,qBAAqB,UAAU;AAAA,MAAG;AAChE,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAO;AACvC,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,UAAI,MAAM,IAAI,8BAA8B,MAAM,UAAU,OAAO,KAAK;AACxE,yBAAmB,OAAO,SAAS,GAAG;AACtC,UAAI,iBAAiB,cAAc,QAAQ;AACvC,YAAI,OAAO,uBAAuB,IAAI;AACtC,cAAM,gBAAgB,GAAG,GAAG,aAAa;AACzC,6BAAqB,IAAI;AAAA,MAC7B;AACA,UAAI,qCAAqC;AAGrC,eAAO,eAAe,IAAI,OAAO,KAAK,OAAO;AAAA,MACjD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,iBAAgB,UAAU,YAAY,SAAU,SAAS;AACrD,aAAO,KAAK,MAAM,UAAU,OAAO;AAAA,IACvC;AACA,IAAAA,iBAAgB,UAAU,UAAU,SAAU,UAAU,iBAAiB;AACrE,UAAI,oBAAoB,QAAQ;AAAE,0BAAkB;AAAA,MAAO;AAC3D,aAAO,KAAK,MAAM,QAAQ,UAAU,eAAe;AAAA,IACvD;AACA,IAAAA,iBAAgB,UAAU,QAAQ,WAAY;AAC1C,aAAO,KAAK,OAAO,CAAC;AAAA,IACxB;AACA,IAAAA,iBAAgB,UAAU,SAAS,WAAY;AAC3C,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,eAAO,EAAE,IAAI,UAAU,EAAE;AAAA,MAC7B;AACA,WAAK,MAAM,KAAK,eAAe;AAC/B,aAAO,MAAM,UAAU,OAAO,MAAM,KAAK,KAAK,GAAG,OAAO,IAAI,SAAU,GAAG;AAAE,eAAQ,kBAAkB,CAAC,IAAI,EAAE,KAAK,IAAI;AAAA,MAAI,CAAC,CAAC;AAAA,IAC/H;AACA,IAAAA,iBAAgB,UAAU,UAAU,SAAU,UAAU;AACpD,aAAO,KAAK,MAAM,gBAAgB,GAAG,KAAK,MAAM,OAAO,QAAQ,QAAQ;AAAA,IAC3E;AAKA,IAAAA,iBAAgB,UAAU,OAAO,WAAY;AACzC,aAAO,KAAK,MAAM;AAAA,IACtB;AACA,IAAAA,iBAAgB,UAAU,SAAS,WAAY;AAE3C,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,IAAAA,iBAAgB,UAAU,OAAO,WAAY;AACzC,WAAK,MAAM,KAAK,eAAe;AAC/B,aAAO,KAAK,MAAM,cAAc,KAAK,MAAM,MAAM;AAAA,IACrD;AAEA,IAAAA,iBAAgB,UAAU,OAAO,SAAU,WAAW,SAAS,WAAW;AACtE,UAAI,cAAc,QAAQ;AAAE,oBAAY;AAAA,MAAG;AAC3C,UAAI,UAAU,WAAW;AACrB,mBAAW,2FAA2F;AAC1G,UAAI,MAAM,KAAK,UAAU,MAAM,MAAM,SAAS;AAC9C,aAAO,QAAQ,KAAK,SAAY,KAAK,IAAI,GAAG;AAAA,IAChD;AAEA,IAAAA,iBAAgB,UAAU,YAAY,SAAU,WAAW,SAAS,WAAW;AAC3E,UAAI,cAAc,QAAQ;AAAE,oBAAY;AAAA,MAAG;AAC3C,UAAI,UAAU,WAAW;AACrB,mBAAW,gGAAgG;AAC/G,UAAI,QAAQ,KAAK,KAAK,GAAG,IAAI,MAAM;AACnC,eAAS,IAAI,WAAW,IAAI,GAAG;AAC3B,YAAI,UAAU,KAAK,SAAS,MAAM,CAAC,GAAG,GAAG,IAAI;AACzC,iBAAO;AACf,aAAO;AAAA,IACX;AAOA,IAAAA,iBAAgB,UAAU,SAAS,SAAU,OAAO,aAAa;AAC7D,UAAI,WAAW,CAAC;AAChB,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAS,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MACnC;AACA,cAAQ,UAAU,QAAQ;AAAA,QACtB,KAAK;AACD,iBAAO,CAAC;AAAA,QACZ,KAAK;AACD,iBAAO,KAAK,MAAM,gBAAgB,KAAK;AAAA,QAC3C,KAAK;AACD,iBAAO,KAAK,MAAM,gBAAgB,OAAO,WAAW;AAAA,MAC5D;AACA,aAAO,KAAK,MAAM,gBAAgB,OAAO,aAAa,QAAQ;AAAA,IAClE;AACA,IAAAA,iBAAgB,UAAU,kBAAkB,SAAU,OAAO,aAAa,UAAU;AAChF,aAAO,KAAK,MAAM,gBAAgB,OAAO,aAAa,QAAQ;AAAA,IAClE;AACA,IAAAA,iBAAgB,UAAU,OAAO,WAAY;AACzC,UAAI,QAAQ,CAAC;AACb,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,cAAM,EAAE,IAAI,UAAU,EAAE;AAAA,MAC5B;AACA,UAAI,MAAM,KAAK;AACf,UAAI,gBAAgB,IAAI,OAAO,QAAQ,GAAG,KAAK;AAC/C,aAAO,IAAI,OAAO;AAAA,IACtB;AACA,IAAAA,iBAAgB,UAAU,MAAM,WAAY;AACxC,aAAO,KAAK,OAAO,KAAK,IAAI,KAAK,MAAM,OAAO,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AAAA,IACtE;AACA,IAAAA,iBAAgB,UAAU,QAAQ,WAAY;AAC1C,aAAO,KAAK,OAAO,GAAG,CAAC,EAAE,CAAC;AAAA,IAC9B;AACA,IAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,UAAI,QAAQ,CAAC;AACb,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,cAAM,EAAE,IAAI,UAAU,EAAE;AAAA,MAC5B;AACA,UAAI,MAAM,KAAK;AACf,UAAI,gBAAgB,GAAG,GAAG,KAAK;AAC/B,aAAO,IAAI,OAAO;AAAA,IACtB;AACA,IAAAA,iBAAgB,UAAU,UAAU,WAAY;AAI5C,UAAI,QAAQ,KAAK,MAAM;AACvB,aAAO,MAAM,QAAQ,MAAM,OAAO,SAAS;AAAA,IAC/C;AACA,IAAAA,iBAAgB,UAAU,OAAO,SAAU,WAAW;AAGlD,UAAI,QAAQ,KAAK,MAAM;AACvB,aAAO,MAAM,KAAK,MAAM,OAAO,SAAS;AAAA,IAC5C;AACA,IAAAA,iBAAgB,UAAU,SAAS,SAAU,OAAO;AAChD,UAAI,MAAM,KAAK,MAAM,cAAc,KAAK,MAAM,MAAM,EAAE,QAAQ,KAAK;AACnE,UAAI,MAAM,IAAI;AACV,aAAK,OAAO,KAAK,CAAC;AAClB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,IAAAA,iBAAgB,UAAU,OAAO,SAAU,WAAW,SAAS;AAC3D,iBAAW,uEAAuE;AAClF,eAAS,WAAW,OAAO;AACvB,YAAI,QAAQ,GAAG;AACX,gBAAM,IAAI,MAAM,uCAAuC,QAAQ,cAAc;AAAA,QACjF;AACA,YAAI,SAAS,KAAK,MAAM,OAAO;AAC/B,YAAI,SAAS,QAAQ;AACjB,gBAAM,IAAI,MAAM,uCAAuC,QAAQ,0BAA0B,MAAM;AAAA,QACnG;AAAA,MACJ;AACA,iBAAW,KAAK,MAAM,SAAS;AAC/B,iBAAW,KAAK,MAAM,OAAO;AAC7B,UAAI,cAAc,SAAS;AACvB;AAAA,MACJ;AACA,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI;AACJ,UAAI,YAAY,SAAS;AACrB,mBAAW,SAAS,SAAS,MAAM,GAAG,SAAS,GAAG,SAAS,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG;AAAA,UAC1F,SAAS,SAAS;AAAA,QACtB,GAAG,SAAS,MAAM,UAAU,CAAC,CAAC;AAAA,MAClC,OACK;AAED,mBAAW,SAAS,SAAS,MAAM,GAAG,OAAO,GAAG;AAAA,UAC5C,SAAS,SAAS;AAAA,QACtB,GAAG,SAAS,MAAM,SAAS,SAAS,GAAG,SAAS,MAAM,YAAY,CAAC,CAAC;AAAA,MACxE;AACA,WAAK,QAAQ,QAAQ;AAAA,IACzB;AAEA,IAAAA,iBAAgB,UAAU,MAAM,SAAU,OAAO;AAC7C,UAAI,OAAO,KAAK;AAChB,UAAI,MAAM;AACN,YAAI,QAAQ,KAAK,OAAO,QAAQ;AAC5B,eAAK,KAAK,eAAe;AACzB,iBAAO,KAAK,aAAa,KAAK,OAAO,KAAK,CAAC;AAAA,QAC/C;AACA,gBAAQ,KAAK,kDAAkD,QAAQ,8BAA8B,KAAK,OAAO,SAAS,gFAAgF;AAAA,MAC9M;AACA,aAAO;AAAA,IACX;AAEA,IAAAA,iBAAgB,UAAU,MAAM,SAAU,OAAO,UAAU;AACvD,UAAI,MAAM,KAAK;AACf,UAAID,UAAS,IAAI;AACjB,UAAI,QAAQA,QAAO,QAAQ;AAEvB,4CAAoC,IAAI,IAAI;AAC5C,YAAI,WAAWA,QAAO,KAAK;AAC3B,YAAI,gBAAgB,GAAG,GAAG;AACtB,cAAI,SAAS,gBAAgB,KAAK;AAAA,YAC9B,MAAM;AAAA,YACN,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,UACJ,CAAC;AACD,cAAI,CAAC;AACD;AACJ,qBAAW,OAAO;AAAA,QACtB;AACA,mBAAW,IAAI,SAAS,UAAU,QAAQ;AAC1C,YAAI,UAAU,aAAa;AAC3B,YAAI,SAAS;AACT,UAAAA,QAAO,KAAK,IAAI;AAChB,cAAI,uBAAuB,OAAO,UAAU,QAAQ;AAAA,QACxD;AAAA,MACJ,WACS,UAAUA,QAAO,QAAQ;AAE9B,YAAI,gBAAgB,OAAO,GAAG,CAAC,QAAQ,CAAC;AAAA,MAC5C,OACK;AAED,cAAM,IAAI,MAAM,uCAAuC,QAAQ,qBAAqBA,QAAO,MAAM;AAAA,MACrG;AAAA,IACJ;AACA,WAAOC;AAAA,EACX,EAAE,SAAS;AAAA;AACX,gBAAgB,gBAAgB,WAAW,WAAY;AACnD,OAAK,MAAM,KAAK,eAAe;AAC/B,MAAIC,QAAO;AACX,MAAI,YAAY;AAChB,SAAO,aAAa;AAAA,IAChB,MAAM,WAAY;AACd,aAAO,YAAYA,MAAK,SAClB,EAAE,OAAOA,MAAK,WAAW,GAAG,MAAM,MAAM,IACxC,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,IACzC;AAAA,EACJ,CAAC;AACL,CAAC;AACD,OAAO,eAAe,gBAAgB,WAAW,UAAU;AAAA,EACvD,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,KAAK,WAAY;AACb,WAAO,KAAK,MAAM,eAAe;AAAA,EACrC;AAAA,EACA,KAAK,SAAU,WAAW;AACtB,SAAK,MAAM,eAAe,SAAS;AAAA,EACvC;AACJ,CAAC;AACD,cAAc,gBAAgB,WAAW,kBAAkB,GAAG,OAAO;AACrE,CAAC,WAAW,QAAQ,eAAe,SAAS,YAAY,gBAAgB,EAAE,QAAQ,SAAU,UAAU;AAClG,MAAI,WAAW,MAAM,UAAU,QAAQ;AACvC,YAAU,OAAO,aAAa,YAAY,oDAAoD,WAAW,GAAG;AAC5G,gBAAc,gBAAgB,WAAW,UAAU,WAAY;AAC3D,WAAO,SAAS,MAAM,KAAK,KAAK,GAAG,SAAS;AAAA,EAChD,CAAC;AACL,CAAC;AACD;AAAA,EACI;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA,EACA;AACJ,EAAE,QAAQ,SAAU,UAAU;AAC1B,MAAI,WAAW,MAAM,UAAU,QAAQ;AACvC,YAAU,OAAO,aAAa,YAAY,oDAAoD,WAAW,GAAG;AAC5G,gBAAc,gBAAgB,WAAW,UAAU,SAAU,UAAU,SAAS;AAC5E,QAAI,QAAQ;AACZ,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,eAAe;AACxB,QAAI,iBAAiB,IAAI,cAAc,IAAI,MAAM;AACjD,WAAO,eAAe,QAAQ,EAAE,SAAU,SAAS,OAAO;AACtD,aAAO,SAAS,KAAK,SAAS,SAAS,OAAO,KAAK;AAAA,IACvD,GAAG,OAAO;AAAA,EACd,CAAC;AACL,CAAC;AACD,CAAC,UAAU,aAAa,EAAE,QAAQ,SAAU,UAAU;AAClD,gBAAc,gBAAgB,WAAW,UAAU,WAAY;AAC3D,QAAI,QAAQ;AACZ,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,eAAe;AAExB,QAAI,WAAW,UAAU,CAAC;AAC1B,cAAU,CAAC,IAAI,SAAU,aAAa,cAAc,OAAO;AACvD,qBAAe,IAAI,aAAa,YAAY;AAC5C,aAAO,SAAS,aAAa,cAAc,OAAO,KAAK;AAAA,IAC3D;AACA,WAAO,IAAI,OAAO,QAAQ,EAAE,MAAM,IAAI,QAAQ,SAAS;AAAA,EAC3D,CAAC;AACL,CAAC;AAID,kBAAkB,gBAAgB,WAAW;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAED,IAAI,UAAU,2BAA2B,CAAC;AAC1C,SAAS,2BAA2B,OAAO;AACvC,SAAO;AAAA,IACH,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,KAAK,WAAY;AACb,aAAO,KAAK,IAAI,KAAK;AAAA,IACzB;AAAA,IACA,KAAK,SAAU,OAAO;AAClB,WAAK,IAAI,OAAO,KAAK;AAAA,IACzB;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,OAAO;AAClC,SAAO,eAAe,gBAAgB,WAAW,KAAK,OAAO,2BAA2B,KAAK,CAAC;AAClG;AACA,SAAS,mBAAmB,KAAK;AAC7B,WAAS,QAAQ,8BAA8B,QAAQ,KAAK;AACxD,0BAAsB,KAAK;AAC/B,iCAA+B;AACnC;AACA,mBAAmB,GAAI;AACvB,IAAI,kCAAkC,0BAA0B,iCAAiC,6BAA6B;AAC9H,SAAS,kBAAkB,OAAO;AAC9B,SAAO,SAAS,KAAK,KAAK,gCAAgC,MAAM,KAAK;AACzE;AAEA,IAAI,sBAAsB,CAAC;AAC3B,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASC,eAAc,aAAa,UAAU,MAAM;AAChD,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAc;AACpD,UAAI,SAAS,QAAQ;AAAE,eAAO,mBAAmB,UAAU;AAAA,MAAG;AAC9D,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK,QAAQ;AACb,WAAK,YAAY,WAAW,KAAK,OAAO,SAAS;AACjD,UAAI,OAAO,QAAQ,YAAY;AAC3B,cAAM,IAAI,MAAM,oGAAoG;AAAA,MACxH;AACA,WAAK,QAAQ,oBAAI,IAAI;AACrB,WAAK,UAAU,oBAAI,IAAI;AACvB,WAAK,MAAM,WAAW;AAAA,IAC1B;AACA,IAAAA,eAAc,UAAU,OAAO,SAAU,KAAK;AAC1C,aAAO,KAAK,MAAM,IAAI,GAAG;AAAA,IAC7B;AACA,IAAAA,eAAc,UAAU,MAAM,SAAU,KAAK;AACzC,UAAI,QAAQ;AACZ,UAAI,CAAC,YAAY;AACb,eAAO,KAAK,KAAK,GAAG;AACxB,UAAI,QAAQ,KAAK,QAAQ,IAAI,GAAG;AAChC,UAAI,CAAC,OAAO;AAER,YAAI,WAAY,QAAQ,IAAI,gBAAgB,KAAK,KAAK,GAAG,GAAG,mBAAmB,KAAK,OAAO,MAAM,aAAa,GAAG,IAAI,KAAK,KAAK;AAC/H,aAAK,QAAQ,IAAI,KAAK,QAAQ;AAC9B,2BAAmB,UAAU,WAAY;AAAE,iBAAO,MAAM,QAAQ,OAAO,GAAG;AAAA,QAAG,CAAC;AAAA,MAClF;AACA,aAAO,MAAM,IAAI;AAAA,IACrB;AACA,IAAAA,eAAc,UAAU,MAAM,SAAU,KAAK,OAAO;AAChD,UAAI,SAAS,KAAK,KAAK,GAAG;AAC1B,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,MAAM,SAAS,WAAW;AAAA,UAC1B,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,MAAM;AAAA,QACV,CAAC;AACD,YAAI,CAAC;AACD,iBAAO;AACX,gBAAQ,OAAO;AAAA,MACnB;AACA,UAAI,QAAQ;AACR,aAAK,aAAa,KAAK,KAAK;AAAA,MAChC,OACK;AACD,aAAK,UAAU,KAAK,KAAK;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,SAAS,SAAU,KAAK;AAC5C,UAAI,QAAQ;AACZ,0CAAoC,KAAK,SAAS;AAClD,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,QACV,CAAC;AACD,YAAI,CAAC;AACD,iBAAO;AAAA,MACf;AACA,UAAI,KAAK,KAAK,GAAG,GAAG;AAChB,YAAI,YAAY,aAAa;AAC7B,YAAI,SAAS,aAAa,IAAI;AAC9B,YAAI,SAAS,UAAU,YACjB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU,KAAK,MAAM,IAAI,GAAG,EAAE;AAAA,UAC9B,MAAM;AAAA,QACV,IACE;AACN,YAAI;AACA,yBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,MAAM,IAAS,CAAC,CAAC;AAChF,oBAAY,WAAY;AACpB,gBAAM,UAAU,cAAc;AAC9B,gBAAM,mBAAmB,KAAK,KAAK;AACnC,cAAInB,cAAa,MAAM,MAAM,IAAI,GAAG;AACpC,UAAAA,YAAW,YAAY,MAAS;AAChC,gBAAM,MAAM,OAAO,GAAG;AAAA,QAC1B,CAAC;AACD,YAAI;AACA,0BAAgB,MAAM,MAAM;AAChC,YAAI;AACA,uBAAa;AACjB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,IAAAmB,eAAc,UAAU,qBAAqB,SAAU,KAAK,OAAO;AAC/D,UAAI,QAAQ,KAAK,QAAQ,IAAI,GAAG;AAChC,UAAI,OAAO;AACP,cAAM,YAAY,KAAK;AAAA,MAC3B;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,KAAK,UAAU;AAC5D,UAAInB,cAAa,KAAK,MAAM,IAAI,GAAG;AACnC,iBAAWA,YAAW,gBAAgB,QAAQ;AAC9C,UAAI,aAAa,YAAY,WAAW;AACpC,YAAI,YAAY,aAAa;AAC7B,YAAI,SAAS,aAAa,IAAI;AAC9B,YAAI,SAAS,UAAU,YACjB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAUA,YAAW;AAAA,UACrB,MAAM;AAAA,UACN;AAAA,QACJ,IACE;AACN,YAAI;AACA,yBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,MAAM,IAAS,CAAC,CAAC;AAChF,QAAAA,YAAW,YAAY,QAAQ;AAC/B,YAAI;AACA,0BAAgB,MAAM,MAAM;AAChC,YAAI;AACA,uBAAa;AAAA,MACrB;AAAA,IACJ;AACA,IAAAmB,eAAc,UAAU,YAAY,SAAU,KAAK,UAAU;AACzD,UAAI,QAAQ;AACZ,0CAAoC,KAAK,SAAS;AAClD,kBAAY,WAAY;AACpB,YAAInB,cAAa,IAAI,gBAAgB,UAAU,MAAM,UAAU,MAAM,OAAO,MAAM,aAAa,GAAG,GAAG,KAAK;AAC1G,cAAM,MAAM,IAAI,KAAKA,WAAU;AAC/B,mBAAWA,YAAW;AACtB,cAAM,mBAAmB,KAAK,IAAI;AAClC,cAAM,UAAU,cAAc;AAAA,MAClC,CAAC;AACD,UAAI,YAAY,aAAa;AAC7B,UAAI,SAAS,aAAa,IAAI;AAC9B,UAAI,SAAS,UAAU,YACjB;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,MACJ,IACE;AACN,UAAI;AACA,uBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,MAAM,IAAS,CAAC,CAAC;AAChF,UAAI;AACA,wBAAgB,MAAM,MAAM;AAChC,UAAI;AACA,qBAAa;AAAA,IACrB;AACA,IAAAmB,eAAc,UAAU,MAAM,SAAU,KAAK;AACzC,UAAI,KAAK,IAAI,GAAG;AACZ,eAAO,KAAK,aAAa,KAAK,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC;AACtD,aAAO,KAAK,aAAa,MAAS;AAAA,IACtC;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,OAAO;AACpD,UAAI,KAAK,aAAa,QAAW;AAC7B,eAAO,KAAK,SAAS,KAAK;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,OAAO,WAAY;AACvC,WAAK,UAAU,eAAe;AAC9B,aAAO,KAAK,MAAM,KAAK;AAAA,IAC3B;AACA,IAAAA,eAAc,UAAU,SAAS,WAAY;AACzC,UAAID,QAAO;AACX,UAAIP,QAAO,KAAK,KAAK;AACrB,aAAO,aAAa;AAAA,QAChB,MAAM,WAAY;AACd,cAAI,KAAKA,MAAK,KAAK,GAAG,OAAO,GAAG,MAAM,QAAQ,GAAG;AACjD,iBAAO;AAAA,YACH;AAAA,YACA,OAAO,OAAO,SAAYO,MAAK,IAAI,KAAK;AAAA,UAC5C;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAC,eAAc,UAAU,UAAU,WAAY;AAC1C,UAAID,QAAO;AACX,UAAIP,QAAO,KAAK,KAAK;AACrB,aAAO,aAAa;AAAA,QAChB,MAAM,WAAY;AACd,cAAI,KAAKA,MAAK,KAAK,GAAG,OAAO,GAAG,MAAM,QAAQ,GAAG;AACjD,iBAAO;AAAA,YACH;AAAA,YACA,OAAO,OAAO,SAAY,CAAC,OAAOO,MAAK,IAAI,KAAK,CAAC;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAC,eAAc,UAAU,UAAU,SAAU,UAAU,SAAS;AAC3D,UAAI,QAAQ;AACZ,WAAK,UAAU,eAAe;AAC9B,WAAK,MAAM,QAAQ,SAAU,GAAG,KAAK;AAAE,eAAO,SAAS,KAAK,SAAS,MAAM,IAAI,GAAG,GAAG,KAAK,KAAK;AAAA,MAAG,CAAC;AAAA,IACvG;AAEA,IAAAA,eAAc,UAAU,QAAQ,SAAU,OAAO;AAC7C,UAAI,QAAQ;AACZ,UAAI,gBAAgB,KAAK,GAAG;AACxB,gBAAQ,MAAM,KAAK;AAAA,MACvB;AACA,kBAAY,WAAY;AACpB,YAAI,OAAO,uBAAuB,IAAI;AACtC,YAAI;AACA,cAAI,cAAc,KAAK;AACnB,mBAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AAAE,qBAAO,MAAM,IAAI,KAAK,MAAM,GAAG,CAAC;AAAA,YAAG,CAAC;AAAA,mBAC3E,MAAM,QAAQ,KAAK;AACxB,kBAAM,QAAQ,SAAU,IAAI;AACxB,kBAAI,KAAK,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AACjD,qBAAO,MAAM,IAAI,KAAK,KAAK;AAAA,YAC/B,CAAC;AAAA,mBACI,SAAS,KAAK,GAAG;AACtB,gBAAI,MAAM,gBAAgB;AACtB,mBAAK,2DAA2D,MAAM,YAAY,IAAI;AAAA;AAEtF,oBAAM,QAAQ,SAAU,OAAO,KAAK;AAAE,uBAAO,MAAM,IAAI,KAAK,KAAK;AAAA,cAAG,CAAC;AAAA,UAC7E,WACS,UAAU,QAAQ,UAAU;AACjC,iBAAK,gCAAgC,KAAK;AAAA,QAClD,UACA;AACI,+BAAqB,IAAI;AAAA,QAC7B;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,QAAQ,WAAY;AACxC,UAAI,QAAQ;AACZ,kBAAY,WAAY;AACpB,kBAAU,WAAY;AAGlB,gBAAM,MAAM,QAAQ,SAAU,GAAG,KAAK;AAAE,mBAAO,MAAM,OAAO,GAAG;AAAA,UAAG,CAAC;AAAA,QACvE,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,UAAU,SAAUH,SAAQ;AAChD,UAAI,QAAQ;AAOZ,kBAAY,WAAY;AAEpB,YAAI,iBAAiB,aAAaA,OAAM;AACxC,YAAI,cAAc,oBAAI,IAAI;AAE1B,YAAI,0BAA0B;AAI9B,cAAM,MAAM,MAAM,KAAK,GAAG,SAAU,KAAK;AAGrC,cAAI,CAAC,eAAe,IAAI,GAAG,GAAG;AAC1B,gBAAI,UAAU,MAAM,OAAO,GAAG;AAE9B,gBAAI,SAAS;AAET,wCAA0B;AAAA,YAC9B,OACK;AAED,kBAAI,QAAQ,MAAM,MAAM,IAAI,GAAG;AAC/B,0BAAY,IAAI,KAAK,KAAK;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ,CAAC;AAED,cAAM,eAAe,QAAQ,GAAG,SAAU,IAAI;AAC1C,cAAI,KAAK,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAEjD,cAAI,aAAa,MAAM,MAAM,IAAI,GAAG;AAEpC,gBAAM,IAAI,KAAK,KAAK;AAEpB,cAAI,MAAM,MAAM,IAAI,GAAG,GAAG;AAItB,gBAAI,UAAU,MAAM,MAAM,IAAI,GAAG;AACjC,wBAAY,IAAI,KAAK,OAAO;AAE5B,gBAAI,CAAC,YAAY;AAEb,wCAA0B;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ,CAAC;AAED,YAAI,CAAC,yBAAyB;AAC1B,cAAI,MAAM,MAAM,SAAS,YAAY,MAAM;AAEvC,kBAAM,UAAU,cAAc;AAAA,UAClC,OACK;AACD,gBAAI,QAAQ,MAAM,MAAM,KAAK;AAC7B,gBAAI,QAAQ,YAAY,KAAK;AAC7B,gBAAI,QAAQ,MAAM,KAAK;AACvB,gBAAI,QAAQ,MAAM,KAAK;AACvB,mBAAO,CAAC,MAAM,MAAM;AAChB,kBAAI,MAAM,UAAU,MAAM,OAAO;AAC7B,sBAAM,UAAU,cAAc;AAC9B;AAAA,cACJ;AACA,sBAAQ,MAAM,KAAK;AACnB,sBAAQ,MAAM,KAAK;AAAA,YACvB;AAAA,UACJ;AAAA,QACJ;AAEA,cAAM,QAAQ;AAAA,MAClB,CAAC;AACD,aAAO;AAAA,IACX;AACA,WAAO,eAAeG,eAAc,WAAW,QAAQ;AAAA,MACnD,KAAK,WAAY;AACb,aAAK,UAAU,eAAe;AAC9B,eAAO,KAAK,MAAM;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AAMD,IAAAA,eAAc,UAAU,SAAS,WAAY;AACzC,UAAI,QAAQ;AACZ,UAAI,MAAM,CAAC;AACX,WAAK,QAAQ,SAAU,GAAG,KAAK;AAC3B,eAAQ,IAAI,OAAO,QAAQ,WAAW,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,IAAI,GAAG;AAAA,MAClF,CAAC;AACD,aAAO;AAAA,IACX;AAKA,IAAAA,eAAc,UAAU,OAAO,WAAY;AACvC,aAAO,IAAI,IAAI,IAAI;AAAA,IACvB;AACA,IAAAA,eAAc,UAAU,SAAS,WAAY;AAEzC,aAAO,KAAK,OAAO;AAAA,IACvB;AACA,IAAAA,eAAc,UAAU,WAAW,WAAY;AAC3C,UAAI,QAAQ;AACZ,aAAQ,KAAK,OACT,QACA,gBAAgB,KAAK,KAAK,CAAC,EACtB,IAAI,SAAU,KAAK;AAAE,eAAO,aAAa,GAAG,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG;AAAA,MAAI,CAAC,EAC/E,KAAK,IAAI,IACd;AAAA,IACR;AAMA,IAAAA,eAAc,UAAU,UAAU,SAAU,UAAU,iBAAiB;AACnE,MACI,UAAU,oBAAoB,MAAM,0EAA0E;AAClH,aAAO,iBAAiB,MAAM,QAAQ;AAAA,IAC1C;AACA,IAAAA,eAAc,UAAU,YAAY,SAAU,SAAS;AACnD,aAAO,oBAAoB,MAAM,OAAO;AAAA,IAC5C;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,SAAS,aAAa,KAAK;AACvB,MAAI,OAAO,IAAI;AACX,WAAO,IAAI,SAAS;AAAA;AAEpB,WAAO,IAAI,OAAO,GAAG,EAAE,SAAS;AACxC;AACA,gBAAgB,cAAc,WAAW,WAAY;AACjD,SAAO,KAAK,QAAQ;AACxB,CAAC;AACD,mBAAmB,cAAc,WAAW,kBAAkB,GAAG,KAAK;AAEtE,IAAI,kBAAkB,0BAA0B,iBAAiB,aAAa;AAE9E,IAAI,sBAAsB,CAAC;AAC3B,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASC,eAAc,aAAa,UAAU,MAAM;AAChD,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAc;AACpD,UAAI,SAAS,QAAQ;AAAE,eAAO,mBAAmB,UAAU;AAAA,MAAG;AAC9D,WAAK,OAAO;AACZ,WAAK,QAAQ;AACb,WAAK,QAAQ,oBAAI,IAAI;AACrB,WAAK,QAAQ,WAAW,KAAK,IAAI;AACjC,UAAI,OAAO,QAAQ,YAAY;AAC3B,cAAM,IAAI,MAAM,oGAAoG;AAAA,MACxH;AACA,WAAK,WAAW,SAAU,MAAM,MAAM;AAAE,eAAO,SAAS,MAAM,MAAM,IAAI;AAAA,MAAG;AAC3E,UAAI,aAAa;AACb,aAAK,QAAQ,WAAW;AAAA,MAC5B;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,eAAe,SAAU,OAAO;AACpD,UAAI,KAAK,aAAa,QAAW;AAC7B,eAAO,KAAK,SAAS,KAAK;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,QAAQ,WAAY;AACxC,UAAI,QAAQ;AACZ,kBAAY,WAAY;AACpB,kBAAU,WAAY;AAClB,gBAAM,MAAM,QAAQ,SAAU,OAAO;AACjC,kBAAM,OAAO,KAAK;AAAA,UACtB,CAAC;AAAA,QACL,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,UAAU,SAAU,YAAY,SAAS;AAC7D,UAAI,QAAQ;AACZ,WAAK,MAAM,eAAe;AAC1B,WAAK,MAAM,QAAQ,SAAU,OAAO;AAChC,mBAAW,KAAK,SAAS,OAAO,OAAO,KAAK;AAAA,MAChD,CAAC;AAAA,IACL;AACA,WAAO,eAAeA,eAAc,WAAW,QAAQ;AAAA,MACnD,KAAK,WAAY;AACb,aAAK,MAAM,eAAe;AAC1B,eAAO,KAAK,MAAM;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,eAAc,UAAU,MAAM,SAAU,OAAO;AAC3C,UAAI,QAAQ;AACZ,0CAAoC,KAAK,KAAK;AAC9C,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,QACd,CAAC;AACD,YAAI,CAAC;AACD,iBAAO;AAAA,MAGf;AACA,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AAClB,oBAAY,WAAY;AACpB,gBAAM,MAAM,IAAI,MAAM,SAAS,OAAO,MAAS,CAAC;AAChD,gBAAM,MAAM,cAAc;AAAA,QAC9B,CAAC;AACD,YAAI,YAAY,aAAa;AAC7B,YAAI,SAAS,aAAa,IAAI;AAC9B,YAAI,SAAS,UAAU,YACjB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,QACd,IACE;AACN,YAAI,aAAa;AACb,yBAAe,MAAM;AACzB,YAAI;AACA,0BAAgB,MAAM,MAAM;AAChC,YAAI,aAAa;AACb,uBAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,SAAS,SAAU,OAAO;AAC9C,UAAI,QAAQ;AACZ,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,QACd,CAAC;AACD,YAAI,CAAC;AACD,iBAAO;AAAA,MACf;AACA,UAAI,KAAK,IAAI,KAAK,GAAG;AACjB,YAAI,YAAY,aAAa;AAC7B,YAAI,SAAS,aAAa,IAAI;AAC9B,YAAI,SAAS,UAAU,YACjB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAU;AAAA,QACd,IACE;AACN,YAAI,aAAa;AACb,yBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC,CAAC;AACtE,oBAAY,WAAY;AACpB,gBAAM,MAAM,cAAc;AAC1B,gBAAM,MAAM,OAAO,KAAK;AAAA,QAC5B,CAAC;AACD,YAAI;AACA,0BAAgB,MAAM,MAAM;AAChC,YAAI,aAAa;AACb,uBAAa;AACjB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,MAAM,SAAU,OAAO;AAC3C,WAAK,MAAM,eAAe;AAC1B,aAAO,KAAK,MAAM,IAAI,KAAK,aAAa,KAAK,CAAC;AAAA,IAClD;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,UAAI,YAAY;AAChB,UAAIT,QAAO,gBAAgB,KAAK,KAAK,CAAC;AACtC,UAAIK,UAAS,gBAAgB,KAAK,OAAO,CAAC;AAC1C,aAAO,aAAa;AAAA,QAChB,MAAM,WAAY;AACd,cAAI,QAAQ;AACZ,uBAAa;AACb,iBAAO,QAAQA,QAAO,SAChB,EAAE,OAAO,CAACL,MAAK,KAAK,GAAGK,QAAO,KAAK,CAAC,GAAG,MAAM,MAAM,IACnD,EAAE,MAAM,KAAK;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAI,eAAc,UAAU,OAAO,WAAY;AACvC,aAAO,KAAK,OAAO;AAAA,IACvB;AACA,IAAAA,eAAc,UAAU,SAAS,WAAY;AACzC,WAAK,MAAM,eAAe;AAC1B,UAAIF,QAAO;AACX,UAAI,YAAY;AAChB,UAAI;AACJ,UAAI,KAAK,MAAM,WAAW,QAAW;AACjC,2BAAmB,gBAAgB,KAAK,MAAM,OAAO,CAAC;AAAA,MAC1D,OACK;AAED,2BAAmB,CAAC;AACpB,aAAK,MAAM,QAAQ,SAAU,GAAG;AAAE,iBAAO,iBAAiB,KAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MACxE;AACA,aAAO,aAAa;AAAA,QAChB,MAAM,WAAY;AACd,iBAAO,YAAY,iBAAiB,SAC9B,EAAE,OAAOA,MAAK,aAAa,iBAAiB,WAAW,CAAC,GAAG,MAAM,MAAM,IACvE,EAAE,MAAM,KAAK;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAE,eAAc,UAAU,UAAU,SAAU,OAAO;AAC/C,UAAI,QAAQ;AACZ,UAAI,gBAAgB,KAAK,GAAG;AACxB,gBAAQ,MAAM,KAAK;AAAA,MACvB;AACA,kBAAY,WAAY;AACpB,YAAI,OAAO,uBAAuB,IAAI;AACtC,YAAI;AACA,cAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,kBAAM,MAAM;AACZ,kBAAM,QAAQ,SAAU,OAAO;AAAE,qBAAO,MAAM,IAAI,KAAK;AAAA,YAAG,CAAC;AAAA,UAC/D,WACS,SAAS,KAAK,GAAG;AACtB,kBAAM,MAAM;AACZ,kBAAM,QAAQ,SAAU,OAAO;AAAE,qBAAO,MAAM,IAAI,KAAK;AAAA,YAAG,CAAC;AAAA,UAC/D,WACS,UAAU,QAAQ,UAAU,QAAW;AAC5C,iBAAK,gCAAgC,KAAK;AAAA,UAC9C;AAAA,QACJ,UACA;AACI,+BAAqB,IAAI;AAAA,QAC7B;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,UAAU,SAAU,UAAU,iBAAiB;AAEnE,MACI,UAAU,oBAAoB,MAAM,0EAA0E;AAClH,aAAO,iBAAiB,MAAM,QAAQ;AAAA,IAC1C;AACA,IAAAA,eAAc,UAAU,YAAY,SAAU,SAAS;AACnD,aAAO,oBAAoB,MAAM,OAAO;AAAA,IAC5C;AACA,IAAAA,eAAc,UAAU,OAAO,WAAY;AACvC,aAAO,IAAI,IAAI,IAAI;AAAA,IACvB;AACA,IAAAA,eAAc,UAAU,WAAW,WAAY;AAC3C,aAAO,KAAK,OAAO,OAAO,gBAAgB,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,IAAI;AAAA,IACxE;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,gBAAgB,cAAc,WAAW,WAAY;AACjD,SAAO,KAAK,OAAO;AACvB,CAAC;AACD,mBAAmB,cAAc,WAAW,kBAAkB,GAAG,KAAK;AACtE,IAAI,kBAAkB,0BAA0B,iBAAiB,aAAa;AAE9E,IAAI;AAAA;AAAA,EAAgD,WAAY;AAC5D,aAASC,gCAA+B,QAAQ,MAAM,iBAAiB;AACnE,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,kBAAkB;AACvB,WAAK,SAAS,CAAC;AAAA,IACnB;AACA,IAAAA,gCAA+B,UAAU,OAAO,SAAU,OAAO,KAAK;AAClE,UAAI,OAAgE;AAChE,aAAK,cAAc,OAAO,GAAG;AAC7B,YAAI,CAAC,KAAK,OAAO,GAAG;AAChB,iBAAO;AAAA,MACf;AACA,aAAO,KAAK,OAAO,GAAG,EAAE,IAAI;AAAA,IAChC;AACA,IAAAA,gCAA+B,UAAU,QAAQ,SAAU,OAAO,KAAK,UAAU;AAC7E,UAAI,WAAW,KAAK;AACpB,UAAI,OAA6D;AAC7D,aAAK,cAAc,OAAO,GAAG;AAAA,MACjC;AACA,UAAIrB,cAAa,KAAK,OAAO,GAAG;AAChC,UAAIA,uBAAsB,eAAe;AACrC,QAAAA,YAAW,IAAI,QAAQ;AACvB;AAAA,MACJ;AAEA,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,UACN;AAAA,QACJ,CAAC;AACD,YAAI,CAAC;AACD;AACJ,mBAAW,OAAO;AAAA,MACtB;AACA,iBAAWA,YAAW,gBAAgB,QAAQ;AAE9C,UAAI,aAAa,YAAY,WAAW;AACpC,YAAI,SAAS,aAAa,IAAI;AAC9B,YAAI,YAAY,aAAa;AAC7B,YAAI,SAAS,UAAU,YACjB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,UAAUA,YAAW;AAAA,UACrB,MAAM;AAAA,UACN;AAAA,QACJ,IACE;AACN,YAAI;AACA,yBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,MAAM,IAAS,CAAC,CAAC;AAChF,QAAAA,YAAW,YAAY,QAAQ;AAC/B,YAAI;AACA,0BAAgB,MAAM,MAAM;AAChC,YAAI;AACA,uBAAa;AAAA,MACrB;AAAA,IACJ;AACA,IAAAqB,gCAA+B,UAAU,SAAS,SAAU,KAAK;AAC7D,UAAI,CAAC,KAAK,OAAO,GAAG;AAChB;AACJ,UAAI,SAAS,KAAK;AAClB,UAAI,gBAAgB,IAAI,GAAG;AACvB,YAAI,SAAS,gBAAgB,MAAM;AAAA,UAC/B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,MAAM;AAAA,QACV,CAAC;AACD,YAAI,CAAC;AACD;AAAA,MACR;AACA,UAAI;AACA,mBAAW;AACX,YAAI,SAAS,aAAa,IAAI;AAC9B,YAAI,YAAY,aAAa;AAC7B,YAAI,WAAW,KAAK,OAAO,GAAG,EAAE,IAAI;AACpC,YAAI,KAAK;AACL,eAAK,KAAK,OAAO,GAAG;AACxB,eAAO,KAAK,OAAO,GAAG;AACtB,eAAO,KAAK,OAAO,GAAG;AACtB,YAAI,SAAS,UAAU,YACjB;AAAA,UACE,MAAM;AAAA,UACN,QAAQ;AAAA,UACR;AAAA,UACA,MAAM;AAAA,QACV,IACE;AACN,YAAI;AACA,yBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,MAAM,IAAS,CAAC,CAAC;AAChF,YAAI;AACA,0BAAgB,MAAM,MAAM;AAChC,YAAI;AACA,uBAAa;AAAA,MACrB,UACA;AACI,iBAAS;AAAA,MACb;AAAA,IACJ;AACA,IAAAA,gCAA+B,UAAU,gBAAgB,SAAU,OAAO,UAAU;AAoBhF,cAAQ,KAAK,eAAe,WAAW,WAAW,QAAQ,mIAAmI;AAAA,IACjM;AAMA,IAAAA,gCAA+B,UAAU,UAAU,SAAU,UAAU,iBAAiB;AACpF,MACI,UAAU,oBAAoB,MAAM,iFAAiF;AACzH,aAAO,iBAAiB,MAAM,QAAQ;AAAA,IAC1C;AACA,IAAAA,gCAA+B,UAAU,YAAY,SAAU,SAAS;AACpE,aAAO,oBAAoB,MAAM,OAAO;AAAA,IAC5C;AACA,IAAAA,gCAA+B,UAAU,UAAU,WAAY;AAC3D,UAAI,QAAQ;AACZ,UAAI,KAAK,SAAS,QAAW;AACzB,aAAK,OAAQ,IAAI,gBAAgB,OAAO,KAAK,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK;AAAE,iBAAO,MAAM,OAAO,GAAG,aAAa;AAAA,QAAiB,CAAC,GAAG,mBAAmB,UAAU,KAAK,OAAO,KAAK,IAAI;AAAA,MACjM;AACA,aAAO,KAAK,KAAK,MAAM;AAAA,IAC3B;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,SAAS,mBAAmB,QAAQ,MAAM,iBAAiB;AACvD,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAI;AAClC,MAAI,oBAAoB,QAAQ;AAAE,sBAAkB;AAAA,EAAc;AAClE,MAAI,MAAM,OAAO;AACjB,MAAI;AACA,WAAO;AACX,EACI,UAAU,OAAO,aAAa,MAAM,GAAG,oEAAoE;AAC/G,MAAI,CAAC,cAAc,MAAM;AACrB,YAAQ,OAAO,YAAY,QAAQ,sBAAsB,MAAM,UAAU;AAC7E,MAAI,CAAC;AACD,WAAO,sBAAsB,UAAU;AAC3C,QAAM,IAAI,+BAA+B,QAAQ,MAAM,eAAe;AACtE,qBAAmB,QAAQ,SAAS,GAAG;AACvC,SAAO;AACX;AACA,SAAS,yBAAyB,QAAQ,UAAU,UAAU,UAAU;AACpE,MAAI,MAAM,mBAAmB,MAAM;AACnC,6BAA2B,QAAQ,QAAQ;AAC3C,MAAI,gBAAgB,GAAG,GAAG;AACtB,QAAI,SAAS,gBAAgB,KAAK;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACJ,CAAC;AACD,QAAI,CAAC;AACD;AACJ,eAAW,OAAO;AAAA,EACtB;AACA,MAAIrB,cAAc,IAAI,OAAO,QAAQ,IAAI,IAAI,gBAAgB,UAAU,UAAU,IAAI,OAAO,MAAM,UAAU,KAAK;AACjH,aAAWA,YAAW;AACtB,SAAO,eAAe,QAAQ,UAAU,6BAA6B,QAAQ,CAAC;AAC9E,MAAI,IAAI;AACJ,QAAI,KAAK,KAAK,QAAQ;AAC1B,yBAAuB,KAAK,QAAQ,UAAU,QAAQ;AAC1D;AACA,SAAS,uBAAuB,QAChC,UAAU,SAAS;AACf,MAAI,MAAM,mBAAmB,MAAM;AACnC,UAAQ,OAAO,IAAI,OAAO,MAAM;AAChC,UAAQ,UAAU;AAClB,MAAI,OAAO,QAAQ,IAAI,IAAI,cAAc,OAAO;AAChD,SAAO,eAAe,QAAQ,UAAU,2BAA2B,QAAQ,CAAC;AAChF;AACA,IAAI,4BAA4B,uBAAO,OAAO,IAAI;AAClD,IAAI,0BAA0B,uBAAO,OAAO,IAAI;AAChD,SAAS,6BAA6B,UAAU;AAC5C,SAAQ,0BAA0B,QAAQ,MACrC,0BAA0B,QAAQ,IAAI;AAAA,IACnC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,KAAK,WAAY;AACb,aAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;AAAA,IACzC;AAAA,IACA,KAAK,SAAU,GAAG;AACd,WAAK,MAAM,MAAM,MAAM,UAAU,CAAC;AAAA,IACtC;AAAA,EACJ;AACR;AACA,SAAS,sCAAsC,OAAO;AAClD,MAAI,MAAM,MAAM;AAChB,MAAI,CAAC,KAAK;AAGN,uBAAmB,KAAK;AACxB,WAAO,MAAM;AAAA,EACjB;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,UAAU;AAC1C,SAAQ,wBAAwB,QAAQ,MACnC,wBAAwB,QAAQ,IAAI;AAAA,IACjC,cAAc,YAAY;AAAA,IAC1B,YAAY;AAAA,IACZ,KAAK,WAAY;AACb,aAAO,sCAAsC,IAAI,EAAE,KAAK,MAAM,QAAQ;AAAA,IAC1E;AAAA,IACA,KAAK,SAAU,GAAG;AACd,4CAAsC,IAAI,EAAE,MAAM,MAAM,UAAU,CAAC;AAAA,IACvE;AAAA,EACJ;AACR;AACA,SAAS,uBAAuB,KAAK,QAAQ,KAAK,UAAU;AACxD,MAAI,SAAS,aAAa,GAAG;AAC7B,MAAI,YAAY,aAAa;AAC7B,MAAI,SAAS,UAAU,YACjB;AAAA,IACE,MAAM;AAAA,IACN;AAAA,IACA,MAAM;AAAA,IACN;AAAA,EACJ,IACE;AACN,MAAI;AACA,mBAAe,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,IAAI,MAAM,IAAS,CAAC,CAAC;AAC/E,MAAI;AACA,oBAAgB,KAAK,MAAM;AAC/B,MAAI;AACA,iBAAa;AACrB;AACA,IAAI,mCAAmC,0BAA0B,kCAAkC,8BAA8B;AACjI,SAAS,mBAAmB,OAAO;AAC/B,MAAI,SAAS,KAAK,GAAG;AAEjB,uBAAmB,KAAK;AACxB,WAAO,iCAAiC,MAAM,KAAK;AAAA,EACvD;AACA,SAAO;AACX;AAEA,SAAS,QAAQ,OAAO,UAAU;AAC9B,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC7C,QAAI,kBAAkB,KAAK,GAAG;AAC1B,UAAI,aAAa;AACb,aACI,mDAAmD;AAC3D,aAAO,MAAM,MAAM;AAAA,IACvB;AACA,QAAI,gBAAgB,KAAK,GAAG;AACxB,aAAO,MAAM;AAAA,IACjB;AACA,QAAI,gBAAgB,KAAK,GAAG;AACxB,UAAI,WAAW;AACf,UAAI,aAAa;AACb,eAAO,SAAS;AACpB,UAAIA,cAAa,SAAS,MAAM,IAAI,QAAQ,KAAK,SAAS,QAAQ,IAAI,QAAQ;AAC9E,UAAI,CAACA;AACD,aACI,gBAAgB,WAAW,6CAA6C,aAAa,KAAK,IAAI,GAAG;AACzG,aAAOA;AAAA,IACX;AAEA,uBAAmB,KAAK;AACxB,QAAI,YAAY,CAAC,MAAM;AACnB,YAAM,QAAQ;AAClB,QAAI,mBAAmB,KAAK,GAAG;AAC3B,UAAI,CAAC;AACD,eAAO,KAA8C,2BAA2B;AACpF,UAAIA,cAAa,MAAM,MAAM,OAAO,QAAQ;AAC5C,UAAI,CAACA;AACD,aACI,6BAA6B,WAAW,uCAAuC,aAAa,KAAK,IAAI,GAAG;AAChH,aAAOA;AAAA,IACX;AACA,QAAI,OAAO,KAAK,KAAK,gBAAgB,KAAK,KAAK,WAAW,KAAK,GAAG;AAC9D,aAAO;AAAA,IACX;AAAA,EACJ,WACS,OAAO,UAAU,YAAY;AAClC,QAAI,WAAW,MAAM,KAAK,GAAG;AAEzB,aAAO,MAAM;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,KAA8C,6BAA6B,KAAK;AAC3F;AACA,SAAS,kBAAkB,OAAO,UAAU;AACxC,MAAI,CAAC;AACD,SAAK,uBAAuB;AAChC,MAAI,aAAa;AACb,WAAO,kBAAkB,QAAQ,OAAO,QAAQ,CAAC;AACrD,MAAI,OAAO,KAAK,KAAK,gBAAgB,KAAK,KAAK,WAAW,KAAK;AAC3D,WAAO;AACX,MAAI,gBAAgB,KAAK,KAAK,gBAAgB,KAAK;AAC/C,WAAO;AAEX,qBAAmB,KAAK;AACxB,MAAI,MAAM;AACN,WAAO,MAAM;AACjB,OAA8C,uCAAuC,KAAK;AAC9F;AACA,SAAS,aAAa,OAAO,UAAU;AACnC,MAAI;AACJ,MAAI,aAAa;AACb,YAAQ,QAAQ,OAAO,QAAQ;AAAA,WAC1B,mBAAmB,KAAK,KAAK,gBAAgB,KAAK,KAAK,gBAAgB,KAAK;AACjF,YAAQ,kBAAkB,KAAK;AAAA;AAE/B,YAAQ,QAAQ,KAAK;AACzB,SAAO,MAAM;AACjB;AAEA,IAAI,WAAW,OAAO,UAAU;AAChC,SAAS,UAAU,GAAG,GAAG,OAAO;AAC5B,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAI;AACpC,SAAO,GAAG,GAAG,GAAG,KAAK;AACzB;AAGA,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,QAAQ;AAGrC,MAAI,MAAM;AACN,WAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAEpC,MAAI,KAAK,QAAQ,KAAK;AAClB,WAAO;AAEX,MAAI,MAAM;AACN,WAAO,MAAM;AAEjB,MAAI,OAAO,OAAO;AAClB,MAAI,SAAS,cAAc,SAAS,YAAY,OAAO,KAAK;AACxD,WAAO;AAEX,MAAI,OAAO,CAAC;AACZ,MAAI,OAAO,CAAC;AAEZ,MAAI,YAAY,SAAS,KAAK,CAAC;AAC/B,MAAI,cAAc,SAAS,KAAK,CAAC;AAC7B,WAAO;AACX,UAAQ,WAAW;AAAA,IAEf,KAAK;AAAA,IAEL,KAAK;AAGD,aAAO,KAAK,MAAM,KAAK;AAAA,IAC3B,KAAK;AAGD,UAAI,CAAC,MAAM,CAAC;AACR,eAAO,CAAC,MAAM,CAAC;AAEnB,aAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AAAA,IACjD,KAAK;AAAA,IACL,KAAK;AAID,aAAO,CAAC,MAAM,CAAC;AAAA,IACnB,KAAK;AACD;AAAA;AAAA,QAEA,OAAO,WAAW,eAAe,OAAO,QAAQ,KAAK,CAAC,MAAM,OAAO,QAAQ,KAAK,CAAC;AAAA;AAAA,EACzF;AACA,MAAI,YAAY,cAAc;AAC9B,MAAI,CAAC,WAAW;AACZ,QAAI,OAAO,KAAK,YAAY,OAAO,KAAK;AACpC,aAAO;AAGX,QAAI,QAAQ,EAAE,aAAa,QAAQ,EAAE;AACrC,QAAI,UAAU,SACV,EAAE,OAAO,UAAU,cACf,iBAAiB,SACjB,OAAO,UAAU,cACjB,iBAAiB,WACpB,iBAAiB,KAAK,iBAAiB,IAAI;AAC5C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,UAAU,GAAG;AACb,WAAO;AAAA,EACX,WACS,QAAQ,GAAG;AAChB,YAAQ;AAAA,EACZ;AAKA,WAAS,UAAU,CAAC;AACpB,WAAS,UAAU,CAAC;AACpB,MAAI,SAAS,OAAO;AACpB,SAAO,UAAU;AAGb,QAAI,OAAO,MAAM,MAAM;AACnB,aAAO,OAAO,MAAM,MAAM;AAAA,EAClC;AAEA,SAAO,KAAK,CAAC;AACb,SAAO,KAAK,CAAC;AAEb,MAAI,WAAW;AAEX,aAAS,EAAE;AACX,QAAI,WAAW,EAAE;AACb,aAAO;AAEX,WAAO,UAAU;AACb,UAAI,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,MAAM;AACnD,eAAO;AAAA,IACf;AAAA,EACJ,OACK;AAED,QAAIW,QAAO,OAAO,KAAK,CAAC;AACxB,QAAI,MAAM;AACV,aAASA,MAAK;AAEd,QAAI,OAAO,KAAK,CAAC,EAAE,WAAW;AAC1B,aAAO;AACX,WAAO,UAAU;AAEb,YAAMA,MAAK,MAAM;AACjB,UAAI,EAAE,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,QAAQ,GAAG,QAAQ,MAAM;AAC/D,eAAO;AAAA,IACf;AAAA,EACJ;AAEA,SAAO,IAAI;AACX,SAAO,IAAI;AACX,SAAO;AACX;AACA,SAAS,OAAO,GAAG;AACf,MAAI,kBAAkB,CAAC;AACnB,WAAO,EAAE,KAAK;AAClB,MAAI,SAAS,CAAC,KAAK,gBAAgB,CAAC;AAChC,WAAO,gBAAgB,EAAE,QAAQ,CAAC;AACtC,MAAI,SAAS,CAAC,KAAK,gBAAgB,CAAC;AAChC,WAAO,gBAAgB,EAAE,QAAQ,CAAC;AACtC,SAAO;AACX;AACA,SAAS,MAAM,GAAG,KAAK;AACnB,SAAO,OAAO,UAAU,eAAe,KAAK,GAAG,GAAG;AACtD;CAgCC,WAAY;AACT,WAAS,uBAAuB;AAAA,EAAE;AAClC,MAAI,qBAAqB,SAAS,0BAC9B,QACA,OAAO,YAAY,eAAe,QAAQ,IAAI,+BAA+B,QAAQ;AAErF,QAAI,UAAU,CAAC,WAAW,OAAO,UAAU,EAAE,KAAK,GAAG;AACrD,YAAQ,KAAK,mDAAmD,UAAU,uGAAuG;AAAA,EACrL;AACJ,GAAG;AAEH,IAAI,QAAQ;AACZ,IAAI,OAAO,kCAAkC,UAAU;AAEnD,gCAA8B,WAAW;AAAA,IACrC;AAAA,IACA,QAAQ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAEA,IACI,OAAO,WAAW,eAClB,OAAO,OAAO,YAAY,aAAa;AACnC,+BAA6B;AACjC,SAAO,eAAe,OAAO,SAAS,WAAW;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,WAAY;AACb,UAAI,CAAC,4BAA4B;AAC7B,qCAA6B;AAC7B,gBAAQ,KAAK,+IAAoJ;AAAA,MACrK;AACA,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD;AAAA,IACI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,QAAQ,SAAU,MAAM;AACtB,WAAO,eAAe,OAAO,SAAS,MAAM;AAAA,MACxC,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,aAAK,MAAM,OAAO,oHAAoH;AAAA,MAC1I;AAAA,MACA,KAAK,WAAY;AAAA,MAAE;AAAA,IACvB,CAAC;AAAA,EACL,CAAC;AACL;AApCQ;", "names": ["Atom", "d", "b", "__assign", "cache", "decorate", "get", "set", "computed", "IDerivationState", "TraceMode", "CaughtException", "hasObservers", "observable", "action", "allowStateChanges", "ObservableValue", "ComputedValue", "MobXGlobals", "global", "Reaction", "reaction", "reactionScheduler", "prop", "keys", "res", "key", "StubArray", "ObservableArrayAdministration", "values", "ObservableArray", "self", "ObservableMap", "ObservableSet", "ObservableObjectAdministration"]}