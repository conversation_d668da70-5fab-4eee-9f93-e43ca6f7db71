{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/capitalize.js"], "sourcesContent": ["var toString = require('./toString'),\n    upperFirst = require('./upperFirst');\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\n\nmodule.exports = capitalize;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,aAAa;AAiBjB,aAAS,WAAW,QAAQ;AAC1B,aAAO,WAAW,SAAS,MAAM,EAAE,YAAY,CAAC;AAAA,IAClD;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}