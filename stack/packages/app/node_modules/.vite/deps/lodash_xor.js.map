{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseXor.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/xor.js"], "sourcesContent": ["var baseDifference = require('./_baseDifference'),\n    baseFlatten = require('./_baseFlatten'),\n    baseUniq = require('./_baseUniq');\n\n/**\n * The base implementation of methods like `_.xor`, without support for\n * iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of values.\n */\nfunction baseXor(arrays, iteratee, comparator) {\n  var length = arrays.length;\n  if (length < 2) {\n    return length ? baseUniq(arrays[0]) : [];\n  }\n  var index = -1,\n      result = Array(length);\n\n  while (++index < length) {\n    var array = arrays[index],\n        othIndex = -1;\n\n    while (++othIndex < length) {\n      if (othIndex != index) {\n        result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);\n      }\n    }\n  }\n  return baseUniq(baseFlatten(result, 1), iteratee, comparator);\n}\n\nmodule.exports = baseXor;\n", "var arrayFilter = require('./_arrayFilter'),\n    baseRest = require('./_baseRest'),\n    baseXor = require('./_baseXor'),\n    isArrayLikeObject = require('./isArrayLikeObject');\n\n/**\n * Creates an array of unique values that is the\n * [symmetric difference](https://en.wikipedia.org/wiki/Symmetric_difference)\n * of the given arrays. The order of result values is determined by the order\n * they occur in the arrays.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.without\n * @example\n *\n * _.xor([2, 1], [2, 3]);\n * // => [1, 3]\n */\nvar xor = baseRest(function(arrays) {\n  return baseXor(arrayFilter(arrays, isArrayLikeObject));\n});\n\nmodule.exports = xor;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,cAAc;AADlB,QAEI,WAAW;AAYf,aAAS,QAAQ,QAAQ,UAAU,YAAY;AAC7C,UAAI,SAAS,OAAO;AACpB,UAAI,SAAS,GAAG;AACd,eAAO,SAAS,SAAS,OAAO,CAAC,CAAC,IAAI,CAAC;AAAA,MACzC;AACA,UAAI,QAAQ,IACR,SAAS,MAAM,MAAM;AAEzB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,OAAO,KAAK,GACpB,WAAW;AAEf,eAAO,EAAE,WAAW,QAAQ;AAC1B,cAAI,YAAY,OAAO;AACrB,mBAAO,KAAK,IAAI,eAAe,OAAO,KAAK,KAAK,OAAO,OAAO,QAAQ,GAAG,UAAU,UAAU;AAAA,UAC/F;AAAA,QACF;AAAA,MACF;AACA,aAAO,SAAS,YAAY,QAAQ,CAAC,GAAG,UAAU,UAAU;AAAA,IAC9D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnCjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,UAAU;AAFd,QAGI,oBAAoB;AAoBxB,QAAI,MAAM,SAAS,SAAS,QAAQ;AAClC,aAAO,QAAQ,YAAY,QAAQ,iBAAiB,CAAC;AAAA,IACvD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}