import {
  require_mapToArray
} from "./chunk-HJC7JR4G.js";
import {
  require_keys
} from "./chunk-TAUPVCQB.js";
import {
  require_getTag
} from "./chunk-JKJ3ONXJ.js";
import "./chunk-AJQMZXLQ.js";
import "./chunk-BTFDFZI2.js";
import "./chunk-4MTN4377.js";
import "./chunk-TOPLOUEN.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-V46UVRHS.js";
import "./chunk-NBE5XRJS.js";
import "./chunk-4GUL7IQK.js";
import {
  require_arrayMap
} from "./chunk-2A43EO37.js";
import "./chunk-4HM7XD2G.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-CXIFQW4L.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseToPairs.js
var require_baseToPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseToPairs.js"(exports, module) {
    var arrayMap = require_arrayMap();
    function baseToPairs(object, props) {
      return arrayMap(props, function(key) {
        return [key, object[key]];
      });
    }
    module.exports = baseToPairs;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_setToPairs.js
var require_setToPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_setToPairs.js"(exports, module) {
    function setToPairs(set) {
      var index = -1, result = Array(set.size);
      set.forEach(function(value) {
        result[++index] = [value, value];
      });
      return result;
    }
    module.exports = setToPairs;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createToPairs.js
var require_createToPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createToPairs.js"(exports, module) {
    var baseToPairs = require_baseToPairs();
    var getTag = require_getTag();
    var mapToArray = require_mapToArray();
    var setToPairs = require_setToPairs();
    var mapTag = "[object Map]";
    var setTag = "[object Set]";
    function createToPairs(keysFunc) {
      return function(object) {
        var tag = getTag(object);
        if (tag == mapTag) {
          return mapToArray(object);
        }
        if (tag == setTag) {
          return setToPairs(object);
        }
        return baseToPairs(object, keysFunc(object));
      };
    }
    module.exports = createToPairs;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/toPairs.js
var require_toPairs = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/toPairs.js"(exports, module) {
    var createToPairs = require_createToPairs();
    var keys = require_keys();
    var toPairs = createToPairs(keys);
    module.exports = toPairs;
  }
});
export default require_toPairs();
//# sourceMappingURL=lodash_toPairs.js.map
