import {
  require_basePropertyOf
} from "./chunk-7667RCYF.js";
import {
  require_toString
} from "./chunk-TWO7A3AO.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_escapeHtmlChar.js
var require_escapeHtmlChar = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_escapeHtmlChar.js"(exports, module) {
    var basePropertyOf = require_basePropertyOf();
    var htmlEscapes = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#39;"
    };
    var escapeHtmlChar = basePropertyOf(htmlEscapes);
    module.exports = escapeHtmlChar;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/escape.js
var require_escape = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/escape.js"(exports, module) {
    var escapeHtmlChar = require_escapeHtmlChar();
    var toString = require_toString();
    var reUnescapedHtml = /[&<>"']/g;
    var reHasUnescapedHtml = RegExp(reUnescapedHtml.source);
    function escape(string) {
      string = toString(string);
      return string && reHasUnescapedHtml.test(string) ? string.replace(reUnescapedHtml, escapeHtmlChar) : string;
    }
    module.exports = escape;
  }
});

export {
  require_escape
};
//# sourceMappingURL=chunk-ZOD72EII.js.map
