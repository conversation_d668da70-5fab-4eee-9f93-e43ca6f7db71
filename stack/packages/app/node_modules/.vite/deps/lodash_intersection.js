import {
  require_isArrayLikeObject
} from "./chunk-S2PLPWHA.js";
import {
  require_baseRest
} from "./chunk-VZDPLNRX.js";
import {
  require_arrayIncludes,
  require_arrayIncludesWith
} from "./chunk-POPKRFLD.js";
import "./chunk-JLXDD2GJ.js";
import "./chunk-SL5LTBZ2.js";
import "./chunk-VQMXUB7P.js";
import {
  require_SetCache,
  require_cacheHas
} from "./chunk-QAED4OXJ.js";
import {
  require_baseUnary
} from "./chunk-GSW7XBCA.js";
import "./chunk-4GUL7IQK.js";
import {
  require_arrayMap
} from "./chunk-2A43EO37.js";
import "./chunk-MUOIXU4T.js";
import "./chunk-U2VWCOJX.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-3MWHQ5U6.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIntersection.js
var require_baseIntersection = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIntersection.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var arrayMap = require_arrayMap();
    var baseUnary = require_baseUnary();
    var cacheHas = require_cacheHas();
    var nativeMin = Math.min;
    function baseIntersection(arrays, iteratee, comparator) {
      var includes = comparator ? arrayIncludesWith : arrayIncludes, length = arrays[0].length, othLength = arrays.length, othIndex = othLength, caches = Array(othLength), maxLength = Infinity, result = [];
      while (othIndex--) {
        var array = arrays[othIndex];
        if (othIndex && iteratee) {
          array = arrayMap(array, baseUnary(iteratee));
        }
        maxLength = nativeMin(array.length, maxLength);
        caches[othIndex] = !comparator && (iteratee || length >= 120 && array.length >= 120) ? new SetCache(othIndex && array) : void 0;
      }
      array = arrays[0];
      var index = -1, seen = caches[0];
      outer:
        while (++index < length && result.length < maxLength) {
          var value = array[index], computed = iteratee ? iteratee(value) : value;
          value = comparator || value !== 0 ? value : 0;
          if (!(seen ? cacheHas(seen, computed) : includes(result, computed, comparator))) {
            othIndex = othLength;
            while (--othIndex) {
              var cache = caches[othIndex];
              if (!(cache ? cacheHas(cache, computed) : includes(arrays[othIndex], computed, comparator))) {
                continue outer;
              }
            }
            if (seen) {
              seen.push(computed);
            }
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseIntersection;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_castArrayLikeObject.js
var require_castArrayLikeObject = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_castArrayLikeObject.js"(exports, module) {
    var isArrayLikeObject = require_isArrayLikeObject();
    function castArrayLikeObject(value) {
      return isArrayLikeObject(value) ? value : [];
    }
    module.exports = castArrayLikeObject;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/intersection.js
var require_intersection = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/intersection.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseIntersection = require_baseIntersection();
    var baseRest = require_baseRest();
    var castArrayLikeObject = require_castArrayLikeObject();
    var intersection = baseRest(function(arrays) {
      var mapped = arrayMap(arrays, castArrayLikeObject);
      return mapped.length && mapped[0] === arrays[0] ? baseIntersection(mapped) : [];
    });
    module.exports = intersection;
  }
});
export default require_intersection();
//# sourceMappingURL=lodash_intersection.js.map
