{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isNil.js"], "sourcesContent": ["/**\n * Checks if `value` is `null` or `undefined`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is nullish, else `false`.\n * @example\n *\n * _.isNil(null);\n * // => true\n *\n * _.isNil(void 0);\n * // => true\n *\n * _.isNil(NaN);\n * // => false\n */\nfunction isNil(value) {\n  return value == null;\n}\n\nmodule.exports = isNil;\n"], "mappings": ";;;;;AAAA;AAAA;AAoBA,aAAS,MAAM,OAAO;AACpB,aAAO,SAAS;AAAA,IAClB;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}