{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/noop.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createSet.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseUniq.js"], "sourcesContent": ["/**\n * This method returns `undefined`.\n *\n * @static\n * @memberOf _\n * @since 2.3.0\n * @category Util\n * @example\n *\n * _.times(2, _.noop);\n * // => [undefined, undefined]\n */\nfunction noop() {\n  // No operation performed.\n}\n\nmodule.exports = noop;\n", "var Set = require('./_Set'),\n    noop = require('./noop'),\n    setToArray = require('./_setToArray');\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Creates a set object of `values`.\n *\n * @private\n * @param {Array} values The values to add to the set.\n * @returns {Object} Returns the new set.\n */\nvar createSet = !(Set && (1 / setToArray(new Set([,-0]))[1]) == INFINITY) ? noop : function(values) {\n  return new Set(values);\n};\n\nmodule.exports = createSet;\n", "var SetCache = require('./_SetCache'),\n    arrayIncludes = require('./_arrayIncludes'),\n    arrayIncludesWith = require('./_arrayIncludesWith'),\n    cacheHas = require('./_cacheHas'),\n    createSet = require('./_createSet'),\n    setToArray = require('./_setToArray');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of `_.uniqBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\nfunction baseUniq(array, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      length = array.length,\n      isCommon = true,\n      result = [],\n      seen = result;\n\n  if (comparator) {\n    isCommon = false;\n    includes = arrayIncludesWith;\n  }\n  else if (length >= LARGE_ARRAY_SIZE) {\n    var set = iteratee ? null : createSet(array);\n    if (set) {\n      return setToArray(set);\n    }\n    isCommon = false;\n    includes = cacheHas;\n    seen = new SetCache;\n  }\n  else {\n    seen = iteratee ? [] : result;\n  }\n  outer:\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (isCommon && computed === computed) {\n      var seenIndex = seen.length;\n      while (seenIndex--) {\n        if (seen[seenIndex] === computed) {\n          continue outer;\n        }\n      }\n      if (iteratee) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n    else if (!includes(seen, computed, comparator)) {\n      if (seen !== result) {\n        seen.push(computed);\n      }\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseUniq;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAYA,aAAS,OAAO;AAAA,IAEhB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAI,MAAM;AAAV,QACI,OAAO;AADX,QAEI,aAAa;AAGjB,QAAI,WAAW,IAAI;AASnB,QAAI,YAAY,EAAE,OAAQ,IAAI,WAAW,IAAI,IAAI,CAAC,EAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAM,YAAY,OAAO,SAAS,QAAQ;AAClG,aAAO,IAAI,IAAI,MAAM;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,gBAAgB;AADpB,QAEI,oBAAoB;AAFxB,QAGI,WAAW;AAHf,QAII,YAAY;AAJhB,QAKI,aAAa;AAGjB,QAAI,mBAAmB;AAWvB,aAAS,SAAS,OAAO,UAAU,YAAY;AAC7C,UAAI,QAAQ,IACR,WAAW,eACX,SAAS,MAAM,QACf,WAAW,MACX,SAAS,CAAC,GACV,OAAO;AAEX,UAAI,YAAY;AACd,mBAAW;AACX,mBAAW;AAAA,MACb,WACS,UAAU,kBAAkB;AACnC,YAAI,MAAM,WAAW,OAAO,UAAU,KAAK;AAC3C,YAAI,KAAK;AACP,iBAAO,WAAW,GAAG;AAAA,QACvB;AACA,mBAAW;AACX,mBAAW;AACX,eAAO,IAAI;AAAA,MACb,OACK;AACH,eAAO,WAAW,CAAC,IAAI;AAAA,MACzB;AACA;AACA,eAAO,EAAE,QAAQ,QAAQ;AACvB,cAAI,QAAQ,MAAM,KAAK,GACnB,WAAW,WAAW,SAAS,KAAK,IAAI;AAE5C,kBAAS,cAAc,UAAU,IAAK,QAAQ;AAC9C,cAAI,YAAY,aAAa,UAAU;AACrC,gBAAI,YAAY,KAAK;AACrB,mBAAO,aAAa;AAClB,kBAAI,KAAK,SAAS,MAAM,UAAU;AAChC,yBAAS;AAAA,cACX;AAAA,YACF;AACA,gBAAI,UAAU;AACZ,mBAAK,KAAK,QAAQ;AAAA,YACpB;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB,WACS,CAAC,SAAS,MAAM,UAAU,UAAU,GAAG;AAC9C,gBAAI,SAAS,QAAQ;AACnB,mBAAK,KAAK,QAAQ;AAAA,YACpB;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}