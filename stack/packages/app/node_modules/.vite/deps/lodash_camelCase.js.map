{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/camelCase.js"], "sourcesContent": ["var capitalize = require('./capitalize'),\n    createCompounder = require('./_createCompounder');\n\n/**\n * Converts `string` to [camel case](https://en.wikipedia.org/wiki/CamelCase).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the camel cased string.\n * @example\n *\n * _.camelCase('Foo Bar');\n * // => 'fooBar'\n *\n * _.camelCase('--foo-bar--');\n * // => 'fooBar'\n *\n * _.camelCase('__FOO_BAR__');\n * // => 'fooBar'\n */\nvar camelCase = createCompounder(function(result, word, index) {\n  word = word.toLowerCase();\n  return result + (index ? capitalize(word) : word);\n});\n\nmodule.exports = camelCase;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,mBAAmB;AAsBvB,QAAI,YAAY,iBAAiB,SAAS,QAAQ,MAAM,OAAO;AAC7D,aAAO,KAAK,YAAY;AACxB,aAAO,UAAU,QAAQ,WAAW,IAAI,IAAI;AAAA,IAC9C,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}