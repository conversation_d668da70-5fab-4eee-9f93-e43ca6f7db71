import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/keycode@2.2.1/node_modules/keycode/index.js
var require_keycode = __commonJS({
  "../../node_modules/.pnpm/keycode@2.2.1/node_modules/keycode/index.js"(exports, module) {
    function keyCode(searchInput) {
      if (searchInput && "object" === typeof searchInput) {
        var hasKeyCode = searchInput.which || searchInput.keyCode || searchInput.charCode;
        if (hasKeyCode) searchInput = hasKeyCode;
      }
      if ("number" === typeof searchInput) return names[searchInput];
      var search = String(searchInput);
      var foundNamedKey = codes[search.toLowerCase()];
      if (foundNamedKey) return foundNamedKey;
      var foundNamedKey = aliases[search.toLowerCase()];
      if (foundNamed<PERSON>ey) return foundNamedKey;
      if (search.length === 1) return search.charCodeAt(0);
      return void 0;
    }
    keyCode.isEventKey = function isEventKey(event, nameOrCode) {
      if (event && "object" === typeof event) {
        var keyCode2 = event.which || event.keyCode || event.charCode;
        if (keyCode2 === null || keyCode2 === void 0) {
          return false;
        }
        if (typeof nameOrCode === "string") {
          var foundNamedKey = codes[nameOrCode.toLowerCase()];
          if (foundNamedKey) {
            return foundNamedKey === keyCode2;
          }
          var foundNamedKey = aliases[nameOrCode.toLowerCase()];
          if (foundNamedKey) {
            return foundNamedKey === keyCode2;
          }
        } else if (typeof nameOrCode === "number") {
          return nameOrCode === keyCode2;
        }
        return false;
      }
    };
    exports = module.exports = keyCode;
    var codes = exports.code = exports.codes = {
      "backspace": 8,
      "tab": 9,
      "enter": 13,
      "shift": 16,
      "ctrl": 17,
      "alt": 18,
      "pause/break": 19,
      "caps lock": 20,
      "esc": 27,
      "space": 32,
      "page up": 33,
      "page down": 34,
      "end": 35,
      "home": 36,
      "left": 37,
      "up": 38,
      "right": 39,
      "down": 40,
      "insert": 45,
      "delete": 46,
      "command": 91,
      "left command": 91,
      "right command": 93,
      "numpad *": 106,
      "numpad +": 107,
      "numpad -": 109,
      "numpad .": 110,
      "numpad /": 111,
      "num lock": 144,
      "scroll lock": 145,
      "my computer": 182,
      "my calculator": 183,
      ";": 186,
      "=": 187,
      ",": 188,
      "-": 189,
      ".": 190,
      "/": 191,
      "`": 192,
      "[": 219,
      "\\": 220,
      "]": 221,
      "'": 222
    };
    var aliases = exports.aliases = {
      "windows": 91,
      "⇧": 16,
      "⌥": 18,
      "⌃": 17,
      "⌘": 91,
      "ctl": 17,
      "control": 17,
      "option": 18,
      "pause": 19,
      "break": 19,
      "caps": 20,
      "return": 13,
      "escape": 27,
      "spc": 32,
      "spacebar": 32,
      "pgup": 33,
      "pgdn": 34,
      "ins": 45,
      "del": 46,
      "cmd": 91
    };
    for (i = 97; i < 123; i++) codes[String.fromCharCode(i)] = i - 32;
    for (i = 48; i < 58; i++) codes[i - 48] = i;
    var i;
    for (i = 1; i < 13; i++) codes["f" + i] = i + 111;
    for (i = 0; i < 10; i++) codes["numpad " + i] = i + 96;
    var names = exports.names = exports.title = {};
    for (i in codes) names[codes[i]] = i;
    for (alias in aliases) {
      codes[alias] = aliases[alias];
    }
    var alias;
  }
});
export default require_keycode();
/*! Bundled license information:

keycode/index.js:
  (*!
   * Programatically add the following
   *)
*/
//# sourceMappingURL=keycode.js.map
