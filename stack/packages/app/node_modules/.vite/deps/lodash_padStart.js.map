{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseRepeat.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createPadding.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/padStart.js"], "sourcesContent": ["/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor;\n\n/**\n * The base implementation of `_.repeat` which doesn't coerce arguments.\n *\n * @private\n * @param {string} string The string to repeat.\n * @param {number} n The number of times to repeat the string.\n * @returns {string} Returns the repeated string.\n */\nfunction baseRepeat(string, n) {\n  var result = '';\n  if (!string || n < 1 || n > MAX_SAFE_INTEGER) {\n    return result;\n  }\n  // Leverage the exponentiation by squaring algorithm for a faster repeat.\n  // See https://en.wikipedia.org/wiki/Exponentiation_by_squaring for more details.\n  do {\n    if (n % 2) {\n      result += string;\n    }\n    n = nativeFloor(n / 2);\n    if (n) {\n      string += string;\n    }\n  } while (n);\n\n  return result;\n}\n\nmodule.exports = baseRepeat;\n", "var baseRepeat = require('./_baseRepeat'),\n    baseToString = require('./_baseToString'),\n    castSlice = require('./_castSlice'),\n    hasUnicode = require('./_hasUnicode'),\n    stringSize = require('./_stringSize'),\n    stringToArray = require('./_stringToArray');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil;\n\n/**\n * Creates the padding for `string` based on `length`. The `chars` string\n * is truncated if the number of characters exceeds `length`.\n *\n * @private\n * @param {number} length The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padding for `string`.\n */\nfunction createPadding(length, chars) {\n  chars = chars === undefined ? ' ' : baseToString(chars);\n\n  var charsLength = chars.length;\n  if (charsLength < 2) {\n    return charsLength ? baseRepeat(chars, length) : chars;\n  }\n  var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));\n  return hasUnicode(chars)\n    ? castSlice(stringToArray(result), 0, length).join('')\n    : result.slice(0, length);\n}\n\nmodule.exports = createPadding;\n", "var createPadding = require('./_createPadding'),\n    stringSize = require('./_stringSize'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/**\n * Pads `string` on the left side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padStart('abc', 6);\n * // => '   abc'\n *\n * _.padStart('abc', 6, '_-');\n * // => '_-_abc'\n *\n * _.padStart('abc', 3);\n * // => 'abc'\n */\nfunction padStart(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n\n  var strLength = length ? stringSize(string) : 0;\n  return (length && strLength < length)\n    ? (createPadding(length - strLength, chars) + string)\n    : string;\n}\n\nmodule.exports = padStart;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AACA,QAAI,mBAAmB;AAGvB,QAAI,cAAc,KAAK;AAUvB,aAAS,WAAW,QAAQ,GAAG;AAC7B,UAAI,SAAS;AACb,UAAI,CAAC,UAAU,IAAI,KAAK,IAAI,kBAAkB;AAC5C,eAAO;AAAA,MACT;AAGA,SAAG;AACD,YAAI,IAAI,GAAG;AACT,oBAAU;AAAA,QACZ;AACA,YAAI,YAAY,IAAI,CAAC;AACrB,YAAI,GAAG;AACL,oBAAU;AAAA,QACZ;AAAA,MACF,SAAS;AAET,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AADnB,QAEI,YAAY;AAFhB,QAGI,aAAa;AAHjB,QAII,aAAa;AAJjB,QAKI,gBAAgB;AAGpB,QAAI,aAAa,KAAK;AAWtB,aAAS,cAAc,QAAQ,OAAO;AACpC,cAAQ,UAAU,SAAY,MAAM,aAAa,KAAK;AAEtD,UAAI,cAAc,MAAM;AACxB,UAAI,cAAc,GAAG;AACnB,eAAO,cAAc,WAAW,OAAO,MAAM,IAAI;AAAA,MACnD;AACA,UAAI,SAAS,WAAW,OAAO,WAAW,SAAS,WAAW,KAAK,CAAC,CAAC;AACrE,aAAO,WAAW,KAAK,IACnB,UAAU,cAAc,MAAM,GAAG,GAAG,MAAM,EAAE,KAAK,EAAE,IACnD,OAAO,MAAM,GAAG,MAAM;AAAA,IAC5B;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,aAAa;AADjB,QAEI,YAAY;AAFhB,QAGI,WAAW;AAyBf,aAAS,SAAS,QAAQ,QAAQ,OAAO;AACvC,eAAS,SAAS,MAAM;AACxB,eAAS,UAAU,MAAM;AAEzB,UAAI,YAAY,SAAS,WAAW,MAAM,IAAI;AAC9C,aAAQ,UAAU,YAAY,SACzB,cAAc,SAAS,WAAW,KAAK,IAAI,SAC5C;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}