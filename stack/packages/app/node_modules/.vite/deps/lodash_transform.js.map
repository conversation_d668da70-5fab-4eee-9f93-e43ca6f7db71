{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/transform.js"], "sourcesContent": ["var arrayEach = require('./_arrayEach'),\n    baseCreate = require('./_baseCreate'),\n    baseForOwn = require('./_baseForOwn'),\n    baseIteratee = require('./_baseIteratee'),\n    getPrototype = require('./_getPrototype'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isFunction = require('./isFunction'),\n    isObject = require('./isObject'),\n    isTypedArray = require('./isTypedArray');\n\n/**\n * An alternative to `_.reduce`; this method transforms `object` to a new\n * `accumulator` object which is the result of running each of its own\n * enumerable string keyed properties thru `iteratee`, with each invocation\n * potentially mutating the `accumulator` object. If `accumulator` is not\n * provided, a new object with the same `[[Prototype]]` will be used. The\n * iteratee is invoked with four arguments: (accumulator, value, key, object).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The custom accumulator value.\n * @returns {*} Returns the accumulated value.\n * @example\n *\n * _.transform([2, 3, 4], function(result, n) {\n *   result.push(n *= n);\n *   return n % 2 == 0;\n * }, []);\n * // => [4, 9]\n *\n * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] }\n */\nfunction transform(object, iteratee, accumulator) {\n  var isArr = isArray(object),\n      isArrLike = isArr || isBuffer(object) || isTypedArray(object);\n\n  iteratee = baseIteratee(iteratee, 4);\n  if (accumulator == null) {\n    var Ctor = object && object.constructor;\n    if (isArrLike) {\n      accumulator = isArr ? new Ctor : [];\n    }\n    else if (isObject(object)) {\n      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};\n    }\n    else {\n      accumulator = {};\n    }\n  }\n  (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object) {\n    return iteratee(accumulator, value, index, object);\n  });\n  return accumulator;\n}\n\nmodule.exports = transform;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,aAAa;AAFjB,QAGI,eAAe;AAHnB,QAII,eAAe;AAJnB,QAKI,UAAU;AALd,QAMI,WAAW;AANf,QAOI,aAAa;AAPjB,QAQI,WAAW;AARf,QASI,eAAe;AAgCnB,aAAS,UAAU,QAAQ,UAAU,aAAa;AAChD,UAAI,QAAQ,QAAQ,MAAM,GACtB,YAAY,SAAS,SAAS,MAAM,KAAK,aAAa,MAAM;AAEhE,iBAAW,aAAa,UAAU,CAAC;AACnC,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,UAAU,OAAO;AAC5B,YAAI,WAAW;AACb,wBAAc,QAAQ,IAAI,SAAO,CAAC;AAAA,QACpC,WACS,SAAS,MAAM,GAAG;AACzB,wBAAc,WAAW,IAAI,IAAI,WAAW,aAAa,MAAM,CAAC,IAAI,CAAC;AAAA,QACvE,OACK;AACH,wBAAc,CAAC;AAAA,QACjB;AAAA,MACF;AACA,OAAC,YAAY,YAAY,YAAY,QAAQ,SAAS,OAAO,OAAOA,SAAQ;AAC1E,eAAO,SAAS,aAAa,OAAO,OAAOA,OAAM;AAAA,MACnD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["object"]}