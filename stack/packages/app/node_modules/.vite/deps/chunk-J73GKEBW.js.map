{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseClamp.js"], "sourcesContent": ["/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n  return number;\n}\n\nmodule.exports = baseClamp;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,UAAU,QAAQ,OAAO,OAAO;AACvC,UAAI,WAAW,QAAQ;AACrB,YAAI,UAAU,QAAW;AACvB,mBAAS,UAAU,QAAQ,SAAS;AAAA,QACtC;AACA,YAAI,UAAU,QAAW;AACvB,mBAAS,UAAU,QAAQ,SAAS;AAAA,QACtC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}