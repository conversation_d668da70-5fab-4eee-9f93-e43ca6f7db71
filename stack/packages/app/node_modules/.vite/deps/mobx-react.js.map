{"version": 3, "sources": ["../../../../../node_modules/.pnpm/mobx-react@6.3.1_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react/dist/mobxreact.esm.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/assertEnvironment.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/utils/reactBatchedUpdates.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/utils.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/observerBatching.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/staticRendering.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/observer.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/useObserver.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/printDebugValue.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/reactionCleanupTracking.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/useQueuedForceUpdate.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/ObserverComponent.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/useAsObservableSource.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/useLocalStore.js", "../../../../../node_modules/.pnpm/mobx-react-lite@2.2.2_mobx@4.15.7_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/mobx-react-lite/es/index.js"], "sourcesContent": ["import { Reaction, _allowStateChanges, _allowStateReadsStart, _allowStateReadsEnd, $mobx, createAtom, untracked, isObservableMap, isObservableObject, isObservableArray, observable } from 'mobx';\nimport React__default, { PureComponent, Component, forwardRef, memo, createElement } from 'react';\nimport { isUsingStaticRendering, Observer, observer as observer$1 } from 'mobx-react-lite';\nexport { Observer, isUsingStaticRendering, observerBatching, useAsObservableSource, useLocalStore, useObserver, useStaticRendering } from 'mobx-react-lite';\n\nvar symbolId = 0;\n\nfunction createSymbol(name) {\n  if (typeof Symbol === \"function\") {\n    return Symbol(name);\n  }\n\n  var symbol = \"__$mobx-react \" + name + \" (\" + symbolId + \")\";\n  symbolId++;\n  return symbol;\n}\n\nvar createdSymbols = {};\nfunction newSymbol(name) {\n  if (!createdSymbols[name]) {\n    createdSymbols[name] = createSymbol(name);\n  }\n\n  return createdSymbols[name];\n}\nfunction shallowEqual(objA, objB) {\n  //From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n  if (is(objA, objB)) return true;\n\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction is(x, y) {\n  // From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n} // based on https://github.com/mridgway/hoist-non-react-statics/blob/master/src/index.js\n\n\nvar hoistBlackList = {\n  $$typeof: 1,\n  render: 1,\n  compare: 1,\n  type: 1,\n  childContextTypes: 1,\n  contextType: 1,\n  contextTypes: 1,\n  defaultProps: 1,\n  getDefaultProps: 1,\n  getDerivedStateFromError: 1,\n  getDerivedStateFromProps: 1,\n  mixins: 1,\n  propTypes: 1\n};\nfunction copyStaticProperties(base, target) {\n  var protoProps = Object.getOwnPropertyNames(Object.getPrototypeOf(base));\n  Object.getOwnPropertyNames(base).forEach(function (key) {\n    if (!hoistBlackList[key] && protoProps.indexOf(key) === -1) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(base, key));\n    }\n  });\n}\n/**\r\n * Helper to set `prop` to `this` as non-enumerable (hidden prop)\r\n * @param target\r\n * @param prop\r\n * @param value\r\n */\n\nfunction setHiddenProp(target, prop, value) {\n  if (!Object.hasOwnProperty.call(target, prop)) {\n    Object.defineProperty(target, prop, {\n      enumerable: false,\n      configurable: true,\n      writable: true,\n      value: value\n    });\n  } else {\n    target[prop] = value;\n  }\n}\n/**\r\n * Utilities for patching componentWillUnmount, to make sure @disposeOnUnmount works correctly icm with user defined hooks\r\n * and the handler provided by mobx-react\r\n */\n\nvar mobxMixins =\n/*#__PURE__*/\nnewSymbol(\"patchMixins\");\nvar mobxPatchedDefinition =\n/*#__PURE__*/\nnewSymbol(\"patchedDefinition\");\n\nfunction getMixins(target, methodName) {\n  var mixins = target[mobxMixins] = target[mobxMixins] || {};\n  var methodMixins = mixins[methodName] = mixins[methodName] || {};\n  methodMixins.locks = methodMixins.locks || 0;\n  methodMixins.methods = methodMixins.methods || [];\n  return methodMixins;\n}\n\nfunction wrapper(realMethod, mixins) {\n  var _this = this;\n\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n\n  // locks are used to ensure that mixins are invoked only once per invocation, even on recursive calls\n  mixins.locks++;\n\n  try {\n    var retVal;\n\n    if (realMethod !== undefined && realMethod !== null) {\n      retVal = realMethod.apply(this, args);\n    }\n\n    return retVal;\n  } finally {\n    mixins.locks--;\n\n    if (mixins.locks === 0) {\n      mixins.methods.forEach(function (mx) {\n        mx.apply(_this, args);\n      });\n    }\n  }\n}\n\nfunction wrapFunction(realMethod, mixins) {\n  var fn = function fn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    wrapper.call.apply(wrapper, [this, realMethod, mixins].concat(args));\n  };\n\n  return fn;\n}\n\nfunction patch(target, methodName, mixinMethod) {\n  var mixins = getMixins(target, methodName);\n\n  if (mixins.methods.indexOf(mixinMethod) < 0) {\n    mixins.methods.push(mixinMethod);\n  }\n\n  var oldDefinition = Object.getOwnPropertyDescriptor(target, methodName);\n\n  if (oldDefinition && oldDefinition[mobxPatchedDefinition]) {\n    // already patched definition, do not repatch\n    return;\n  }\n\n  var originalMethod = target[methodName];\n  var newDefinition = createDefinition(target, methodName, oldDefinition ? oldDefinition.enumerable : undefined, mixins, originalMethod);\n  Object.defineProperty(target, methodName, newDefinition);\n}\n\nfunction createDefinition(target, methodName, enumerable, mixins, originalMethod) {\n  var _ref;\n\n  var wrappedFunc = wrapFunction(originalMethod, mixins);\n  return _ref = {}, _ref[mobxPatchedDefinition] = true, _ref.get = function get() {\n    return wrappedFunc;\n  }, _ref.set = function set(value) {\n    if (this === target) {\n      wrappedFunc = wrapFunction(value, mixins);\n    } else {\n      // when it is an instance of the prototype/a child prototype patch that particular case again separately\n      // since we need to store separate values depending on wether it is the actual instance, the prototype, etc\n      // e.g. the method for super might not be the same as the method for the prototype which might be not the same\n      // as the method for the instance\n      var newDefinition = createDefinition(this, methodName, enumerable, mixins, value);\n      Object.defineProperty(this, methodName, newDefinition);\n    }\n  }, _ref.configurable = true, _ref.enumerable = enumerable, _ref;\n}\n\nvar mobxAdminProperty = $mobx || \"$mobx\";\nvar mobxObserverProperty =\n/*#__PURE__*/\nnewSymbol(\"isMobXReactObserver\");\nvar mobxIsUnmounted =\n/*#__PURE__*/\nnewSymbol(\"isUnmounted\");\nvar skipRenderKey =\n/*#__PURE__*/\nnewSymbol(\"skipRender\");\nvar isForcingUpdateKey =\n/*#__PURE__*/\nnewSymbol(\"isForcingUpdate\");\nfunction makeClassComponentObserver(componentClass) {\n  var target = componentClass.prototype;\n\n  if (componentClass[mobxObserverProperty]) {\n    var displayName = getDisplayName(target);\n    console.warn(\"The provided component class (\" + displayName + \") \\n                has already been declared as an observer component.\");\n  } else {\n    componentClass[mobxObserverProperty] = true;\n  }\n\n  if (target.componentWillReact) throw new Error(\"The componentWillReact life-cycle event is no longer supported\");\n\n  if (componentClass[\"__proto__\"] !== PureComponent) {\n    if (!target.shouldComponentUpdate) target.shouldComponentUpdate = observerSCU;else if (target.shouldComponentUpdate !== observerSCU) // n.b. unequal check, instead of existence check, as @observer might be on superclass as well\n      throw new Error(\"It is not allowed to use shouldComponentUpdate in observer based components.\");\n  } // this.props and this.state are made observable, just to make sure @computed fields that\n  // are defined inside the component, and which rely on state or props, re-compute if state or props change\n  // (otherwise the computed wouldn't update and become stale on props change, since props are not observable)\n  // However, this solution is not without it's own problems: https://github.com/mobxjs/mobx-react/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3Aobservable-props-or-not+\n\n\n  makeObservableProp(target, \"props\");\n  makeObservableProp(target, \"state\");\n  var baseRender = target.render;\n\n  target.render = function () {\n    return makeComponentReactive.call(this, baseRender);\n  };\n\n  patch(target, \"componentWillUnmount\", function () {\n    var _this$render$mobxAdmi;\n\n    if (isUsingStaticRendering() === true) return;\n    (_this$render$mobxAdmi = this.render[mobxAdminProperty]) === null || _this$render$mobxAdmi === void 0 ? void 0 : _this$render$mobxAdmi.dispose();\n    this[mobxIsUnmounted] = true;\n\n    if (!this.render[mobxAdminProperty]) {\n      // Render may have been hot-swapped and/or overriden by a subclass.\n      var _displayName = getDisplayName(this);\n\n      console.warn(\"The reactive render of an observer class component (\" + _displayName + \") \\n                was overriden after MobX attached. This may result in a memory leak if the \\n                overriden reactive render was not properly disposed.\");\n    }\n  });\n  return componentClass;\n} // Generates a friendly name for debugging\n\nfunction getDisplayName(comp) {\n  return comp.displayName || comp.name || comp.constructor && (comp.constructor.displayName || comp.constructor.name) || \"<component>\";\n}\n\nfunction makeComponentReactive(render) {\n  var _this = this;\n\n  if (isUsingStaticRendering() === true) return render.call(this);\n  /**\r\n   * If props are shallowly modified, react will render anyway,\r\n   * so atom.reportChanged() should not result in yet another re-render\r\n   */\n\n  setHiddenProp(this, skipRenderKey, false);\n  /**\r\n   * forceUpdate will re-assign this.props. We don't want that to cause a loop,\r\n   * so detect these changes\r\n   */\n\n  setHiddenProp(this, isForcingUpdateKey, false);\n  var initialName = getDisplayName(this);\n  var baseRender = render.bind(this);\n  var isRenderingPending = false;\n  var reaction = new Reaction(initialName + \".render()\", function () {\n    if (!isRenderingPending) {\n      // N.B. Getting here *before mounting* means that a component constructor has side effects (see the relevant test in misc.js)\n      // This unidiomatic React usage but React will correctly warn about this so we continue as usual\n      // See #85 / Pull #44\n      isRenderingPending = true;\n\n      if (_this[mobxIsUnmounted] !== true) {\n        var hasError = true;\n\n        try {\n          setHiddenProp(_this, isForcingUpdateKey, true);\n          if (!_this[skipRenderKey]) Component.prototype.forceUpdate.call(_this);\n          hasError = false;\n        } finally {\n          setHiddenProp(_this, isForcingUpdateKey, false);\n          if (hasError) reaction.dispose();\n        }\n      }\n    }\n  });\n  reaction[\"reactComponent\"] = this;\n  reactiveRender[mobxAdminProperty] = reaction;\n  this.render = reactiveRender;\n\n  function reactiveRender() {\n    isRenderingPending = false;\n    var exception = undefined;\n    var rendering = undefined;\n    reaction.track(function () {\n      try {\n        rendering = _allowStateChanges(false, baseRender);\n      } catch (e) {\n        exception = e;\n      }\n    });\n\n    if (exception) {\n      throw exception;\n    }\n\n    return rendering;\n  }\n\n  return reactiveRender.call(this);\n}\n\nfunction observerSCU(nextProps, nextState) {\n  if (isUsingStaticRendering()) {\n    console.warn(\"[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side.\");\n  } // update on any state changes (as is the default)\n\n\n  if (this.state !== nextState) {\n    return true;\n  } // update if props are shallowly not equal, inspired by PureRenderMixin\n  // we could return just 'false' here, and avoid the `skipRender` checks etc\n  // however, it is nicer if lifecycle events are triggered like usually,\n  // so we return true here if props are shallowly modified.\n\n\n  return !shallowEqual(this.props, nextProps);\n}\n\nfunction makeObservableProp(target, propName) {\n  var valueHolderKey = newSymbol(\"reactProp_\" + propName + \"_valueHolder\");\n  var atomHolderKey = newSymbol(\"reactProp_\" + propName + \"_atomHolder\");\n\n  function getAtom() {\n    if (!this[atomHolderKey]) {\n      setHiddenProp(this, atomHolderKey, createAtom(\"reactive \" + propName));\n    }\n\n    return this[atomHolderKey];\n  }\n\n  Object.defineProperty(target, propName, {\n    configurable: true,\n    enumerable: true,\n    get: function get() {\n      var prevReadState = false;\n\n      if (_allowStateReadsStart && _allowStateReadsEnd) {\n        prevReadState = _allowStateReadsStart(true);\n      }\n\n      getAtom.call(this).reportObserved();\n\n      if (_allowStateReadsStart && _allowStateReadsEnd) {\n        _allowStateReadsEnd(prevReadState);\n      }\n\n      return this[valueHolderKey];\n    },\n    set: function set(v) {\n      if (!this[isForcingUpdateKey] && !shallowEqual(this[valueHolderKey], v)) {\n        setHiddenProp(this, valueHolderKey, v);\n        setHiddenProp(this, skipRenderKey, true);\n        getAtom.call(this).reportChanged();\n        setHiddenProp(this, skipRenderKey, false);\n      } else {\n        setHiddenProp(this, valueHolderKey, v);\n      }\n    }\n  });\n}\n\nvar hasSymbol = typeof Symbol === \"function\" && Symbol.for; // Using react-is had some issues (and operates on elements, not on types), see #608 / #609\n\nvar ReactForwardRefSymbol = hasSymbol ?\n/*#__PURE__*/\nSymbol.for(\"react.forward_ref\") : typeof forwardRef === \"function\" &&\n/*#__PURE__*/\nforwardRef(function (props) {\n  return null;\n})[\"$$typeof\"];\nvar ReactMemoSymbol = hasSymbol ?\n/*#__PURE__*/\nSymbol.for(\"react.memo\") : typeof memo === \"function\" &&\n/*#__PURE__*/\nmemo(function (props) {\n  return null;\n})[\"$$typeof\"];\n/**\r\n * Observer function / decorator\r\n */\n\nfunction observer(component) {\n  if (component[\"isMobxInjector\"] === true) {\n    console.warn(\"Mobx observer: You are trying to use 'observer' on a component that already has 'inject'. Please apply 'observer' before applying 'inject'\");\n  }\n\n  if (ReactMemoSymbol && component[\"$$typeof\"] === ReactMemoSymbol) {\n    throw new Error(\"Mobx observer: You are trying to use 'observer' on a function component wrapped in either another observer or 'React.memo'. The observer already applies 'React.memo' for you.\");\n  } // Unwrap forward refs into `<Observer>` component\n  // we need to unwrap the render, because it is the inner render that needs to be tracked,\n  // not the ForwardRef HoC\n\n\n  if (ReactForwardRefSymbol && component[\"$$typeof\"] === ReactForwardRefSymbol) {\n    var baseRender = component[\"render\"];\n    if (typeof baseRender !== \"function\") throw new Error(\"render property of ForwardRef was not a function\");\n    return forwardRef(function ObserverForwardRef() {\n      var args = arguments;\n      return createElement(Observer, null, function () {\n        return baseRender.apply(undefined, args);\n      });\n    });\n  } // Function component\n\n\n  if (typeof component === \"function\" && (!component.prototype || !component.prototype.render) && !component[\"isReactClass\"] && !Object.prototype.isPrototypeOf.call(Component, component)) {\n    return observer$1(component);\n  }\n\n  return makeClassComponentObserver(component);\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar MobXProviderContext =\n/*#__PURE__*/\nReact__default.createContext({});\nfunction Provider(props) {\n  var children = props.children,\n      stores = _objectWithoutPropertiesLoose(props, [\"children\"]);\n\n  var parentValue = React__default.useContext(MobXProviderContext);\n  var mutableProviderRef = React__default.useRef(_extends({}, parentValue, stores));\n  var value = mutableProviderRef.current;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var newValue = _extends({}, value, stores); // spread in previous state for the context based stores\n\n\n    if (!shallowEqual(value, newValue)) {\n      throw new Error(\"MobX Provider: The set of provided stores has changed. See: https://github.com/mobxjs/mobx-react#the-set-of-provided-stores-has-changed-error.\");\n    }\n  }\n\n  return React__default.createElement(MobXProviderContext.Provider, {\n    value: value\n  }, children);\n}\nProvider.displayName = \"MobXProvider\";\n\n/**\r\n * Store Injection\r\n */\n\nfunction createStoreInjector(grabStoresFn, component, injectNames, makeReactive) {\n  // Support forward refs\n  var Injector = React__default.forwardRef(function (props, ref) {\n    var newProps = _extends({}, props);\n\n    var context = React__default.useContext(MobXProviderContext);\n    Object.assign(newProps, grabStoresFn(context || {}, newProps) || {});\n\n    if (ref) {\n      newProps.ref = ref;\n    }\n\n    return React__default.createElement(component, newProps);\n  });\n  if (makeReactive) Injector = observer(Injector);\n  Injector[\"isMobxInjector\"] = true; // assigned late to suppress observer warning\n  // Static fields from component should be visible on the generated Injector\n\n  copyStaticProperties(component, Injector);\n  Injector[\"wrappedComponent\"] = component;\n  Injector.displayName = getInjectName(component, injectNames);\n  return Injector;\n}\n\nfunction getInjectName(component, injectNames) {\n  var displayName;\n  var componentName = component.displayName || component.name || component.constructor && component.constructor.name || \"Component\";\n  if (injectNames) displayName = \"inject-with-\" + injectNames + \"(\" + componentName + \")\";else displayName = \"inject(\" + componentName + \")\";\n  return displayName;\n}\n\nfunction grabStoresByName(storeNames) {\n  return function (baseStores, nextProps) {\n    storeNames.forEach(function (storeName) {\n      if (storeName in nextProps // prefer props over stores\n      ) return;\n      if (!(storeName in baseStores)) throw new Error(\"MobX injector: Store '\" + storeName + \"' is not available! Make sure it is provided by some Provider\");\n      nextProps[storeName] = baseStores[storeName];\n    });\n    return nextProps;\n  };\n}\n/**\r\n * higher order component that injects stores to a child.\r\n * takes either a varargs list of strings, which are stores read from the context,\r\n * or a function that manually maps the available stores from the context to props:\r\n * storesToProps(mobxStores, props, context) => newProps\r\n */\n\n\nfunction inject() {\n  for (var _len = arguments.length, storeNames = new Array(_len), _key = 0; _key < _len; _key++) {\n    storeNames[_key] = arguments[_key];\n  }\n\n  if (typeof arguments[0] === \"function\") {\n    var grabStoresFn = arguments[0];\n    return function (componentClass) {\n      return createStoreInjector(grabStoresFn, componentClass, grabStoresFn.name, true);\n    };\n  } else {\n    return function (componentClass) {\n      return createStoreInjector(grabStoresByName(storeNames), componentClass, storeNames.join(\"-\"), false);\n    };\n  }\n}\n\nvar protoStoreKey =\n/*#__PURE__*/\nnewSymbol(\"disposeOnUnmountProto\");\nvar instStoreKey =\n/*#__PURE__*/\nnewSymbol(\"disposeOnUnmountInst\");\n\nfunction runDisposersOnWillUnmount() {\n  var _this = this;\n  [].concat(this[protoStoreKey] || [], this[instStoreKey] || []).forEach(function (propKeyOrFunction) {\n    var prop = typeof propKeyOrFunction === \"string\" ? _this[propKeyOrFunction] : propKeyOrFunction;\n\n    if (prop !== undefined && prop !== null) {\n      if (Array.isArray(prop)) prop.map(function (f) {\n        return f();\n      });else prop();\n    }\n  });\n}\n\nfunction disposeOnUnmount(target, propertyKeyOrFunction) {\n  if (Array.isArray(propertyKeyOrFunction)) {\n    return propertyKeyOrFunction.map(function (fn) {\n      return disposeOnUnmount(target, fn);\n    });\n  }\n\n  var c = Object.getPrototypeOf(target).constructor;\n  var c2 = Object.getPrototypeOf(target.constructor); // Special case for react-hot-loader\n\n  var c3 = Object.getPrototypeOf(Object.getPrototypeOf(target));\n\n  if (!(c === React__default.Component || c === React__default.PureComponent || c2 === React__default.Component || c2 === React__default.PureComponent || c3 === React__default.Component || c3 === React__default.PureComponent)) {\n    throw new Error(\"[mobx-react] disposeOnUnmount only supports direct subclasses of React.Component or React.PureComponent.\");\n  }\n\n  if (typeof propertyKeyOrFunction !== \"string\" && typeof propertyKeyOrFunction !== \"function\" && !Array.isArray(propertyKeyOrFunction)) {\n    throw new Error(\"[mobx-react] disposeOnUnmount only works if the parameter is either a property key or a function.\");\n  } // decorator's target is the prototype, so it doesn't have any instance properties like props\n\n\n  var isDecorator = typeof propertyKeyOrFunction === \"string\"; // add property key / function we want run (disposed) to the store\n\n  var componentWasAlreadyModified = !!target[protoStoreKey] || !!target[instStoreKey];\n  var store = isDecorator ? // decorators are added to the prototype store\n  target[protoStoreKey] || (target[protoStoreKey] = []) : // functions are added to the instance store\n  target[instStoreKey] || (target[instStoreKey] = []);\n  store.push(propertyKeyOrFunction); // tweak the component class componentWillUnmount if not done already\n\n  if (!componentWasAlreadyModified) {\n    patch(target, \"componentWillUnmount\", runDisposersOnWillUnmount);\n  } // return the disposer as is if invoked as a non decorator\n\n\n  if (typeof propertyKeyOrFunction !== \"string\") {\n    return propertyKeyOrFunction;\n  }\n}\n\nfunction createChainableTypeChecker(validator) {\n  function checkType(isRequired, props, propName, componentName, location, propFullName) {\n    for (var _len = arguments.length, rest = new Array(_len > 6 ? _len - 6 : 0), _key = 6; _key < _len; _key++) {\n      rest[_key - 6] = arguments[_key];\n    }\n\n    return untracked(function () {\n      componentName = componentName || \"<<anonymous>>\";\n      propFullName = propFullName || propName;\n\n      if (props[propName] == null) {\n        if (isRequired) {\n          var actual = props[propName] === null ? \"null\" : \"undefined\";\n          return new Error(\"The \" + location + \" `\" + propFullName + \"` is marked as required \" + \"in `\" + componentName + \"`, but its value is `\" + actual + \"`.\");\n        }\n\n        return null;\n      } else {\n        // @ts-ignore rest arg is necessary for some React internals - fails tests otherwise\n        return validator.apply(void 0, [props, propName, componentName, location, propFullName].concat(rest));\n      }\n    });\n  }\n\n  var chainedCheckType = checkType.bind(null, false); // Add isRequired to satisfy Requirable\n\n  chainedCheckType.isRequired = checkType.bind(null, true);\n  return chainedCheckType;\n} // Copied from React.PropTypes\n\n\nfunction isSymbol(propType, propValue) {\n  // Native Symbol.\n  if (propType === \"symbol\") {\n    return true;\n  } // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n\n\n  if (propValue[\"@@toStringTag\"] === \"Symbol\") {\n    return true;\n  } // Fallback for non-spec compliant Symbols which are polyfilled.\n\n\n  if (typeof Symbol === \"function\" && propValue instanceof Symbol) {\n    return true;\n  }\n\n  return false;\n} // Copied from React.PropTypes\n\n\nfunction getPropType(propValue) {\n  var propType = typeof propValue;\n\n  if (Array.isArray(propValue)) {\n    return \"array\";\n  }\n\n  if (propValue instanceof RegExp) {\n    // Old webkits (at least until Android 4.0) return 'function' rather than\n    // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n    // passes PropTypes.object.\n    return \"object\";\n  }\n\n  if (isSymbol(propType, propValue)) {\n    return \"symbol\";\n  }\n\n  return propType;\n} // This handles more types than `getPropType`. Only used for error messages.\n// Copied from React.PropTypes\n\n\nfunction getPreciseType(propValue) {\n  var propType = getPropType(propValue);\n\n  if (propType === \"object\") {\n    if (propValue instanceof Date) {\n      return \"date\";\n    } else if (propValue instanceof RegExp) {\n      return \"regexp\";\n    }\n  }\n\n  return propType;\n}\n\nfunction createObservableTypeCheckerCreator(allowNativeType, mobxType) {\n  return createChainableTypeChecker(function (props, propName, componentName, location, propFullName) {\n    return untracked(function () {\n      if (allowNativeType) {\n        if (getPropType(props[propName]) === mobxType.toLowerCase()) return null;\n      }\n\n      var mobxChecker;\n\n      switch (mobxType) {\n        case \"Array\":\n          mobxChecker = isObservableArray;\n          break;\n\n        case \"Object\":\n          mobxChecker = isObservableObject;\n          break;\n\n        case \"Map\":\n          mobxChecker = isObservableMap;\n          break;\n\n        default:\n          throw new Error(\"Unexpected mobxType: \" + mobxType);\n      }\n\n      var propValue = props[propName];\n\n      if (!mobxChecker(propValue)) {\n        var preciseType = getPreciseType(propValue);\n        var nativeTypeExpectationMessage = allowNativeType ? \" or javascript `\" + mobxType.toLowerCase() + \"`\" : \"\";\n        return new Error(\"Invalid prop `\" + propFullName + \"` of type `\" + preciseType + \"` supplied to\" + \" `\" + componentName + \"`, expected `mobx.Observable\" + mobxType + \"`\" + nativeTypeExpectationMessage + \".\");\n      }\n\n      return null;\n    });\n  });\n}\n\nfunction createObservableArrayOfTypeChecker(allowNativeType, typeChecker) {\n  return createChainableTypeChecker(function (props, propName, componentName, location, propFullName) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 5 ? _len2 - 5 : 0), _key2 = 5; _key2 < _len2; _key2++) {\n      rest[_key2 - 5] = arguments[_key2];\n    }\n\n    return untracked(function () {\n      if (typeof typeChecker !== \"function\") {\n        return new Error(\"Property `\" + propFullName + \"` of component `\" + componentName + \"` has \" + \"invalid PropType notation.\");\n      } else {\n        var error = createObservableTypeCheckerCreator(allowNativeType, \"Array\")(props, propName, componentName, location, propFullName);\n        if (error instanceof Error) return error;\n        var propValue = props[propName];\n\n        for (var i = 0; i < propValue.length; i++) {\n          error = typeChecker.apply(void 0, [propValue, i, componentName, location, propFullName + \"[\" + i + \"]\"].concat(rest));\n          if (error instanceof Error) return error;\n        }\n\n        return null;\n      }\n    });\n  });\n}\n\nvar observableArray =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(false, \"Array\");\nvar observableArrayOf =\n/*#__PURE__*/\ncreateObservableArrayOfTypeChecker.bind(null, false);\nvar observableMap =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(false, \"Map\");\nvar observableObject =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(false, \"Object\");\nvar arrayOrObservableArray =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(true, \"Array\");\nvar arrayOrObservableArrayOf =\n/*#__PURE__*/\ncreateObservableArrayOfTypeChecker.bind(null, true);\nvar objectOrObservableObject =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(true, \"Object\");\nvar PropTypes = {\n  observableArray: observableArray,\n  observableArrayOf: observableArrayOf,\n  observableMap: observableMap,\n  observableObject: observableObject,\n  arrayOrObservableArray: arrayOrObservableArray,\n  arrayOrObservableArrayOf: arrayOrObservableArrayOf,\n  objectOrObservableObject: objectOrObservableObject\n};\n\nif (!Component) throw new Error(\"mobx-react requires React to be available\");\nif (!observable) throw new Error(\"mobx-react requires mobx to be available\");\n\nexport { MobXProviderContext, PropTypes, Provider, disposeOnUnmount, inject, observer };\n//# sourceMappingURL=mobxreact.esm.js.map\n", "import { spy } from \"mobx\";\nimport { useState } from \"react\";\nif (!useState) {\n    throw new Error(\"mobx-react-lite requires React with Hooks support\");\n}\nif (!spy) {\n    throw new Error(\"mobx-react-lite requires mobx at least version 4 to be available\");\n}\n", "export { unstable_batchedUpdates } from \"react-dom\";\n", "var __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport { useCallback, useEffect, useState } from \"react\";\nvar EMPTY_ARRAY = [];\nexport function useUnmount(fn) {\n    useEffect(function () { return fn; }, EMPTY_ARRAY);\n}\nexport function useForceUpdate() {\n    var _a = __read(useState(0), 2), setTick = _a[1];\n    var update = useCallback(function () {\n        setTick(function (tick) { return tick + 1; });\n    }, []);\n    return update;\n}\nexport function isPlainObject(value) {\n    if (!value || typeof value !== \"object\") {\n        return false;\n    }\n    var proto = Object.getPrototypeOf(value);\n    return !proto || proto === Object.prototype;\n}\nexport function getSymbol(name) {\n    if (typeof Symbol === \"function\") {\n        return Symbol.for(name);\n    }\n    return \"__$mobx-react \" + name + \"__\";\n}\nvar mockGlobal = {};\nexport function getGlobal() {\n    if (typeof window !== \"undefined\") {\n        return window;\n    }\n    if (typeof global !== \"undefined\") {\n        return global;\n    }\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    return mockGlobal;\n}\n", "import { configure } from \"mobx\";\nimport { getGlobal, getSymbol } from \"./utils\";\nvar observerBatchingConfiguredSymbol = getSymbol(\"observerBatching\");\nexport function defaultNoopBatch(callback) {\n    callback();\n}\nexport function observerBatching(reactionScheduler) {\n    if (!reactionScheduler) {\n        reactionScheduler = defaultNoopBatch;\n        if (\"production\" !== process.env.NODE_ENV) {\n            console.warn(\"[MobX] Failed to get unstable_batched updates from react-dom / react-native\");\n        }\n    }\n    configure({ reactionScheduler: reactionScheduler });\n    getGlobal()[observerBatchingConfiguredSymbol] = true;\n}\nexport var isObserverBatched = function () { return !!getGlobal()[observerBatchingConfiguredSymbol]; };\n", "var globalIsUsingStaticRendering = false;\nexport function useStaticRendering(enable) {\n    globalIsUsingStaticRendering = enable;\n}\nexport function isUsingStaticRendering() {\n    return globalIsUsingStaticRendering;\n}\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { forwardRef, memo } from \"react\";\nimport { isUsingStaticRendering } from \"./staticRendering\";\nimport { useObserver } from \"./useObserver\";\n// n.b. base case is not used for actual typings or exported in the typing files\nexport function observer(baseComponent, options) {\n    // The working of observer is explained step by step in this talk: https://www.youtube.com/watch?v=cPF4iBedoF0&feature=youtu.be&t=1307\n    if (isUsingStaticRendering()) {\n        return baseComponent;\n    }\n    var realOptions = __assign({ forwardRef: false }, options);\n    var baseComponentName = baseComponent.displayName || baseComponent.name;\n    var wrappedComponent = function (props, ref) {\n        return useObserver(function () { return baseComponent(props, ref); }, baseComponentName);\n    };\n    wrappedComponent.displayName = baseComponentName;\n    // memo; we are not interested in deep updates\n    // in props; we assume that if deep objects are changed,\n    // this is in observables, which would have been tracked anyway\n    var memoComponent;\n    if (realOptions.forwardRef) {\n        // we have to use forwardRef here because:\n        // 1. it cannot go before memo, only after it\n        // 2. forwardRef converts the function into an actual component, so we can't let the baseComponent do it\n        //    since it wouldn't be a callable function anymore\n        memoComponent = memo(forwardRef(wrappedComponent));\n    }\n    else {\n        memoComponent = memo(wrappedComponent);\n    }\n    copyStaticProperties(baseComponent, memoComponent);\n    memoComponent.displayName = baseComponentName;\n    return memoComponent;\n}\n// based on https://github.com/mridgway/hoist-non-react-statics/blob/master/src/index.js\nvar hoistBlackList = {\n    $$typeof: true,\n    render: true,\n    compare: true,\n    type: true\n};\nfunction copyStaticProperties(base, target) {\n    Object.keys(base).forEach(function (key) {\n        if (!hoistBlackList[key]) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(base, key));\n        }\n    });\n}\n", "import { Reaction } from \"mobx\";\nimport React from \"react\";\nimport { printDebugValue } from \"./printDebugValue\";\nimport { createTrackingData, recordReactionAsCommitted, scheduleCleanupOfReactionIfLeaked } from \"./reactionCleanupTracking\";\nimport { isUsingStaticRendering } from \"./staticRendering\";\nimport { useForceUpdate } from \"./utils\";\nimport { useQueuedForceUpdate, useQueuedForceUpdateBlock } from \"./useQueuedForceUpdate\";\nvar EMPTY_OBJECT = {};\nfunction observerComponentNameFor(baseComponentName) {\n    return \"observer\" + baseComponentName;\n}\nexport function useObserver(fn, baseComponentName, options) {\n    if (baseComponentName === void 0) { baseComponentName = \"observed\"; }\n    if (options === void 0) { options = EMPTY_OBJECT; }\n    if (isUsingStaticRendering()) {\n        return fn();\n    }\n    var wantedForceUpdateHook = options.useForceUpdate || useForceUpdate;\n    var forceUpdate = wantedForceUpdateHook();\n    var queuedForceUpdate = useQueuedForceUpdate(forceUpdate);\n    // StrictMode/ConcurrentMode/Suspense may mean that our component is\n    // rendered and abandoned multiple times, so we need to track leaked\n    // Reactions.\n    var reactionTrackingRef = React.useRef(null);\n    if (!reactionTrackingRef.current) {\n        // First render for this component (or first time since a previous\n        // reaction from an abandoned render was disposed).\n        var newReaction_1 = new Reaction(observerComponentNameFor(baseComponentName), function () {\n            // Observable has changed, meaning we want to re-render\n            // BUT if we're a component that hasn't yet got to the useEffect()\n            // stage, we might be a component that _started_ to render, but\n            // got dropped, and we don't want to make state changes then.\n            // (It triggers warnings in StrictMode, for a start.)\n            if (trackingData_1.mounted) {\n                // We have reached useEffect(), so we're mounted, and can trigger an update\n                queuedForceUpdate();\n            }\n            else {\n                // We haven't yet reached useEffect(), so we'll need to trigger a re-render\n                // when (and if) useEffect() arrives.  The easiest way to do that is just to\n                // drop our current reaction and allow useEffect() to recreate it.\n                newReaction_1.dispose();\n                reactionTrackingRef.current = null;\n            }\n        });\n        var trackingData_1 = createTrackingData(newReaction_1);\n        reactionTrackingRef.current = trackingData_1;\n        scheduleCleanupOfReactionIfLeaked(reactionTrackingRef);\n    }\n    var reaction = reactionTrackingRef.current.reaction;\n    React.useDebugValue(reaction, printDebugValue);\n    React.useEffect(function () {\n        // Called on first mount only\n        recordReactionAsCommitted(reactionTrackingRef);\n        if (reactionTrackingRef.current) {\n            // Great. We've already got our reaction from our render;\n            // all we need to do is to record that it's now mounted,\n            // to allow future observable changes to trigger re-renders\n            reactionTrackingRef.current.mounted = true;\n        }\n        else {\n            // The reaction we set up in our render has been disposed.\n            // This is either due to bad timings of renderings, e.g. our\n            // component was paused for a _very_ long time, and our\n            // reaction got cleaned up, or we got a observable change\n            // between render and useEffect\n            // Re-create the reaction\n            reactionTrackingRef.current = {\n                reaction: new Reaction(observerComponentNameFor(baseComponentName), function () {\n                    // We've definitely already been mounted at this point\n                    queuedForceUpdate();\n                }),\n                cleanAt: Infinity\n            };\n            queuedForceUpdate();\n        }\n        return function () {\n            reactionTrackingRef.current.reaction.dispose();\n            reactionTrackingRef.current = null;\n        };\n    }, []);\n    // delay all force-update calls after rendering of this component\n    return useQueuedForceUpdateBlock(function () {\n        // render the original component, but have the\n        // reaction track the observables, so that rendering\n        // can be invalidated (see above) once a dependency changes\n        var rendering;\n        var exception;\n        reaction.track(function () {\n            try {\n                rendering = fn();\n            }\n            catch (e) {\n                exception = e;\n            }\n        });\n        if (exception) {\n            throw exception; // re-throw any exceptions caught during rendering\n        }\n        return rendering;\n    });\n}\n", "import { getDependencyTree } from \"mobx\";\nexport function printDebugValue(v) {\n    return getDependencyTree(v);\n}\n", "export function createTrackingData(reaction) {\n    var trackingData = {\n        cleanAt: Date.now() + CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS,\n        reaction: reaction\n    };\n    return trackingData;\n}\n/**\n * The minimum time before we'll clean up a Reaction created in a render\n * for a component that hasn't managed to run its effects. This needs to\n * be big enough to ensure that a component won't turn up and have its\n * effects run without being re-rendered.\n */\nexport var CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS = 10000;\n/**\n * The frequency with which we'll check for leaked reactions.\n */\nexport var CLEANUP_TIMER_LOOP_MILLIS = 10000;\n/**\n * Reactions created by components that have yet to be fully mounted.\n */\nvar uncommittedReactionRefs = new Set();\n/**\n * Latest 'uncommitted reactions' cleanup timer handle.\n */\nvar reactionCleanupHandle;\nfunction ensureCleanupTimerRunning() {\n    if (reactionCleanupHandle === undefined) {\n        reactionCleanupHandle = setTimeout(cleanUncommittedReactions, CLEANUP_TIMER_LOOP_MILLIS);\n    }\n}\nexport function scheduleCleanupOfReactionIfLeaked(ref) {\n    uncommittedReactionRefs.add(ref);\n    ensureCleanupTimerRunning();\n}\nexport function recordReactionAsCommitted(reactionRef) {\n    uncommittedReactionRefs.delete(reactionRef);\n}\n/**\n * Run by the cleanup timer to dispose any outstanding reactions\n */\nfunction cleanUncommittedReactions() {\n    reactionCleanupHandle = undefined;\n    // Loop through all the candidate leaked reactions; those older\n    // than CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS get tidied.\n    var now = Date.now();\n    uncommittedReactionRefs.forEach(function (ref) {\n        var tracking = ref.current;\n        if (tracking) {\n            if (now >= tracking.cleanAt) {\n                // It's time to tidy up this leaked reaction.\n                tracking.reaction.dispose();\n                ref.current = null;\n                uncommittedReactionRefs.delete(ref);\n            }\n        }\n    });\n    if (uncommittedReactionRefs.size > 0) {\n        // We've just finished a round of cleanups but there are still\n        // some leak candidates outstanding.\n        ensureCleanupTimerRunning();\n    }\n}\n/* istanbul ignore next */\n/**\n * Only to be used by test functions; do not export outside of mobx-react-lite\n */\nexport function forceCleanupTimerToRunNowForTests() {\n    // This allows us to control the execution of the cleanup timer\n    // to force it to run at awkward times in unit tests.\n    if (reactionCleanupHandle) {\n        clearTimeout(reactionCleanupHandle);\n        cleanUncommittedReactions();\n    }\n}\n/* istanbul ignore next */\nexport function resetCleanupScheduleForTests() {\n    if (reactionCleanupHandle) {\n        clearTimeout(reactionCleanupHandle);\n        reactionCleanupHandle = undefined;\n    }\n    uncommittedReactionRefs.clear();\n}\n", "import React from \"react\";\nvar insideRender = false;\nvar forceUpdateQueue = [];\nexport function useQueuedForceUpdate(forceUpdate) {\n    return function () {\n        if (insideRender) {\n            forceUpdateQueue.push(forceUpdate);\n        }\n        else {\n            forceUpdate();\n        }\n    };\n}\nexport function useQueuedForceUpdateBlock(callback) {\n    // start intercepting force-update calls\n    insideRender = true;\n    forceUpdateQueue = [];\n    try {\n        var result = callback();\n        // stop intercepting force-update\n        insideRender = false;\n        // store queue or nothing if it was empty to execute useLayoutEffect only when necessary\n        var queue_1 = forceUpdateQueue.length > 0 ? forceUpdateQueue : undefined;\n        // run force-update queue in useLayoutEffect\n        React.useLayoutEffect(function () {\n            if (queue_1) {\n                queue_1.forEach(function (x) { return x(); });\n            }\n        }, [queue_1]);\n        return result;\n    }\n    finally {\n        insideRender = false;\n    }\n}\n", "import { useObserver } from \"./useObserver\";\nfunction ObserverComponent(_a) {\n    var children = _a.children, render = _a.render;\n    var component = children || render;\n    if (typeof component !== \"function\") {\n        return null;\n    }\n    return useObserver(component);\n}\nObserverComponent.propTypes = {\n    children: ObserverPropsCheck,\n    render: ObserverPropsCheck\n};\nObserverComponent.displayName = \"Observer\";\nexport { ObserverComponent as Observer };\nfunction ObserverPropsCheck(props, key, componentName, location, propFullName) {\n    var extraKey = key === \"children\" ? \"render\" : \"children\";\n    var hasProp = typeof props[key] === \"function\";\n    var hasExtraProp = typeof props[extraKey] === \"function\";\n    if (hasProp && hasExtraProp) {\n        return new Error(\"MobX Observer: Do not use children and render in the same time in`\" + componentName);\n    }\n    if (hasProp || hasExtraProp) {\n        return null;\n    }\n    return new Error(\"Invalid prop `\" +\n        propFullName +\n        \"` of type `\" +\n        typeof props[key] +\n        \"` supplied to\" +\n        \" `\" +\n        componentName +\n        \"`, expected `function`.\");\n}\n", "var __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport { observable, runInAction } from \"mobx\";\nimport React from \"react\";\nimport { isPlainObject } from \"./utils\";\nexport function useAsObservableSourceInternal(current, usedByLocalStore) {\n    var culprit = usedByLocalStore ? \"useLocalStore\" : \"useAsObservableSource\";\n    if (\"production\" !== process.env.NODE_ENV && usedByLocalStore) {\n        var _a = __read(React.useState(current), 1), initialSource = _a[0];\n        if ((initialSource !== undefined && current === undefined) ||\n            (initialSource === undefined && current !== undefined)) {\n            throw new Error(\"make sure you never pass `undefined` to \" + culprit);\n        }\n    }\n    if (usedByLocalStore && current === undefined) {\n        return undefined;\n    }\n    if (\"production\" !== process.env.NODE_ENV && !isPlainObject(current)) {\n        throw new Error(culprit + \" expects a plain object as \" + (usedByLocalStore ? \"second\" : \"first\") + \" argument\");\n    }\n    var _b = __read(React.useState(function () { return observable(current, {}, { deep: false }); }), 1), res = _b[0];\n    if (\"production\" !== process.env.NODE_ENV &&\n        Object.keys(res).length !== Object.keys(current).length) {\n        throw new Error(\"the shape of objects passed to \" + culprit + \" should be stable\");\n    }\n    runInAction(function () {\n        Object.assign(res, current);\n    });\n    return res;\n}\nexport function useAsObservableSource(current) {\n    return useAsObservableSourceInternal(current, false);\n}\n", "import { observable, runInAction, transaction } from \"mobx\";\nimport React from \"react\";\nimport { useAsObservableSourceInternal } from \"./useAsObservableSource\";\nimport { isPlainObject } from \"./utils\";\nexport function useLocalStore(initializer, current) {\n    var source = useAsObservableSourceInternal(current, true);\n    return React.useState(function () {\n        var local = observable(initializer(source));\n        if (isPlainObject(local)) {\n            runInAction(function () {\n                Object.keys(local).forEach(function (key) {\n                    var value = local[key];\n                    if (typeof value === \"function\") {\n                        // @ts-ignore No idea why ts2536 is popping out here\n                        local[key] = wrapInTransaction(value, local);\n                    }\n                });\n            });\n        }\n        return local;\n    })[0];\n}\n// tslint:disable-next-line: ban-types\nfunction wrapInTransaction(fn, context) {\n    return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return transaction(function () { return fn.apply(context, args); });\n    };\n}\n", "import \"./assertEnvironment\";\nimport { unstable_batchedUpdates as batch } from \"./utils/reactBatchedUpdates\";\nimport { observerBatching } from \"./observerBatching\";\nobserverBatching(batch);\nexport { isUsingStaticRendering, useStaticRendering } from \"./staticRendering\";\nexport { observer } from \"./observer\";\nexport { useObserver } from \"./useObserver\";\nexport { Observer } from \"./ObserverComponent\";\nexport { useForceUpdate } from \"./utils\";\nexport { useAsObservableSource } from \"./useAsObservableSource\";\nexport { useLocalStore } from \"./useLocalStore\";\nexport { useQueuedForceUpdate, useQueuedForceUpdateBlock } from \"./useQueuedForceUpdate\";\nexport { isObserverBatched, observerBatching } from \"./observerBatching\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,gBAA0F;;;ACA1F,mBAAyB;AACzB,IAAI,CAAC,uBAAU;AACX,QAAM,IAAI,MAAM,mDAAmD;AACvE;AACA,IAAI,CAAC,KAAK;AACN,QAAM,IAAI,MAAM,kEAAkE;AACtF;;;ACPA,uBAAwC;;;ACgBxC,IAAAC,gBAAiD;AAhBjD,IAAI,SAAkC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI,EAAG,OAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAMO,SAAS,iBAAiB;AAC7B,MAAI,KAAK,WAAO,wBAAS,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAC/C,MAAI,aAAS,2BAAY,WAAY;AACjC,YAAQ,SAAU,MAAM;AAAE,aAAO,OAAO;AAAA,IAAG,CAAC;AAAA,EAChD,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AACO,SAAS,cAAc,OAAO;AACjC,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACrC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,OAAO,eAAe,KAAK;AACvC,SAAO,CAAC,SAAS,UAAU,OAAO;AACtC;AACO,SAAS,UAAU,MAAM;AAC5B,MAAI,OAAO,WAAW,YAAY;AAC9B,WAAO,OAAO,IAAI,IAAI;AAAA,EAC1B;AACA,SAAO,mBAAmB,OAAO;AACrC;AACA,IAAI,aAAa,CAAC;AACX,SAAS,YAAY;AACxB,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACnDA,IAAI,mCAAmC,UAAU,kBAAkB;AAC5D,SAAS,iBAAiB,UAAU;AACvC,WAAS;AACb;AACO,SAAS,iBAAiB,mBAAmB;AAChD,MAAI,CAAC,mBAAmB;AACpB,wBAAoB;AACpB,QAAI,MAAuC;AACvC,cAAQ,KAAK,6EAA6E;AAAA,IAC9F;AAAA,EACJ;AACA,YAAU,EAAE,kBAAqC,CAAC;AAClD,YAAU,EAAE,gCAAgC,IAAI;AACpD;;;ACfA,IAAI,+BAA+B;AAC5B,SAAS,mBAAmB,QAAQ;AACvC,iCAA+B;AACnC;AACO,SAAS,yBAAyB;AACrC,SAAO;AACX;;;ACKA,IAAAC,gBAAiC;;;ACVjC,IAAAC,gBAAkB;;;ACAX,SAAS,gBAAgB,GAAG;AAC/B,SAAO,kBAAkB,CAAC;AAC9B;;;ACHO,SAAS,mBAAmB,UAAU;AACzC,MAAI,eAAe;AAAA,IACf,SAAS,KAAK,IAAI,IAAI;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AACX;AAOO,IAAI,wCAAwC;AAI5C,IAAI,4BAA4B;AAIvC,IAAI,0BAA0B,oBAAI,IAAI;AAItC,IAAI;AACJ,SAAS,4BAA4B;AACjC,MAAI,0BAA0B,QAAW;AACrC,4BAAwB,WAAW,2BAA2B,yBAAyB;AAAA,EAC3F;AACJ;AACO,SAAS,kCAAkC,KAAK;AACnD,0BAAwB,IAAI,GAAG;AAC/B,4BAA0B;AAC9B;AACO,SAAS,0BAA0B,aAAa;AACnD,0BAAwB,OAAO,WAAW;AAC9C;AAIA,SAAS,4BAA4B;AACjC,0BAAwB;AAGxB,MAAI,MAAM,KAAK,IAAI;AACnB,0BAAwB,QAAQ,SAAU,KAAK;AAC3C,QAAI,WAAW,IAAI;AACnB,QAAI,UAAU;AACV,UAAI,OAAO,SAAS,SAAS;AAEzB,iBAAS,SAAS,QAAQ;AAC1B,YAAI,UAAU;AACd,gCAAwB,OAAO,GAAG;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,MAAI,wBAAwB,OAAO,GAAG;AAGlC,8BAA0B;AAAA,EAC9B;AACJ;;;AC9DA,IAAAC,gBAAkB;AAClB,IAAI,eAAe;AACnB,IAAI,mBAAmB,CAAC;AACjB,SAAS,qBAAqB,aAAa;AAC9C,SAAO,WAAY;AACf,QAAI,cAAc;AACd,uBAAiB,KAAK,WAAW;AAAA,IACrC,OACK;AACD,kBAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AACO,SAAS,0BAA0B,UAAU;AAEhD,iBAAe;AACf,qBAAmB,CAAC;AACpB,MAAI;AACA,QAAI,SAAS,SAAS;AAEtB,mBAAe;AAEf,QAAI,UAAU,iBAAiB,SAAS,IAAI,mBAAmB;AAE/D,kBAAAC,QAAM,gBAAgB,WAAY;AAC9B,UAAI,SAAS;AACT,gBAAQ,QAAQ,SAAU,GAAG;AAAE,iBAAO,EAAE;AAAA,QAAG,CAAC;AAAA,MAChD;AAAA,IACJ,GAAG,CAAC,OAAO,CAAC;AACZ,WAAO;AAAA,EACX,UACA;AACI,mBAAe;AAAA,EACnB;AACJ;;;AH3BA,IAAI,eAAe,CAAC;AACpB,SAAS,yBAAyB,mBAAmB;AACjD,SAAO,aAAa;AACxB;AACO,SAAS,YAAY,IAAI,mBAAmB,SAAS;AACxD,MAAI,sBAAsB,QAAQ;AAAE,wBAAoB;AAAA,EAAY;AACpE,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAc;AAClD,MAAI,uBAAuB,GAAG;AAC1B,WAAO,GAAG;AAAA,EACd;AACA,MAAI,wBAAwB,QAAQ,kBAAkB;AACtD,MAAI,cAAc,sBAAsB;AACxC,MAAI,oBAAoB,qBAAqB,WAAW;AAIxD,MAAI,sBAAsB,cAAAC,QAAM,OAAO,IAAI;AAC3C,MAAI,CAAC,oBAAoB,SAAS;AAG9B,QAAI,gBAAgB,IAAI,SAAS,yBAAyB,iBAAiB,GAAG,WAAY;AAMtF,UAAI,eAAe,SAAS;AAExB,0BAAkB;AAAA,MACtB,OACK;AAID,sBAAc,QAAQ;AACtB,4BAAoB,UAAU;AAAA,MAClC;AAAA,IACJ,CAAC;AACD,QAAI,iBAAiB,mBAAmB,aAAa;AACrD,wBAAoB,UAAU;AAC9B,sCAAkC,mBAAmB;AAAA,EACzD;AACA,MAAI,WAAW,oBAAoB,QAAQ;AAC3C,gBAAAA,QAAM,cAAc,UAAU,eAAe;AAC7C,gBAAAA,QAAM,UAAU,WAAY;AAExB,8BAA0B,mBAAmB;AAC7C,QAAI,oBAAoB,SAAS;AAI7B,0BAAoB,QAAQ,UAAU;AAAA,IAC1C,OACK;AAOD,0BAAoB,UAAU;AAAA,QAC1B,UAAU,IAAI,SAAS,yBAAyB,iBAAiB,GAAG,WAAY;AAE5E,4BAAkB;AAAA,QACtB,CAAC;AAAA,QACD,SAAS;AAAA,MACb;AACA,wBAAkB;AAAA,IACtB;AACA,WAAO,WAAY;AACf,0BAAoB,QAAQ,SAAS,QAAQ;AAC7C,0BAAoB,UAAU;AAAA,IAClC;AAAA,EACJ,GAAG,CAAC,CAAC;AAEL,SAAO,0BAA0B,WAAY;AAIzC,QAAI;AACJ,QAAI;AACJ,aAAS,MAAM,WAAY;AACvB,UAAI;AACA,oBAAY,GAAG;AAAA,MACnB,SACO,GAAG;AACN,oBAAY;AAAA,MAChB;AAAA,IACJ,CAAC;AACD,QAAI,WAAW;AACX,YAAM;AAAA,IACV;AACA,WAAO;AAAA,EACX,CAAC;AACL;;;ADrGA,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAKO,SAAS,SAAS,eAAe,SAAS;AAE7C,MAAI,uBAAuB,GAAG;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,cAAc,SAAS,EAAE,YAAY,MAAM,GAAG,OAAO;AACzD,MAAI,oBAAoB,cAAc,eAAe,cAAc;AACnE,MAAI,mBAAmB,SAAU,OAAO,KAAK;AACzC,WAAO,YAAY,WAAY;AAAE,aAAO,cAAc,OAAO,GAAG;AAAA,IAAG,GAAG,iBAAiB;AAAA,EAC3F;AACA,mBAAiB,cAAc;AAI/B,MAAI;AACJ,MAAI,YAAY,YAAY;AAKxB,wBAAgB,wBAAK,0BAAW,gBAAgB,CAAC;AAAA,EACrD,OACK;AACD,wBAAgB,oBAAK,gBAAgB;AAAA,EACzC;AACA,uBAAqB,eAAe,aAAa;AACjD,gBAAc,cAAc;AAC5B,SAAO;AACX;AAEA,IAAI,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACV;AACA,SAAS,qBAAqB,MAAM,QAAQ;AACxC,SAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,KAAK;AACrC,QAAI,CAAC,eAAe,GAAG,GAAG;AACtB,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,MAAM,GAAG,CAAC;AAAA,IACjF;AAAA,EACJ,CAAC;AACL;;;AKxDA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,WAAW,GAAG,UAAU,SAAS,GAAG;AACxC,MAAI,YAAY,YAAY;AAC5B,MAAI,OAAO,cAAc,YAAY;AACjC,WAAO;AAAA,EACX;AACA,SAAO,YAAY,SAAS;AAChC;AACA,kBAAkB,YAAY;AAAA,EAC1B,UAAU;AAAA,EACV,QAAQ;AACZ;AACA,kBAAkB,cAAc;AAEhC,SAAS,mBAAmB,OAAO,KAAK,eAAe,UAAU,cAAc;AAC3E,MAAI,WAAW,QAAQ,aAAa,WAAW;AAC/C,MAAI,UAAU,OAAO,MAAM,GAAG,MAAM;AACpC,MAAI,eAAe,OAAO,MAAM,QAAQ,MAAM;AAC9C,MAAI,WAAW,cAAc;AACzB,WAAO,IAAI,MAAM,uEAAuE,aAAa;AAAA,EACzG;AACA,MAAI,WAAW,cAAc;AACzB,WAAO;AAAA,EACX;AACA,SAAO,IAAI,MAAM,mBACb,eACA,gBACA,OAAO,MAAM,GAAG,IAChB,oBAEA,gBACA,yBAAyB;AACjC;;;AChBA,IAAAC,gBAAkB;AAjBlB,IAAIC,UAAkC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI,EAAG,OAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAIO,SAAS,8BAA8B,SAAS,kBAAkB;AACrE,MAAI,UAAU,mBAAmB,kBAAkB;AACnD,MAA6C,kBAAkB;AAC3D,QAAI,KAAKA,QAAO,cAAAC,QAAM,SAAS,OAAO,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AACjE,QAAK,kBAAkB,UAAa,YAAY,UAC3C,kBAAkB,UAAa,YAAY,QAAY;AACxD,YAAM,IAAI,MAAM,6CAA6C,OAAO;AAAA,IACxE;AAAA,EACJ;AACA,MAAI,oBAAoB,YAAY,QAAW;AAC3C,WAAO;AAAA,EACX;AACA,MAA6C,CAAC,cAAc,OAAO,GAAG;AAClE,UAAM,IAAI,MAAM,UAAU,iCAAiC,mBAAmB,WAAW,WAAW,WAAW;AAAA,EACnH;AACA,MAAI,KAAKD,QAAO,cAAAC,QAAM,SAAS,WAAY;AAAE,WAAO,WAAW,SAAS,CAAC,GAAG,EAAE,MAAM,MAAM,CAAC;AAAA,EAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AAChH,MACI,OAAO,KAAK,GAAG,EAAE,WAAW,OAAO,KAAK,OAAO,EAAE,QAAQ;AACzD,UAAM,IAAI,MAAM,oCAAoC,UAAU,mBAAmB;AAAA,EACrF;AACA,cAAY,WAAY;AACpB,WAAO,OAAO,KAAK,OAAO;AAAA,EAC9B,CAAC;AACD,SAAO;AACX;AACO,SAAS,sBAAsB,SAAS;AAC3C,SAAO,8BAA8B,SAAS,KAAK;AACvD;;;AC7CA,IAAAC,gBAAkB;AAGX,SAAS,cAAc,aAAa,SAAS;AAChD,MAAI,SAAS,8BAA8B,SAAS,IAAI;AACxD,SAAO,cAAAC,QAAM,SAAS,WAAY;AAC9B,QAAI,QAAQ,WAAW,YAAY,MAAM,CAAC;AAC1C,QAAI,cAAc,KAAK,GAAG;AACtB,kBAAY,WAAY;AACpB,eAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AACtC,cAAI,QAAQ,MAAM,GAAG;AACrB,cAAI,OAAO,UAAU,YAAY;AAE7B,kBAAM,GAAG,IAAI,kBAAkB,OAAO,KAAK;AAAA,UAC/C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX,CAAC,EAAE,CAAC;AACR;AAEA,SAAS,kBAAkB,IAAI,SAAS;AACpC,SAAO,WAAY;AACf,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,YAAY,WAAY;AAAE,aAAO,GAAG,MAAM,SAAS,IAAI;AAAA,IAAG,CAAC;AAAA,EACtE;AACJ;;;AC5BA,iBAAiB,wCAAK;;;AdEtB,IAAI,WAAW;AAEf,SAAS,aAAa,MAAM;AAC1B,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,IAAI;AAAA,EACpB;AAEA,MAAI,SAAS,mBAAmB,OAAO,OAAO,WAAW;AACzD;AACA,SAAO;AACT;AAEA,IAAI,iBAAiB,CAAC;AACtB,SAAS,UAAU,MAAM;AACvB,MAAI,CAAC,eAAe,IAAI,GAAG;AACzB,mBAAe,IAAI,IAAI,aAAa,IAAI;AAAA,EAC1C;AAEA,SAAO,eAAe,IAAI;AAC5B;AACA,SAAS,aAAa,MAAM,MAAM;AAEhC,MAAI,GAAG,MAAM,IAAI,EAAG,QAAO;AAE3B,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,MAAM,WAAW,MAAM,OAAQ,QAAO;AAE1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,OAAO,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,GAAG,GAAG,GAAG;AAEhB,MAAI,MAAM,GAAG;AACX,WAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EAClC,OAAO;AACL,WAAO,MAAM,KAAK,MAAM;AAAA,EAC1B;AACF;AAGA,IAAIC,kBAAiB;AAAA,EACnB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,EACR,WAAW;AACb;AACA,SAASC,sBAAqB,MAAM,QAAQ;AAC1C,MAAI,aAAa,OAAO,oBAAoB,OAAO,eAAe,IAAI,CAAC;AACvE,SAAO,oBAAoB,IAAI,EAAE,QAAQ,SAAU,KAAK;AACtD,QAAI,CAACD,gBAAe,GAAG,KAAK,WAAW,QAAQ,GAAG,MAAM,IAAI;AAC1D,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,MAAM,GAAG,CAAC;AAAA,IAC/E;AAAA,EACF,CAAC;AACH;AAQA,SAAS,cAAc,QAAQ,MAAM,OAAO;AAC1C,MAAI,CAAC,OAAO,eAAe,KAAK,QAAQ,IAAI,GAAG;AAC7C,WAAO,eAAe,QAAQ,MAAM;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAMA,IAAI,aAEJ,UAAU,aAAa;AACvB,IAAI,wBAEJ,UAAU,mBAAmB;AAE7B,SAAS,UAAU,QAAQ,YAAY;AACrC,MAAI,SAAS,OAAO,UAAU,IAAI,OAAO,UAAU,KAAK,CAAC;AACzD,MAAI,eAAe,OAAO,UAAU,IAAI,OAAO,UAAU,KAAK,CAAC;AAC/D,eAAa,QAAQ,aAAa,SAAS;AAC3C,eAAa,UAAU,aAAa,WAAW,CAAC;AAChD,SAAO;AACT;AAEA,SAAS,QAAQ,YAAY,QAAQ;AACnC,MAAI,QAAQ;AAEZ,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AAGA,SAAO;AAEP,MAAI;AACF,QAAI;AAEJ,QAAI,eAAe,UAAa,eAAe,MAAM;AACnD,eAAS,WAAW,MAAM,MAAM,IAAI;AAAA,IACtC;AAEA,WAAO;AAAA,EACT,UAAE;AACA,WAAO;AAEP,QAAI,OAAO,UAAU,GAAG;AACtB,aAAO,QAAQ,QAAQ,SAAU,IAAI;AACnC,WAAG,MAAM,OAAO,IAAI;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,aAAa,YAAY,QAAQ;AACxC,MAAI,KAAK,SAASE,MAAK;AACrB,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AAEA,YAAQ,KAAK,MAAM,SAAS,CAAC,MAAM,YAAY,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,EACrE;AAEA,SAAO;AACT;AAEA,SAAS,MAAM,QAAQ,YAAY,aAAa;AAC9C,MAAI,SAAS,UAAU,QAAQ,UAAU;AAEzC,MAAI,OAAO,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAC3C,WAAO,QAAQ,KAAK,WAAW;AAAA,EACjC;AAEA,MAAI,gBAAgB,OAAO,yBAAyB,QAAQ,UAAU;AAEtE,MAAI,iBAAiB,cAAc,qBAAqB,GAAG;AAEzD;AAAA,EACF;AAEA,MAAI,iBAAiB,OAAO,UAAU;AACtC,MAAI,gBAAgB,iBAAiB,QAAQ,YAAY,gBAAgB,cAAc,aAAa,QAAW,QAAQ,cAAc;AACrI,SAAO,eAAe,QAAQ,YAAY,aAAa;AACzD;AAEA,SAAS,iBAAiB,QAAQ,YAAY,YAAY,QAAQ,gBAAgB;AAChF,MAAI;AAEJ,MAAI,cAAc,aAAa,gBAAgB,MAAM;AACrD,SAAO,OAAO,CAAC,GAAG,KAAK,qBAAqB,IAAI,MAAM,KAAK,MAAM,SAAS,MAAM;AAC9E,WAAO;AAAA,EACT,GAAG,KAAK,MAAM,SAAS,IAAI,OAAO;AAChC,QAAI,SAAS,QAAQ;AACnB,oBAAc,aAAa,OAAO,MAAM;AAAA,IAC1C,OAAO;AAKL,UAAI,gBAAgB,iBAAiB,MAAM,YAAY,YAAY,QAAQ,KAAK;AAChF,aAAO,eAAe,MAAM,YAAY,aAAa;AAAA,IACvD;AAAA,EACF,GAAG,KAAK,eAAe,MAAM,KAAK,aAAa,YAAY;AAC7D;AAEA,IAAI,oBAAoB,SAAS;AACjC,IAAI,uBAEJ,UAAU,qBAAqB;AAC/B,IAAI,kBAEJ,UAAU,aAAa;AACvB,IAAI,gBAEJ,UAAU,YAAY;AACtB,IAAI,qBAEJ,UAAU,iBAAiB;AAC3B,SAAS,2BAA2B,gBAAgB;AAClD,MAAI,SAAS,eAAe;AAE5B,MAAI,eAAe,oBAAoB,GAAG;AACxC,QAAI,cAAc,eAAe,MAAM;AACvC,YAAQ,KAAK,mCAAmC,cAAc,yEAAyE;AAAA,EACzI,OAAO;AACL,mBAAe,oBAAoB,IAAI;AAAA,EACzC;AAEA,MAAI,OAAO,mBAAoB,OAAM,IAAI,MAAM,gEAAgE;AAE/G,MAAI,eAAe,WAAW,MAAM,6BAAe;AACjD,QAAI,CAAC,OAAO,sBAAuB,QAAO,wBAAwB;AAAA,aAAqB,OAAO,0BAA0B;AACtH,YAAM,IAAI,MAAM,8EAA8E;AAAA,EAClG;AAMA,qBAAmB,QAAQ,OAAO;AAClC,qBAAmB,QAAQ,OAAO;AAClC,MAAI,aAAa,OAAO;AAExB,SAAO,SAAS,WAAY;AAC1B,WAAO,sBAAsB,KAAK,MAAM,UAAU;AAAA,EACpD;AAEA,QAAM,QAAQ,wBAAwB,WAAY;AAChD,QAAI;AAEJ,QAAI,uBAAuB,MAAM,KAAM;AACvC,KAAC,wBAAwB,KAAK,OAAO,iBAAiB,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,QAAQ;AAC/I,SAAK,eAAe,IAAI;AAExB,QAAI,CAAC,KAAK,OAAO,iBAAiB,GAAG;AAEnC,UAAI,eAAe,eAAe,IAAI;AAEtC,cAAQ,KAAK,yDAAyD,eAAe,uKAAuK;AAAA,IAC9P;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,gBAAgB,KAAK,YAAY,eAAe,KAAK,YAAY,SAAS;AACzH;AAEA,SAAS,sBAAsB,QAAQ;AACrC,MAAI,QAAQ;AAEZ,MAAI,uBAAuB,MAAM,KAAM,QAAO,OAAO,KAAK,IAAI;AAM9D,gBAAc,MAAM,eAAe,KAAK;AAMxC,gBAAc,MAAM,oBAAoB,KAAK;AAC7C,MAAI,cAAc,eAAe,IAAI;AACrC,MAAI,aAAa,OAAO,KAAK,IAAI;AACjC,MAAI,qBAAqB;AACzB,MAAI,WAAW,IAAI,SAAS,cAAc,aAAa,WAAY;AACjE,QAAI,CAAC,oBAAoB;AAIvB,2BAAqB;AAErB,UAAI,MAAM,eAAe,MAAM,MAAM;AACnC,YAAI,WAAW;AAEf,YAAI;AACF,wBAAc,OAAO,oBAAoB,IAAI;AAC7C,cAAI,CAAC,MAAM,aAAa,EAAG,yBAAU,UAAU,YAAY,KAAK,KAAK;AACrE,qBAAW;AAAA,QACb,UAAE;AACA,wBAAc,OAAO,oBAAoB,KAAK;AAC9C,cAAI,SAAU,UAAS,QAAQ;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,WAAS,gBAAgB,IAAI;AAC7B,iBAAe,iBAAiB,IAAI;AACpC,OAAK,SAAS;AAEd,WAAS,iBAAiB;AACxB,yBAAqB;AACrB,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,aAAS,MAAM,WAAY;AACzB,UAAI;AACF,oBAAY,kBAAmB,OAAO,UAAU;AAAA,MAClD,SAAS,GAAG;AACV,oBAAY;AAAA,MACd;AAAA,IACF,CAAC;AAED,QAAI,WAAW;AACb,YAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,eAAe,KAAK,IAAI;AACjC;AAEA,SAAS,YAAY,WAAW,WAAW;AACzC,MAAI,uBAAuB,GAAG;AAC5B,YAAQ,KAAK,iLAAiL;AAAA,EAChM;AAGA,MAAI,KAAK,UAAU,WAAW;AAC5B,WAAO;AAAA,EACT;AAMA,SAAO,CAAC,aAAa,KAAK,OAAO,SAAS;AAC5C;AAEA,SAAS,mBAAmB,QAAQ,UAAU;AAC5C,MAAI,iBAAiB,UAAU,eAAe,WAAW,cAAc;AACvE,MAAI,gBAAgB,UAAU,eAAe,WAAW,aAAa;AAErE,WAAS,UAAU;AACjB,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,oBAAc,MAAM,eAAe,WAAW,cAAc,QAAQ,CAAC;AAAA,IACvE;AAEA,WAAO,KAAK,aAAa;AAAA,EAC3B;AAEA,SAAO,eAAe,QAAQ,UAAU;AAAA,IACtC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,KAAK,SAAS,MAAM;AAClB,UAAI,gBAAgB;AAEpB,UAAI,wBAAyB,oBAAqB;AAChD,wBAAgB,qBAAsB,IAAI;AAAA,MAC5C;AAEA,cAAQ,KAAK,IAAI,EAAE,eAAe;AAElC,UAAI,wBAAyB,oBAAqB;AAChD,2BAAoB,aAAa;AAAA,MACnC;AAEA,aAAO,KAAK,cAAc;AAAA,IAC5B;AAAA,IACA,KAAK,SAAS,IAAI,GAAG;AACnB,UAAI,CAAC,KAAK,kBAAkB,KAAK,CAAC,aAAa,KAAK,cAAc,GAAG,CAAC,GAAG;AACvE,sBAAc,MAAM,gBAAgB,CAAC;AACrC,sBAAc,MAAM,eAAe,IAAI;AACvC,gBAAQ,KAAK,IAAI,EAAE,cAAc;AACjC,sBAAc,MAAM,eAAe,KAAK;AAAA,MAC1C,OAAO;AACL,sBAAc,MAAM,gBAAgB,CAAC;AAAA,MACvC;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,IAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AAEvD,IAAI,wBAAwB,YAE5B,OAAO,IAAI,mBAAmB,IAAI,OAAO,6BAAe,kBAExD,0BAAW,SAAU,OAAO;AAC1B,SAAO;AACT,CAAC,EAAE,UAAU;AACb,IAAI,kBAAkB,YAEtB,OAAO,IAAI,YAAY,IAAI,OAAO,uBAAS,kBAE3C,oBAAK,SAAU,OAAO;AACpB,SAAO;AACT,CAAC,EAAE,UAAU;AAKb,SAASC,UAAS,WAAW;AAC3B,MAAI,UAAU,gBAAgB,MAAM,MAAM;AACxC,YAAQ,KAAK,4IAA4I;AAAA,EAC3J;AAEA,MAAI,mBAAmB,UAAU,UAAU,MAAM,iBAAiB;AAChE,UAAM,IAAI,MAAM,gLAAgL;AAAA,EAClM;AAKA,MAAI,yBAAyB,UAAU,UAAU,MAAM,uBAAuB;AAC5E,QAAI,aAAa,UAAU,QAAQ;AACnC,QAAI,OAAO,eAAe,WAAY,OAAM,IAAI,MAAM,kDAAkD;AACxG,eAAO,0BAAW,SAAS,qBAAqB;AAC9C,UAAI,OAAO;AACX,iBAAO,6BAAc,mBAAU,MAAM,WAAY;AAC/C,eAAO,WAAW,MAAM,QAAW,IAAI;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAGA,MAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa,CAAC,UAAU,UAAU,WAAW,CAAC,UAAU,cAAc,KAAK,CAAC,OAAO,UAAU,cAAc,KAAK,yBAAW,SAAS,GAAG;AACxL,WAAO,SAAW,SAAS;AAAA,EAC7B;AAEA,SAAO,2BAA2B,SAAS;AAC7C;AAEA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAI,sBAEJ,cAAAC,QAAe,cAAc,CAAC,CAAC;AAC/B,SAAS,SAAS,OAAO;AACvB,MAAI,WAAW,MAAM,UACjB,SAAS,8BAA8B,OAAO,CAAC,UAAU,CAAC;AAE9D,MAAI,cAAc,cAAAA,QAAe,WAAW,mBAAmB;AAC/D,MAAI,qBAAqB,cAAAA,QAAe,OAAO,SAAS,CAAC,GAAG,aAAa,MAAM,CAAC;AAChF,MAAI,QAAQ,mBAAmB;AAE/B,MAAI,MAAuC;AACzC,QAAI,WAAW,SAAS,CAAC,GAAG,OAAO,MAAM;AAGzC,QAAI,CAAC,aAAa,OAAO,QAAQ,GAAG;AAClC,YAAM,IAAI,MAAM,gJAAgJ;AAAA,IAClK;AAAA,EACF;AAEA,SAAO,cAAAA,QAAe,cAAc,oBAAoB,UAAU;AAAA,IAChE;AAAA,EACF,GAAG,QAAQ;AACb;AACA,SAAS,cAAc;AAMvB,SAAS,oBAAoB,cAAc,WAAW,aAAa,cAAc;AAE/E,MAAI,WAAW,cAAAA,QAAe,WAAW,SAAU,OAAO,KAAK;AAC7D,QAAI,WAAW,SAAS,CAAC,GAAG,KAAK;AAEjC,QAAI,UAAU,cAAAA,QAAe,WAAW,mBAAmB;AAC3D,WAAO,OAAO,UAAU,aAAa,WAAW,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC;AAEnE,QAAI,KAAK;AACP,eAAS,MAAM;AAAA,IACjB;AAEA,WAAO,cAAAA,QAAe,cAAc,WAAW,QAAQ;AAAA,EACzD,CAAC;AACD,MAAI,aAAc,YAAWD,UAAS,QAAQ;AAC9C,WAAS,gBAAgB,IAAI;AAG7B,EAAAF,sBAAqB,WAAW,QAAQ;AACxC,WAAS,kBAAkB,IAAI;AAC/B,WAAS,cAAc,cAAc,WAAW,WAAW;AAC3D,SAAO;AACT;AAEA,SAAS,cAAc,WAAW,aAAa;AAC7C,MAAI;AACJ,MAAI,gBAAgB,UAAU,eAAe,UAAU,QAAQ,UAAU,eAAe,UAAU,YAAY,QAAQ;AACtH,MAAI,YAAa,eAAc,iBAAiB,cAAc,MAAM,gBAAgB;AAAA,MAAS,eAAc,YAAY,gBAAgB;AACvI,SAAO;AACT;AAEA,SAAS,iBAAiB,YAAY;AACpC,SAAO,SAAU,YAAY,WAAW;AACtC,eAAW,QAAQ,SAAU,WAAW;AACtC,UAAI,aAAa,UACf;AACF,UAAI,EAAE,aAAa,YAAa,OAAM,IAAI,MAAM,2BAA2B,YAAY,+DAA+D;AACtJ,gBAAU,SAAS,IAAI,WAAW,SAAS;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACT;AACF;AASA,SAAS,SAAS;AAChB,WAAS,OAAO,UAAU,QAAQ,aAAa,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC7F,eAAW,IAAI,IAAI,UAAU,IAAI;AAAA,EACnC;AAEA,MAAI,OAAO,UAAU,CAAC,MAAM,YAAY;AACtC,QAAI,eAAe,UAAU,CAAC;AAC9B,WAAO,SAAU,gBAAgB;AAC/B,aAAO,oBAAoB,cAAc,gBAAgB,aAAa,MAAM,IAAI;AAAA,IAClF;AAAA,EACF,OAAO;AACL,WAAO,SAAU,gBAAgB;AAC/B,aAAO,oBAAoB,iBAAiB,UAAU,GAAG,gBAAgB,WAAW,KAAK,GAAG,GAAG,KAAK;AAAA,IACtG;AAAA,EACF;AACF;AAEA,IAAI,gBAEJ,UAAU,uBAAuB;AACjC,IAAI,eAEJ,UAAU,sBAAsB;AAEhC,SAAS,4BAA4B;AACnC,MAAI,QAAQ;AACZ,GAAC,EAAE,OAAO,KAAK,aAAa,KAAK,CAAC,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,SAAU,mBAAmB;AAClG,QAAI,OAAO,OAAO,sBAAsB,WAAW,MAAM,iBAAiB,IAAI;AAE9E,QAAI,SAAS,UAAa,SAAS,MAAM;AACvC,UAAI,MAAM,QAAQ,IAAI,EAAG,MAAK,IAAI,SAAU,GAAG;AAC7C,eAAO,EAAE;AAAA,MACX,CAAC;AAAA,UAAO,MAAK;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAEA,SAAS,iBAAiB,QAAQ,uBAAuB;AACvD,MAAI,MAAM,QAAQ,qBAAqB,GAAG;AACxC,WAAO,sBAAsB,IAAI,SAAU,IAAI;AAC7C,aAAO,iBAAiB,QAAQ,EAAE;AAAA,IACpC,CAAC;AAAA,EACH;AAEA,MAAI,IAAI,OAAO,eAAe,MAAM,EAAE;AACtC,MAAI,KAAK,OAAO,eAAe,OAAO,WAAW;AAEjD,MAAI,KAAK,OAAO,eAAe,OAAO,eAAe,MAAM,CAAC;AAE5D,MAAI,EAAE,MAAM,cAAAG,QAAe,aAAa,MAAM,cAAAA,QAAe,iBAAiB,OAAO,cAAAA,QAAe,aAAa,OAAO,cAAAA,QAAe,iBAAiB,OAAO,cAAAA,QAAe,aAAa,OAAO,cAAAA,QAAe,gBAAgB;AAC/N,UAAM,IAAI,MAAM,0GAA0G;AAAA,EAC5H;AAEA,MAAI,OAAO,0BAA0B,YAAY,OAAO,0BAA0B,cAAc,CAAC,MAAM,QAAQ,qBAAqB,GAAG;AACrI,UAAM,IAAI,MAAM,mGAAmG;AAAA,EACrH;AAGA,MAAI,cAAc,OAAO,0BAA0B;AAEnD,MAAI,8BAA8B,CAAC,CAAC,OAAO,aAAa,KAAK,CAAC,CAAC,OAAO,YAAY;AAClF,MAAI,QAAQ;AAAA;AAAA,IACZ,OAAO,aAAa,MAAM,OAAO,aAAa,IAAI,CAAC;AAAA;AAAA;AAAA,IACnD,OAAO,YAAY,MAAM,OAAO,YAAY,IAAI,CAAC;AAAA;AACjD,QAAM,KAAK,qBAAqB;AAEhC,MAAI,CAAC,6BAA6B;AAChC,UAAM,QAAQ,wBAAwB,yBAAyB;AAAA,EACjE;AAGA,MAAI,OAAO,0BAA0B,UAAU;AAC7C,WAAO;AAAA,EACT;AACF;AAEA,SAAS,2BAA2B,WAAW;AAC7C,WAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc;AACrF,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,UAAU,WAAY;AAC3B,sBAAgB,iBAAiB;AACjC,qBAAe,gBAAgB;AAE/B,UAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,YAAI,YAAY;AACd,cAAI,SAAS,MAAM,QAAQ,MAAM,OAAO,SAAS;AACjD,iBAAO,IAAI,MAAM,SAAS,WAAW,OAAO,eAAe,iCAAsC,gBAAgB,0BAA0B,SAAS,IAAI;AAAA,QAC1J;AAEA,eAAO;AAAA,MACT,OAAO;AAEL,eAAO,UAAU,MAAM,QAAQ,CAAC,OAAO,UAAU,eAAe,UAAU,YAAY,EAAE,OAAO,IAAI,CAAC;AAAA,MACtG;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AAEjD,mBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AACvD,SAAO;AACT;AAGA,SAAS,SAAS,UAAU,WAAW;AAErC,MAAI,aAAa,UAAU;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGA,SAAS,YAAY,WAAW;AAC9B,MAAI,WAAW,OAAO;AAEtB,MAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,qBAAqB,QAAQ;AAI/B,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,UAAU,SAAS,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAIA,SAAS,eAAe,WAAW;AACjC,MAAI,WAAW,YAAY,SAAS;AAEpC,MAAI,aAAa,UAAU;AACzB,QAAI,qBAAqB,MAAM;AAC7B,aAAO;AAAA,IACT,WAAW,qBAAqB,QAAQ;AACtC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,mCAAmC,iBAAiB,UAAU;AACrE,SAAO,2BAA2B,SAAU,OAAO,UAAU,eAAe,UAAU,cAAc;AAClG,WAAO,UAAU,WAAY;AAC3B,UAAI,iBAAiB;AACnB,YAAI,YAAY,MAAM,QAAQ,CAAC,MAAM,SAAS,YAAY,EAAG,QAAO;AAAA,MACtE;AAEA,UAAI;AAEJ,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,wBAAc;AACd;AAAA,QAEF,KAAK;AACH,wBAAc;AACd;AAAA,QAEF,KAAK;AACH,wBAAc;AACd;AAAA,QAEF;AACE,gBAAM,IAAI,MAAM,0BAA0B,QAAQ;AAAA,MACtD;AAEA,UAAI,YAAY,MAAM,QAAQ;AAE9B,UAAI,CAAC,YAAY,SAAS,GAAG;AAC3B,YAAI,cAAc,eAAe,SAAS;AAC1C,YAAI,+BAA+B,kBAAkB,qBAAqB,SAAS,YAAY,IAAI,MAAM;AACzG,eAAO,IAAI,MAAM,mBAAmB,eAAe,gBAAgB,cAAc,oBAAyB,gBAAgB,iCAAiC,WAAW,MAAM,+BAA+B,GAAG;AAAA,MAChN;AAEA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,mCAAmC,iBAAiB,aAAa;AACxE,SAAO,2BAA2B,SAAU,OAAO,UAAU,eAAe,UAAU,cAAc;AAClG,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,WAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,IACnC;AAEA,WAAO,UAAU,WAAY;AAC3B,UAAI,OAAO,gBAAgB,YAAY;AACrC,eAAO,IAAI,MAAM,eAAe,eAAe,qBAAqB,gBAAgB,kCAAuC;AAAA,MAC7H,OAAO;AACL,YAAI,QAAQ,mCAAmC,iBAAiB,OAAO,EAAE,OAAO,UAAU,eAAe,UAAU,YAAY;AAC/H,YAAI,iBAAiB,MAAO,QAAO;AACnC,YAAI,YAAY,MAAM,QAAQ;AAE9B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAQ,YAAY,MAAM,QAAQ,CAAC,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC;AACpH,cAAI,iBAAiB,MAAO,QAAO;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAI,kBAEJ,mCAAmC,OAAO,OAAO;AACjD,IAAI,oBAEJ,mCAAmC,KAAK,MAAM,KAAK;AACnD,IAAI,gBAEJ,mCAAmC,OAAO,KAAK;AAC/C,IAAI,mBAEJ,mCAAmC,OAAO,QAAQ;AAClD,IAAI,yBAEJ,mCAAmC,MAAM,OAAO;AAChD,IAAI,2BAEJ,mCAAmC,KAAK,MAAM,IAAI;AAClD,IAAI,2BAEJ,mCAAmC,MAAM,QAAQ;AACjD,IAAI,YAAY;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,CAAC,wBAAW,OAAM,IAAI,MAAM,2CAA2C;AAC3E,IAAI,CAAC,WAAY,OAAM,IAAI,MAAM,0CAA0C;", "names": ["import_react", "import_react", "import_react", "import_react", "import_react", "React", "React", "import_react", "__read", "React", "import_react", "React", "hoistBlackList", "copyStaticProperties", "fn", "observer", "React__default"]}