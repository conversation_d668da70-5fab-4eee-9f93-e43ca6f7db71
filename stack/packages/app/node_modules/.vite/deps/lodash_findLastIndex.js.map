{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/findLastIndex.js"], "sourcesContent": ["var baseFindIndex = require('./_baseFindIndex'),\n    baseIteratee = require('./_baseIteratee'),\n    toInteger = require('./toInteger');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * This method is like `_.findIndex` except that it iterates over elements\n * of `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=array.length-1] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.findLastIndex(users, function(o) { return o.user == 'pebbles'; });\n * // => 2\n *\n * // The `_.matches` iteratee shorthand.\n * _.findLastIndex(users, { 'user': 'barney', 'active': true });\n * // => 0\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findLastIndex(users, ['active', false]);\n * // => 2\n *\n * // The `_.property` iteratee shorthand.\n * _.findLastIndex(users, 'active');\n * // => 0\n */\nfunction findLastIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = length - 1;\n  if (fromIndex !== undefined) {\n    index = toInteger(fromIndex);\n    index = fromIndex < 0\n      ? nativeMax(length + index, 0)\n      : nativeMin(index, length - 1);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index, true);\n}\n\nmodule.exports = findLastIndex;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,eAAe;AADnB,QAEI,YAAY;AAGhB,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAqCrB,aAAS,cAAc,OAAO,WAAW,WAAW;AAClD,UAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,SAAS;AACrB,UAAI,cAAc,QAAW;AAC3B,gBAAQ,UAAU,SAAS;AAC3B,gBAAQ,YAAY,IAChB,UAAU,SAAS,OAAO,CAAC,IAC3B,UAAU,OAAO,SAAS,CAAC;AAAA,MACjC;AACA,aAAO,cAAc,OAAO,aAAa,WAAW,CAAC,GAAG,OAAO,IAAI;AAAA,IACrE;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}