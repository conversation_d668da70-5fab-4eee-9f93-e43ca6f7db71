import {
  require_baseClamp
} from "./chunk-J73GKEBW.js";
import {
  require_toInteger
} from "./chunk-3M5FHVBU.js";
import "./chunk-7V26ZGEI.js";
import {
  require_baseToString,
  require_toString
} from "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/startsWith.js
var require_startsWith = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/startsWith.js"(exports, module) {
    var baseClamp = require_baseClamp();
    var baseToString = require_baseToString();
    var toInteger = require_toInteger();
    var toString = require_toString();
    function startsWith(string, target, position) {
      string = toString(string);
      position = position == null ? 0 : baseClamp(toInteger(position), 0, string.length);
      target = baseToString(target);
      return string.slice(position, position + target.length) == target;
    }
    module.exports = startsWith;
  }
});
export default require_startsWith();
//# sourceMappingURL=lodash_startsWith.js.map
