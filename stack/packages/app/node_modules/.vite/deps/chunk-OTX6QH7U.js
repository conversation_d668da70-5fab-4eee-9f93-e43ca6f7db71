import {
  require_isArrayLike
} from "./chunk-4GUL7IQK.js";
import {
  require_isIndex
} from "./chunk-CXIFQW4L.js";
import {
  require_eq
} from "./chunk-3MWHQ5U6.js";
import {
  require_isObject
} from "./chunk-MBKML2QC.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_isIterateeCall.js
var require_isIterateeCall = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_isIterateeCall.js"(exports, module) {
    var eq = require_eq();
    var isArrayLike = require_isArrayLike();
    var isIndex = require_isIndex();
    var isObject = require_isObject();
    function isIterateeCall(value, index, object) {
      if (!isObject(object)) {
        return false;
      }
      var type = typeof index;
      if (type == "number" ? isArrayLike(object) && isIndex(index, object.length) : type == "string" && index in object) {
        return eq(object[index], value);
      }
      return false;
    }
    module.exports = isIterateeCall;
  }
});

export {
  require_isIterateeCall
};
//# sourceMappingURL=chunk-OTX6QH7U.js.map
