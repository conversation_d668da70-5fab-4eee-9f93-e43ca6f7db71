import {
  require_isObjectLike
} from "./chunk-WLFKI2V4.js";
import {
  require_baseGetTag
} from "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isBoolean.js
var require_isBoolean = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isBoolean.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var boolTag = "[object Boolean]";
    function isBoolean(value) {
      return value === true || value === false || isObjectLike(value) && baseGetTag(value) == boolTag;
    }
    module.exports = isBoolean;
  }
});
export default require_isBoolean();
//# sourceMappingURL=lodash_isBoolean.js.map
