{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseToPairs.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_setToPairs.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createToPairs.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/toPairs.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap');\n\n/**\n * The base implementation of `_.toPairs` and `_.toPairsIn` which creates an array\n * of key-value pairs for `object` corresponding to the property names of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the key-value pairs.\n */\nfunction baseToPairs(object, props) {\n  return arrayMap(props, function(key) {\n    return [key, object[key]];\n  });\n}\n\nmodule.exports = baseToPairs;\n", "/**\n * Converts `set` to its value-value pairs.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the value-value pairs.\n */\nfunction setToPairs(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = [value, value];\n  });\n  return result;\n}\n\nmodule.exports = setToPairs;\n", "var baseToPairs = require('./_baseToPairs'),\n    getTag = require('./_getTag'),\n    mapToArray = require('./_mapToArray'),\n    setToPairs = require('./_setToPairs');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/**\n * Creates a `_.toPairs` or `_.toPairsIn` function.\n *\n * @private\n * @param {Function} keysFunc The function to get the keys of a given object.\n * @returns {Function} Returns the new pairs function.\n */\nfunction createToPairs(keysFunc) {\n  return function(object) {\n    var tag = getTag(object);\n    if (tag == mapTag) {\n      return mapToArray(object);\n    }\n    if (tag == setTag) {\n      return setToPairs(object);\n    }\n    return baseToPairs(object, keysFunc(object));\n  };\n}\n\nmodule.exports = createToPairs;\n", "var createToPairs = require('./_createToPairs'),\n    keys = require('./keys');\n\n/**\n * Creates an array of own enumerable string keyed-value pairs for `object`\n * which can be consumed by `_.fromPairs`. If `object` is a map or set, its\n * entries are returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @alias entries\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the key-value pairs.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.toPairs(new Foo);\n * // => [['a', 1], ['b', 2]] (iteration order is not guaranteed)\n */\nvar toPairs = createToPairs(keys);\n\nmodule.exports = toPairs;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAWf,aAAS,YAAY,QAAQ,OAAO;AAClC,aAAO,SAAS,OAAO,SAAS,KAAK;AACnC,eAAO,CAAC,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAOA,aAAS,WAAW,KAAK;AACvB,UAAI,QAAQ,IACR,SAAS,MAAM,IAAI,IAAI;AAE3B,UAAI,QAAQ,SAAS,OAAO;AAC1B,eAAO,EAAE,KAAK,IAAI,CAAC,OAAO,KAAK;AAAA,MACjC,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,SAAS;AADb,QAEI,aAAa;AAFjB,QAGI,aAAa;AAGjB,QAAI,SAAS;AAAb,QACI,SAAS;AASb,aAAS,cAAc,UAAU;AAC/B,aAAO,SAAS,QAAQ;AACtB,YAAI,MAAM,OAAO,MAAM;AACvB,YAAI,OAAO,QAAQ;AACjB,iBAAO,WAAW,MAAM;AAAA,QAC1B;AACA,YAAI,OAAO,QAAQ;AACjB,iBAAO,WAAW,MAAM;AAAA,QAC1B;AACA,eAAO,YAAY,QAAQ,SAAS,MAAM,CAAC;AAAA,MAC7C;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,OAAO;AA0BX,QAAI,UAAU,cAAc,IAAI;AAEhC,WAAO,UAAU;AAAA;AAAA;", "names": []}