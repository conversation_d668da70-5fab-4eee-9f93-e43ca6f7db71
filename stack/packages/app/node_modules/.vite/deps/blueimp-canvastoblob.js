// ../../node_modules/.pnpm/blueimp-canvastoblob@2.1.0/node_modules/blueimp-canvastoblob/js/canvas-to-blob.js
(function(window2) {
  "use strict";
  var CanvasPrototype = window2.HTMLCanvasElement && window2.HTMLCanvasElement.prototype, hasBlobConstructor = window2.Blob && function() {
    try {
      return Boolean(new Blob());
    } catch (e) {
      return false;
    }
  }(), hasArrayBufferViewSupport = hasBlobConstructor && window2.Uint8Array && function() {
    try {
      return new Blob([new Uint8Array(100)]).size === 100;
    } catch (e) {
      return false;
    }
  }(), BlobBuilder = window2.BlobBuilder || window2.WebKitBlobBuilder || window2.MozBlobBuilder || window2.MSBlobBuilder, dataURLtoBlob = (hasBlobConstructor || BlobBuilder) && window2.atob && window2.ArrayBuffer && window2.Uint8Array && function(dataURI) {
    var byteString, arrayBuffer, intArray, i, mimeString, bb;
    if (dataURI.split(",")[0].indexOf("base64") >= 0) {
      byteString = atob(dataURI.split(",")[1]);
    } else {
      byteString = decodeURIComponent(dataURI.split(",")[1]);
    }
    arrayBuffer = new ArrayBuffer(byteString.length);
    intArray = new Uint8Array(arrayBuffer);
    for (i = 0; i < byteString.length; i += 1) {
      intArray[i] = byteString.charCodeAt(i);
    }
    mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    if (hasBlobConstructor) {
      return new Blob(
        [hasArrayBufferViewSupport ? intArray : arrayBuffer],
        { type: mimeString }
      );
    }
    bb = new BlobBuilder();
    bb.append(arrayBuffer);
    return bb.getBlob(mimeString);
  };
  if (window2.HTMLCanvasElement && !CanvasPrototype.toBlob) {
    if (CanvasPrototype.mozGetAsFile) {
      CanvasPrototype.toBlob = function(callback, type, quality) {
        if (quality && CanvasPrototype.toDataURL && dataURLtoBlob) {
          callback(dataURLtoBlob(this.toDataURL(type, quality)));
        } else {
          callback(this.mozGetAsFile("blob", type));
        }
      };
    } else if (CanvasPrototype.toDataURL && dataURLtoBlob) {
      CanvasPrototype.toBlob = function(callback, type, quality) {
        callback(dataURLtoBlob(this.toDataURL(type, quality)));
      };
    }
  }
  if (typeof define === "function" && define.amd) {
    define(function() {
      return dataURLtoBlob;
    });
  } else {
    window2.dataURLtoBlob = dataURLtoBlob;
  }
})(window);
//# sourceMappingURL=blueimp-canvastoblob.js.map
