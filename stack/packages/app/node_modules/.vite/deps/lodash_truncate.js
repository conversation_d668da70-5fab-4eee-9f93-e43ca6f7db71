import {
  require_stringSize
} from "./chunk-GTQIYH5I.js";
import {
  require_castSlice,
  require_hasUnicode,
  require_stringToArray
} from "./chunk-5XJARE5H.js";
import "./chunk-EE6E7F7Q.js";
import "./chunk-3MCR5VVW.js";
import {
  require_toInteger
} from "./chunk-3M5FHVBU.js";
import "./chunk-7V26ZGEI.js";
import {
  require_nodeUtil
} from "./chunk-TOPLOUEN.js";
import {
  require_baseUnary
} from "./chunk-GSW7XBCA.js";
import {
  require_baseToString,
  require_toString
} from "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-XDZT3BAO.js";
import {
  require_isObject
} from "./chunk-MBKML2QC.js";
import {
  require_isObjectLike
} from "./chunk-WLFKI2V4.js";
import {
  require_baseGetTag
} from "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsRegExp.js
var require_baseIsRegExp = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsRegExp.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var regexpTag = "[object RegExp]";
    function baseIsRegExp(value) {
      return isObjectLike(value) && baseGetTag(value) == regexpTag;
    }
    module.exports = baseIsRegExp;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isRegExp.js
var require_isRegExp = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isRegExp.js"(exports, module) {
    var baseIsRegExp = require_baseIsRegExp();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsRegExp = nodeUtil && nodeUtil.isRegExp;
    var isRegExp = nodeIsRegExp ? baseUnary(nodeIsRegExp) : baseIsRegExp;
    module.exports = isRegExp;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/truncate.js
var require_truncate = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/truncate.js"(exports, module) {
    var baseToString = require_baseToString();
    var castSlice = require_castSlice();
    var hasUnicode = require_hasUnicode();
    var isObject = require_isObject();
    var isRegExp = require_isRegExp();
    var stringSize = require_stringSize();
    var stringToArray = require_stringToArray();
    var toInteger = require_toInteger();
    var toString = require_toString();
    var DEFAULT_TRUNC_LENGTH = 30;
    var DEFAULT_TRUNC_OMISSION = "...";
    var reFlags = /\w*$/;
    function truncate(string, options) {
      var length = DEFAULT_TRUNC_LENGTH, omission = DEFAULT_TRUNC_OMISSION;
      if (isObject(options)) {
        var separator = "separator" in options ? options.separator : separator;
        length = "length" in options ? toInteger(options.length) : length;
        omission = "omission" in options ? baseToString(options.omission) : omission;
      }
      string = toString(string);
      var strLength = string.length;
      if (hasUnicode(string)) {
        var strSymbols = stringToArray(string);
        strLength = strSymbols.length;
      }
      if (length >= strLength) {
        return string;
      }
      var end = length - stringSize(omission);
      if (end < 1) {
        return omission;
      }
      var result = strSymbols ? castSlice(strSymbols, 0, end).join("") : string.slice(0, end);
      if (separator === void 0) {
        return result + omission;
      }
      if (strSymbols) {
        end += result.length - end;
      }
      if (isRegExp(separator)) {
        if (string.slice(end).search(separator)) {
          var match, substring = result;
          if (!separator.global) {
            separator = RegExp(separator.source, toString(reFlags.exec(separator)) + "g");
          }
          separator.lastIndex = 0;
          while (match = separator.exec(substring)) {
            var newEnd = match.index;
          }
          result = result.slice(0, newEnd === void 0 ? end : newEnd);
        }
      } else if (string.indexOf(baseToString(separator), end) != end) {
        var index = result.lastIndexOf(separator);
        if (index > -1) {
          result = result.slice(0, index);
        }
      }
      return result + omission;
    }
    module.exports = truncate;
  }
});
export default require_truncate();
//# sourceMappingURL=lodash_truncate.js.map
