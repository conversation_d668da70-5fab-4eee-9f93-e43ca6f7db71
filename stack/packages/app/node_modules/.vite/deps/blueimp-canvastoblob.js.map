{"version": 3, "sources": ["../../../../../node_modules/.pnpm/blueimp-canvastoblob@2.1.0/node_modules/blueimp-canvastoblob/js/canvas-to-blob.js"], "sourcesContent": ["/*\n * JavaScript Canvas to Blob 2.0.5\n * https://github.com/blueimp/JavaScript-Canvas-to-Blob\n *\n * Copyright 2012, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * http://www.opensource.org/licenses/MIT\n *\n * Based on stackoverflow user Stoive's code snippet:\n * http://stackoverflow.com/q/4998908\n */\n\n/*jslint nomen: true, regexp: true */\n/*global window, atob, Blob, ArrayBuffer, Uint8Array, define */\n\n(function (window) {\n    'use strict';\n    var CanvasPrototype = window.HTMLCanvasElement &&\n            window.HTMLCanvasElement.prototype,\n        hasBlobConstructor = window.Blob && (function () {\n            try {\n                return Boolean(new Blob());\n            } catch (e) {\n                return false;\n            }\n        }()),\n        hasArrayBufferViewSupport = hasBlobConstructor && window.Uint8Array &&\n            (function () {\n                try {\n                    return new Blob([new Uint8Array(100)]).size === 100;\n                } catch (e) {\n                    return false;\n                }\n            }()),\n        BlobBuilder = window.BlobBuilder || window.WebKitBlobBuilder ||\n            window.MozBlobBuilder || window.MSBlobBuilder,\n        dataURLtoBlob = (hasBlobConstructor || BlobBuilder) && window.atob &&\n            window.ArrayBuffer && window.Uint8Array && function (dataURI) {\n                var byteString,\n                    arrayBuffer,\n                    intArray,\n                    i,\n                    mimeString,\n                    bb;\n                if (dataURI.split(',')[0].indexOf('base64') >= 0) {\n                    // Convert base64 to raw binary data held in a string:\n                    byteString = atob(dataURI.split(',')[1]);\n                } else {\n                    // Convert base64/URLEncoded data component to raw binary data:\n                    byteString = decodeURIComponent(dataURI.split(',')[1]);\n                }\n                // Write the bytes of the string to an ArrayBuffer:\n                arrayBuffer = new ArrayBuffer(byteString.length);\n                intArray = new Uint8Array(arrayBuffer);\n                for (i = 0; i < byteString.length; i += 1) {\n                    intArray[i] = byteString.charCodeAt(i);\n                }\n                // Separate out the mime component:\n                mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];\n                // Write the ArrayBuffer (or ArrayBufferView) to a blob:\n                if (hasBlobConstructor) {\n                    return new Blob(\n                        [hasArrayBufferViewSupport ? intArray : arrayBuffer],\n                        {type: mimeString}\n                    );\n                }\n                bb = new BlobBuilder();\n                bb.append(arrayBuffer);\n                return bb.getBlob(mimeString);\n            };\n    if (window.HTMLCanvasElement && !CanvasPrototype.toBlob) {\n        if (CanvasPrototype.mozGetAsFile) {\n            CanvasPrototype.toBlob = function (callback, type, quality) {\n                if (quality && CanvasPrototype.toDataURL && dataURLtoBlob) {\n                    callback(dataURLtoBlob(this.toDataURL(type, quality)));\n                } else {\n                    callback(this.mozGetAsFile('blob', type));\n                }\n            };\n        } else if (CanvasPrototype.toDataURL && dataURLtoBlob) {\n            CanvasPrototype.toBlob = function (callback, type, quality) {\n                callback(dataURLtoBlob(this.toDataURL(type, quality)));\n            };\n        }\n    }\n    if (typeof define === 'function' && define.amd) {\n        define(function () {\n            return dataURLtoBlob;\n        });\n    } else {\n        window.dataURLtoBlob = dataURLtoBlob;\n    }\n}(window));\n"], "mappings": ";CAiBC,SAAUA,SAAQ;AACf;AACA,MAAI,kBAAkBA,QAAO,qBACrBA,QAAO,kBAAkB,WAC7B,qBAAqBA,QAAO,QAAS,WAAY;AAC7C,QAAI;AACA,aAAO,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC7B,SAAS,GAAG;AACR,aAAO;AAAA,IACX;AAAA,EACJ,EAAE,GACF,4BAA4B,sBAAsBA,QAAO,cACpD,WAAY;AACT,QAAI;AACA,aAAO,IAAI,KAAK,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE,SAAS;AAAA,IACpD,SAAS,GAAG;AACR,aAAO;AAAA,IACX;AAAA,EACJ,EAAE,GACN,cAAcA,QAAO,eAAeA,QAAO,qBACvCA,QAAO,kBAAkBA,QAAO,eACpC,iBAAiB,sBAAsB,gBAAgBA,QAAO,QAC1DA,QAAO,eAAeA,QAAO,cAAc,SAAU,SAAS;AAC1D,QAAI,YACA,aACA,UACA,GACA,YACA;AACJ,QAAI,QAAQ,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,QAAQ,KAAK,GAAG;AAE9C,mBAAa,KAAK,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,IAC3C,OAAO;AAEH,mBAAa,mBAAmB,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,IACzD;AAEA,kBAAc,IAAI,YAAY,WAAW,MAAM;AAC/C,eAAW,IAAI,WAAW,WAAW;AACrC,SAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACvC,eAAS,CAAC,IAAI,WAAW,WAAW,CAAC;AAAA,IACzC;AAEA,iBAAa,QAAQ,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAE7D,QAAI,oBAAoB;AACpB,aAAO,IAAI;AAAA,QACP,CAAC,4BAA4B,WAAW,WAAW;AAAA,QACnD,EAAC,MAAM,WAAU;AAAA,MACrB;AAAA,IACJ;AACA,SAAK,IAAI,YAAY;AACrB,OAAG,OAAO,WAAW;AACrB,WAAO,GAAG,QAAQ,UAAU;AAAA,EAChC;AACR,MAAIA,QAAO,qBAAqB,CAAC,gBAAgB,QAAQ;AACrD,QAAI,gBAAgB,cAAc;AAC9B,sBAAgB,SAAS,SAAU,UAAU,MAAM,SAAS;AACxD,YAAI,WAAW,gBAAgB,aAAa,eAAe;AACvD,mBAAS,cAAc,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;AAAA,QACzD,OAAO;AACH,mBAAS,KAAK,aAAa,QAAQ,IAAI,CAAC;AAAA,QAC5C;AAAA,MACJ;AAAA,IACJ,WAAW,gBAAgB,aAAa,eAAe;AACnD,sBAAgB,SAAS,SAAU,UAAU,MAAM,SAAS;AACxD,iBAAS,cAAc,KAAK,UAAU,MAAM,OAAO,CAAC,CAAC;AAAA,MACzD;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,WAAO,WAAY;AACf,aAAO;AAAA,IACX,CAAC;AAAA,EACL,OAAO;AACH,IAAAA,QAAO,gBAAgB;AAAA,EAC3B;AACJ,GAAE,MAAM;", "names": ["window"]}