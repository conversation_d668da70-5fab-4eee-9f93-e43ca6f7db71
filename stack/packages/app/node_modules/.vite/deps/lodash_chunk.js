import {
  require_isIterateeCall
} from "./chunk-OTX6QH7U.js";
import {
  require_baseSlice
} from "./chunk-EE6E7F7Q.js";
import {
  require_toInteger
} from "./chunk-3M5FHVBU.js";
import "./chunk-7V26ZGEI.js";
import "./chunk-4GUL7IQK.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-CXIFQW4L.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-3MWHQ5U6.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/chunk.js
var require_chunk = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/chunk.js"(exports, module) {
    var baseSlice = require_baseSlice();
    var isIterateeCall = require_isIterateeCall();
    var toInteger = require_toInteger();
    var nativeCeil = Math.ceil;
    var nativeMax = Math.max;
    function chunk(array, size, guard) {
      if (guard ? isIterateeCall(array, size, guard) : size === void 0) {
        size = 1;
      } else {
        size = nativeMax(toInteger(size), 0);
      }
      var length = array == null ? 0 : array.length;
      if (!length || size < 1) {
        return [];
      }
      var index = 0, resIndex = 0, result = Array(nativeCeil(length / size));
      while (index < length) {
        result[resIndex++] = baseSlice(array, index, index += size);
      }
      return result;
    }
    module.exports = chunk;
  }
});
export default require_chunk();
//# sourceMappingURL=lodash_chunk.js.map
