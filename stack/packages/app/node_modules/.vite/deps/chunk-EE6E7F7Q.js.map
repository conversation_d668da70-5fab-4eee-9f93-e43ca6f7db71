{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseSlice.js"], "sourcesContent": ["/**\n * The base implementation of `_.slice` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseSlice(array, start, end) {\n  var index = -1,\n      length = array.length;\n\n  if (start < 0) {\n    start = -start > length ? 0 : (length + start);\n  }\n  end = end > length ? length : end;\n  if (end < 0) {\n    end += length;\n  }\n  length = start > end ? 0 : ((end - start) >>> 0);\n  start >>>= 0;\n\n  var result = Array(length);\n  while (++index < length) {\n    result[index] = array[index + start];\n  }\n  return result;\n}\n\nmodule.exports = baseSlice;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,UAAU,OAAO,OAAO,KAAK;AACpC,UAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,UAAI,QAAQ,GAAG;AACb,gBAAQ,CAAC,QAAQ,SAAS,IAAK,SAAS;AAAA,MAC1C;AACA,YAAM,MAAM,SAAS,SAAS;AAC9B,UAAI,MAAM,GAAG;AACX,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,MAAM,IAAM,MAAM,UAAW;AAC9C,iBAAW;AAEX,UAAI,SAAS,MAAM,MAAM;AACzB,aAAO,EAAE,QAAQ,QAAQ;AACvB,eAAO,KAAK,IAAI,MAAM,QAAQ,KAAK;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}