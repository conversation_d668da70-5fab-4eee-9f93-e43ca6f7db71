import {
  require_baseDifference
} from "./chunk-WWIZCUMZ.js";
import {
  require_isArrayLikeObject
} from "./chunk-S2PLPWHA.js";
import {
  require_baseRest
} from "./chunk-VZDPLNRX.js";
import "./chunk-POPKRFLD.js";
import {
  require_baseFlatten
} from "./chunk-OLJOAOLE.js";
import "./chunk-JLXDD2GJ.js";
import "./chunk-SL5LTBZ2.js";
import "./chunk-VQMXUB7P.js";
import "./chunk-QAED4OXJ.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-4GUL7IQK.js";
import "./chunk-2A43EO37.js";
import "./chunk-MUOIXU4T.js";
import "./chunk-UQ4Y4P6I.js";
import "./chunk-4HM7XD2G.js";
import "./chunk-U2VWCOJX.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-3MWHQ5U6.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/difference.js
var require_difference = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/difference.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseFlatten = require_baseFlatten();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var difference = baseRest(function(array, values) {
      return isArrayLikeObject(array) ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true)) : [];
    });
    module.exports = difference;
  }
});
export default require_difference();
//# sourceMappingURL=lodash_difference.js.map
