import {
  require_last
} from "./chunk-IPT6NIPO.js";
import {
  require_isPlainObject
} from "./chunk-O7BYROIV.js";
import {
  require_baseSlice
} from "./chunk-EE6E7F7Q.js";
import {
  require_baseClone,
  require_getAllKeysIn
} from "./chunk-RKXWVQJA.js";
import "./chunk-OWXVDWHL.js";
import {
  require_copyObject
} from "./chunk-KVXYJQ4O.js";
import "./chunk-7PXD4JGH.js";
import "./chunk-EMY2ZYSH.js";
import "./chunk-6RHF6BK4.js";
import {
  require_flatRest
} from "./chunk-7ZB2QQAZ.js";
import "./chunk-3LRRGZUN.js";
import "./chunk-OLJOAOLE.js";
import "./chunk-JLXDD2GJ.js";
import "./chunk-Q72BQYBD.js";
import "./chunk-5EO5MQJI.js";
import "./chunk-SL5LTBZ2.js";
import "./chunk-NEILXCVD.js";
import "./chunk-ZOPFXEDK.js";
import "./chunk-TAUPVCQB.js";
import "./chunk-JKJ3ONXJ.js";
import "./chunk-AJQMZXLQ.js";
import "./chunk-6GT6XG4G.js";
import "./chunk-BTFDFZI2.js";
import "./chunk-4MTN4377.js";
import "./chunk-TOPLOUEN.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-V46UVRHS.js";
import "./chunk-NBE5XRJS.js";
import "./chunk-4GUL7IQK.js";
import {
  require_baseGet,
  require_castPath,
  require_toKey
} from "./chunk-WO7Y3QNL.js";
import "./chunk-TWO7A3AO.js";
import {
  require_arrayMap
} from "./chunk-2A43EO37.js";
import "./chunk-MUOIXU4T.js";
import "./chunk-RIFCQF3N.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-UQ4Y4P6I.js";
import "./chunk-4HM7XD2G.js";
import "./chunk-U2VWCOJX.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-CXIFQW4L.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-3MWHQ5U6.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_parent.js
var require_parent = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_parent.js"(exports, module) {
    var baseGet = require_baseGet();
    var baseSlice = require_baseSlice();
    function parent(object, path) {
      return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));
    }
    module.exports = parent;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseUnset.js
var require_baseUnset = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseUnset.js"(exports, module) {
    var castPath = require_castPath();
    var last = require_last();
    var parent = require_parent();
    var toKey = require_toKey();
    function baseUnset(object, path) {
      path = castPath(path, object);
      object = parent(object, path);
      return object == null || delete object[toKey(last(path))];
    }
    module.exports = baseUnset;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_customOmitClone.js
var require_customOmitClone = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_customOmitClone.js"(exports, module) {
    var isPlainObject = require_isPlainObject();
    function customOmitClone(value) {
      return isPlainObject(value) ? void 0 : value;
    }
    module.exports = customOmitClone;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/omit.js
var require_omit = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/omit.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseClone = require_baseClone();
    var baseUnset = require_baseUnset();
    var castPath = require_castPath();
    var copyObject = require_copyObject();
    var customOmitClone = require_customOmitClone();
    var flatRest = require_flatRest();
    var getAllKeysIn = require_getAllKeysIn();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_FLAT_FLAG = 2;
    var CLONE_SYMBOLS_FLAG = 4;
    var omit = flatRest(function(object, paths) {
      var result = {};
      if (object == null) {
        return result;
      }
      var isDeep = false;
      paths = arrayMap(paths, function(path) {
        path = castPath(path, object);
        isDeep || (isDeep = path.length > 1);
        return path;
      });
      copyObject(object, getAllKeysIn(object), result);
      if (isDeep) {
        result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);
      }
      var length = paths.length;
      while (length--) {
        baseUnset(result, paths[length]);
      }
      return result;
    });
    module.exports = omit;
  }
});
export default require_omit();
//# sourceMappingURL=lodash_omit.js.map
