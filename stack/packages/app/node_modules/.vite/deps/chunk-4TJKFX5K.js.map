{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/findIndex.js"], "sourcesContent": ["var baseFindIndex = require('./_baseFindIndex'),\n    baseIteratee = require('./_baseIteratee'),\n    toInteger = require('./toInteger');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.find` except that it returns the index of the first\n * element `predicate` returns truthy for instead of the element itself.\n *\n * @static\n * @memberOf _\n * @since 1.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': false },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': true }\n * ];\n *\n * _.findIndex(users, function(o) { return o.user == 'barney'; });\n * // => 0\n *\n * // The `_.matches` iteratee shorthand.\n * _.findIndex(users, { 'user': 'fred', 'active': false });\n * // => 1\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findIndex(users, ['active', false]);\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.findIndex(users, 'active');\n * // => 2\n */\nfunction findIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = fromIndex == null ? 0 : toInteger(fromIndex);\n  if (index < 0) {\n    index = nativeMax(length + index, 0);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index);\n}\n\nmodule.exports = findIndex;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,eAAe;AADnB,QAEI,YAAY;AAGhB,QAAI,YAAY,KAAK;AAqCrB,aAAS,UAAU,OAAO,WAAW,WAAW;AAC9C,UAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,aAAa,OAAO,IAAI,UAAU,SAAS;AACvD,UAAI,QAAQ,GAAG;AACb,gBAAQ,UAAU,SAAS,OAAO,CAAC;AAAA,MACrC;AACA,aAAO,cAAc,OAAO,aAAa,WAAW,CAAC,GAAG,KAAK;AAAA,IAC/D;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}