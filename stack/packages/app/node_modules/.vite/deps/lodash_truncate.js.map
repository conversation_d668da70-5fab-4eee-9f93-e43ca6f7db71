{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsRegExp.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isRegExp.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/truncate.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar regexpTag = '[object RegExp]';\n\n/**\n * The base implementation of `_.isRegExp` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a regexp, else `false`.\n */\nfunction baseIsRegExp(value) {\n  return isObjectLike(value) && baseGetTag(value) == regexpTag;\n}\n\nmodule.exports = baseIsRegExp;\n", "var baseIsRegExp = require('./_baseIsRegExp'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsRegExp = nodeUtil && nodeUtil.isRegExp;\n\n/**\n * Checks if `value` is classified as a `RegExp` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a regexp, else `false`.\n * @example\n *\n * _.isRegExp(/abc/);\n * // => true\n *\n * _.isRegExp('/abc/');\n * // => false\n */\nvar isRegExp = nodeIsRegExp ? baseUnary(nodeIsRegExp) : baseIsRegExp;\n\nmodule.exports = isRegExp;\n", "var baseToString = require('./_baseToString'),\n    castSlice = require('./_castSlice'),\n    hasUnicode = require('./_hasUnicode'),\n    isObject = require('./isObject'),\n    isRegExp = require('./isRegExp'),\n    stringSize = require('./_stringSize'),\n    stringToArray = require('./_stringToArray'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/** Used as default options for `_.truncate`. */\nvar DEFAULT_TRUNC_LENGTH = 30,\n    DEFAULT_TRUNC_OMISSION = '...';\n\n/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Truncates `string` if it's longer than the given maximum string length.\n * The last characters of the truncated string are replaced with the omission\n * string which defaults to \"...\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to truncate.\n * @param {Object} [options={}] The options object.\n * @param {number} [options.length=30] The maximum string length.\n * @param {string} [options.omission='...'] The string to indicate text is omitted.\n * @param {RegExp|string} [options.separator] The separator pattern to truncate to.\n * @returns {string} Returns the truncated string.\n * @example\n *\n * _.truncate('hi-diddly-ho there, neighborino');\n * // => 'hi-diddly-ho there, neighbo...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'length': 24,\n *   'separator': ' '\n * });\n * // => 'hi-diddly-ho there,...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'length': 24,\n *   'separator': /,? +/\n * });\n * // => 'hi-diddly-ho there...'\n *\n * _.truncate('hi-diddly-ho there, neighborino', {\n *   'omission': ' [...]'\n * });\n * // => 'hi-diddly-ho there, neig [...]'\n */\nfunction truncate(string, options) {\n  var length = DEFAULT_TRUNC_LENGTH,\n      omission = DEFAULT_TRUNC_OMISSION;\n\n  if (isObject(options)) {\n    var separator = 'separator' in options ? options.separator : separator;\n    length = 'length' in options ? toInteger(options.length) : length;\n    omission = 'omission' in options ? baseToString(options.omission) : omission;\n  }\n  string = toString(string);\n\n  var strLength = string.length;\n  if (hasUnicode(string)) {\n    var strSymbols = stringToArray(string);\n    strLength = strSymbols.length;\n  }\n  if (length >= strLength) {\n    return string;\n  }\n  var end = length - stringSize(omission);\n  if (end < 1) {\n    return omission;\n  }\n  var result = strSymbols\n    ? castSlice(strSymbols, 0, end).join('')\n    : string.slice(0, end);\n\n  if (separator === undefined) {\n    return result + omission;\n  }\n  if (strSymbols) {\n    end += (result.length - end);\n  }\n  if (isRegExp(separator)) {\n    if (string.slice(end).search(separator)) {\n      var match,\n          substring = result;\n\n      if (!separator.global) {\n        separator = RegExp(separator.source, toString(reFlags.exec(separator)) + 'g');\n      }\n      separator.lastIndex = 0;\n      while ((match = separator.exec(substring))) {\n        var newEnd = match.index;\n      }\n      result = result.slice(0, newEnd === undefined ? end : newEnd);\n    }\n  } else if (string.indexOf(baseToString(separator), end) != end) {\n    var index = result.lastIndexOf(separator);\n    if (index > -1) {\n      result = result.slice(0, index);\n    }\n  }\n  return result + omission;\n}\n\nmodule.exports = truncate;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AAGnB,QAAI,YAAY;AAShB,aAAS,aAAa,OAAO;AAC3B,aAAO,aAAa,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,IACrD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,YAAY;AADhB,QAEI,WAAW;AAGf,QAAI,eAAe,YAAY,SAAS;AAmBxC,QAAI,WAAW,eAAe,UAAU,YAAY,IAAI;AAExD,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,YAAY;AADhB,QAEI,aAAa;AAFjB,QAGI,WAAW;AAHf,QAII,WAAW;AAJf,QAKI,aAAa;AALjB,QAMI,gBAAgB;AANpB,QAOI,YAAY;AAPhB,QAQI,WAAW;AAGf,QAAI,uBAAuB;AAA3B,QACI,yBAAyB;AAG7B,QAAI,UAAU;AAuCd,aAAS,SAAS,QAAQ,SAAS;AACjC,UAAI,SAAS,sBACT,WAAW;AAEf,UAAI,SAAS,OAAO,GAAG;AACrB,YAAI,YAAY,eAAe,UAAU,QAAQ,YAAY;AAC7D,iBAAS,YAAY,UAAU,UAAU,QAAQ,MAAM,IAAI;AAC3D,mBAAW,cAAc,UAAU,aAAa,QAAQ,QAAQ,IAAI;AAAA,MACtE;AACA,eAAS,SAAS,MAAM;AAExB,UAAI,YAAY,OAAO;AACvB,UAAI,WAAW,MAAM,GAAG;AACtB,YAAI,aAAa,cAAc,MAAM;AACrC,oBAAY,WAAW;AAAA,MACzB;AACA,UAAI,UAAU,WAAW;AACvB,eAAO;AAAA,MACT;AACA,UAAI,MAAM,SAAS,WAAW,QAAQ;AACtC,UAAI,MAAM,GAAG;AACX,eAAO;AAAA,MACT;AACA,UAAI,SAAS,aACT,UAAU,YAAY,GAAG,GAAG,EAAE,KAAK,EAAE,IACrC,OAAO,MAAM,GAAG,GAAG;AAEvB,UAAI,cAAc,QAAW;AAC3B,eAAO,SAAS;AAAA,MAClB;AACA,UAAI,YAAY;AACd,eAAQ,OAAO,SAAS;AAAA,MAC1B;AACA,UAAI,SAAS,SAAS,GAAG;AACvB,YAAI,OAAO,MAAM,GAAG,EAAE,OAAO,SAAS,GAAG;AACvC,cAAI,OACA,YAAY;AAEhB,cAAI,CAAC,UAAU,QAAQ;AACrB,wBAAY,OAAO,UAAU,QAAQ,SAAS,QAAQ,KAAK,SAAS,CAAC,IAAI,GAAG;AAAA,UAC9E;AACA,oBAAU,YAAY;AACtB,iBAAQ,QAAQ,UAAU,KAAK,SAAS,GAAI;AAC1C,gBAAI,SAAS,MAAM;AAAA,UACrB;AACA,mBAAS,OAAO,MAAM,GAAG,WAAW,SAAY,MAAM,MAAM;AAAA,QAC9D;AAAA,MACF,WAAW,OAAO,QAAQ,aAAa,SAAS,GAAG,GAAG,KAAK,KAAK;AAC9D,YAAI,QAAQ,OAAO,YAAY,SAAS;AACxC,YAAI,QAAQ,IAAI;AACd,mBAAS,OAAO,MAAM,GAAG,KAAK;AAAA,QAChC;AAAA,MACF;AACA,aAAO,SAAS;AAAA,IAClB;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}