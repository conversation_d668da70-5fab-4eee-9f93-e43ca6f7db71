import {
  require_isLength
} from "./chunk-JQ6QUUYU.js";
import {
  require_isFunction
} from "./chunk-PFJGCGTW.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isArrayLike.js
var require_isArrayLike = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isArrayLike.js"(exports, module) {
    var isFunction = require_isFunction();
    var isLength = require_isLength();
    function isArrayLike(value) {
      return value != null && isLength(value.length) && !isFunction(value);
    }
    module.exports = isArrayLike;
  }
});

export {
  require_isArrayLike
};
//# sourceMappingURL=chunk-4GUL7IQK.js.map
