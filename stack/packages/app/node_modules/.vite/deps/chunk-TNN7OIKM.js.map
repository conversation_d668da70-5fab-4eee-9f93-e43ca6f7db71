{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Map.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\n\nmodule.exports = Map;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,OAAO;AAGX,QAAI,MAAM,UAAU,MAAM,KAAK;AAE/B,WAAO,UAAU;AAAA;AAAA;", "names": []}