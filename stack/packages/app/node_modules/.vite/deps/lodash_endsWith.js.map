{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/endsWith.js"], "sourcesContent": ["var baseClamp = require('./_baseClamp'),\n    baseToString = require('./_baseToString'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/**\n * Checks if `string` ends with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=string.length] The position to search up to.\n * @returns {boolean} Returns `true` if `string` ends with `target`,\n *  else `false`.\n * @example\n *\n * _.endsWith('abc', 'c');\n * // => true\n *\n * _.endsWith('abc', 'b');\n * // => false\n *\n * _.endsWith('abc', 'b', 2);\n * // => true\n */\nfunction endsWith(string, target, position) {\n  string = toString(string);\n  target = baseToString(target);\n\n  var length = string.length;\n  position = position === undefined\n    ? length\n    : baseClamp(toInteger(position), 0, length);\n\n  var end = position;\n  position -= target.length;\n  return position >= 0 && string.slice(position, end) == target;\n}\n\nmodule.exports = endsWith;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,eAAe;AADnB,QAEI,YAAY;AAFhB,QAGI,WAAW;AAyBf,aAAS,SAAS,QAAQ,QAAQ,UAAU;AAC1C,eAAS,SAAS,MAAM;AACxB,eAAS,aAAa,MAAM;AAE5B,UAAI,SAAS,OAAO;AACpB,iBAAW,aAAa,SACpB,SACA,UAAU,UAAU,QAAQ,GAAG,GAAG,MAAM;AAE5C,UAAI,MAAM;AACV,kBAAY,OAAO;AACnB,aAAO,YAAY,KAAK,OAAO,MAAM,UAAU,GAAG,KAAK;AAAA,IACzD;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}