{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseCreate.js"], "sourcesContent": ["var isObject = require('./isObject');\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\nmodule.exports = baseCreate;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAGf,QAAI,eAAe,OAAO;AAU1B,QAAI,aAAc,2BAAW;AAC3B,eAAS,SAAS;AAAA,MAAC;AACnB,aAAO,SAAS,OAAO;AACrB,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,cAAc;AAChB,iBAAO,aAAa,KAAK;AAAA,QAC3B;AACA,eAAO,YAAY;AACnB,YAAI,SAAS,IAAI;AACjB,eAAO,YAAY;AACnB,eAAO;AAAA,MACT;AAAA,IACF,EAAE;AAEF,WAAO,UAAU;AAAA;AAAA;", "names": []}