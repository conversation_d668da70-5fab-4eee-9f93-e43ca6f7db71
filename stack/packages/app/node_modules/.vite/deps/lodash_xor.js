import {
  require_baseDifference
} from "./chunk-WWIZCUMZ.js";
import {
  require_isArrayLikeObject
} from "./chunk-S2PLPWHA.js";
import {
  require_baseRest
} from "./chunk-VZDPLNRX.js";
import {
  require_baseUniq
} from "./chunk-VL5NBHGO.js";
import "./chunk-POPKRFLD.js";
import {
  require_baseFlatten
} from "./chunk-OLJOAOLE.js";
import "./chunk-JLXDD2GJ.js";
import "./chunk-SL5LTBZ2.js";
import "./chunk-VQMXUB7P.js";
import "./chunk-PXDQSTWR.js";
import "./chunk-QAED4OXJ.js";
import {
  require_arrayFilter
} from "./chunk-ZOPFXEDK.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-V46UVRHS.js";
import "./chunk-4GUL7IQK.js";
import "./chunk-2A43EO37.js";
import "./chunk-MUOIXU4T.js";
import "./chunk-UQ4Y4P6I.js";
import "./chunk-4HM7XD2G.js";
import "./chunk-U2VWCOJX.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-3MWHQ5U6.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseXor.js
var require_baseXor = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseXor.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseFlatten = require_baseFlatten();
    var baseUniq = require_baseUniq();
    function baseXor(arrays, iteratee, comparator) {
      var length = arrays.length;
      if (length < 2) {
        return length ? baseUniq(arrays[0]) : [];
      }
      var index = -1, result = Array(length);
      while (++index < length) {
        var array = arrays[index], othIndex = -1;
        while (++othIndex < length) {
          if (othIndex != index) {
            result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);
          }
        }
      }
      return baseUniq(baseFlatten(result, 1), iteratee, comparator);
    }
    module.exports = baseXor;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/xor.js
var require_xor = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/xor.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var baseRest = require_baseRest();
    var baseXor = require_baseXor();
    var isArrayLikeObject = require_isArrayLikeObject();
    var xor = baseRest(function(arrays) {
      return baseXor(arrayFilter(arrays, isArrayLikeObject));
    });
    module.exports = xor;
  }
});
export default require_xor();
//# sourceMappingURL=lodash_xor.js.map
