{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createFind.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/find.js"], "sourcesContent": ["var baseIteratee = require('./_baseIteratee'),\n    isArrayLike = require('./isArrayLike'),\n    keys = require('./keys');\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function(collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function(key) { return iteratee(iterable[key], key, iterable); };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\n\nmodule.exports = createFind;\n", "var createFind = require('./_createFind'),\n    findIndex = require('./findIndex');\n\n/**\n * Iterates over elements of `collection`, returning the first element\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {*} Returns the matched element, else `undefined`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36, 'active': true },\n *   { 'user': 'fred',    'age': 40, 'active': false },\n *   { 'user': 'pebbles', 'age': 1,  'active': true }\n * ];\n *\n * _.find(users, function(o) { return o.age < 40; });\n * // => object for 'barney'\n *\n * // The `_.matches` iteratee shorthand.\n * _.find(users, { 'age': 1, 'active': true });\n * // => object for 'pebbles'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.find(users, ['active', false]);\n * // => object for 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.find(users, 'active');\n * // => object for 'barney'\n */\nvar find = createFind(findIndex);\n\nmodule.exports = find;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,cAAc;AADlB,QAEI,OAAO;AASX,aAAS,WAAW,eAAe;AACjC,aAAO,SAAS,YAAY,WAAW,WAAW;AAChD,YAAI,WAAW,OAAO,UAAU;AAChC,YAAI,CAAC,YAAY,UAAU,GAAG;AAC5B,cAAI,WAAW,aAAa,WAAW,CAAC;AACxC,uBAAa,KAAK,UAAU;AAC5B,sBAAY,SAAS,KAAK;AAAE,mBAAO,SAAS,SAAS,GAAG,GAAG,KAAK,QAAQ;AAAA,UAAG;AAAA,QAC7E;AACA,YAAI,QAAQ,cAAc,YAAY,WAAW,SAAS;AAC1D,eAAO,QAAQ,KAAK,SAAS,WAAW,WAAW,KAAK,IAAI,KAAK,IAAI;AAAA,MACvE;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,YAAY;AAsChB,QAAI,OAAO,WAAW,SAAS;AAE/B,WAAO,UAAU;AAAA;AAAA;", "names": []}