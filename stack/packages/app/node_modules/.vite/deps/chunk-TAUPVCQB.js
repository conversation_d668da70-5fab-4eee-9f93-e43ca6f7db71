import {
  require_baseKeys
} from "./chunk-AJQMZXLQ.js";
import {
  require_arrayLikeKeys
} from "./chunk-BTFDFZI2.js";
import {
  require_isArrayLike
} from "./chunk-4GUL7IQK.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/keys.js
var require_keys = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/keys.js"(exports, module) {
    var arrayLikeKeys = require_arrayLikeKeys();
    var baseKeys = require_baseKeys();
    var isArrayLike = require_isArrayLike();
    function keys(object) {
      return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);
    }
    module.exports = keys;
  }
});

export {
  require_keys
};
//# sourceMappingURL=chunk-TAUPVCQB.js.map
