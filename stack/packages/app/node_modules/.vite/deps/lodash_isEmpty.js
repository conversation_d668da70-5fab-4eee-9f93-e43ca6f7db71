import {
  require_getTag
} from "./chunk-JKJ3ONXJ.js";
import {
  require_baseKeys
} from "./chunk-AJQMZXLQ.js";
import {
  require_isBuffer,
  require_isPrototype,
  require_isTypedArray
} from "./chunk-4MTN4377.js";
import "./chunk-TOPLOUEN.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-V46UVRHS.js";
import "./chunk-NBE5XRJS.js";
import {
  require_isArrayLike
} from "./chunk-4GUL7IQK.js";
import {
  require_isArguments
} from "./chunk-4HM7XD2G.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import {
  require_isArray
} from "./chunk-XDZT3BAO.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-PFJGCGTW.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js
var require_isEmpty = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js"(exports, module) {
    var baseKeys = require_baseKeys();
    var getTag = require_getTag();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isArrayLike = require_isArrayLike();
    var isBuffer = require_isBuffer();
    var isPrototype = require_isPrototype();
    var isTypedArray = require_isTypedArray();
    var mapTag = "[object Map]";
    var setTag = "[object Set]";
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function isEmpty(value) {
      if (value == null) {
        return true;
      }
      if (isArrayLike(value) && (isArray(value) || typeof value == "string" || typeof value.splice == "function" || isBuffer(value) || isTypedArray(value) || isArguments(value))) {
        return !value.length;
      }
      var tag = getTag(value);
      if (tag == mapTag || tag == setTag) {
        return !value.size;
      }
      if (isPrototype(value)) {
        return !baseKeys(value).length;
      }
      for (var key in value) {
        if (hasOwnProperty.call(value, key)) {
          return false;
        }
      }
      return true;
    }
    module.exports = isEmpty;
  }
});
export default require_isEmpty();
//# sourceMappingURL=lodash_isEmpty.js.map
