import {
  require_stringSize
} from "./chunk-GTQIYH5I.js";
import {
  require_castSlice,
  require_hasUnicode,
  require_stringToArray
} from "./chunk-5XJARE5H.js";
import "./chunk-EE6E7F7Q.js";
import "./chunk-3MCR5VVW.js";
import {
  require_toInteger
} from "./chunk-3M5FHVBU.js";
import "./chunk-7V26ZGEI.js";
import {
  require_baseToString,
  require_toString
} from "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseRepeat.js
var require_baseRepeat = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseRepeat.js"(exports, module) {
    var MAX_SAFE_INTEGER = 9007199254740991;
    var nativeFloor = Math.floor;
    function baseRepeat(string, n) {
      var result = "";
      if (!string || n < 1 || n > MAX_SAFE_INTEGER) {
        return result;
      }
      do {
        if (n % 2) {
          result += string;
        }
        n = nativeFloor(n / 2);
        if (n) {
          string += string;
        }
      } while (n);
      return result;
    }
    module.exports = baseRepeat;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createPadding.js
var require_createPadding = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createPadding.js"(exports, module) {
    var baseRepeat = require_baseRepeat();
    var baseToString = require_baseToString();
    var castSlice = require_castSlice();
    var hasUnicode = require_hasUnicode();
    var stringSize = require_stringSize();
    var stringToArray = require_stringToArray();
    var nativeCeil = Math.ceil;
    function createPadding(length, chars) {
      chars = chars === void 0 ? " " : baseToString(chars);
      var charsLength = chars.length;
      if (charsLength < 2) {
        return charsLength ? baseRepeat(chars, length) : chars;
      }
      var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));
      return hasUnicode(chars) ? castSlice(stringToArray(result), 0, length).join("") : result.slice(0, length);
    }
    module.exports = createPadding;
  }
});

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/padStart.js
var require_padStart = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/padStart.js"(exports, module) {
    var createPadding = require_createPadding();
    var stringSize = require_stringSize();
    var toInteger = require_toInteger();
    var toString = require_toString();
    function padStart(string, length, chars) {
      string = toString(string);
      length = toInteger(length);
      var strLength = length ? stringSize(string) : 0;
      return length && strLength < length ? createPadding(length - strLength, chars) + string : string;
    }
    module.exports = padStart;
  }
});
export default require_padStart();
//# sourceMappingURL=lodash_padStart.js.map
