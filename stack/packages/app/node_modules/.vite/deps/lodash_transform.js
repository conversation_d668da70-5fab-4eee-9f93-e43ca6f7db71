import {
  require_baseForOwn
} from "./chunk-OC6REVJJ.js";
import "./chunk-DJAUCU6Q.js";
import {
  require_arrayEach
} from "./chunk-7PXD4JGH.js";
import {
  require_baseCreate
} from "./chunk-EMY2ZYSH.js";
import {
  require_getPrototype
} from "./chunk-6RHF6BK4.js";
import {
  require_baseIteratee
} from "./chunk-JYHWN6MQ.js";
import "./chunk-3MCR5VVW.js";
import "./chunk-YAT66NN5.js";
import "./chunk-UJAYMAAP.js";
import "./chunk-HJC7JR4G.js";
import "./chunk-PXDQSTWR.js";
import "./chunk-QAED4OXJ.js";
import "./chunk-NEILXCVD.js";
import "./chunk-ZOPFXEDK.js";
import "./chunk-TAUPVCQB.js";
import "./chunk-JKJ3ONXJ.js";
import "./chunk-AJQMZXLQ.js";
import "./chunk-6GT6XG4G.js";
import "./chunk-BTFDFZI2.js";
import {
  require_isBuffer,
  require_isTypedArray
} from "./chunk-4MTN4377.js";
import "./chunk-TOPLOUEN.js";
import "./chunk-GSW7XBCA.js";
import "./chunk-V46UVRHS.js";
import "./chunk-NBE5XRJS.js";
import "./chunk-4GUL7IQK.js";
import "./chunk-S7P2O4EU.js";
import "./chunk-WO7Y3QNL.js";
import "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-MUOIXU4T.js";
import "./chunk-RIFCQF3N.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-UQ4Y4P6I.js";
import "./chunk-4HM7XD2G.js";
import "./chunk-U2VWCOJX.js";
import "./chunk-TNN7OIKM.js";
import "./chunk-3CCHXQR4.js";
import {
  require_isArray
} from "./chunk-XDZT3BAO.js";
import "./chunk-CXIFQW4L.js";
import "./chunk-JQ6QUUYU.js";
import "./chunk-3MWHQ5U6.js";
import {
  require_isFunction
} from "./chunk-PFJGCGTW.js";
import {
  require_isObject
} from "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/transform.js
var require_transform = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/transform.js"(exports, module) {
    var arrayEach = require_arrayEach();
    var baseCreate = require_baseCreate();
    var baseForOwn = require_baseForOwn();
    var baseIteratee = require_baseIteratee();
    var getPrototype = require_getPrototype();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isFunction = require_isFunction();
    var isObject = require_isObject();
    var isTypedArray = require_isTypedArray();
    function transform(object, iteratee, accumulator) {
      var isArr = isArray(object), isArrLike = isArr || isBuffer(object) || isTypedArray(object);
      iteratee = baseIteratee(iteratee, 4);
      if (accumulator == null) {
        var Ctor = object && object.constructor;
        if (isArrLike) {
          accumulator = isArr ? new Ctor() : [];
        } else if (isObject(object)) {
          accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};
        } else {
          accumulator = {};
        }
      }
      (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object2) {
        return iteratee(accumulator, value, index, object2);
      });
      return accumulator;
    }
    module.exports = transform;
  }
});
export default require_transform();
//# sourceMappingURL=lodash_transform.js.map
