{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseDifference.js"], "sourcesContent": ["var SetCache = require('./_SetCache'),\n    arrayIncludes = require('./_arrayIncludes'),\n    arrayIncludesWith = require('./_arrayIncludesWith'),\n    arrayMap = require('./_arrayMap'),\n    baseUnary = require('./_baseUnary'),\n    cacheHas = require('./_cacheHas');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of methods like `_.difference` without support\n * for excluding multiple arrays or iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Array} values The values to exclude.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n */\nfunction baseDifference(array, values, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      isCommon = true,\n      length = array.length,\n      result = [],\n      valuesLength = values.length;\n\n  if (!length) {\n    return result;\n  }\n  if (iteratee) {\n    values = arrayMap(values, baseUnary(iteratee));\n  }\n  if (comparator) {\n    includes = arrayIncludesWith;\n    isCommon = false;\n  }\n  else if (values.length >= LARGE_ARRAY_SIZE) {\n    includes = cacheHas;\n    isCommon = false;\n    values = new SetCache(values);\n  }\n  outer:\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee == null ? value : iteratee(value);\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (isCommon && computed === computed) {\n      var valuesIndex = valuesLength;\n      while (valuesIndex--) {\n        if (values[valuesIndex] === computed) {\n          continue outer;\n        }\n      }\n      result.push(value);\n    }\n    else if (!includes(values, computed, comparator)) {\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseDifference;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,gBAAgB;AADpB,QAEI,oBAAoB;AAFxB,QAGI,WAAW;AAHf,QAII,YAAY;AAJhB,QAKI,WAAW;AAGf,QAAI,mBAAmB;AAavB,aAAS,eAAe,OAAO,QAAQ,UAAU,YAAY;AAC3D,UAAI,QAAQ,IACR,WAAW,eACX,WAAW,MACX,SAAS,MAAM,QACf,SAAS,CAAC,GACV,eAAe,OAAO;AAE1B,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,UAAI,UAAU;AACZ,iBAAS,SAAS,QAAQ,UAAU,QAAQ,CAAC;AAAA,MAC/C;AACA,UAAI,YAAY;AACd,mBAAW;AACX,mBAAW;AAAA,MACb,WACS,OAAO,UAAU,kBAAkB;AAC1C,mBAAW;AACX,mBAAW;AACX,iBAAS,IAAI,SAAS,MAAM;AAAA,MAC9B;AACA;AACA,eAAO,EAAE,QAAQ,QAAQ;AACvB,cAAI,QAAQ,MAAM,KAAK,GACnB,WAAW,YAAY,OAAO,QAAQ,SAAS,KAAK;AAExD,kBAAS,cAAc,UAAU,IAAK,QAAQ;AAC9C,cAAI,YAAY,aAAa,UAAU;AACrC,gBAAI,cAAc;AAClB,mBAAO,eAAe;AACpB,kBAAI,OAAO,WAAW,MAAM,UAAU;AACpC,yBAAS;AAAA,cACX;AAAA,YACF;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB,WACS,CAAC,SAAS,QAAQ,UAAU,UAAU,GAAG;AAChD,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}