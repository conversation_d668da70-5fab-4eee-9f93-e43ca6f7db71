import {
  require_overRest,
  require_setToString
} from "./chunk-JLXDD2GJ.js";
import {
  require_identity
} from "./chunk-MUOIXU4T.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseRest.js
var require_baseRest = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseRest.js"(exports, module) {
    var identity = require_identity();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function baseRest(func, start) {
      return setToString(overRest(func, start, identity), func + "");
    }
    module.exports = baseRest;
  }
});

export {
  require_baseRest
};
//# sourceMappingURL=chunk-VZDPLNRX.js.map
