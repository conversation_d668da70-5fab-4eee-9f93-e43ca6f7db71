{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "../../../../../node_modules/.pnpm/state-local@1.0.7/node_modules/state-local/lib/es/state-local.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/config/index.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/curry.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/validators/index.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/compose.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js", "../../../../../node_modules/.pnpm/@monaco-editor+loader@1.5.0/node_modules/@monaco-editor/loader/lib/es/loader/index.js", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/DiffEditor/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/DiffEditor/DiffEditor.tsx", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/MonacoContainer/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/MonacoContainer/MonacoContainer.tsx", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/MonacoContainer/styles.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/Loading/Loading.tsx", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/Loading/styles.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/Loading/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/hooks/useMount/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/hooks/useUpdate/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/utils/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/hooks/useMonaco/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/Editor/index.ts", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/Editor/Editor.tsx", "../../../../../node_modules/.pnpm/@monaco-editor+react@4.7.0_monaco-editor@0.45.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@monaco-editor/react/src/hooks/usePrevious/index.ts"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n", "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n", "function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n", "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n", "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n", "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n", "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n", "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n", "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n", "import loader from '@monaco-editor/loader';\nexport { loader };\n\nimport DiffEditor from './DiffEditor';\nexport * from './DiffEditor/types';\nexport { DiffEditor };\n\nimport useMonaco from './hooks/useMonaco';\nexport { useMonaco };\n\nimport Editor from './Editor';\nexport * from './Editor/types';\nexport { Editor };\nexport default Editor;\n\n// Monaco\nimport type * as monaco from 'monaco-editor/esm/vs/editor/editor.api';\nexport type Monaco = typeof monaco;\n\n// Default themes\nexport type Theme = 'vs-dark' | 'light';\n", "import { memo } from 'react';\n\nimport DiffEditor from './DiffEditor';\n\nexport * from './types';\n\nexport default memo(DiffEditor);\n", "'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport MonacoContainer from '../MonacoContainer';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type DiffEditorProps, type MonacoDiffEditor } from './types';\nimport { type Monaco } from '..';\n\nfunction DiffEditor({\n  original,\n  modified,\n  language,\n  originalLanguage,\n  modifiedLanguage,\n  originalModelPath,\n  modifiedModelPath,\n  keepCurrentOriginalModel = false,\n  keepCurrentModifiedModel = false,\n  theme = 'light',\n  loading = 'Loading...',\n  options = {},\n  height = '100%',\n  width = '100%',\n  className,\n  wrapperProps = {},\n  beforeMount = noop,\n  onMount = noop,\n}: DiffEditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const editorRef = useRef<MonacoDiffEditor | null>(null);\n  const monacoRef = useRef<Monaco | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const preventCreation = useRef(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const originalEditor = editorRef.current.getOriginalEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          original || '',\n          originalLanguage || language || 'text',\n          originalModelPath || '',\n        );\n\n        if (model !== originalEditor.getModel()) {\n          originalEditor.setModel(model);\n        }\n      }\n    },\n    [originalModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const modifiedEditor = editorRef.current.getModifiedEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          modified || '',\n          modifiedLanguage || language || 'text',\n          modifiedModelPath || '',\n        );\n\n        if (model !== modifiedEditor.getModel()) {\n          modifiedEditor.setModel(model);\n        }\n      }\n    },\n    [modifiedModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const modifiedEditor = editorRef.current!.getModifiedEditor();\n      if (modifiedEditor.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        modifiedEditor.setValue(modified || '');\n      } else {\n        if (modified !== modifiedEditor.getValue()) {\n          modifiedEditor.executeEdits('', [\n            {\n              range: modifiedEditor.getModel()!.getFullModelRange(),\n              text: modified || '',\n              forceMoveMarkers: true,\n            },\n          ]);\n\n          modifiedEditor.pushUndoStop();\n        }\n      }\n    },\n    [modified],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.getModel()?.original.setValue(original || '');\n    },\n    [original],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const { original, modified } = editorRef.current!.getModel()!;\n\n      monacoRef.current!.editor.setModelLanguage(original, originalLanguage || language || 'text');\n      monacoRef.current!.editor.setModelLanguage(modified, modifiedLanguage || language || 'text');\n    },\n    [language, originalLanguage, modifiedLanguage],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  const setModels = useCallback(() => {\n    if (!monacoRef.current) return;\n    beforeMountRef.current(monacoRef.current);\n    const originalModel = getOrCreateModel(\n      monacoRef.current,\n      original || '',\n      originalLanguage || language || 'text',\n      originalModelPath || '',\n    );\n\n    const modifiedModel = getOrCreateModel(\n      monacoRef.current,\n      modified || '',\n      modifiedLanguage || language || 'text',\n      modifiedModelPath || '',\n    );\n\n    editorRef.current?.setModel({\n      original: originalModel,\n      modified: modifiedModel,\n    });\n  }, [\n    language,\n    modified,\n    modifiedLanguage,\n    original,\n    originalLanguage,\n    originalModelPath,\n    modifiedModelPath,\n  ]);\n\n  const createEditor = useCallback(() => {\n    if (!preventCreation.current && containerRef.current) {\n      editorRef.current = monacoRef.current!.editor.createDiffEditor(containerRef.current, {\n        automaticLayout: true,\n        ...options,\n      });\n\n      setModels();\n\n      monacoRef.current?.editor.setTheme(theme);\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [options, theme, setModels]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  function disposeEditor() {\n    const models = editorRef.current?.getModel();\n\n    if (!keepCurrentOriginalModel) {\n      models?.original?.dispose();\n    }\n\n    if (!keepCurrentModifiedModel) {\n      models?.modified?.dispose();\n    }\n\n    editorRef.current?.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default DiffEditor;\n", "import { memo } from 'react';\n\nimport MonacoContainer from './MonacoContainer';\n\nexport default memo(MonacoContainer);\n", "import React from 'react';\n\nimport styles from './styles';\nimport Loading from '../Loading';\nimport { type ContainerProps } from './types';\n\n// ** forwardref render functions do not support proptypes or defaultprops **\n// one of the reasons why we use a separate prop for passing ref instead of using forwardref\n\nfunction MonacoContainer({\n  width,\n  height,\n  isEditorReady,\n  loading,\n  _ref,\n  className,\n  wrapperProps,\n}: ContainerProps) {\n  return (\n    <section style={{ ...styles.wrapper, width, height }} {...wrapperProps}>\n      {!isEditorReady && <Loading>{loading}</Loading>}\n      <div\n        ref={_ref}\n        style={{ ...styles.fullWidth, ...(!isEditorReady && styles.hide) }}\n        className={className}\n      />\n    </section>\n  );\n}\n\nexport default MonacoContainer;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  wrapper: {\n    display: 'flex',\n    position: 'relative',\n    textAlign: 'initial',\n  },\n  fullWidth: {\n    width: '100%',\n  },\n  hide: {\n    display: 'none',\n  },\n};\n\nexport default styles;\n", "import React, { type PropsWithChildren } from 'react';\n\nimport styles from './styles';\n\nfunction Loading({ children }: PropsWithChildren) {\n  return <div style={styles.container}>{children}</div>;\n}\n\nexport default Loading;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  container: {\n    display: 'flex',\n    height: '100%',\n    width: '100%',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n};\n\nexport default styles;\n", "import Loading from './Loading';\n\nexport default Loading;\n", "import { useEffect, type EffectCallback } from 'react';\n\nfunction useMount(effect: EffectCallback) {\n  useEffect(effect, []);\n}\n\nexport default useMount;\n", "import { useEffect, useRef, type DependencyList, type EffectCallback } from 'react';\n\nfunction useUpdate(effect: EffectCallback, deps: DependencyList, applyChanges = true) {\n  const isInitialMount = useRef(true);\n\n  useEffect(\n    isInitialMount.current || !applyChanges\n      ? () => {\n          isInitialMount.current = false;\n        }\n      : effect,\n    deps,\n  );\n}\n\nexport default useUpdate;\n", "import { type Monaco } from '..';\n\n/**\n * noop is a helper function that does nothing\n * @returns undefined\n */\nfunction noop() {\n  /** no-op */\n}\n\n/**\n * getOrCreateModel is a helper function that will return a model if it exists\n * or create a new model if it does not exist.\n * This is useful for when you want to create a model for a file that may or may not exist yet.\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was found or created\n */\nfunction getOrCreateModel(monaco: Monaco, value: string, language: string, path: string) {\n  return getModel(monaco, path) || createModel(monaco, value, language, path);\n}\n\n/**\n * getModel is a helper function that will return a model if it exists\n * or return undefined if it does not exist.\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model that was found or undefined\n */\nfunction getModel(monaco: Monaco, path: string) {\n  return monaco.editor.getModel(createModelUri(monaco, path));\n}\n\n/**\n * createModel is a helper function that will create a new model\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was created\n */\nfunction createModel(monaco: Monaco, value: string, language?: string, path?: string) {\n  return monaco.editor.createModel(\n    value,\n    language,\n    path ? createModelUri(monaco, path) : undefined,\n  );\n}\n\n/**\n * createModelUri is a helper function that will create a new model uri\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model uri that was created\n */\nfunction createModelUri(monaco: Monaco, path: string) {\n  return monaco.Uri.parse(path);\n}\n\nexport { noop, getOrCreateModel };\n", "import { useState } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport useMount from '../useMount';\n\nfunction useMonaco() {\n  const [monaco, setMonaco] = useState(loader.__getMonacoInstance());\n\n  useMount(() => {\n    let cancelable: ReturnType<typeof loader.init>;\n\n    if (!monaco) {\n      cancelable = loader.init();\n\n      cancelable.then((monaco) => {\n        setMonaco(monaco);\n      });\n    }\n\n    return () => cancelable?.cancel();\n  });\n\n  return monaco;\n}\n\nexport default useMonaco;\n", "import { memo } from 'react';\n\nimport Editor from './Editor';\n\nexport * from './types';\n\nexport default memo(Editor);\n", "'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport loader from '@monaco-editor/loader';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport usePrevious from '../hooks/usePrevious';\nimport { type IDisposable, type editor } from 'monaco-editor';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type EditorProps } from './types';\nimport { type Monaco } from '..';\nimport MonacoContainer from '../MonacoContainer';\n\nconst viewStates = new Map();\n\nfunction Editor({\n  defaultValue,\n  defaultLanguage,\n  defaultPath,\n  value,\n  language,\n  path,\n  /* === */\n  theme = 'light',\n  line,\n  loading = 'Loading...',\n  options = {},\n  overrideServices = {},\n  saveViewState = true,\n  keepCurrentModel = false,\n  /* === */\n  width = '100%',\n  height = '100%',\n  className,\n  wrapperProps = {},\n  /* === */\n  beforeMount = noop,\n  onMount = noop,\n  onChange,\n  onValidate = noop,\n}: EditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const monacoRef = useRef<Monaco | null>(null);\n  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const subscriptionRef = useRef<IDisposable>();\n  const valueRef = useRef(value);\n  const previousPath = usePrevious(path);\n  const preventCreation = useRef(false);\n  const preventTriggerChangeEvent = useRef<boolean>(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      const model = getOrCreateModel(\n        monacoRef.current!,\n        defaultValue || value || '',\n        defaultLanguage || language || '',\n        path || defaultPath || '',\n      );\n\n      if (model !== editorRef.current?.getModel()) {\n        if (saveViewState) viewStates.set(previousPath, editorRef.current?.saveViewState());\n        editorRef.current?.setModel(model);\n        if (saveViewState) editorRef.current?.restoreViewState(viewStates.get(path));\n      }\n    },\n    [path],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (!editorRef.current || value === undefined) return;\n      if (editorRef.current.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        editorRef.current.setValue(value);\n      } else if (value !== editorRef.current.getValue()) {\n        preventTriggerChangeEvent.current = true;\n        editorRef.current.executeEdits('', [\n          {\n            range: editorRef.current.getModel()!.getFullModelRange(),\n            text: value,\n            forceMoveMarkers: true,\n          },\n        ]);\n\n        editorRef.current.pushUndoStop();\n        preventTriggerChangeEvent.current = false;\n      }\n    },\n    [value],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const model = editorRef.current?.getModel();\n      if (model && language) monacoRef.current?.editor.setModelLanguage(model, language);\n    },\n    [language],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      // reason for undefined check: https://github.com/suren-atoyan/monaco-react/pull/188\n      if (line !== undefined) {\n        editorRef.current?.revealLine(line);\n      }\n    },\n    [line],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  const createEditor = useCallback(() => {\n    if (!containerRef.current || !monacoRef.current) return;\n    if (!preventCreation.current) {\n      beforeMountRef.current(monacoRef.current);\n      const autoCreatedModelPath = path || defaultPath;\n\n      const defaultModel = getOrCreateModel(\n        monacoRef.current,\n        value || defaultValue || '',\n        defaultLanguage || language || '',\n        autoCreatedModelPath || '',\n      );\n\n      editorRef.current = monacoRef.current?.editor.create(\n        containerRef.current,\n        {\n          model: defaultModel,\n          automaticLayout: true,\n          ...options,\n        },\n        overrideServices,\n      );\n\n      saveViewState && editorRef.current.restoreViewState(viewStates.get(autoCreatedModelPath));\n\n      monacoRef.current.editor.setTheme(theme);\n\n      if (line !== undefined) {\n        editorRef.current.revealLine(line);\n      }\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [\n    defaultValue,\n    defaultLanguage,\n    defaultPath,\n    value,\n    language,\n    path,\n    options,\n    overrideServices,\n    saveViewState,\n    theme,\n    line,\n  ]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  // subscription\n  // to avoid unnecessary updates (attach - dispose listener) in subscription\n  valueRef.current = value;\n\n  // onChange\n  useEffect(() => {\n    if (isEditorReady && onChange) {\n      subscriptionRef.current?.dispose();\n      subscriptionRef.current = editorRef.current?.onDidChangeModelContent((event) => {\n        if (!preventTriggerChangeEvent.current) {\n          onChange(editorRef.current!.getValue(), event);\n        }\n      });\n    }\n  }, [isEditorReady, onChange]);\n\n  // onValidate\n  useEffect(() => {\n    if (isEditorReady) {\n      const changeMarkersListener = monacoRef.current!.editor.onDidChangeMarkers((uris) => {\n        const editorUri = editorRef.current!.getModel()?.uri;\n\n        if (editorUri) {\n          const currentEditorHasMarkerChanges = uris.find((uri) => uri.path === editorUri.path);\n          if (currentEditorHasMarkerChanges) {\n            const markers = monacoRef.current!.editor.getModelMarkers({\n              resource: editorUri,\n            });\n            onValidate?.(markers);\n          }\n        }\n      });\n\n      return () => {\n        changeMarkersListener?.dispose();\n      };\n    }\n    return () => {\n      // eslint happy\n    };\n  }, [isEditorReady, onValidate]);\n\n  function disposeEditor() {\n    subscriptionRef.current?.dispose();\n\n    if (keepCurrentModel) {\n      saveViewState && viewStates.set(path, editorRef.current!.saveViewState());\n    } else {\n      editorRef.current!.getModel()?.dispose();\n    }\n\n    editorRef.current!.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default Editor;\n", "import { useEffect, useRef } from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n\nexport default usePrevious;\n"], "mappings": ";;;;;;;;AAAA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI,eAAgB,WAAU,QAAQ,OAAO,SAAU,KAAK;AAC1D,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC;AACD,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,OAAO,WAAW,eAAe,EAAE,OAAO,YAAY,OAAO,GAAG,GAAI;AACxE,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAIA,MAAK;AAET,MAAI;AACF,aAAS,KAAK,IAAI,OAAO,QAAQ,EAAE,GAAG,IAAI,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAClF,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,IAAAA,MAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAMA;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;;;AC3IA,SAASC,iBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAASC,SAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI,eAAgB,WAAU,QAAQ,OAAO,SAAU,KAAK;AAC1D,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC;AACD,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAASC,gBAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,MAAAD,SAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,QAAAD,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,MAAAC,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,UAAU;AACjB,WAAS,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACtF,QAAI,IAAI,IAAI,UAAU,IAAI;AAAA,EAC5B;AAEA,SAAO,SAAU,GAAG;AAClB,WAAO,IAAI,YAAY,SAAU,GAAG,GAAG;AACrC,aAAO,EAAE,CAAC;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AACF;AAEA,SAAS,MAAM,IAAI;AACjB,SAAO,SAAS,UAAU;AACxB,QAAI,QAAQ;AAEZ,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AAEA,WAAO,KAAK,UAAU,GAAG,SAAS,GAAG,MAAM,MAAM,IAAI,IAAI,WAAY;AACnE,eAAS,QAAQ,UAAU,QAAQ,WAAW,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjG,iBAAS,KAAK,IAAI,UAAU,KAAK;AAAA,MACnC;AAEA,aAAO,QAAQ,MAAM,OAAO,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,CAAC,EAAE,SAAS,KAAK,KAAK,EAAE,SAAS,QAAQ;AAClD;AAEA,SAAS,QAAQ,KAAK;AACpB,SAAO,CAAC,OAAO,KAAK,GAAG,EAAE;AAC3B;AAEA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,eAAe,QAAQ,UAAU;AACxC,SAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ;AAC9D;AAEA,SAAS,gBAAgB,SAAS,SAAS;AACzC,MAAI,CAAC,SAAS,OAAO,EAAG,cAAa,YAAY;AACjD,MAAI,OAAO,KAAK,OAAO,EAAE,KAAK,SAAU,OAAO;AAC7C,WAAO,CAAC,eAAe,SAAS,KAAK;AAAA,EACvC,CAAC,EAAG,cAAa,aAAa;AAC9B,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU;AAClC,MAAI,CAAC,WAAW,QAAQ,EAAG,cAAa,cAAc;AACxD;AAEA,SAAS,gBAAgB,SAAS;AAChC,MAAI,EAAE,WAAW,OAAO,KAAK,SAAS,OAAO,GAAI,cAAa,aAAa;AAC3E,MAAI,SAAS,OAAO,KAAK,OAAO,OAAO,OAAO,EAAE,KAAK,SAAU,UAAU;AACvE,WAAO,CAAC,WAAW,QAAQ;AAAA,EAC7B,CAAC,EAAG,cAAa,cAAc;AACjC;AAEA,SAAS,gBAAgB,SAAS;AAChC,MAAI,CAAC,QAAS,cAAa,mBAAmB;AAC9C,MAAI,CAAC,SAAS,OAAO,EAAG,cAAa,aAAa;AAClD,MAAI,QAAQ,OAAO,EAAG,cAAa,gBAAgB;AACrD;AAEA,SAAS,WAAWE,gBAAe,MAAM;AACvC,QAAM,IAAI,MAAMA,eAAc,IAAI,KAAKA,eAAc,SAAS,CAAC;AACjE;AAEA,IAAI,gBAAgB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AACb;AACA,IAAI,eAAe,MAAM,UAAU,EAAE,aAAa;AAClD,IAAI,aAAa;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AACX;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,aAAW,QAAQ,OAAO;AAC1B,aAAW,QAAQ,OAAO;AAC1B,MAAI,QAAQ;AAAA,IACV,SAAS;AAAA,EACX;AACA,MAAI,YAAY,MAAM,cAAc,EAAE,OAAO,OAAO;AACpD,MAAI,SAAS,MAAM,WAAW,EAAE,KAAK;AACrC,MAAI,WAAW,MAAM,WAAW,OAAO,EAAE,OAAO;AAChD,MAAI,aAAa,MAAM,cAAc,EAAE,KAAK;AAE5C,WAASC,YAAW;AAClB,QAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,SAAUC,QAAO;AAClG,aAAOA;AAAA,IACT;AACA,eAAW,SAAS,QAAQ;AAC5B,WAAO,SAAS,MAAM,OAAO;AAAA,EAC/B;AAEA,WAASC,UAAS,eAAe;AAC/B,YAAQ,WAAW,QAAQ,UAAU,UAAU,EAAE,aAAa;AAAA,EAChE;AAEA,SAAO,CAACF,WAAUE,SAAQ;AAC5B;AAEA,SAAS,eAAe,OAAO,eAAe;AAC5C,SAAO,WAAW,aAAa,IAAI,cAAc,MAAM,OAAO,IAAI;AACpE;AAEA,SAAS,YAAY,OAAO,SAAS;AACnC,QAAM,UAAUJ,gBAAeA,gBAAe,CAAC,GAAG,MAAM,OAAO,GAAG,OAAO;AACzE,SAAO;AACT;AAEA,SAAS,eAAe,OAAO,SAAS,SAAS;AAC/C,aAAW,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI,OAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,OAAO;AAC3F,QAAI;AAEJ,YAAQ,iBAAiB,QAAQ,KAAK,OAAO,QAAQ,mBAAmB,SAAS,SAAS,eAAe,KAAK,SAAS,MAAM,QAAQ,KAAK,CAAC;AAAA,EAC7I,CAAC;AACD,SAAO;AACT;AAEA,IAAI,QAAQ;AAAA,EACV;AACF;AAEA,IAAO,sBAAQ;;;AChMf,IAAI,SAAS;AAAA,EACX,OAAO;AAAA,IACL,IAAI;AAAA,EACN;AACF;AAEA,IAAO,iBAAQ;;;ACNf,SAASK,OAAM,IAAI;AACjB,SAAO,SAAS,UAAU;AACxB,QAAI,QAAQ;AAEZ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,WAAO,KAAK,UAAU,GAAG,SAAS,GAAG,MAAM,MAAM,IAAI,IAAI,WAAY;AACnE,eAAS,QAAQ,UAAU,QAAQ,WAAW,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjG,iBAAS,KAAK,IAAI,UAAU,KAAK;AAAA,MACnC;AAEA,aAAO,QAAQ,MAAM,OAAO,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AACF;AAEA,IAAO,gBAAQA;;;AClBf,SAASC,UAAS,OAAO;AACvB,SAAO,CAAC,EAAE,SAAS,KAAK,KAAK,EAAE,SAAS,QAAQ;AAClD;AAEA,IAAO,mBAAQA;;;ACKf,SAAS,eAAeC,SAAQ;AAC9B,MAAI,CAACA,QAAQ,CAAAC,cAAa,kBAAkB;AAC5C,MAAI,CAAC,iBAASD,OAAM,EAAG,CAAAC,cAAa,YAAY;AAEhD,MAAID,QAAO,MAAM;AACf,2BAAuB;AACvB,WAAO;AAAA,MACL,OAAO;AAAA,QACL,IAAIA,QAAO,KAAK;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAEA,SAAOA;AACT;AAMA,SAAS,yBAAyB;AAChC,UAAQ,KAAKE,eAAc,WAAW;AACxC;AAEA,SAASC,YAAWD,gBAAe,MAAM;AACvC,QAAM,IAAI,MAAMA,eAAc,IAAI,KAAKA,eAAc,SAAS,CAAC;AACjE;AAEA,IAAIA,iBAAgB;AAAA,EAClB,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AACf;AACA,IAAID,gBAAe,cAAME,WAAU,EAAED,cAAa;AAClD,IAAIE,cAAa;AAAA,EACf,QAAQ;AACV;AAEA,IAAO,qBAAQA;;;AChDf,IAAIC,WAAU,SAASA,WAAU;AAC/B,WAAS,OAAO,UAAU,QAAQ,MAAM,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACtF,QAAI,IAAI,IAAI,UAAU,IAAI;AAAA,EAC5B;AAEA,SAAO,SAAU,GAAG;AAClB,WAAO,IAAI,YAAY,SAAU,GAAG,GAAG;AACrC,aAAO,EAAE,CAAC;AAAA,IACZ,GAAG,CAAC;AAAA,EACN;AACF;AAEA,IAAO,kBAAQA;;;ACVf,SAAS,MAAM,QAAQ,QAAQ;AAC7B,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,QAAI,OAAO,GAAG,aAAa,QAAQ;AACjC,UAAI,OAAO,GAAG,GAAG;AACf,eAAO,OAAO,OAAO,GAAG,GAAG,MAAM,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC,CAAC;AAAA,MAC5D;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,eAAe,eAAe,CAAC,GAAG,MAAM,GAAG,MAAM;AAC1D;AAEA,IAAO,oBAAQ;;;ACZf,IAAI,sBAAsB;AAAA,EACxB,MAAM;AAAA,EACN,KAAK;AACP;AAEA,SAAS,eAAe,SAAS;AAC/B,MAAI,eAAe;AACnB,MAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1D,YAAQ,KAAK,SAAU,KAAK;AAC1B,aAAO,eAAe,OAAO,mBAAmB,IAAI,QAAQ,GAAG;AAAA,IACjE,CAAC;AACD,YAAQ,OAAO,EAAE,MAAM;AAAA,EACzB,CAAC;AACD,SAAO,eAAe,SAAS,WAAY;AACzC,WAAO,eAAe;AAAA,EACxB,GAAG;AACL;AAEA,IAAO,yBAAQ;;;ACTf,IAAI,gBAAgB,oBAAM,OAAO;AAAA,EAC/B,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AACV,CAAC;AAND,IAOI,iBAAiB,eAAe,eAAe,CAAC;AAPpD,IAQI,WAAW,eAAe,CAAC;AAR/B,IASI,WAAW,eAAe,CAAC;AAO/B,SAASC,QAAO,cAAc;AAC5B,MAAI,qBAAqB,mBAAW,OAAO,YAAY,GACnD,SAAS,mBAAmB,QAC5BA,UAAS,yBAAyB,oBAAoB,CAAC,QAAQ,CAAC;AAEpE,WAAS,SAAU,OAAO;AACxB,WAAO;AAAA,MACL,QAAQ,kBAAM,MAAM,QAAQA,OAAM;AAAA,MAClC;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAOA,SAAS,OAAO;AACd,MAAI,QAAQ,SAAS,SAAU,MAAM;AACnC,QAAI,SAAS,KAAK,QACd,gBAAgB,KAAK,eACrB,UAAU,KAAK;AACnB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,CAAC,MAAM,eAAe;AACxB,aAAS;AAAA,MACP,eAAe;AAAA,IACjB,CAAC;AAED,QAAI,MAAM,QAAQ;AAChB,YAAM,QAAQ,MAAM,MAAM;AAC1B,aAAO,uBAAe,cAAc;AAAA,IACtC;AAEA,QAAI,OAAO,UAAU,OAAO,OAAO,QAAQ;AACzC,0BAAoB,OAAO,MAAM;AACjC,YAAM,QAAQ,OAAO,MAAM;AAC3B,aAAO,uBAAe,cAAc;AAAA,IACtC;AAEA,oBAAQ,eAAe,qBAAqB,EAAE,eAAe;AAAA,EAC/D;AAEA,SAAO,uBAAe,cAAc;AACtC;AAQA,SAAS,cAAc,QAAQ;AAC7B,SAAO,SAAS,KAAK,YAAY,MAAM;AACzC;AAQA,SAAS,aAAa,KAAK;AACzB,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,SAAO,QAAQ,OAAO,MAAM,MAAM;AACpC;AAOA,SAAS,sBAAsBC,kBAAiB;AAC9C,MAAI,QAAQ,SAAS,SAAU,OAAO;AACpC,QAAID,UAAS,MAAM,QACf,SAAS,MAAM;AACnB,WAAO;AAAA,MACL,QAAQA;AAAA,MACR;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,eAAe,aAAa,GAAG,OAAO,MAAM,OAAO,MAAM,IAAI,YAAY,CAAC;AAE9E,eAAa,SAAS,WAAY;AAChC,WAAOC,iBAAgB;AAAA,EACzB;AAEA,eAAa,UAAU,MAAM;AAC7B,SAAO;AACT;AAMA,SAAS,kBAAkB;AACzB,MAAI,QAAQ,SAAS,SAAU,OAAO;AACpC,QAAID,UAAS,MAAM,QACf,UAAU,MAAM,SAChB,SAAS,MAAM;AACnB,WAAO;AAAA,MACL,QAAQA;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAIE,WAAU,OAAO;AAErB,EAAAA,SAAQ,OAAO,MAAM,MAAM;AAE3B,EAAAA,SAAQ,CAAC,uBAAuB,GAAG,SAAU,QAAQ;AACnD,wBAAoB,MAAM;AAC1B,UAAM,QAAQ,MAAM;AAAA,EACtB,GAAG,SAAU,OAAO;AAClB,UAAM,OAAO,KAAK;AAAA,EACpB,CAAC;AACH;AAMA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,CAAC,SAAS,EAAE,QAAQ;AACtB,aAAS;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQA,SAAS,sBAAsB;AAC7B,SAAO,SAAS,SAAU,OAAO;AAC/B,QAAI,SAAS,MAAM;AACnB,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1D,SAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAI,SAAS;AAAA,EACX,QAAQF;AAAA,EACR;AAAA,EACA;AACF;AAEA,IAAO,iBAAQ;;;AE3Lf,mBAAqB;ACErB,IAAAG,gBAAgE;ACFhE,IAAAC,gBAAqB;ACArB,IAAAA,gBAAkB;AEAlB,IAAAA,gBAA8C;AGA9C,IAAAA,gBAA+C;ACA/C,IAAAA,gBAA4E;AEA5E,IAAAA,gBAAyB;ACAzB,IAAAC,gBAAqB;ACErB,IAAAA,iBAAgE;ACFhE,IAAAC,iBAAkC;AVElC,IAAMC,KAAwC,EAC5C,SAAS,EACP,SAAS,QACT,UAAU,YACV,WAAW,UACb,GACA,WAAW,EACT,OAAO,OACT,GACA,MAAM,EACJ,SAAS,OACX,EACF;AAZA,IAcOC,IAAQD;AEdf,IAAMA,KAAwC,EAC5C,WAAW,EACT,SAAS,QACT,QAAQ,QACR,OAAO,QACP,gBAAgB,UAChB,YAAY,SACd,EACF;AARA,IAUOC,IAAQD;ADRf,SAASE,GAAQ,EAAE,UAAAC,EAAS,GAAsB;AAChD,SAAOC,cAAAA,QAAA,cAAC,OAAA,EAAI,OAAOH,EAAO,UAAA,GAAYE,CAAS;AACjD;AAEA,IAAOE,IAAQH;AENf,IAAOG,IAAQA;AJOf,SAASC,GAAgB,EACvB,OAAAC,GACA,QAAAC,GACA,eAAAC,GACA,SAAAC,GACA,MAAAC,GACA,WAAAC,GACA,cAAAC,EACF,GAAmB;AACjB,SACET,cAAAA,QAAA,cAAC,WAAA,EAAQ,OAAO,EAAE,GAAGH,EAAO,SAAS,OAAAM,GAAO,QAAAC,EAAO,GAAI,GAAGK,EAAAA,GACvD,CAACJ,KAAiBL,cAAAA,QAAA,cAACC,GAAA,MAASK,CAAQ,GACrCN,cAAAA,QAAA,cAAC,OAAA,EACC,KAAKO,GACL,OAAO,EAAE,GAAGV,EAAO,WAAW,GAAI,CAACQ,KAAiBR,EAAO,KAAM,GACjE,WAAWW,EAAAA,CACb,CACF;AAEJ;AAEA,IAAOE,KAAQR;AD1Bf,IAAOQ,QAAQC,cAAAA,MAAKD,EAAe;AMFnC,SAASE,GAASC,GAAwB;AACxCC,oBAAAA,WAAUD,GAAQ,CAAC,CAAC;AACtB;AAEA,IAAOE,IAAQH;ACJf,SAASI,GAAUH,GAAwBI,GAAsBC,IAAe,MAAM;AACpF,MAAMC,QAAiBC,cAAAA,QAAO,IAAI;AAElCN,oBAAAA,WACEK,EAAe,WAAW,CAACD,IACvB,MAAM;AACJC,MAAe,UAAU;EAC3B,IACAN,GACJI,CACF;AACF;AAEA,IAAOI,IAAQL;ACTf,SAASM,IAAO;AAEhB;AAYA,SAASC,EAAiBC,GAAgBC,GAAeC,GAAkBC,GAAc;AACvF,SAAOC,GAASJ,GAAQG,CAAI,KAAKE,GAAYL,GAAQC,GAAOC,GAAUC,CAAI;AAC5E;AASA,SAASC,GAASJ,GAAgBG,GAAc;AAC9C,SAAOH,EAAO,OAAO,SAASM,GAAeN,GAAQG,CAAI,CAAC;AAC5D;AAUA,SAASE,GAAYL,GAAgBC,GAAeC,GAAmBC,GAAe;AACpF,SAAOH,EAAO,OAAO,YACnBC,GACAC,GACAC,IAAOG,GAAeN,GAAQG,CAAI,IAAI,MACxC;AACF;AAQA,SAASG,GAAeN,GAAgBG,GAAc;AACpD,SAAOH,EAAO,IAAI,MAAMG,CAAI;AAC9B;AT/CA,SAASI,GAAW,EAClB,UAAAC,GACA,UAAAC,GACA,UAAAP,GACA,kBAAAQ,GACA,kBAAAC,GACA,mBAAAC,GACA,mBAAAC,GACA,0BAAAC,IAA2B,OAC3B,0BAAAC,IAA2B,OAC3B,OAAAC,IAAQ,SACR,SAAAlC,IAAU,cACV,SAAAmC,IAAU,CAAC,GACX,QAAArC,IAAS,QACT,OAAAD,IAAQ,QACR,WAAAK,GACA,cAAAC,IAAe,CAAC,GAChB,aAAAiC,IAAcpB,GACd,SAAAqB,IAAUrB,EACZ,GAAoB;AAClB,MAAM,CAACjB,GAAeuC,CAAgB,QAAIC,cAAAA,UAAS,KAAK,GAClD,CAACC,GAAkBC,CAAmB,QAAIF,cAAAA,UAAS,IAAI,GACvDG,QAAY5B,cAAAA,QAAgC,IAAI,GAChD6B,QAAY7B,cAAAA,QAAsB,IAAI,GACtC8B,QAAe9B,cAAAA,QAAuB,IAAI,GAC1C+B,QAAa/B,cAAAA,QAAOuB,CAAO,GAC3BS,QAAiBhC,cAAAA,QAAOsB,CAAW,GACnCW,QAAkBjC,cAAAA,QAAO,KAAK;AAEpCL,IAAS,MAAM;AACb,QAAMuC,IAAaC,eAAO,KAAK;AAE/B,WAAAD,EACG,KAAM9B,QAAYyB,EAAU,UAAUzB,MAAWuB,EAAoB,KAAK,CAAC,EAC3E,MACES,QACCA,uBAAO,UAAS,iBAAiB,QAAQ,MAAM,iCAAiCA,CAAK,CACzF,GAEK,MAAOR,EAAU,UAAUS,EAAc,IAAIH,EAAW,OAAO;EACxE,CAAC,GAEDjC,EACE,MAAM;AACJ,QAAI2B,EAAU,WAAWC,EAAU,SAAS;AAC1C,UAAMS,IAAiBV,EAAU,QAAQ,kBAAkB,GACrDW,IAAQpC,EACZ0B,EAAU,SACVjB,KAAY,IACZE,KAAoBR,KAAY,QAChCU,KAAqB,EACvB;AAEIuB,YAAUD,EAAe,SAAS,KACpCA,EAAe,SAASC,CAAK;IAAA;EAGnC,GACA,CAACvB,CAAiB,GAClB/B,CACF,GAEAgB,EACE,MAAM;AACJ,QAAI2B,EAAU,WAAWC,EAAU,SAAS;AAC1C,UAAMW,IAAiBZ,EAAU,QAAQ,kBAAkB,GACrDW,IAAQpC,EACZ0B,EAAU,SACVhB,KAAY,IACZE,KAAoBT,KAAY,QAChCW,KAAqB,EACvB;AAEIsB,YAAUC,EAAe,SAAS,KACpCA,EAAe,SAASD,CAAK;IAAA;EAGnC,GACA,CAACtB,CAAiB,GAClBhC,CACF,GAEAgB,EACE,MAAM;AACJ,QAAMuC,IAAiBZ,EAAU,QAAS,kBAAkB;AACxDY,MAAe,UAAUX,EAAU,QAAS,OAAO,aAAa,QAAQ,IAC1EW,EAAe,SAAS3B,KAAY,EAAE,IAElCA,MAAa2B,EAAe,SAAS,MACvCA,EAAe,aAAa,IAAI,CAC9B,EACE,OAAOA,EAAe,SAAS,EAAG,kBAAkB,GACpD,MAAM3B,KAAY,IAClB,kBAAkB,KACpB,CACF,CAAC,GAED2B,EAAe,aAAa;EAGlC,GACA,CAAC3B,CAAQ,GACT5B,CACF,GAEAgB,EACE,MAAM;AFtHV;AEuHM2B,kBAAU,YAAVA,mBAAmB,eAAnBA,mBAA+B,SAAS,SAAShB,KAAY;EAC/D,GACA,CAACA,CAAQ,GACT3B,CACF,GAEAgB,EACE,MAAM;AACJ,QAAM,EAAE,UAAAW,GAAU,UAAAC,EAAS,IAAIe,EAAU,QAAS,SAAS;AAE3DC,MAAU,QAAS,OAAO,iBAAiBjB,GAAUE,KAAoBR,KAAY,MAAM,GAC3FuB,EAAU,QAAS,OAAO,iBAAiBhB,GAAUE,KAAoBT,KAAY,MAAM;EAC7F,GACA,CAACA,GAAUQ,GAAkBC,CAAgB,GAC7C9B,CACF,GAEAgB,EACE,MAAM;AFzIV;AE0IM4B,YAAU,YAAVA,mBAAmB,OAAO,SAAST;EACrC,GACA,CAACA,CAAK,GACNnC,CACF,GAEAgB,EACE,MAAM;AFjJV;AEkJM2B,YAAU,YAAVA,mBAAmB,cAAcP;EACnC,GACA,CAACA,CAAO,GACRpC,CACF;AAEA,MAAMwD,QAAYC,cAAAA,aAAY,MAAM;AFxJtC;AEyJI,QAAI,CAACb,EAAU,QAAS;AACxBG,MAAe,QAAQH,EAAU,OAAO;AACxC,QAAMc,IAAgBxC,EACpB0B,EAAU,SACVjB,KAAY,IACZE,KAAoBR,KAAY,QAChCU,KAAqB,EACvB,GAEM4B,IAAgBzC,EACpB0B,EAAU,SACVhB,KAAY,IACZE,KAAoBT,KAAY,QAChCW,KAAqB,EACvB;AAEAW,YAAU,YAAVA,mBAAmB,SAAS,EAC1B,UAAUe,GACV,UAAUC,EACZ;EACF,GAAG,CACDtC,GACAO,GACAE,GACAH,GACAE,GACAE,GACAC,CACF,CAAC,GAEK4B,QAAeH,cAAAA,aAAY,MAAM;AFvLzC;AEwLQ,KAACT,EAAgB,WAAWH,EAAa,YAC3CF,EAAU,UAAUC,EAAU,QAAS,OAAO,iBAAiBC,EAAa,SAAS,EACnF,iBAAiB,MACjB,GAAGT,EACL,CAAC,GAEDoB,EAAU,IAEVZ,OAAU,YAAVA,mBAAmB,OAAO,SAAST,IAEnCI,EAAiB,IAAI,GACrBS,EAAgB,UAAU;EAE9B,GAAG,CAACZ,GAASD,GAAOqB,CAAS,CAAC;AAE9B/C,oBAAAA,WAAU,MAAM;AACVT,SACF8C,EAAW,QAAQH,EAAU,SAAUC,EAAU,OAAQ;EAE7D,GAAG,CAAC5C,CAAa,CAAC,OAElBS,cAAAA,WAAU,MAAM;AACd,KAACgC,KAAoB,CAACzC,KAAiB4D,EAAa;EACtD,GAAG,CAACnB,GAAkBzC,GAAe4D,CAAY,CAAC;AAElD,WAASR,IAAgB;AFjN3B;AEkNI,QAAMS,KAASlB,OAAU,YAAVA,mBAAmB;AAE7BV,WACH4B,4BAAQ,aAARA,mBAAkB,YAGf3B,OACH2B,4BAAQ,aAARA,mBAAkB,aAGpBlB,OAAU,YAAVA,mBAAmB;EACrB;AAEA,SACEhD,cAAAA,QAAA,cAACU,GAAA,EACC,OAAOP,GACP,QAAQC,GACR,eAAeC,GACf,SAASC,GACT,MAAM4C,GACN,WAAW1C,GACX,cAAcC,EAAAA,CAChB;AAEJ;AAEA,IAAO0D,KAAQpC;ADtOf,IAAOoC,SAAQxD,aAAAA,MAAKwD,EAAU;AWD9B,SAASC,KAAY;AACnB,MAAM,CAAC5C,GAAQ6C,CAAS,QAAIxB,cAAAA,UAASU,eAAO,oBAAoB,CAAC;AAEjE,SAAAxC,EAAS,MAAM;AACb,QAAIuC;AAEJ,WAAK9B,MACH8B,IAAaC,eAAO,KAAK,GAEzBD,EAAW,KAAM9B,OAAW;AAC1B6C,QAAU7C,CAAM;IAClB,CAAC,IAGI,MAAM8B,uBAAY;EAC3B,CAAC,GAEM9B;AACT;AAEA,IAAO8C,KAAQF;AGvBf,SAASG,GAAe9C,GAAU;AAChC,MAAM+C,QAAMpD,eAAAA,QAAU;AAEtB,aAAAN,eAAAA,WAAU,MAAM;AACd0D,MAAI,UAAU/C;EAChB,GAAG,CAACA,CAAK,CAAC,GAEH+C,EAAI;AACb;AAEA,IAAOC,KAAQF;ADCf,IAAMG,IAAa,oBAAI;AAEvB,SAASC,GAAO,EACd,cAAAC,GACA,iBAAAC,GACA,aAAAC,GACA,OAAArD,GACA,UAAAC,GACA,MAAAC,GAEA,OAAAa,IAAQ,SACR,MAAAuC,GACA,SAAAzE,IAAU,cACV,SAAAmC,IAAU,CAAC,GACX,kBAAAuC,IAAmB,CAAC,GACpB,eAAAC,IAAgB,MAChB,kBAAAC,IAAmB,OAEnB,OAAA/E,IAAQ,QACR,QAAAC,IAAS,QACT,WAAAI,GACA,cAAAC,IAAe,CAAC,GAEhB,aAAAiC,IAAcpB,GACd,SAAAqB,IAAUrB,GACV,UAAA6D,GACA,YAAAC,IAAa9D,EACf,GAAgB;AACd,MAAM,CAACjB,GAAeuC,CAAgB,QAAIC,eAAAA,UAAS,KAAK,GAClD,CAACC,GAAkBC,CAAmB,QAAIF,eAAAA,UAAS,IAAI,GACvDI,QAAY7B,eAAAA,QAAsB,IAAI,GACtC4B,QAAY5B,eAAAA,QAA4C,IAAI,GAC5D8B,QAAe9B,eAAAA,QAAuB,IAAI,GAC1C+B,QAAa/B,eAAAA,QAAOuB,CAAO,GAC3BS,QAAiBhC,eAAAA,QAAOsB,CAAW,GACnC2C,QAAkBjE,eAAAA,QAAoB,GACtCkE,QAAWlE,eAAAA,QAAOK,CAAK,GACvB8D,IAAed,GAAY9C,CAAI,GAC/B0B,QAAkBjC,eAAAA,QAAO,KAAK,GAC9BoE,QAA4BpE,eAAAA,QAAgB,KAAK;AAEvDL,IAAS,MAAM;AACb,QAAMuC,IAAaC,eAAO,KAAK;AAE/B,WAAAD,EACG,KAAM9B,QAAYyB,EAAU,UAAUzB,MAAWuB,EAAoB,KAAK,CAAC,EAC3E,MACES,QACCA,uBAAO,UAAS,iBAAiB,QAAQ,MAAM,iCAAiCA,CAAK,CACzF,GAEK,MAAOR,EAAU,UAAUS,GAAc,IAAIH,EAAW,OAAO;EACxE,CAAC,GAEDjC,EACE,MAAM;AdpEV;AcqEM,QAAMsC,IAAQpC,EACZ0B,EAAU,SACV2B,KAAgBnD,KAAS,IACzBoD,KAAmBnD,KAAY,IAC/BC,KAAQmD,KAAe,EACzB;AAEInB,YAAUX,OAAU,YAAVA,mBAAmB,gBAC3BiC,KAAeP,EAAW,IAAIa,IAAcvC,OAAU,YAAVA,mBAAmB,eAAe,IAClFA,OAAU,YAAVA,mBAAmB,SAASW,IACxBsB,OAAejC,OAAU,YAAVA,mBAAmB,iBAAiB0B,EAAW,IAAI/C,CAAI;EAE9E,GACA,CAACA,CAAI,GACLtB,CACF,GAEAgB,EACE,MAAM;AdvFV;AcwFM2B,YAAU,YAAVA,mBAAmB,cAAcP;EACnC,GACA,CAACA,CAAO,GACRpC,CACF,GAEAgB,EACE,MAAM;AACA,KAAC2B,EAAU,WAAWvB,MAAU,WAChCuB,EAAU,QAAQ,UAAUC,EAAU,QAAS,OAAO,aAAa,QAAQ,IAC7ED,EAAU,QAAQ,SAASvB,CAAK,IACvBA,MAAUuB,EAAU,QAAQ,SAAS,MAC9CwC,EAA0B,UAAU,MACpCxC,EAAU,QAAQ,aAAa,IAAI,CACjC,EACE,OAAOA,EAAU,QAAQ,SAAS,EAAG,kBAAkB,GACvD,MAAMvB,GACN,kBAAkB,KACpB,CACF,CAAC,GAEDuB,EAAU,QAAQ,aAAa,GAC/BwC,EAA0B,UAAU;EAExC,GACA,CAAC/D,CAAK,GACNpB,CACF,GAEAgB,EACE,MAAM;AdtHV;AcuHM,QAAMsC,KAAQX,OAAU,YAAVA,mBAAmB;AAC7BW,SAASjC,OAAUuB,OAAU,YAAVA,mBAAmB,OAAO,iBAAiBU,GAAOjC;EAC3E,GACA,CAACA,CAAQ,GACTrB,CACF,GAEAgB,EACE,MAAM;Ad/HV;AciIU0D,UAAS,YACX/B,OAAU,YAAVA,mBAAmB,WAAW+B;EAElC,GACA,CAACA,CAAI,GACL1E,CACF,GAEAgB,EACE,MAAM;Ad1IV;Ac2IM4B,YAAU,YAAVA,mBAAmB,OAAO,SAAST;EACrC,GACA,CAACA,CAAK,GACNnC,CACF;AAEA,MAAM4D,QAAeH,eAAAA,aAAY,MAAM;AdjJzC;AckJI,QAAI,EAAA,CAACZ,EAAa,WAAW,CAACD,EAAU,YACpC,CAACI,EAAgB,SAAS;AAC5BD,QAAe,QAAQH,EAAU,OAAO;AACxC,UAAMwC,IAAuB9D,KAAQmD,GAE/BY,IAAenE,EACnB0B,EAAU,SACVxB,KAASmD,KAAgB,IACzBC,KAAmBnD,KAAY,IAC/B+D,KAAwB,EAC1B;AAEAzC,QAAU,WAAUC,OAAU,YAAVA,mBAAmB,OAAO,OAC5CC,EAAa,SACb,EACE,OAAOwC,GACP,iBAAiB,MACjB,GAAGjD,EACL,GACAuC,IAGFC,KAAiBjC,EAAU,QAAQ,iBAAiB0B,EAAW,IAAIe,CAAoB,CAAC,GAExFxC,EAAU,QAAQ,OAAO,SAAST,CAAK,GAEnCuC,MAAS,UACX/B,EAAU,QAAQ,WAAW+B,CAAI,GAGnCnC,EAAiB,IAAI,GACrBS,EAAgB,UAAU;IAAA;EAE9B,GAAG,CACDuB,GACAC,GACAC,GACArD,GACAC,GACAC,GACAc,GACAuC,GACAC,GACAzC,GACAuC,CACF,CAAC;AAEDjE,qBAAAA,WAAU,MAAM;AACVT,SACF8C,EAAW,QAAQH,EAAU,SAAUC,EAAU,OAAQ;EAE7D,GAAG,CAAC5C,CAAa,CAAC,OAElBS,eAAAA,WAAU,MAAM;AACd,KAACgC,KAAoB,CAACzC,KAAiB4D,EAAa;EACtD,GAAG,CAACnB,GAAkBzC,GAAe4D,CAAY,CAAC,GAIlDqB,EAAS,UAAU7D,OAGnBX,eAAAA,WAAU,MAAM;AdhNlB;AciNQT,SAAiB8E,OACnBE,OAAgB,YAAhBA,mBAAyB,WACzBA,EAAgB,WAAUrC,OAAU,YAAVA,mBAAmB,wBAAyB2C,OAAU;AACzEH,QAA0B,WAC7BL,EAASnC,EAAU,QAAS,SAAS,GAAG2C,CAAK;IAEjD;EAEJ,GAAG,CAACtF,GAAe8E,CAAQ,CAAC,OAG5BrE,eAAAA,WAAU,MAAM;AACd,QAAIT,GAAe;AACjB,UAAMuF,IAAwB3C,EAAU,QAAS,OAAO,mBAAoB4C,OAAS;Ad9N3F;Ac+NQ,YAAMC,KAAY9C,OAAU,QAAS,SAAS,MAA5BA,mBAA+B;AAEjD,YAAI8C,KACoCD,EAAK,KAAME,OAAQA,EAAI,SAASD,EAAU,IAAI,GACjD;AACjC,cAAME,IAAU/C,EAAU,QAAS,OAAO,gBAAgB,EACxD,UAAU6C,EACZ,CAAC;AACDV,iCAAaY;QAAO;MAG1B,CAAC;AAED,aAAO,MAAM;AACXJ,+BAAuB;MACzB;IAAA;AAEF,WAAO,MAAM;IAEb;EACF,GAAG,CAACvF,GAAe+E,CAAU,CAAC;AAE9B,WAAS3B,KAAgB;AdrP3B;AcsPI4B,YAAgB,YAAhBA,mBAAyB,WAErBH,IACFD,KAAiBP,EAAW,IAAI/C,GAAMqB,EAAU,QAAS,cAAc,CAAC,KAExEA,OAAU,QAAS,SAAS,MAA5BA,mBAA+B,WAGjCA,EAAU,QAAS,QAAQ;EAC7B;AAEA,SACEhD,eAAAA,QAAA,cAACU,GAAA,EACC,OAAOP,GACP,QAAQC,GACR,eAAeC,GACf,SAASC,GACT,MAAM4C,GACN,WAAW1C,GACX,cAAcC,EAAAA,CAChB;AAEJ;AAEA,IAAOwF,KAAQtB;ADxQf,IAAOsB,SAAQtF,cAAAA,MAAKsF,EAAM;AbO1B,IAAOC,KAAQD;", "names": ["_e", "_defineProperty", "ownKeys", "_objectSpread2", "errorMessages", "getState", "state", "setState", "curry", "isObject", "config", "<PERSON><PERSON><PERSON><PERSON>", "errorMessages", "throwError", "validators", "compose", "config", "configure<PERSON><PERSON><PERSON>", "require", "import_react", "import_react", "import_react", "import_react", "styles", "styles_default", "Loading", "children", "React", "Loading_default", "MonacoContainer", "width", "height", "isEditorReady", "loading", "_ref", "className", "wrapperProps", "MonacoContainer_default", "memo", "useMount", "effect", "useEffect", "useMount_default", "useUpdate", "deps", "applyChanges", "isInitialMount", "useRef", "useUpdate_default", "noop", "getOrCreateModel", "monaco", "value", "language", "path", "getModel", "createModel", "createModelUri", "DiffE<PERSON>or", "original", "modified", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "keepCurrentModifiedModel", "theme", "options", "beforeMount", "onMount", "setIsEditorReady", "useState", "isMonacoMounting", "setIsMonacoMounting", "editor<PERSON><PERSON>", "monacoRef", "containerRef", "onMountRef", "beforeMountRef", "preventCreation", "cancelable", "loader", "error", "dispose<PERSON><PERSON><PERSON>", "originalEditor", "model", "modifiedEditor", "setModels", "useCallback", "originalModel", "modifiedModel", "createEditor", "models", "DiffEditor_default", "useMonaco", "setMonaco", "useMonaco_default", "usePrevious", "ref", "usePrevious_default", "viewStates", "Editor", "defaultValue", "defaultLanguage", "defaultPath", "line", "overrideServices", "saveViewState", "keepCurrentModel", "onChange", "onValidate", "subscriptionRef", "valueRef", "previousPath", "preventTriggerChangeEvent", "autoCreatedModelPath", "defaultModel", "event", "changeMarkersListener", "uris", "editor<PERSON><PERSON>", "uri", "markers", "Editor_default", "src_default"]}