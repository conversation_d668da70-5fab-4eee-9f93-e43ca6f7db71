{"version": 3, "sources": ["../../../../../node_modules/.pnpm/mobx-state-tree@3.17.3_mobx@4.15.7/node_modules/mobx-state-tree/dist/mobx-state-tree.module.js"], "sourcesContent": ["import { isComputed<PERSON>rop, isObservableProp, createAtom, action, _allowStateChangesInsideComputed, reaction, computed, runInAction, observable, values, entries, isObservableArray, $mobx, getAtom, ObservableMap, _interceptReads, intercept, observe, _getAdministration, set } from 'mobx';\n\nvar livelinessChecking = \"warn\";\n/**\n * Defines what MST should do when running into reads / writes to objects that have died.\n * By default it will print a warning.\n * Use the `\"error\"` option to easy debugging to see where the error was thrown and when the offending read / write took place\n *\n * @param mode `\"warn\"`, `\"error\"` or `\"ignore\"`\n */\nfunction setLivelinessChecking(mode) {\n    livelinessChecking = mode;\n}\n/**\n * Returns the current liveliness checking mode.\n *\n * @returns `\"warn\"`, `\"error\"` or `\"ignore\"`\n */\nfunction getLivelinessChecking() {\n    return livelinessChecking;\n}\n/**\n * @deprecated use setLivelinessChecking instead\n * @hidden\n *\n * Defines what MST should do when running into reads / writes to objects that have died.\n * By default it will print a warning.\n * Use the `\"error\"` option to easy debugging to see where the error was thrown and when the offending read / write took place\n *\n * @param mode `\"warn\"`, `\"error\"` or `\"ignore\"`\n */\nfunction setLivelynessChecking(mode) {\n    setLivelinessChecking(mode);\n}\n\n/**\n * @hidden\n */\nvar Hook;\n(function (Hook) {\n    Hook[\"afterCreate\"] = \"afterCreate\";\n    Hook[\"afterAttach\"] = \"afterAttach\";\n    Hook[\"afterCreationFinalization\"] = \"afterCreationFinalization\";\n    Hook[\"beforeDetach\"] = \"beforeDetach\";\n    Hook[\"beforeDestroy\"] = \"beforeDestroy\";\n})(Hook || (Hook = {}));\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nfunction __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nfunction __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nfunction __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\n\n/**\n * Returns the _actual_ type of the given tree node. (Or throws)\n *\n * @param object\n * @returns\n */\nfunction getType(object) {\n    assertIsStateTreeNode(object, 1);\n    return getStateTreeNode(object).type;\n}\n/**\n * Returns the _declared_ type of the given sub property of an object, array or map.\n * In the case of arrays and maps the property name is optional and will be ignored.\n *\n * Example:\n * ```ts\n * const Box = types.model({ x: 0, y: 0 })\n * const box = Box.create()\n *\n * console.log(getChildType(box, \"x\").name) // 'number'\n * ```\n *\n * @param object\n * @param propertyName\n * @returns\n */\nfunction getChildType(object, propertyName) {\n    assertIsStateTreeNode(object, 1);\n    return getStateTreeNode(object).getChildType(propertyName);\n}\n/**\n * Registers a function that will be invoked for each mutation that is applied to the provided model instance, or to any of its children.\n * See [patches](https://github.com/mobxjs/mobx-state-tree#patches) for more details. onPatch events are emitted immediately and will not await the end of a transaction.\n * Patches can be used to deep observe a model tree.\n *\n * @param target the model instance from which to receive patches\n * @param callback the callback that is invoked for each patch. The reversePatch is a patch that would actually undo the emitted patch\n * @returns function to remove the listener\n */\nfunction onPatch(target, callback) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsFunction(callback, 2);\n    return getStateTreeNode(target).onPatch(callback);\n}\n/**\n * Registers a function that is invoked whenever a new snapshot for the given model instance is available.\n * The listener will only be fire at the end of the current MobX (trans)action.\n * See [snapshots](https://github.com/mobxjs/mobx-state-tree#snapshots) for more details.\n *\n * @param target\n * @param callback\n * @returns\n */\nfunction onSnapshot(target, callback) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsFunction(callback, 2);\n    return getStateTreeNode(target).onSnapshot(callback);\n}\n/**\n * Applies a JSON-patch to the given model instance or bails out if the patch couldn't be applied\n * See [patches](https://github.com/mobxjs/mobx-state-tree#patches) for more details.\n *\n * Can apply a single past, or an array of patches.\n *\n * @param target\n * @param patch\n * @returns\n */\nfunction applyPatch(target, patch) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertArg(patch, function (p) { return typeof p === \"object\"; }, \"object or array\", 2);\n    getStateTreeNode(target).applyPatches(asArray(patch));\n}\n/**\n * Small abstraction around `onPatch` and `applyPatch`, attaches a patch listener to a tree and records all the patches.\n * Returns an recorder object with the following signature:\n *\n * Example:\n * ```ts\n * export interface IPatchRecorder {\n *      // the recorded patches\n *      patches: IJsonPatch[]\n *      // the inverse of the recorded patches\n *      inversePatches: IJsonPatch[]\n *      // true if currently recording\n *      recording: boolean\n *      // stop recording patches\n *      stop(): void\n *      // resume recording patches\n *      resume(): void\n *      // apply all the recorded patches on the given target (the original subject if omitted)\n *      replay(target?: IAnyStateTreeNode): void\n *      // reverse apply the recorded patches on the given target  (the original subject if omitted)\n *      // stops the recorder if not already stopped\n *      undo(): void\n * }\n * ```\n *\n * The optional filter function allows to skip recording certain patches.\n *\n * @param subject\n * @param filter\n * @returns\n */\nfunction recordPatches(subject, filter) {\n    // check all arguments\n    assertIsStateTreeNode(subject, 1);\n    var data = {\n        patches: [],\n        reversedInversePatches: []\n    };\n    // we will generate the immutable copy of patches on demand for public consumption\n    var publicData = {};\n    var disposer;\n    var recorder = {\n        get recording() {\n            return !!disposer;\n        },\n        get patches() {\n            if (!publicData.patches) {\n                publicData.patches = data.patches.slice();\n            }\n            return publicData.patches;\n        },\n        get reversedInversePatches() {\n            if (!publicData.reversedInversePatches) {\n                publicData.reversedInversePatches = data.reversedInversePatches.slice();\n            }\n            return publicData.reversedInversePatches;\n        },\n        get inversePatches() {\n            if (!publicData.inversePatches) {\n                publicData.inversePatches = data.reversedInversePatches.slice().reverse();\n            }\n            return publicData.inversePatches;\n        },\n        stop: function () {\n            if (disposer) {\n                disposer();\n                disposer = undefined;\n            }\n        },\n        resume: function () {\n            if (disposer)\n                return;\n            disposer = onPatch(subject, function (patch, inversePatch) {\n                // skip patches that are asked to be filtered if there's a filter in place\n                if (filter && !filter(patch, inversePatch, getRunningActionContext())) {\n                    return;\n                }\n                data.patches.push(patch);\n                data.reversedInversePatches.unshift(inversePatch);\n                // mark immutable public patches as dirty\n                publicData.patches = undefined;\n                publicData.inversePatches = undefined;\n                publicData.reversedInversePatches = undefined;\n            });\n        },\n        replay: function (target) {\n            applyPatch(target || subject, data.patches);\n        },\n        undo: function (target) {\n            applyPatch(target || subject, data.reversedInversePatches);\n        }\n    };\n    recorder.resume();\n    return recorder;\n}\n/**\n * The inverse of `unprotect`.\n *\n * @param target\n */\nfunction protect(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    var node = getStateTreeNode(target);\n    if (!node.isRoot)\n        throw fail$1(\"`protect` can only be invoked on root nodes\");\n    node.isProtectionEnabled = true;\n}\n/**\n * By default it is not allowed to directly modify a model. Models can only be modified through actions.\n * However, in some cases you don't care about the advantages (like replayability, traceability, etc) this yields.\n * For example because you are building a PoC or don't have any middleware attached to your tree.\n *\n * In that case you can disable this protection by calling `unprotect` on the root of your tree.\n *\n * Example:\n * ```ts\n * const Todo = types.model({\n *     done: false\n * }).actions(self => ({\n *     toggle() {\n *         self.done = !self.done\n *     }\n * }))\n *\n * const todo = Todo.create()\n * todo.done = true // throws!\n * todo.toggle() // OK\n * unprotect(todo)\n * todo.done = false // OK\n * ```\n */\nfunction unprotect(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    var node = getStateTreeNode(target);\n    if (!node.isRoot)\n        throw fail$1(\"`unprotect` can only be invoked on root nodes\");\n    node.isProtectionEnabled = false;\n}\n/**\n * Returns true if the object is in protected mode, @see protect\n */\nfunction isProtected(target) {\n    return getStateTreeNode(target).isProtected;\n}\n/**\n * Applies a snapshot to a given model instances. Patch and snapshot listeners will be invoked as usual.\n *\n * @param target\n * @param snapshot\n * @returns\n */\nfunction applySnapshot(target, snapshot) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).applySnapshot(snapshot);\n}\n/**\n * Calculates a snapshot from the given model instance. The snapshot will always reflect the latest state but use\n * structural sharing where possible. Doesn't require MobX transactions to be completed.\n *\n * @param target\n * @param applyPostProcess If true (the default) then postProcessSnapshot gets applied.\n * @returns\n */\nfunction getSnapshot(target, applyPostProcess) {\n    if (applyPostProcess === void 0) { applyPostProcess = true; }\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    var node = getStateTreeNode(target);\n    if (applyPostProcess)\n        return node.snapshot;\n    return freeze(node.type.getSnapshot(node, false));\n}\n/**\n * Given a model instance, returns `true` if the object has a parent, that is, is part of another object, map or array.\n *\n * @param target\n * @param depth How far should we look upward? 1 by default.\n * @returns\n */\nfunction hasParent(target, depth) {\n    if (depth === void 0) { depth = 1; }\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsNumber(depth, 2, 0);\n    var parent = getStateTreeNode(target).parent;\n    while (parent) {\n        if (--depth === 0)\n            return true;\n        parent = parent.parent;\n    }\n    return false;\n}\n/**\n * Returns the immediate parent of this object, or throws.\n *\n * Note that the immediate parent can be either an object, map or array, and\n * doesn't necessarily refer to the parent model.\n *\n * Please note that in child nodes access to the root is only possible\n * once the `afterAttach` hook has fired.\n *\n * @param target\n * @param depth How far should we look upward? 1 by default.\n * @returns\n */\nfunction getParent(target, depth) {\n    if (depth === void 0) { depth = 1; }\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsNumber(depth, 2, 0);\n    var d = depth;\n    var parent = getStateTreeNode(target).parent;\n    while (parent) {\n        if (--d === 0)\n            return parent.storedValue;\n        parent = parent.parent;\n    }\n    throw fail$1(\"Failed to find the parent of \" + getStateTreeNode(target) + \" at depth \" + depth);\n}\n/**\n * Given a model instance, returns `true` if the object has a parent of given type, that is, is part of another object, map or array\n *\n * @param target\n * @param type\n * @returns\n */\nfunction hasParentOfType(target, type) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsType(type, 2);\n    var parent = getStateTreeNode(target).parent;\n    while (parent) {\n        if (type.is(parent.storedValue))\n            return true;\n        parent = parent.parent;\n    }\n    return false;\n}\n/**\n * Returns the target's parent of a given type, or throws.\n *\n * @param target\n * @param type\n * @returns\n */\nfunction getParentOfType(target, type) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsType(type, 2);\n    var parent = getStateTreeNode(target).parent;\n    while (parent) {\n        if (type.is(parent.storedValue))\n            return parent.storedValue;\n        parent = parent.parent;\n    }\n    throw fail$1(\"Failed to find the parent of \" + getStateTreeNode(target) + \" of a given type\");\n}\n/**\n * Given an object in a model tree, returns the root object of that tree.\n *\n * Please note that in child nodes access to the root is only possible\n * once the `afterAttach` hook has fired.\n *\n * @param target\n * @returns\n */\nfunction getRoot(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).root.storedValue;\n}\n/**\n * Returns the path of the given object in the model tree\n *\n * @param target\n * @returns\n */\nfunction getPath(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).path;\n}\n/**\n * Returns the path of the given object as unescaped string array.\n *\n * @param target\n * @returns\n */\nfunction getPathParts(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return splitJsonPath(getStateTreeNode(target).path);\n}\n/**\n * Returns true if the given object is the root of a model tree.\n *\n * @param target\n * @returns\n */\nfunction isRoot(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).isRoot;\n}\n/**\n * Resolves a path relatively to a given object.\n * Returns undefined if no value can be found.\n *\n * @param target\n * @param path escaped json path\n * @returns\n */\nfunction resolvePath(target, path) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsString(path, 2);\n    var node = resolveNodeByPath(getStateTreeNode(target), path);\n    return node ? node.value : undefined;\n}\n/**\n * Resolves a model instance given a root target, the type and the identifier you are searching for.\n * Returns undefined if no value can be found.\n *\n * @param type\n * @param target\n * @param identifier\n * @returns\n */\nfunction resolveIdentifier(type, target, identifier) {\n    // check all arguments\n    assertIsType(type, 1);\n    assertIsStateTreeNode(target, 2);\n    assertIsValidIdentifier(identifier, 3);\n    var node = getStateTreeNode(target).root.identifierCache.resolve(type, normalizeIdentifier(identifier));\n    return node ? node.value : undefined;\n}\n/**\n * Returns the identifier of the target node.\n * This is the *string normalized* identifier, which might not match the type of the identifier attribute\n *\n * @param target\n * @returns\n */\nfunction getIdentifier(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).identifier;\n}\n/**\n * Tests if a reference is valid (pointing to an existing node and optionally if alive) and returns such reference if it the check passes,\n * else it returns undefined.\n *\n * @param getter Function to access the reference.\n * @param checkIfAlive true to also make sure the referenced node is alive (default), false to skip this check.\n * @returns\n */\nfunction tryReference(getter, checkIfAlive) {\n    if (checkIfAlive === void 0) { checkIfAlive = true; }\n    try {\n        var node = getter();\n        if (node === undefined || node === null) {\n            return undefined;\n        }\n        else if (isStateTreeNode(node)) {\n            if (!checkIfAlive) {\n                return node;\n            }\n            else {\n                return isAlive(node) ? node : undefined;\n            }\n        }\n        else {\n            throw fail$1(\"The reference to be checked is not one of node, null or undefined\");\n        }\n    }\n    catch (e) {\n        if (e instanceof InvalidReferenceError) {\n            return undefined;\n        }\n        throw e;\n    }\n}\n/**\n * Tests if a reference is valid (pointing to an existing node and optionally if alive) and returns if the check passes or not.\n *\n * @param getter Function to access the reference.\n * @param checkIfAlive true to also make sure the referenced node is alive (default), false to skip this check.\n * @returns\n */\nfunction isValidReference(getter, checkIfAlive) {\n    if (checkIfAlive === void 0) { checkIfAlive = true; }\n    try {\n        var node = getter();\n        if (node === undefined || node === null) {\n            return false;\n        }\n        else if (isStateTreeNode(node)) {\n            return checkIfAlive ? isAlive(node) : true;\n        }\n        else {\n            throw fail$1(\"The reference to be checked is not one of node, null or undefined\");\n        }\n    }\n    catch (e) {\n        if (e instanceof InvalidReferenceError) {\n            return false;\n        }\n        throw e;\n    }\n}\n/**\n * Try to resolve a given path relative to a given node.\n *\n * @param target\n * @param path\n * @returns\n */\nfunction tryResolve(target, path) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsString(path, 2);\n    var node = resolveNodeByPath(getStateTreeNode(target), path, false);\n    if (node === undefined)\n        return undefined;\n    try {\n        return node.value;\n    }\n    catch (e) {\n        // For what ever reason not resolvable (e.g. totally not existing path, or value that cannot be fetched)\n        // see test / issue: 'try resolve doesn't work #686'\n        return undefined;\n    }\n}\n/**\n * Given two state tree nodes that are part of the same tree,\n * returns the shortest jsonpath needed to navigate from the one to the other\n *\n * @param base\n * @param target\n * @returns\n */\nfunction getRelativePath(base, target) {\n    // check all arguments\n    assertIsStateTreeNode(base, 1);\n    assertIsStateTreeNode(target, 2);\n    return getRelativePathBetweenNodes(getStateTreeNode(base), getStateTreeNode(target));\n}\n/**\n * Returns a deep copy of the given state tree node as new tree.\n * Short hand for `snapshot(x) = getType(x).create(getSnapshot(x))`\n *\n * _Tip: clone will create a literal copy, including the same identifiers. To modify identifiers etc during cloning, don't use clone but take a snapshot of the tree, modify it, and create new instance_\n *\n * @param source\n * @param keepEnvironment indicates whether the clone should inherit the same environment (`true`, the default), or not have an environment (`false`). If an object is passed in as second argument, that will act as the environment for the cloned tree.\n * @returns\n */\nfunction clone(source, keepEnvironment) {\n    if (keepEnvironment === void 0) { keepEnvironment = true; }\n    // check all arguments\n    assertIsStateTreeNode(source, 1);\n    var node = getStateTreeNode(source);\n    return node.type.create(node.snapshot, keepEnvironment === true\n        ? node.root.environment\n        : keepEnvironment === false\n            ? undefined\n            : keepEnvironment); // it's an object or something else\n}\n/**\n * Removes a model element from the state tree, and let it live on as a new state tree\n */\nfunction detach(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    getStateTreeNode(target).detach();\n    return target;\n}\n/**\n * Removes a model element from the state tree, and mark it as end-of-life; the element should not be used anymore\n */\nfunction destroy(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    var node = getStateTreeNode(target);\n    if (node.isRoot)\n        node.die();\n    else\n        node.parent.removeChild(node.subpath);\n}\n/**\n * Returns true if the given state tree node is not killed yet.\n * This means that the node is still a part of a tree, and that `destroy`\n * has not been called. If a node is not alive anymore, the only thing one can do with it\n * is requesting it's last path and snapshot\n *\n * @param target\n * @returns\n */\nfunction isAlive(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).observableIsAlive;\n}\n/**\n * Use this utility to register a function that should be called whenever the\n * targeted state tree node is destroyed. This is a useful alternative to managing\n * cleanup methods yourself using the `beforeDestroy` hook.\n *\n * This methods returns the same disposer that was passed as argument.\n *\n * Example:\n * ```ts\n * const Todo = types.model({\n *   title: types.string\n * }).actions(self => ({\n *   afterCreate() {\n *     const autoSaveDisposer = reaction(\n *       () => getSnapshot(self),\n *       snapshot => sendSnapshotToServerSomehow(snapshot)\n *     )\n *     // stop sending updates to server if this\n *     // instance is destroyed\n *     addDisposer(self, autoSaveDisposer)\n *   }\n * }))\n * ```\n *\n * @param target\n * @param disposer\n * @returns The same disposer that was passed as argument\n */\nfunction addDisposer(target, disposer) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsFunction(disposer, 2);\n    var node = getStateTreeNode(target);\n    node.addDisposer(disposer);\n    return disposer;\n}\n/**\n * Returns the environment of the current state tree. For more info on environments,\n * see [Dependency injection](https://github.com/mobxjs/mobx-state-tree#dependency-injection)\n *\n * Please note that in child nodes access to the root is only possible\n * once the `afterAttach` hook has fired\n *\n * Returns an empty environment if the tree wasn't initialized with an environment\n *\n * @param target\n * @returns\n */\nfunction getEnv(target) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    var node = getStateTreeNode(target);\n    var env = node.root.environment;\n    if (!env)\n        return EMPTY_OBJECT;\n    return env;\n}\n/**\n * Performs a depth first walk through a tree.\n */\nfunction walk(target, processor) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertIsFunction(processor, 2);\n    var node = getStateTreeNode(target);\n    // tslint:disable-next-line:no_unused-variable\n    node.getChildren().forEach(function (child) {\n        if (isStateTreeNode(child.storedValue))\n            walk(child.storedValue, processor);\n    });\n    processor(node.storedValue);\n}\n/**\n * Returns a reflection of the model type properties and name for either a model type or model node.\n *\n * @param typeOrNode\n * @returns\n */\nfunction getPropertyMembers(typeOrNode) {\n    var type;\n    if (isStateTreeNode(typeOrNode)) {\n        type = getType(typeOrNode);\n    }\n    else {\n        type = typeOrNode;\n    }\n    assertArg(type, function (t) { return isModelType(t); }, \"model type or model instance\", 1);\n    return {\n        name: type.name,\n        properties: __assign({}, type.properties)\n    };\n}\n/**\n * Returns a reflection of the model node, including name, properties, views, volatile and actions.\n *\n * @param target\n * @returns\n */\nfunction getMembers(target) {\n    var type = getStateTreeNode(target).type;\n    var reflected = __assign(__assign({}, getPropertyMembers(type)), { actions: [], volatile: [], views: [] });\n    var props = Object.getOwnPropertyNames(target);\n    props.forEach(function (key) {\n        if (key in reflected.properties)\n            return;\n        var descriptor = Object.getOwnPropertyDescriptor(target, key);\n        if (descriptor.get) {\n            if (isComputedProp(target, key))\n                reflected.views.push(key);\n            else\n                reflected.volatile.push(key);\n            return;\n        }\n        if (descriptor.value._isMSTAction === true)\n            reflected.actions.push(key);\n        else if (isObservableProp(target, key))\n            reflected.volatile.push(key);\n        else\n            reflected.views.push(key);\n    });\n    return reflected;\n}\n/**\n * Casts a node snapshot or instance type to an instance type so it can be assigned to a type instance.\n * Note that this is just a cast for the type system, this is, it won't actually convert a snapshot to an instance,\n * but just fool typescript into thinking so.\n * Either way, casting when outside an assignation operation won't compile.\n *\n * Example:\n * ```ts\n * const ModelA = types.model({\n *   n: types.number\n * }).actions(self => ({\n *   setN(aNumber: number) {\n *     self.n = aNumber\n *   }\n * }))\n *\n * const ModelB = types.model({\n *   innerModel: ModelA\n * }).actions(self => ({\n *   someAction() {\n *     // this will allow the compiler to assign a snapshot to the property\n *     self.innerModel = cast({ a: 5 })\n *   }\n * }))\n * ```\n *\n * @param snapshotOrInstance Snapshot or instance\n * @returns The same object casted as an instance\n */\nfunction cast(snapshotOrInstance) {\n    return snapshotOrInstance;\n}\n/**\n * Casts a node instance type to an snapshot type so it can be assigned to a type snapshot (e.g. to be used inside a create call).\n * Note that this is just a cast for the type system, this is, it won't actually convert an instance to a snapshot,\n * but just fool typescript into thinking so.\n *\n * Example:\n * ```ts\n * const ModelA = types.model({\n *   n: types.number\n * }).actions(self => ({\n *   setN(aNumber: number) {\n *     self.n = aNumber\n *   }\n * }))\n *\n * const ModelB = types.model({\n *   innerModel: ModelA\n * })\n *\n * const a = ModelA.create({ n: 5 });\n * // this will allow the compiler to use a model as if it were a snapshot\n * const b = ModelB.create({ innerModel: castToSnapshot(a)})\n * ```\n *\n * @param snapshotOrInstance Snapshot or instance\n * @returns The same object casted as an input (creation) snapshot\n */\nfunction castToSnapshot(snapshotOrInstance) {\n    return snapshotOrInstance;\n}\n/**\n * Casts a node instance type to a reference snapshot type so it can be assigned to a refernence snapshot (e.g. to be used inside a create call).\n * Note that this is just a cast for the type system, this is, it won't actually convert an instance to a refererence snapshot,\n * but just fool typescript into thinking so.\n *\n * Example:\n * ```ts\n * const ModelA = types.model({\n *   id: types.identifier,\n *   n: types.number\n * }).actions(self => ({\n *   setN(aNumber: number) {\n *     self.n = aNumber\n *   }\n * }))\n *\n * const ModelB = types.model({\n *   refA: types.reference(ModelA)\n * })\n *\n * const a = ModelA.create({ id: 'someId', n: 5 });\n * // this will allow the compiler to use a model as if it were a reference snapshot\n * const b = ModelB.create({ refA: castToReference(a)})\n * ```\n *\n * @param instance Instance\n * @returns The same object casted as an reference snapshot (string or number)\n */\nfunction castToReferenceSnapshot(instance) {\n    return instance;\n}\n/**\n * Returns the unique node id (not to be confused with the instance identifier) for a\n * given instance.\n * This id is a number that is unique for each instance.\n *\n * @export\n * @param target\n * @returns\n */\nfunction getNodeId(target) {\n    assertIsStateTreeNode(target, 1);\n    return getStateTreeNode(target).nodeId;\n}\n\n/**\n * @internal\n * @hidden\n */\nvar BaseNode = /** @class */ (function () {\n    function BaseNode(type, parent, subpath, environment) {\n        this.type = type;\n        this.environment = environment;\n        this._state = NodeLifeCycle.INITIALIZING;\n        this.environment = environment;\n        this.baseSetParent(parent, subpath);\n    }\n    Object.defineProperty(BaseNode.prototype, \"subpath\", {\n        get: function () {\n            return this._subpath;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"subpathUponDeath\", {\n        get: function () {\n            return this._subpathUponDeath;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"pathUponDeath\", {\n        get: function () {\n            return this._pathUponDeath;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"value\", {\n        get: function () {\n            return this.type.getValue(this);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"state\", {\n        get: function () {\n            return this._state;\n        },\n        set: function (val) {\n            var wasAlive = this.isAlive;\n            this._state = val;\n            var isAlive = this.isAlive;\n            if (this.aliveAtom && wasAlive !== isAlive) {\n                this.aliveAtom.reportChanged();\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BaseNode.prototype.fireInternalHook = function (name) {\n        if (this._hookSubscribers) {\n            this._hookSubscribers.emit(name, this, name);\n        }\n    };\n    BaseNode.prototype.registerHook = function (hook, hookHandler) {\n        if (!this._hookSubscribers) {\n            this._hookSubscribers = new EventHandlers();\n        }\n        return this._hookSubscribers.register(hook, hookHandler);\n    };\n    Object.defineProperty(BaseNode.prototype, \"parent\", {\n        get: function () {\n            return this._parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BaseNode.prototype.baseSetParent = function (parent, subpath) {\n        this._parent = parent;\n        this._subpath = subpath;\n        this._escapedSubpath = undefined; // regenerate when needed\n        if (this.pathAtom) {\n            this.pathAtom.reportChanged();\n        }\n    };\n    Object.defineProperty(BaseNode.prototype, \"path\", {\n        /*\n         * Returns (escaped) path representation as string\n         */\n        get: function () {\n            return this.getEscapedPath(true);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BaseNode.prototype.getEscapedPath = function (reportObserved) {\n        if (reportObserved) {\n            if (!this.pathAtom) {\n                this.pathAtom = createAtom(\"path\");\n            }\n            this.pathAtom.reportObserved();\n        }\n        if (!this.parent)\n            return \"\";\n        // regenerate escaped subpath if needed\n        if (this._escapedSubpath === undefined) {\n            this._escapedSubpath = !this._subpath ? \"\" : escapeJsonPath(this._subpath);\n        }\n        return this.parent.getEscapedPath(reportObserved) + \"/\" + this._escapedSubpath;\n    };\n    Object.defineProperty(BaseNode.prototype, \"isRoot\", {\n        get: function () {\n            return this.parent === null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"isAlive\", {\n        get: function () {\n            return this.state !== NodeLifeCycle.DEAD;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"isDetaching\", {\n        get: function () {\n            return this.state === NodeLifeCycle.DETACHING;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseNode.prototype, \"observableIsAlive\", {\n        get: function () {\n            if (!this.aliveAtom) {\n                this.aliveAtom = createAtom(\"alive\");\n            }\n            this.aliveAtom.reportObserved();\n            return this.isAlive;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    BaseNode.prototype.baseFinalizeCreation = function (whenFinalized) {\n        if (devMode()) {\n            if (!this.isAlive) {\n                // istanbul ignore next\n                throw fail(\"assertion failed: cannot finalize the creation of a node that is already dead\");\n            }\n        }\n        // goal: afterCreate hooks runs depth-first. After attach runs parent first, so on afterAttach the parent has completed already\n        if (this.state === NodeLifeCycle.CREATED) {\n            if (this.parent) {\n                if (this.parent.state !== NodeLifeCycle.FINALIZED) {\n                    // parent not ready yet, postpone\n                    return;\n                }\n                this.fireHook(Hook.afterAttach);\n            }\n            this.state = NodeLifeCycle.FINALIZED;\n            if (whenFinalized) {\n                whenFinalized();\n            }\n        }\n    };\n    BaseNode.prototype.baseFinalizeDeath = function () {\n        if (this._hookSubscribers) {\n            this._hookSubscribers.clearAll();\n        }\n        this._subpathUponDeath = this._subpath;\n        this._pathUponDeath = this.getEscapedPath(false);\n        this.baseSetParent(null, \"\");\n        this.state = NodeLifeCycle.DEAD;\n    };\n    BaseNode.prototype.baseAboutToDie = function () {\n        this.fireHook(Hook.beforeDestroy);\n    };\n    return BaseNode;\n}());\n\n/**\n * @internal\n * @hidden\n */\nvar ScalarNode = /** @class */ (function (_super) {\n    __extends(ScalarNode, _super);\n    function ScalarNode(simpleType, parent, subpath, environment, initialSnapshot) {\n        var _this = _super.call(this, simpleType, parent, subpath, environment) || this;\n        try {\n            _this.storedValue = simpleType.createNewInstance(initialSnapshot);\n        }\n        catch (e) {\n            // short-cut to die the instance, to avoid the snapshot computed starting to throw...\n            _this.state = NodeLifeCycle.DEAD;\n            throw e;\n        }\n        _this.state = NodeLifeCycle.CREATED;\n        // for scalar nodes there's no point in firing this event since it would fire on the constructor, before\n        // anybody can actually register for/listen to it\n        // this.fireHook(Hook.AfterCreate)\n        _this.finalizeCreation();\n        return _this;\n    }\n    Object.defineProperty(ScalarNode.prototype, \"root\", {\n        get: function () {\n            // future optimization: store root ref in the node and maintain it\n            if (!this.parent)\n                throw fail$1(\"This scalar node is not part of a tree\");\n            return this.parent.root;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ScalarNode.prototype.setParent = function (newParent, subpath) {\n        var parentChanged = this.parent !== newParent;\n        var subpathChanged = this.subpath !== subpath;\n        if (!parentChanged && !subpathChanged) {\n            return;\n        }\n        if (devMode()) {\n            if (!subpath) {\n                // istanbul ignore next\n                throw fail$1(\"assertion failed: subpath expected\");\n            }\n            if (!newParent) {\n                // istanbul ignore next\n                throw fail$1(\"assertion failed: parent expected\");\n            }\n            if (parentChanged) {\n                // istanbul ignore next\n                throw fail$1(\"assertion failed: scalar nodes cannot change their parent\");\n            }\n        }\n        this.environment = undefined; // use parent's\n        this.baseSetParent(this.parent, subpath);\n    };\n    Object.defineProperty(ScalarNode.prototype, \"snapshot\", {\n        get: function () {\n            return freeze(this.getSnapshot());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ScalarNode.prototype.getSnapshot = function () {\n        return this.type.getSnapshot(this);\n    };\n    ScalarNode.prototype.toString = function () {\n        var path = (this.isAlive ? this.path : this.pathUponDeath) || \"<root>\";\n        return this.type.name + \"@\" + path + (this.isAlive ? \"\" : \" [dead]\");\n    };\n    ScalarNode.prototype.die = function () {\n        if (!this.isAlive || this.state === NodeLifeCycle.DETACHING)\n            return;\n        this.aboutToDie();\n        this.finalizeDeath();\n    };\n    ScalarNode.prototype.finalizeCreation = function () {\n        this.baseFinalizeCreation();\n    };\n    ScalarNode.prototype.aboutToDie = function () {\n        this.baseAboutToDie();\n    };\n    ScalarNode.prototype.finalizeDeath = function () {\n        this.baseFinalizeDeath();\n    };\n    ScalarNode.prototype.fireHook = function (name) {\n        this.fireInternalHook(name);\n    };\n    __decorate([\n        action\n    ], ScalarNode.prototype, \"die\", null);\n    return ScalarNode;\n}(BaseNode));\n\nvar nextNodeId = 1;\nvar snapshotReactionOptions = {\n    onError: function (e) {\n        throw e;\n    }\n};\n/**\n * @internal\n * @hidden\n */\nvar ObjectNode = /** @class */ (function (_super) {\n    __extends(ObjectNode, _super);\n    function ObjectNode(complexType, parent, subpath, environment, initialValue) {\n        var _this = _super.call(this, complexType, parent, subpath, environment) || this;\n        _this.nodeId = ++nextNodeId;\n        _this.isProtectionEnabled = true;\n        _this._autoUnbox = true; // unboxing is disabled when reading child nodes\n        _this._isRunningAction = false; // only relevant for root\n        _this._hasSnapshotReaction = false;\n        _this._observableInstanceState = 0 /* UNINITIALIZED */;\n        _this._cachedInitialSnapshotCreated = false;\n        _this.unbox = _this.unbox.bind(_this);\n        _this._initialSnapshot = freeze(initialValue);\n        _this.identifierAttribute = complexType.identifierAttribute;\n        if (!parent) {\n            _this.identifierCache = new IdentifierCache();\n        }\n        _this._childNodes = complexType.initializeChildNodes(_this, _this._initialSnapshot);\n        // identifier can not be changed during lifecycle of a node\n        // so we safely can read it from initial snapshot\n        _this.identifier = null;\n        _this.unnormalizedIdentifier = null;\n        if (_this.identifierAttribute && _this._initialSnapshot) {\n            var id = _this._initialSnapshot[_this.identifierAttribute];\n            if (id === undefined) {\n                // try with the actual node if not (for optional identifiers)\n                var childNode = _this._childNodes[_this.identifierAttribute];\n                if (childNode) {\n                    id = childNode.value;\n                }\n            }\n            if (typeof id !== \"string\" && typeof id !== \"number\") {\n                throw fail$1(\"Instance identifier '\" + _this.identifierAttribute + \"' for type '\" + _this.type.name + \"' must be a string or a number\");\n            }\n            // normalize internal identifier to string\n            _this.identifier = normalizeIdentifier(id);\n            _this.unnormalizedIdentifier = id;\n        }\n        if (!parent) {\n            _this.identifierCache.addNodeToCache(_this);\n        }\n        else {\n            parent.root.identifierCache.addNodeToCache(_this);\n        }\n        return _this;\n    }\n    ObjectNode.prototype.applyPatches = function (patches) {\n        this.createObservableInstanceIfNeeded();\n        this._applyPatches(patches);\n    };\n    ObjectNode.prototype.applySnapshot = function (snapshot) {\n        this.createObservableInstanceIfNeeded();\n        this._applySnapshot(snapshot);\n    };\n    ObjectNode.prototype.createObservableInstanceIfNeeded = function () {\n        if (this._observableInstanceState === 0 /* UNINITIALIZED */) {\n            this.createObservableInstance();\n        }\n    };\n    ObjectNode.prototype.createObservableInstance = function () {\n        var e_1, _a;\n        if (devMode()) {\n            if (this.state !== NodeLifeCycle.INITIALIZING) {\n                // istanbul ignore next\n                throw fail$1(\"assertion failed: the creation of the observable instance must be done on the initializing phase\");\n            }\n        }\n        this._observableInstanceState = 1 /* CREATING */;\n        // make sure the parent chain is created as well\n        // array with parent chain from parent to child\n        var parentChain = [];\n        var parent = this.parent;\n        // for performance reasons we never go back further than the most direct\n        // uninitialized parent\n        // this is done to avoid traversing the whole tree to the root when using\n        // the same reference again\n        while (parent &&\n            parent._observableInstanceState === 0 /* UNINITIALIZED */) {\n            parentChain.unshift(parent);\n            parent = parent.parent;\n        }\n        try {\n            // initialize the uninitialized parent chain from parent to child\n            for (var parentChain_1 = __values(parentChain), parentChain_1_1 = parentChain_1.next(); !parentChain_1_1.done; parentChain_1_1 = parentChain_1.next()) {\n                var p = parentChain_1_1.value;\n                p.createObservableInstanceIfNeeded();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (parentChain_1_1 && !parentChain_1_1.done && (_a = parentChain_1.return)) _a.call(parentChain_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var type = this.type;\n        try {\n            this.storedValue = type.createNewInstance(this._childNodes);\n            this.preboot();\n            this._isRunningAction = true;\n            type.finalizeNewInstance(this, this.storedValue);\n        }\n        catch (e) {\n            // short-cut to die the instance, to avoid the snapshot computed starting to throw...\n            this.state = NodeLifeCycle.DEAD;\n            throw e;\n        }\n        finally {\n            this._isRunningAction = false;\n        }\n        this._observableInstanceState = 2 /* CREATED */;\n        // NOTE: we need to touch snapshot, because non-observable\n        // \"_observableInstanceState\" field was touched\n        invalidateComputed(this, \"snapshot\");\n        if (this.isRoot)\n            this._addSnapshotReaction();\n        this._childNodes = EMPTY_OBJECT;\n        this.state = NodeLifeCycle.CREATED;\n        this.fireHook(Hook.afterCreate);\n        this.finalizeCreation();\n    };\n    Object.defineProperty(ObjectNode.prototype, \"root\", {\n        get: function () {\n            var parent = this.parent;\n            return parent ? parent.root : this;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ObjectNode.prototype.clearParent = function () {\n        if (!this.parent)\n            return;\n        // detach if attached\n        this.fireHook(Hook.beforeDetach);\n        var previousState = this.state;\n        this.state = NodeLifeCycle.DETACHING;\n        var root = this.root;\n        var newEnv = root.environment;\n        var newIdCache = root.identifierCache.splitCache(this);\n        try {\n            this.parent.removeChild(this.subpath);\n            this.baseSetParent(null, \"\");\n            this.environment = newEnv;\n            this.identifierCache = newIdCache;\n        }\n        finally {\n            this.state = previousState;\n        }\n    };\n    ObjectNode.prototype.setParent = function (newParent, subpath) {\n        var parentChanged = newParent !== this.parent;\n        var subpathChanged = subpath !== this.subpath;\n        if (!parentChanged && !subpathChanged) {\n            return;\n        }\n        if (devMode()) {\n            if (!subpath) {\n                // istanbul ignore next\n                throw fail$1(\"assertion failed: subpath expected\");\n            }\n            if (!newParent) {\n                // istanbul ignore next\n                throw fail$1(\"assertion failed: new parent expected\");\n            }\n            if (this.parent && parentChanged) {\n                throw fail$1(\"A node cannot exists twice in the state tree. Failed to add \" + this + \" to path '\" + newParent.path + \"/\" + subpath + \"'.\");\n            }\n            if (!this.parent && newParent.root === this) {\n                throw fail$1(\"A state tree is not allowed to contain itself. Cannot assign \" + this + \" to path '\" + newParent.path + \"/\" + subpath + \"'\");\n            }\n            if (!this.parent &&\n                !!this.environment &&\n                this.environment !== newParent.root.environment) {\n                throw fail$1(\"A state tree cannot be made part of another state tree as long as their environments are different.\");\n            }\n        }\n        if (parentChanged) {\n            // attach to new parent\n            this.environment = undefined; // will use root's\n            newParent.root.identifierCache.mergeCache(this);\n            this.baseSetParent(newParent, subpath);\n            this.fireHook(Hook.afterAttach);\n        }\n        else if (subpathChanged) {\n            // moving to a new subpath on the same parent\n            this.baseSetParent(this.parent, subpath);\n        }\n    };\n    ObjectNode.prototype.fireHook = function (name) {\n        var _this = this;\n        this.fireInternalHook(name);\n        var fn = this.storedValue &&\n            typeof this.storedValue === \"object\" &&\n            this.storedValue[name];\n        if (typeof fn === \"function\") {\n            // we check for it to allow old mobx peer dependencies that don't have the method to work (even when still bugged)\n            if (_allowStateChangesInsideComputed) {\n                _allowStateChangesInsideComputed(function () {\n                    fn.apply(_this.storedValue);\n                });\n            }\n            else {\n                fn.apply(this.storedValue);\n            }\n        }\n    };\n    Object.defineProperty(ObjectNode.prototype, \"snapshot\", {\n        // advantage of using computed for a snapshot is that nicely respects transactions etc.\n        get: function () {\n            return freeze(this.getSnapshot());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    // NOTE: we use this method to get snapshot without creating @computed overhead\n    ObjectNode.prototype.getSnapshot = function () {\n        if (!this.isAlive)\n            return this._snapshotUponDeath;\n        return this._observableInstanceState === 2 /* CREATED */\n            ? this._getActualSnapshot()\n            : this._getCachedInitialSnapshot();\n    };\n    ObjectNode.prototype._getActualSnapshot = function () {\n        return this.type.getSnapshot(this);\n    };\n    ObjectNode.prototype._getCachedInitialSnapshot = function () {\n        if (!this._cachedInitialSnapshotCreated) {\n            var type = this.type;\n            var childNodes = this._childNodes;\n            var snapshot = this._initialSnapshot;\n            this._cachedInitialSnapshot = type.processInitialSnapshot(childNodes, snapshot);\n            this._cachedInitialSnapshotCreated = true;\n        }\n        return this._cachedInitialSnapshot;\n    };\n    ObjectNode.prototype.isRunningAction = function () {\n        if (this._isRunningAction)\n            return true;\n        if (this.isRoot)\n            return false;\n        return this.parent.isRunningAction();\n    };\n    ObjectNode.prototype.assertAlive = function (context) {\n        var livelinessChecking = getLivelinessChecking();\n        if (!this.isAlive && livelinessChecking !== \"ignore\") {\n            var error = this._getAssertAliveError(context);\n            switch (livelinessChecking) {\n                case \"error\":\n                    throw fail$1(error);\n                case \"warn\":\n                    warnError(error);\n            }\n        }\n    };\n    ObjectNode.prototype._getAssertAliveError = function (context) {\n        var escapedPath = this.getEscapedPath(false) || this.pathUponDeath || \"\";\n        var subpath = (context.subpath && escapeJsonPath(context.subpath)) || \"\";\n        var actionContext = context.actionContext || getCurrentActionContext();\n        // try to use a real action context if possible since it includes the action name\n        if (actionContext && actionContext.type !== \"action\" && actionContext.parentActionEvent) {\n            actionContext = actionContext.parentActionEvent;\n        }\n        var actionFullPath = \"\";\n        if (actionContext && actionContext.name != null) {\n            // try to use the context, and if it not available use the node one\n            var actionPath = (actionContext && actionContext.context && getPath(actionContext.context)) ||\n                escapedPath;\n            actionFullPath = actionPath + \".\" + actionContext.name + \"()\";\n        }\n        return \"You are trying to read or write to an object that is no longer part of a state tree. (Object type: '\" + this.type.name + \"', Path upon death: '\" + escapedPath + \"', Subpath: '\" + subpath + \"', Action: '\" + actionFullPath + \"'). Either detach nodes first, or don't use objects after removing / replacing them in the tree.\";\n    };\n    ObjectNode.prototype.getChildNode = function (subpath) {\n        this.assertAlive({\n            subpath: subpath\n        });\n        this._autoUnbox = false;\n        try {\n            return this._observableInstanceState === 2 /* CREATED */\n                ? this.type.getChildNode(this, subpath)\n                : this._childNodes[subpath];\n        }\n        finally {\n            this._autoUnbox = true;\n        }\n    };\n    ObjectNode.prototype.getChildren = function () {\n        this.assertAlive(EMPTY_OBJECT);\n        this._autoUnbox = false;\n        try {\n            return this._observableInstanceState === 2 /* CREATED */\n                ? this.type.getChildren(this)\n                : convertChildNodesToArray(this._childNodes);\n        }\n        finally {\n            this._autoUnbox = true;\n        }\n    };\n    ObjectNode.prototype.getChildType = function (propertyName) {\n        return this.type.getChildType(propertyName);\n    };\n    Object.defineProperty(ObjectNode.prototype, \"isProtected\", {\n        get: function () {\n            return this.root.isProtectionEnabled;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ObjectNode.prototype.assertWritable = function (context) {\n        this.assertAlive(context);\n        if (!this.isRunningAction() && this.isProtected) {\n            throw fail$1(\"Cannot modify '\" + this + \"', the object is protected and can only be modified by using an action.\");\n        }\n    };\n    ObjectNode.prototype.removeChild = function (subpath) {\n        this.type.removeChild(this, subpath);\n    };\n    // bound on the constructor\n    ObjectNode.prototype.unbox = function (childNode) {\n        if (!childNode)\n            return childNode;\n        this.assertAlive({\n            subpath: childNode.subpath || childNode.subpathUponDeath\n        });\n        return this._autoUnbox ? childNode.value : childNode;\n    };\n    ObjectNode.prototype.toString = function () {\n        var path = (this.isAlive ? this.path : this.pathUponDeath) || \"<root>\";\n        var identifier = this.identifier ? \"(id: \" + this.identifier + \")\" : \"\";\n        return this.type.name + \"@\" + path + identifier + (this.isAlive ? \"\" : \" [dead]\");\n    };\n    ObjectNode.prototype.finalizeCreation = function () {\n        var _this = this;\n        this.baseFinalizeCreation(function () {\n            var e_2, _a;\n            try {\n                for (var _b = __values(_this.getChildren()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var child = _c.value;\n                    child.finalizeCreation();\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            _this.fireInternalHook(Hook.afterCreationFinalization);\n        });\n    };\n    ObjectNode.prototype.detach = function () {\n        if (!this.isAlive)\n            throw fail$1(\"Error while detaching, node is not alive.\");\n        this.clearParent();\n    };\n    ObjectNode.prototype.preboot = function () {\n        var self = this;\n        this._applyPatches = createActionInvoker(this.storedValue, \"@APPLY_PATCHES\", function (patches) {\n            patches.forEach(function (patch) {\n                var parts = splitJsonPath(patch.path);\n                var node = resolveNodeByPathParts(self, parts.slice(0, -1));\n                node.applyPatchLocally(parts[parts.length - 1], patch);\n            });\n        });\n        this._applySnapshot = createActionInvoker(this.storedValue, \"@APPLY_SNAPSHOT\", function (snapshot) {\n            // if the snapshot is the same as the current one, avoid performing a reconcile\n            if (snapshot === self.snapshot)\n                return;\n            // else, apply it by calling the type logic\n            return self.type.applySnapshot(self, snapshot);\n        });\n        addHiddenFinalProp(this.storedValue, \"$treenode\", this);\n        addHiddenFinalProp(this.storedValue, \"toJSON\", toJSON);\n    };\n    ObjectNode.prototype.die = function () {\n        if (!this.isAlive || this.state === NodeLifeCycle.DETACHING)\n            return;\n        this.aboutToDie();\n        this.finalizeDeath();\n    };\n    ObjectNode.prototype.aboutToDie = function () {\n        if (this._observableInstanceState === 0 /* UNINITIALIZED */) {\n            return;\n        }\n        this.getChildren().forEach(function (node) {\n            node.aboutToDie();\n        });\n        // beforeDestroy should run before the disposers since else we could end up in a situation where\n        // a disposer added with addDisposer at this stage (beforeDestroy) is actually never released\n        this.baseAboutToDie();\n        this._internalEventsEmit(\"dispose\" /* Dispose */);\n        this._internalEventsClear(\"dispose\" /* Dispose */);\n    };\n    ObjectNode.prototype.finalizeDeath = function () {\n        // invariant: not called directly but from \"die\"\n        this.getChildren().forEach(function (node) {\n            node.finalizeDeath();\n        });\n        this.root.identifierCache.notifyDied(this);\n        // \"kill\" the computed prop and just store the last snapshot\n        var snapshot = this.snapshot;\n        this._snapshotUponDeath = snapshot;\n        this._internalEventsClearAll();\n        this.baseFinalizeDeath();\n    };\n    ObjectNode.prototype.onSnapshot = function (onChange) {\n        this._addSnapshotReaction();\n        return this._internalEventsRegister(\"snapshot\" /* Snapshot */, onChange);\n    };\n    ObjectNode.prototype.emitSnapshot = function (snapshot) {\n        this._internalEventsEmit(\"snapshot\" /* Snapshot */, snapshot);\n    };\n    ObjectNode.prototype.onPatch = function (handler) {\n        return this._internalEventsRegister(\"patch\" /* Patch */, handler);\n    };\n    ObjectNode.prototype.emitPatch = function (basePatch, source) {\n        if (this._internalEventsHasSubscribers(\"patch\" /* Patch */)) {\n            var localizedPatch = extend({}, basePatch, {\n                path: source.path.substr(this.path.length) + \"/\" + basePatch.path // calculate the relative path of the patch\n            });\n            var _a = __read(splitPatch(localizedPatch), 2), patch = _a[0], reversePatch = _a[1];\n            this._internalEventsEmit(\"patch\" /* Patch */, patch, reversePatch);\n        }\n        if (this.parent)\n            this.parent.emitPatch(basePatch, source);\n    };\n    ObjectNode.prototype.hasDisposer = function (disposer) {\n        return this._internalEventsHas(\"dispose\" /* Dispose */, disposer);\n    };\n    ObjectNode.prototype.addDisposer = function (disposer) {\n        if (!this.hasDisposer(disposer)) {\n            this._internalEventsRegister(\"dispose\" /* Dispose */, disposer, true);\n            return;\n        }\n        throw fail$1(\"cannot add a disposer when it is already registered for execution\");\n    };\n    ObjectNode.prototype.removeDisposer = function (disposer) {\n        if (!this._internalEventsHas(\"dispose\" /* Dispose */, disposer)) {\n            throw fail$1(\"cannot remove a disposer which was never registered for execution\");\n        }\n        this._internalEventsUnregister(\"dispose\" /* Dispose */, disposer);\n    };\n    ObjectNode.prototype.removeMiddleware = function (middleware) {\n        if (this.middlewares) {\n            var index = this.middlewares.indexOf(middleware);\n            if (index >= 0) {\n                this.middlewares.splice(index, 1);\n            }\n        }\n    };\n    ObjectNode.prototype.addMiddleWare = function (handler, includeHooks) {\n        var _this = this;\n        if (includeHooks === void 0) { includeHooks = true; }\n        var middleware = { handler: handler, includeHooks: includeHooks };\n        if (!this.middlewares)\n            this.middlewares = [middleware];\n        else\n            this.middlewares.push(middleware);\n        return function () {\n            _this.removeMiddleware(middleware);\n        };\n    };\n    ObjectNode.prototype.applyPatchLocally = function (subpath, patch) {\n        this.assertWritable({\n            subpath: subpath\n        });\n        this.createObservableInstanceIfNeeded();\n        this.type.applyPatchLocally(this, subpath, patch);\n    };\n    ObjectNode.prototype._addSnapshotReaction = function () {\n        var _this = this;\n        if (!this._hasSnapshotReaction) {\n            var snapshotDisposer = reaction(function () { return _this.snapshot; }, function (snapshot) { return _this.emitSnapshot(snapshot); }, snapshotReactionOptions);\n            this.addDisposer(snapshotDisposer);\n            this._hasSnapshotReaction = true;\n        }\n    };\n    // we proxy the methods to avoid creating an EventHandlers instance when it is not needed\n    ObjectNode.prototype._internalEventsHasSubscribers = function (event) {\n        return !!this._internalEvents && this._internalEvents.hasSubscribers(event);\n    };\n    ObjectNode.prototype._internalEventsRegister = function (event, eventHandler, atTheBeginning) {\n        if (atTheBeginning === void 0) { atTheBeginning = false; }\n        if (!this._internalEvents) {\n            this._internalEvents = new EventHandlers();\n        }\n        return this._internalEvents.register(event, eventHandler, atTheBeginning);\n    };\n    ObjectNode.prototype._internalEventsHas = function (event, eventHandler) {\n        return !!this._internalEvents && this._internalEvents.has(event, eventHandler);\n    };\n    ObjectNode.prototype._internalEventsUnregister = function (event, eventHandler) {\n        if (this._internalEvents) {\n            this._internalEvents.unregister(event, eventHandler);\n        }\n    };\n    ObjectNode.prototype._internalEventsEmit = function (event) {\n        var _a;\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (this._internalEvents) {\n            (_a = this._internalEvents).emit.apply(_a, __spread([event], args));\n        }\n    };\n    ObjectNode.prototype._internalEventsClear = function (event) {\n        if (this._internalEvents) {\n            this._internalEvents.clear(event);\n        }\n    };\n    ObjectNode.prototype._internalEventsClearAll = function () {\n        if (this._internalEvents) {\n            this._internalEvents.clearAll();\n        }\n    };\n    __decorate([\n        action\n    ], ObjectNode.prototype, \"createObservableInstance\", null);\n    __decorate([\n        computed\n    ], ObjectNode.prototype, \"snapshot\", null);\n    __decorate([\n        action\n    ], ObjectNode.prototype, \"detach\", null);\n    __decorate([\n        action\n    ], ObjectNode.prototype, \"die\", null);\n    return ObjectNode;\n}(BaseNode));\n\n/**\n * @internal\n * @hidden\n */\nvar TypeFlags;\n(function (TypeFlags) {\n    TypeFlags[TypeFlags[\"String\"] = 1] = \"String\";\n    TypeFlags[TypeFlags[\"Number\"] = 2] = \"Number\";\n    TypeFlags[TypeFlags[\"Boolean\"] = 4] = \"Boolean\";\n    TypeFlags[TypeFlags[\"Date\"] = 8] = \"Date\";\n    TypeFlags[TypeFlags[\"Literal\"] = 16] = \"Literal\";\n    TypeFlags[TypeFlags[\"Array\"] = 32] = \"Array\";\n    TypeFlags[TypeFlags[\"Map\"] = 64] = \"Map\";\n    TypeFlags[TypeFlags[\"Object\"] = 128] = \"Object\";\n    TypeFlags[TypeFlags[\"Frozen\"] = 256] = \"Frozen\";\n    TypeFlags[TypeFlags[\"Optional\"] = 512] = \"Optional\";\n    TypeFlags[TypeFlags[\"Reference\"] = 1024] = \"Reference\";\n    TypeFlags[TypeFlags[\"Identifier\"] = 2048] = \"Identifier\";\n    TypeFlags[TypeFlags[\"Late\"] = 4096] = \"Late\";\n    TypeFlags[TypeFlags[\"Refinement\"] = 8192] = \"Refinement\";\n    TypeFlags[TypeFlags[\"Union\"] = 16384] = \"Union\";\n    TypeFlags[TypeFlags[\"Null\"] = 32768] = \"Null\";\n    TypeFlags[TypeFlags[\"Undefined\"] = 65536] = \"Undefined\";\n    TypeFlags[TypeFlags[\"Integer\"] = 131072] = \"Integer\";\n    TypeFlags[TypeFlags[\"Custom\"] = 262144] = \"Custom\";\n    TypeFlags[TypeFlags[\"SnapshotProcessor\"] = 524288] = \"SnapshotProcessor\";\n})(TypeFlags || (TypeFlags = {}));\n/**\n * @internal\n * @hidden\n */\nvar cannotDetermineSubtype = \"cannotDetermine\";\n/**\n * A base type produces a MST node (Node in the state tree)\n *\n * @internal\n * @hidden\n */\nvar BaseType = /** @class */ (function () {\n    function BaseType(name) {\n        this.isType = true;\n        this.name = name;\n    }\n    BaseType.prototype.create = function (snapshot, environment) {\n        typecheckInternal(this, snapshot);\n        return this.instantiate(null, \"\", environment, snapshot).value;\n    };\n    BaseType.prototype.getSnapshot = function (node, applyPostProcess) {\n        // istanbul ignore next\n        throw fail$1(\"unimplemented method\");\n    };\n    BaseType.prototype.isAssignableFrom = function (type) {\n        return type === this;\n    };\n    BaseType.prototype.validate = function (value, context) {\n        var node = getStateTreeNodeSafe(value);\n        if (node) {\n            var valueType = getType(value);\n            return this.isAssignableFrom(valueType)\n                ? typeCheckSuccess()\n                : typeCheckFailure(context, value);\n            // it is tempting to compare snapshots, but in that case we should always clone on assignments...\n        }\n        return this.isValidSnapshot(value, context);\n    };\n    BaseType.prototype.is = function (thing) {\n        return this.validate(thing, [{ path: \"\", type: this }]).length === 0;\n    };\n    Object.defineProperty(BaseType.prototype, \"Type\", {\n        get: function () {\n            // istanbul ignore next\n            throw fail$1(\"Factory.Type should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.Type`\");\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseType.prototype, \"TypeWithoutSTN\", {\n        get: function () {\n            // istanbul ignore next\n            throw fail$1(\"Factory.TypeWithoutSTN should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.TypeWithoutSTN`\");\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseType.prototype, \"SnapshotType\", {\n        get: function () {\n            // istanbul ignore next\n            throw fail$1(\"Factory.SnapshotType should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.SnapshotType`\");\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BaseType.prototype, \"CreationType\", {\n        get: function () {\n            // istanbul ignore next\n            throw fail$1(\"Factory.CreationType should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.CreationType`\");\n        },\n        enumerable: false,\n        configurable: true\n    });\n    __decorate([\n        action\n    ], BaseType.prototype, \"create\", null);\n    return BaseType;\n}());\n/**\n * A complex type produces a MST node (Node in the state tree)\n *\n * @internal\n * @hidden\n */\nvar ComplexType = /** @class */ (function (_super) {\n    __extends(ComplexType, _super);\n    function ComplexType(name) {\n        return _super.call(this, name) || this;\n    }\n    ComplexType.prototype.create = function (snapshot, environment) {\n        if (snapshot === void 0) { snapshot = this.getDefaultSnapshot(); }\n        return _super.prototype.create.call(this, snapshot, environment);\n    };\n    ComplexType.prototype.getValue = function (node) {\n        node.createObservableInstanceIfNeeded();\n        return node.storedValue;\n    };\n    ComplexType.prototype.tryToReconcileNode = function (current, newValue) {\n        if (current.isDetaching)\n            return false;\n        if (current.snapshot === newValue) {\n            // newValue is the current snapshot of the node, noop\n            return true;\n        }\n        if (isStateTreeNode(newValue) && getStateTreeNode(newValue) === current) {\n            // the current node is the same as the new one\n            return true;\n        }\n        if (current.type === this &&\n            isMutable(newValue) &&\n            !isStateTreeNode(newValue) &&\n            (!current.identifierAttribute ||\n                current.identifier ===\n                    normalizeIdentifier(newValue[current.identifierAttribute]))) {\n            // the newValue has no node, so can be treated like a snapshot\n            // we can reconcile\n            current.applySnapshot(newValue);\n            return true;\n        }\n        return false;\n    };\n    ComplexType.prototype.reconcile = function (current, newValue, parent, subpath) {\n        var nodeReconciled = this.tryToReconcileNode(current, newValue);\n        if (nodeReconciled) {\n            current.setParent(parent, subpath);\n            return current;\n        }\n        // current node cannot be recycled in any way\n        current.die(); // noop if detaching\n        // attempt to reuse the new one\n        if (isStateTreeNode(newValue) && this.isAssignableFrom(getType(newValue))) {\n            // newValue is a Node as well, move it here..\n            var newNode = getStateTreeNode(newValue);\n            newNode.setParent(parent, subpath);\n            return newNode;\n        }\n        // nothing to do, we have to create a new node\n        return this.instantiate(parent, subpath, undefined, newValue);\n    };\n    ComplexType.prototype.getSubTypes = function () {\n        return null;\n    };\n    __decorate([\n        action\n    ], ComplexType.prototype, \"create\", null);\n    return ComplexType;\n}(BaseType));\n/**\n * @internal\n * @hidden\n */\nvar SimpleType = /** @class */ (function (_super) {\n    __extends(SimpleType, _super);\n    function SimpleType() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    SimpleType.prototype.createNewInstance = function (snapshot) {\n        return snapshot;\n    };\n    SimpleType.prototype.getValue = function (node) {\n        // if we ever find a case where scalar nodes can be accessed without iterating through its parent\n        // uncomment this to make sure the parent chain is created when this is accessed\n        // if (node.parent) {\n        //     node.parent.createObservableInstanceIfNeeded()\n        // }\n        return node.storedValue;\n    };\n    SimpleType.prototype.getSnapshot = function (node) {\n        return node.storedValue;\n    };\n    SimpleType.prototype.reconcile = function (current, newValue, parent, subpath) {\n        // reconcile only if type and value are still the same, and only if the node is not detaching\n        if (!current.isDetaching && current.type === this && current.storedValue === newValue) {\n            return current;\n        }\n        var res = this.instantiate(parent, subpath, undefined, newValue);\n        current.die(); // noop if detaching\n        return res;\n    };\n    SimpleType.prototype.getSubTypes = function () {\n        return null;\n    };\n    return SimpleType;\n}(BaseType));\n/**\n * Returns if a given value represents a type.\n *\n * @param value Value to check.\n * @returns `true` if the value is a type.\n */\nfunction isType(value) {\n    return typeof value === \"object\" && value && value.isType === true;\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertIsType(type, argNumber) {\n    assertArg(type, isType, \"mobx-state-tree type\", argNumber);\n}\n\nvar runningActions = new Map();\n/**\n * Note: Consider migrating to `createActionTrackingMiddleware2`, it is easier to use.\n *\n * Convenience utility to create action based middleware that supports async processes more easily.\n * All hooks are called for both synchronous and asynchronous actions. Except that either `onSuccess` or `onFail` is called\n *\n * The create middleware tracks the process of an action (assuming it passes the `filter`).\n * `onResume` can return any value, which will be passed as second argument to any other hook. This makes it possible to keep state during a process.\n *\n * See the `atomic` middleware for an example\n *\n * @param hooks\n * @returns\n */\nfunction createActionTrackingMiddleware(hooks) {\n    return function actionTrackingMiddleware(call, next, abort) {\n        switch (call.type) {\n            case \"action\": {\n                if (!hooks.filter || hooks.filter(call) === true) {\n                    var context = hooks.onStart(call);\n                    hooks.onResume(call, context);\n                    runningActions.set(call.id, {\n                        call: call,\n                        context: context,\n                        async: false\n                    });\n                    try {\n                        var res = next(call);\n                        hooks.onSuspend(call, context);\n                        if (runningActions.get(call.id).async === false) {\n                            runningActions.delete(call.id);\n                            hooks.onSuccess(call, context, res);\n                        }\n                        return res;\n                    }\n                    catch (e) {\n                        runningActions.delete(call.id);\n                        hooks.onFail(call, context, e);\n                        throw e;\n                    }\n                }\n                else {\n                    return next(call);\n                }\n            }\n            case \"flow_spawn\": {\n                var root = runningActions.get(call.rootId);\n                root.async = true;\n                return next(call);\n            }\n            case \"flow_resume\":\n            case \"flow_resume_error\": {\n                var root = runningActions.get(call.rootId);\n                hooks.onResume(call, root.context);\n                try {\n                    return next(call);\n                }\n                finally {\n                    hooks.onSuspend(call, root.context);\n                }\n            }\n            case \"flow_throw\": {\n                var root = runningActions.get(call.rootId);\n                runningActions.delete(call.rootId);\n                hooks.onFail(call, root.context, call.args[0]);\n                return next(call);\n            }\n            case \"flow_return\": {\n                var root = runningActions.get(call.rootId);\n                runningActions.delete(call.rootId);\n                hooks.onSuccess(call, root.context, call.args[0]);\n                return next(call);\n            }\n        }\n    };\n}\n\nvar RunningAction = /** @class */ (function () {\n    function RunningAction(hooks, call) {\n        this.hooks = hooks;\n        this.call = call;\n        this.flowsPending = 0;\n        this.running = true;\n        if (hooks) {\n            hooks.onStart(call);\n        }\n    }\n    RunningAction.prototype.finish = function (error) {\n        if (this.running) {\n            this.running = false;\n            if (this.hooks) {\n                this.hooks.onFinish(this.call, error);\n            }\n        }\n    };\n    RunningAction.prototype.incFlowsPending = function () {\n        this.flowsPending++;\n    };\n    RunningAction.prototype.decFlowsPending = function () {\n        this.flowsPending--;\n    };\n    Object.defineProperty(RunningAction.prototype, \"hasFlowsPending\", {\n        get: function () {\n            return this.flowsPending > 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return RunningAction;\n}());\n/**\n * Convenience utility to create action based middleware that supports async processes more easily.\n * The flow is like this:\n * - for each action: if filter passes -> `onStart` -> (inner actions recursively) -> `onFinish`\n *\n * Example: if we had an action `a` that called inside an action `b1`, then `b2` the flow would be:\n * - `filter(a)`\n * - `onStart(a)`\n *   - `filter(b1)`\n *   - `onStart(b1)`\n *   - `onFinish(b1)`\n *   - `filter(b2)`\n *   - `onStart(b2)`\n *   - `onFinish(b2)`\n * - `onFinish(a)`\n *\n * The flow is the same no matter if the actions are sync or async.\n *\n * See the `atomic` middleware for an example\n *\n * @param hooks\n * @returns\n */\nfunction createActionTrackingMiddleware2(middlewareHooks) {\n    var runningActions = new WeakMap();\n    return function actionTrackingMiddleware(call, next) {\n        // find parentRunningAction\n        var parentRunningAction = call.parentActionEvent\n            ? runningActions.get(call.parentActionEvent)\n            : undefined;\n        if (call.type === \"action\") {\n            var newCall = __assign(__assign({}, call), { \n                // make a shallow copy of the parent action env\n                env: parentRunningAction && parentRunningAction.call.env, parentCall: parentRunningAction && parentRunningAction.call });\n            var passesFilter = !middlewareHooks.filter || middlewareHooks.filter(newCall);\n            var hooks = passesFilter ? middlewareHooks : undefined;\n            var runningAction = new RunningAction(hooks, newCall);\n            runningActions.set(call, runningAction);\n            var res = void 0;\n            try {\n                res = next(call);\n            }\n            catch (e) {\n                runningAction.finish(e);\n                throw e;\n            }\n            if (!runningAction.hasFlowsPending) {\n                // sync action finished\n                runningAction.finish();\n            }\n            return res;\n        }\n        else {\n            if (!parentRunningAction) {\n                return next(call);\n            }\n            switch (call.type) {\n                case \"flow_spawn\": {\n                    parentRunningAction.incFlowsPending();\n                    return next(call);\n                }\n                case \"flow_resume\":\n                case \"flow_resume_error\": {\n                    return next(call);\n                }\n                case \"flow_throw\": {\n                    var error = call.args[0];\n                    try {\n                        return next(call);\n                    }\n                    finally {\n                        parentRunningAction.decFlowsPending();\n                        if (!parentRunningAction.hasFlowsPending) {\n                            parentRunningAction.finish(error);\n                        }\n                    }\n                }\n                case \"flow_return\": {\n                    try {\n                        return next(call);\n                    }\n                    finally {\n                        parentRunningAction.decFlowsPending();\n                        if (!parentRunningAction.hasFlowsPending) {\n                            parentRunningAction.finish();\n                        }\n                    }\n                }\n            }\n        }\n    };\n}\n\nfunction serializeArgument(node, actionName, index, arg) {\n    if (arg instanceof Date)\n        return { $MST_DATE: arg.getTime() };\n    if (isPrimitive(arg))\n        return arg;\n    // We should not serialize MST nodes, even if we can, because we don't know if the receiving party can handle a raw snapshot instead of an\n    // MST type instance. So if one wants to serialize a MST node that was pass in, either explitly pass: 1: an id, 2: a (relative) path, 3: a snapshot\n    if (isStateTreeNode(arg))\n        return serializeTheUnserializable(\"[MSTNode: \" + getType(arg).name + \"]\");\n    if (typeof arg === \"function\")\n        return serializeTheUnserializable(\"[function]\");\n    if (typeof arg === \"object\" && !isPlainObject(arg) && !isArray(arg))\n        return serializeTheUnserializable(\"[object \" + ((arg && arg.constructor && arg.constructor.name) ||\n            \"Complex Object\") + \"]\");\n    try {\n        // Check if serializable, cycle free etc...\n        // MWE: there must be a better way....\n        JSON.stringify(arg); // or throws\n        return arg;\n    }\n    catch (e) {\n        return serializeTheUnserializable(\"\" + e);\n    }\n}\nfunction deserializeArgument(adm, value) {\n    if (value && typeof value === \"object\" && \"$MST_DATE\" in value)\n        return new Date(value[\"$MST_DATE\"]);\n    return value;\n}\nfunction serializeTheUnserializable(baseType) {\n    return {\n        $MST_UNSERIALIZABLE: true,\n        type: baseType\n    };\n}\n/**\n * Applies an action or a series of actions in a single MobX transaction.\n * Does not return any value\n * Takes an action description as produced by the `onAction` middleware.\n *\n * @param target\n * @param actions\n */\nfunction applyAction(target, actions) {\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    assertArg(actions, function (a) { return typeof a === \"object\"; }, \"object or array\", 2);\n    runInAction(function () {\n        asArray(actions).forEach(function (action) { return baseApplyAction(target, action); });\n    });\n}\nfunction baseApplyAction(target, action) {\n    var resolvedTarget = tryResolve(target, action.path || \"\");\n    if (!resolvedTarget)\n        throw fail$1(\"Invalid action path: \" + (action.path || \"\"));\n    var node = getStateTreeNode(resolvedTarget);\n    // Reserved functions\n    if (action.name === \"@APPLY_PATCHES\") {\n        return applyPatch.call(null, resolvedTarget, action.args[0]);\n    }\n    if (action.name === \"@APPLY_SNAPSHOT\") {\n        return applySnapshot.call(null, resolvedTarget, action.args[0]);\n    }\n    if (!(typeof resolvedTarget[action.name] === \"function\"))\n        throw fail$1(\"Action '\" + action.name + \"' does not exist in '\" + node.path + \"'\");\n    return resolvedTarget[action.name].apply(resolvedTarget, action.args ? action.args.map(function (v) { return deserializeArgument(node, v); }) : []);\n}\n/**\n * Small abstraction around `onAction` and `applyAction`, attaches an action listener to a tree and records all the actions emitted.\n * Returns an recorder object with the following signature:\n *\n * Example:\n * ```ts\n * export interface IActionRecorder {\n *      // the recorded actions\n *      actions: ISerializedActionCall[]\n *      // true if currently recording\n *      recording: boolean\n *      // stop recording actions\n *      stop(): void\n *      // resume recording actions\n *      resume(): void\n *      // apply all the recorded actions on the given object\n *      replay(target: IAnyStateTreeNode): void\n * }\n * ```\n *\n * The optional filter function allows to skip recording certain actions.\n *\n * @param subject\n * @returns\n */\nfunction recordActions(subject, filter) {\n    // check all arguments\n    assertIsStateTreeNode(subject, 1);\n    var actions = [];\n    var listener = function (call) {\n        var recordThis = filter ? filter(call, getRunningActionContext()) : true;\n        if (recordThis) {\n            actions.push(call);\n        }\n    };\n    var disposer;\n    var recorder = {\n        actions: actions,\n        get recording() {\n            return !!disposer;\n        },\n        stop: function () {\n            if (disposer) {\n                disposer();\n                disposer = undefined;\n            }\n        },\n        resume: function () {\n            if (disposer)\n                return;\n            disposer = onAction(subject, listener);\n        },\n        replay: function (target) {\n            applyAction(target, actions);\n        }\n    };\n    recorder.resume();\n    return recorder;\n}\n/**\n * Registers a function that will be invoked for each action that is called on the provided model instance, or to any of its children.\n * See [actions](https://github.com/mobxjs/mobx-state-tree#actions) for more details. onAction events are emitted only for the outermost called action in the stack.\n * Action can also be intercepted by middleware using addMiddleware to change the function call before it will be run.\n *\n * Not all action arguments might be serializable. For unserializable arguments, a struct like `{ $MST_UNSERIALIZABLE: true, type: \"someType\" }` will be generated.\n * MST Nodes are considered non-serializable as well (they could be serialized as there snapshot, but it is uncertain whether an replaying party will be able to handle such a non-instantiated snapshot).\n * Rather, when using `onAction` middleware, one should consider in passing arguments which are 1: an id, 2: a (relative) path, or 3: a snapshot. Instead of a real MST node.\n *\n * Example:\n * ```ts\n * const Todo = types.model({\n *   task: types.string\n * })\n *\n * const TodoStore = types.model({\n *   todos: types.array(Todo)\n * }).actions(self => ({\n *   add(todo) {\n *     self.todos.push(todo);\n *   }\n * }))\n *\n * const s = TodoStore.create({ todos: [] })\n *\n * let disposer = onAction(s, (call) => {\n *   console.log(call);\n * })\n *\n * s.add({ task: \"Grab a coffee\" })\n * // Logs: { name: \"add\", path: \"\", args: [{ task: \"Grab a coffee\" }] }\n * ```\n *\n * @param target\n * @param listener\n * @param attachAfter (default false) fires the listener *after* the action has executed instead of before.\n * @returns\n */\nfunction onAction(target, listener, attachAfter) {\n    if (attachAfter === void 0) { attachAfter = false; }\n    // check all arguments\n    assertIsStateTreeNode(target, 1);\n    if (devMode()) {\n        if (!isRoot(target))\n            warnError(\"Warning: Attaching onAction listeners to non root nodes is dangerous: No events will be emitted for actions initiated higher up in the tree.\");\n        if (!isProtected(target))\n            warnError(\"Warning: Attaching onAction listeners to non protected nodes is dangerous: No events will be emitted for direct modifications without action.\");\n    }\n    return addMiddleware(target, function handler(rawCall, next) {\n        if (rawCall.type === \"action\" && rawCall.id === rawCall.rootId) {\n            var sourceNode_1 = getStateTreeNode(rawCall.context);\n            var info = {\n                name: rawCall.name,\n                path: getRelativePathBetweenNodes(getStateTreeNode(target), sourceNode_1),\n                args: rawCall.args.map(function (arg, index) {\n                    return serializeArgument(sourceNode_1, rawCall.name, index, arg);\n                })\n            };\n            if (attachAfter) {\n                var res = next(rawCall);\n                listener(info);\n                return res;\n            }\n            else {\n                listener(info);\n                return next(rawCall);\n            }\n        }\n        else {\n            return next(rawCall);\n        }\n    });\n}\n\nvar nextActionId = 1;\nvar currentActionContext;\n/**\n * @internal\n * @hidden\n */\nfunction getCurrentActionContext() {\n    return currentActionContext;\n}\n/**\n * @internal\n * @hidden\n */\nfunction getNextActionId() {\n    return nextActionId++;\n}\n// TODO: optimize away entire action context if there is no middleware in tree?\n/**\n * @internal\n * @hidden\n */\nfunction runWithActionContext(context, fn) {\n    var node = getStateTreeNode(context.context);\n    if (context.type === \"action\") {\n        node.assertAlive({\n            actionContext: context\n        });\n    }\n    var baseIsRunningAction = node._isRunningAction;\n    node._isRunningAction = true;\n    var previousContext = currentActionContext;\n    currentActionContext = context;\n    try {\n        return runMiddleWares(node, context, fn);\n    }\n    finally {\n        currentActionContext = previousContext;\n        node._isRunningAction = baseIsRunningAction;\n    }\n}\n/**\n * @internal\n * @hidden\n */\nfunction getParentActionContext(parentContext) {\n    if (!parentContext)\n        return undefined;\n    if (parentContext.type === \"action\")\n        return parentContext;\n    return parentContext.parentActionEvent;\n}\n/**\n * @internal\n * @hidden\n */\nfunction createActionInvoker(target, name, fn) {\n    var res = function () {\n        var id = getNextActionId();\n        var parentContext = currentActionContext;\n        var parentActionContext = getParentActionContext(parentContext);\n        return runWithActionContext({\n            type: \"action\",\n            name: name,\n            id: id,\n            args: argsToArray(arguments),\n            context: target,\n            tree: getRoot(target),\n            rootId: parentContext ? parentContext.rootId : id,\n            parentId: parentContext ? parentContext.id : 0,\n            allParentIds: parentContext\n                ? __spread(parentContext.allParentIds, [parentContext.id]) : [],\n            parentEvent: parentContext,\n            parentActionEvent: parentActionContext\n        }, fn);\n    };\n    res._isMSTAction = true;\n    return res;\n}\n/**\n * Middleware can be used to intercept any action is invoked on the subtree where it is attached.\n * If a tree is protected (by default), this means that any mutation of the tree will pass through your middleware.\n *\n * For more details, see the [middleware docs](concepts/middleware.md)\n *\n * @param target Node to apply the middleware to.\n * @param middleware Middleware to apply.\n * @returns A callable function to dispose the middleware.\n */\nfunction addMiddleware(target, handler, includeHooks) {\n    if (includeHooks === void 0) { includeHooks = true; }\n    var node = getStateTreeNode(target);\n    if (devMode()) {\n        if (!node.isProtectionEnabled) {\n            warnError(\"It is recommended to protect the state tree before attaching action middleware, as otherwise it cannot be guaranteed that all changes are passed through middleware. See `protect`\");\n        }\n    }\n    return node.addMiddleWare(handler, includeHooks);\n}\n/**\n * Binds middleware to a specific action.\n *\n * Example:\n * ```ts\n * type.actions(self => {\n *   function takeA____() {\n *       self.toilet.donate()\n *       self.wipe()\n *       self.wipe()\n *       self.toilet.flush()\n *   }\n *   return {\n *     takeA____: decorate(atomic, takeA____)\n *   }\n * })\n * ```\n *\n * @param handler\n * @param fn\n * @param includeHooks\n * @returns The original function\n */\nfunction decorate(handler, fn, includeHooks) {\n    if (includeHooks === void 0) { includeHooks = true; }\n    var middleware = { handler: handler, includeHooks: includeHooks };\n    fn.$mst_middleware = fn.$mst_middleware || [];\n    fn.$mst_middleware.push(middleware);\n    return fn;\n}\nvar CollectedMiddlewares = /** @class */ (function () {\n    function CollectedMiddlewares(node, fn) {\n        this.arrayIndex = 0;\n        this.inArrayIndex = 0;\n        this.middlewares = [];\n        // we just push middleware arrays into an array of arrays to avoid making copies\n        if (fn.$mst_middleware) {\n            this.middlewares.push(fn.$mst_middleware);\n        }\n        var n = node;\n        // Find all middlewares. Optimization: cache this?\n        while (n) {\n            if (n.middlewares)\n                this.middlewares.push(n.middlewares);\n            n = n.parent;\n        }\n    }\n    Object.defineProperty(CollectedMiddlewares.prototype, \"isEmpty\", {\n        get: function () {\n            return this.middlewares.length <= 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CollectedMiddlewares.prototype.getNextMiddleware = function () {\n        var array = this.middlewares[this.arrayIndex];\n        if (!array)\n            return undefined;\n        var item = array[this.inArrayIndex++];\n        if (!item) {\n            this.arrayIndex++;\n            this.inArrayIndex = 0;\n            return this.getNextMiddleware();\n        }\n        return item;\n    };\n    return CollectedMiddlewares;\n}());\nfunction runMiddleWares(node, baseCall, originalFn) {\n    var middlewares = new CollectedMiddlewares(node, originalFn);\n    // Short circuit\n    if (middlewares.isEmpty)\n        return action(originalFn).apply(null, baseCall.args);\n    var result = null;\n    function runNextMiddleware(call) {\n        var middleware = middlewares.getNextMiddleware();\n        var handler = middleware && middleware.handler;\n        if (!handler) {\n            return action(originalFn).apply(null, call.args);\n        }\n        // skip hooks if asked to\n        if (!middleware.includeHooks && Hook[call.name]) {\n            return runNextMiddleware(call);\n        }\n        var nextInvoked = false;\n        function next(call2, callback) {\n            nextInvoked = true;\n            // the result can contain\n            // - the non manipulated return value from an action\n            // - the non manipulated abort value\n            // - one of the above but manipulated through the callback function\n            result = runNextMiddleware(call2);\n            if (callback) {\n                result = callback(result);\n            }\n        }\n        var abortInvoked = false;\n        function abort(value) {\n            abortInvoked = true;\n            // overwrite the result\n            // can be manipulated through middlewares earlier in the queue using the callback fn\n            result = value;\n        }\n        handler(call, next, abort);\n        if (devMode()) {\n            if (!nextInvoked && !abortInvoked) {\n                var node2 = getStateTreeNode(call.tree);\n                throw fail$1(\"Neither the next() nor the abort() callback within the middleware \" + handler.name + \" for the action: \\\"\" + call.name + \"\\\" on the node: \" + node2.type.name + \" was invoked.\");\n            }\n            else if (nextInvoked && abortInvoked) {\n                var node2 = getStateTreeNode(call.tree);\n                throw fail$1(\"The next() and abort() callback within the middleware \" + handler.name + \" for the action: \\\"\" + call.name + \"\\\" on the node: \" + node2.type.name + \" were invoked.\");\n            }\n        }\n        return result;\n    }\n    return runNextMiddleware(baseCall);\n}\n\n/**\n * Returns the currently executing MST action context, or undefined if none.\n */\nfunction getRunningActionContext() {\n    var current = getCurrentActionContext();\n    while (current && current.type !== \"action\") {\n        current = current.parentActionEvent;\n    }\n    return current;\n}\nfunction _isActionContextThisOrChildOf(actionContext, sameOrParent, includeSame) {\n    var parentId = typeof sameOrParent === \"number\" ? sameOrParent : sameOrParent.id;\n    var current = includeSame\n        ? actionContext\n        : actionContext.parentActionEvent;\n    while (current) {\n        if (current.id === parentId) {\n            return true;\n        }\n        current = current.parentActionEvent;\n    }\n    return false;\n}\n/**\n * Returns if the given action context is a parent of this action context.\n */\nfunction isActionContextChildOf(actionContext, parent) {\n    return _isActionContextThisOrChildOf(actionContext, parent, false);\n}\n/**\n * Returns if the given action context is this or a parent of this action context.\n */\nfunction isActionContextThisOrChildOf(actionContext, parentOrThis) {\n    return _isActionContextThisOrChildOf(actionContext, parentOrThis, true);\n}\n\nfunction safeStringify(value) {\n    try {\n        return JSON.stringify(value);\n    }\n    catch (e) {\n        // istanbul ignore next\n        return \"<Unserializable: \" + e + \">\";\n    }\n}\n/**\n * @internal\n * @hidden\n */\nfunction prettyPrintValue(value) {\n    return typeof value === \"function\"\n        ? \"<function\" + (value.name ? \" \" + value.name : \"\") + \">\"\n        : isStateTreeNode(value)\n            ? \"<\" + value + \">\"\n            : \"`\" + safeStringify(value) + \"`\";\n}\nfunction shortenPrintValue(valueInString) {\n    return valueInString.length < 280\n        ? valueInString\n        : valueInString.substring(0, 272) + \"......\" + valueInString.substring(valueInString.length - 8);\n}\nfunction toErrorString(error) {\n    var value = error.value;\n    var type = error.context[error.context.length - 1].type;\n    var fullPath = error.context\n        .map(function (_a) {\n        var path = _a.path;\n        return path;\n    })\n        .filter(function (path) { return path.length > 0; })\n        .join(\"/\");\n    var pathPrefix = fullPath.length > 0 ? \"at path \\\"/\" + fullPath + \"\\\" \" : \"\";\n    var currentTypename = isStateTreeNode(value)\n        ? \"value of type \" + getStateTreeNode(value).type.name + \":\"\n        : isPrimitive(value)\n            ? \"value\"\n            : \"snapshot\";\n    var isSnapshotCompatible = type && isStateTreeNode(value) && type.is(getStateTreeNode(value).snapshot);\n    return (\"\" + pathPrefix + currentTypename + \" \" + prettyPrintValue(value) + \" is not assignable \" + (type ? \"to type: `\" + type.name + \"`\" : \"\") +\n        (error.message ? \" (\" + error.message + \")\" : \"\") +\n        (type\n            ? isPrimitiveType(type) || isPrimitive(value)\n                ? \".\"\n                : \", expected an instance of `\" + type.name + \"` or a snapshot like `\" + type.describe() + \"` instead.\" +\n                    (isSnapshotCompatible\n                        ? \" (Note that a snapshot of the provided value is compatible with the targeted type)\"\n                        : \"\")\n            : \".\"));\n}\n/**\n * @internal\n * @hidden\n */\nfunction getContextForPath(context, path, type) {\n    return context.concat([{ path: path, type: type }]);\n}\n/**\n * @internal\n * @hidden\n */\nfunction typeCheckSuccess() {\n    return EMPTY_ARRAY;\n}\n/**\n * @internal\n * @hidden\n */\nfunction typeCheckFailure(context, value, message) {\n    return [{ context: context, value: value, message: message }];\n}\n/**\n * @internal\n * @hidden\n */\nfunction flattenTypeErrors(errors) {\n    return errors.reduce(function (a, i) { return a.concat(i); }, []);\n}\n// TODO; doublecheck: typecheck should only needed to be invoked from: type.create and array / map / value.property will change\n/**\n * @internal\n * @hidden\n */\nfunction typecheckInternal(type, value) {\n    // runs typeChecking if it is in dev-mode or through a process.env.ENABLE_TYPE_CHECK flag\n    if (isTypeCheckingEnabled()) {\n        typecheck(type, value);\n    }\n}\n/**\n * Run's the typechecker for the given type on the given value, which can be a snapshot or an instance.\n * Throws if the given value is not according the provided type specification.\n * Use this if you need typechecks even in a production build (by default all automatic runtime type checks will be skipped in production builds)\n *\n * @param type Type to check against.\n * @param value Value to be checked, either a snapshot or an instance.\n */\nfunction typecheck(type, value) {\n    var errors = type.validate(value, [{ path: \"\", type: type }]);\n    if (errors.length > 0) {\n        throw fail$1(validationErrorsToString(type, value, errors));\n    }\n}\nfunction validationErrorsToString(type, value, errors) {\n    if (errors.length === 0) {\n        return undefined;\n    }\n    return (\"Error while converting \" + shortenPrintValue(prettyPrintValue(value)) + \" to `\" + type.name + \"`:\\n\\n    \" + errors.map(toErrorString).join(\"\\n    \"));\n}\n\nvar identifierCacheId = 0;\n/**\n * @internal\n * @hidden\n */\nvar IdentifierCache = /** @class */ (function () {\n    function IdentifierCache() {\n        this.cacheId = identifierCacheId++;\n        // n.b. in cache all identifiers are normalized to strings\n        this.cache = observable.map();\n        // last time the cache (array) for a given time changed\n        // n.b. it is not really the time, but just an integer that gets increased after each modification to the array\n        this.lastCacheModificationPerId = observable.map();\n    }\n    IdentifierCache.prototype.updateLastCacheModificationPerId = function (identifier) {\n        var lcm = this.lastCacheModificationPerId.get(identifier);\n        // we start at 1 since 0 means no update since cache creation\n        this.lastCacheModificationPerId.set(identifier, lcm === undefined ? 1 : lcm + 1);\n    };\n    IdentifierCache.prototype.getLastCacheModificationPerId = function (identifier) {\n        var modificationId = this.lastCacheModificationPerId.get(identifier) || 0;\n        return this.cacheId + \"-\" + modificationId;\n    };\n    IdentifierCache.prototype.addNodeToCache = function (node, lastCacheUpdate) {\n        if (lastCacheUpdate === void 0) { lastCacheUpdate = true; }\n        if (node.identifierAttribute) {\n            var identifier = node.identifier;\n            if (!this.cache.has(identifier)) {\n                this.cache.set(identifier, observable.array([], mobxShallow));\n            }\n            var set = this.cache.get(identifier);\n            if (set.indexOf(node) !== -1)\n                throw fail$1(\"Already registered\");\n            set.push(node);\n            if (lastCacheUpdate) {\n                this.updateLastCacheModificationPerId(identifier);\n            }\n        }\n    };\n    IdentifierCache.prototype.mergeCache = function (node) {\n        var _this = this;\n        values(node.identifierCache.cache).forEach(function (nodes) {\n            return nodes.forEach(function (child) {\n                _this.addNodeToCache(child);\n            });\n        });\n    };\n    IdentifierCache.prototype.notifyDied = function (node) {\n        if (node.identifierAttribute) {\n            var id = node.identifier;\n            var set = this.cache.get(id);\n            if (set) {\n                set.remove(node);\n                // remove empty sets from cache\n                if (!set.length) {\n                    this.cache.delete(id);\n                }\n                this.updateLastCacheModificationPerId(node.identifier);\n            }\n        }\n    };\n    IdentifierCache.prototype.splitCache = function (node) {\n        var _this = this;\n        var res = new IdentifierCache();\n        var basePath = node.path;\n        entries(this.cache).forEach(function (_a) {\n            var _b = __read(_a, 2), id = _b[0], nodes = _b[1];\n            var modified = false;\n            for (var i = nodes.length - 1; i >= 0; i--) {\n                if (nodes[i].path.indexOf(basePath) === 0) {\n                    res.addNodeToCache(nodes[i], false); // no need to update lastUpdated since it is a whole new cache\n                    nodes.splice(i, 1);\n                    modified = true;\n                }\n            }\n            if (modified) {\n                _this.updateLastCacheModificationPerId(id);\n            }\n        });\n        return res;\n    };\n    IdentifierCache.prototype.has = function (type, identifier) {\n        var set = this.cache.get(identifier);\n        if (!set)\n            return false;\n        return set.some(function (candidate) { return type.isAssignableFrom(candidate.type); });\n    };\n    IdentifierCache.prototype.resolve = function (type, identifier) {\n        var set = this.cache.get(identifier);\n        if (!set)\n            return null;\n        var matches = set.filter(function (candidate) { return type.isAssignableFrom(candidate.type); });\n        switch (matches.length) {\n            case 0:\n                return null;\n            case 1:\n                return matches[0];\n            default:\n                throw fail$1(\"Cannot resolve a reference to type '\" + type.name + \"' with id: '\" + identifier + \"' unambigously, there are multiple candidates: \" + matches\n                    .map(function (n) { return n.path; })\n                    .join(\", \"));\n        }\n    };\n    return IdentifierCache;\n}());\n\n/**\n * @internal\n * @hidden\n */\nfunction createObjectNode(type, parent, subpath, environment, initialValue) {\n    var existingNode = getStateTreeNodeSafe(initialValue);\n    if (existingNode) {\n        if (existingNode.parent) {\n            // istanbul ignore next\n            throw fail$1(\"Cannot add an object to a state tree if it is already part of the same or another state tree. Tried to assign an object to '\" + (parent ? parent.path : \"\") + \"/\" + subpath + \"', but it lives already at '\" + existingNode.path + \"'\");\n        }\n        if (parent) {\n            existingNode.setParent(parent, subpath);\n        }\n        // else it already has no parent since it is a pre-requisite\n        return existingNode;\n    }\n    // not a node, a snapshot\n    return new ObjectNode(type, parent, subpath, environment, initialValue);\n}\n/**\n * @internal\n * @hidden\n */\nfunction createScalarNode(type, parent, subpath, environment, initialValue) {\n    return new ScalarNode(type, parent, subpath, environment, initialValue);\n}\n/**\n * @internal\n * @hidden\n */\nfunction isNode(value) {\n    return value instanceof ScalarNode || value instanceof ObjectNode;\n}\n\n/**\n * @internal\n * @hidden\n */\nvar NodeLifeCycle;\n(function (NodeLifeCycle) {\n    NodeLifeCycle[NodeLifeCycle[\"INITIALIZING\"] = 0] = \"INITIALIZING\";\n    NodeLifeCycle[NodeLifeCycle[\"CREATED\"] = 1] = \"CREATED\";\n    NodeLifeCycle[NodeLifeCycle[\"FINALIZED\"] = 2] = \"FINALIZED\";\n    NodeLifeCycle[NodeLifeCycle[\"DETACHING\"] = 3] = \"DETACHING\";\n    NodeLifeCycle[NodeLifeCycle[\"DEAD\"] = 4] = \"DEAD\"; // no coming back from this one\n})(NodeLifeCycle || (NodeLifeCycle = {}));\n/**\n * Returns true if the given value is a node in a state tree.\n * More precisely, that is, if the value is an instance of a\n * `types.model`, `types.array` or `types.map`.\n *\n * @param value\n * @returns true if the value is a state tree node.\n */\nfunction isStateTreeNode(value) {\n    return !!(value && value.$treenode);\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertIsStateTreeNode(value, argNumber) {\n    assertArg(value, isStateTreeNode, \"mobx-state-tree node\", argNumber);\n}\n/**\n * @internal\n * @hidden\n */\nfunction getStateTreeNode(value) {\n    if (!isStateTreeNode(value)) {\n        // istanbul ignore next\n        throw fail$1(\"Value \" + value + \" is no MST Node\");\n    }\n    return value.$treenode;\n}\n/**\n * @internal\n * @hidden\n */\nfunction getStateTreeNodeSafe(value) {\n    return (value && value.$treenode) || null;\n}\n/**\n * @internal\n * @hidden\n */\nfunction toJSON() {\n    return getStateTreeNode(this).snapshot;\n}\nvar doubleDot = function (_) { return \"..\"; };\n/**\n * @internal\n * @hidden\n */\nfunction getRelativePathBetweenNodes(base, target) {\n    // PRE condition target is (a child of) base!\n    if (base.root !== target.root) {\n        throw fail$1(\"Cannot calculate relative path: objects '\" + base + \"' and '\" + target + \"' are not part of the same object tree\");\n    }\n    var baseParts = splitJsonPath(base.path);\n    var targetParts = splitJsonPath(target.path);\n    var common = 0;\n    for (; common < baseParts.length; common++) {\n        if (baseParts[common] !== targetParts[common])\n            break;\n    }\n    // TODO: assert that no targetParts paths are \"..\", \".\" or \"\"!\n    return (baseParts.slice(common).map(doubleDot).join(\"/\") + joinJsonPath(targetParts.slice(common)));\n}\n/**\n * @internal\n * @hidden\n */\nfunction resolveNodeByPath(base, path, failIfResolveFails) {\n    if (failIfResolveFails === void 0) { failIfResolveFails = true; }\n    return resolveNodeByPathParts(base, splitJsonPath(path), failIfResolveFails);\n}\n/**\n * @internal\n * @hidden\n */\nfunction resolveNodeByPathParts(base, pathParts, failIfResolveFails) {\n    if (failIfResolveFails === void 0) { failIfResolveFails = true; }\n    var current = base;\n    for (var i = 0; i < pathParts.length; i++) {\n        var part = pathParts[i];\n        if (part === \"..\") {\n            current = current.parent;\n            if (current)\n                continue; // not everything has a parent\n        }\n        else if (part === \".\") {\n            continue;\n        }\n        else if (current) {\n            if (current instanceof ScalarNode) {\n                // check if the value of a scalar resolves to a state tree node (e.g. references)\n                // then we can continue resolving...\n                try {\n                    var value = current.value;\n                    if (isStateTreeNode(value)) {\n                        current = getStateTreeNode(value);\n                        // fall through\n                    }\n                }\n                catch (e) {\n                    if (!failIfResolveFails) {\n                        return undefined;\n                    }\n                    throw e;\n                }\n            }\n            if (current instanceof ObjectNode) {\n                var subType = current.getChildType(part);\n                if (subType) {\n                    current = current.getChildNode(part);\n                    if (current)\n                        continue;\n                }\n            }\n        }\n        if (failIfResolveFails)\n            throw fail$1(\"Could not resolve '\" + part + \"' in path '\" + (joinJsonPath(pathParts.slice(0, i)) || \"/\") + \"' while resolving '\" + joinJsonPath(pathParts) + \"'\");\n        else\n            return undefined;\n    }\n    return current;\n}\n/**\n * @internal\n * @hidden\n */\nfunction convertChildNodesToArray(childNodes) {\n    if (!childNodes)\n        return EMPTY_ARRAY;\n    var keys = Object.keys(childNodes);\n    if (!keys.length)\n        return EMPTY_ARRAY;\n    var result = new Array(keys.length);\n    keys.forEach(function (key, index) {\n        result[index] = childNodes[key];\n    });\n    return result;\n}\n\n// based on: https://github.com/mobxjs/mobx-utils/blob/master/src/async-action.ts\n/*\n    All contents of this file are deprecated.\n\n    The term `process` has been replaced with `flow` to avoid conflicts with the\n    global `process` object.\n\n    Refer to `flow.ts` for any further changes to this implementation.\n*/\nvar DEPRECATION_MESSAGE = \"See https://github.com/mobxjs/mobx-state-tree/issues/399 for more information. \" +\n    \"Note that the middleware event types starting with `process` now start with `flow`.\";\n/**\n * @hidden\n *\n * @deprecated has been renamed to `flow()`.\n * See https://github.com/mobxjs/mobx-state-tree/issues/399 for more information.\n * Note that the middleware event types starting with `process` now start with `flow`.\n *\n * @returns {Promise}\n */\nfunction process$1(asyncAction) {\n    deprecated(\"process\", \"`process()` has been renamed to `flow()`. \" + DEPRECATION_MESSAGE);\n    return flow(asyncAction);\n}\n\n/**\n * @internal\n * @hidden\n */\nvar EMPTY_ARRAY = Object.freeze([]);\n/**\n * @internal\n * @hidden\n */\nvar EMPTY_OBJECT = Object.freeze({});\n/**\n * @internal\n * @hidden\n */\nvar mobxShallow = typeof $mobx === \"string\" ? { deep: false } : { deep: false, proxy: false };\nObject.freeze(mobxShallow);\n/**\n * @internal\n * @hidden\n */\nfunction fail$1(message) {\n    if (message === void 0) { message = \"Illegal state\"; }\n    return new Error(\"[mobx-state-tree] \" + message);\n}\n/**\n * @internal\n * @hidden\n */\nfunction identity(_) {\n    return _;\n}\n/**\n * pollyfill (for IE) suggested in MDN:\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n * @internal\n * @hidden\n */\nvar isInteger = Number.isInteger ||\n    function (value) {\n        return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n    };\n/**\n * @internal\n * @hidden\n */\nfunction isArray(val) {\n    return Array.isArray(val) || isObservableArray(val);\n}\n/**\n * @internal\n * @hidden\n */\nfunction asArray(val) {\n    if (!val)\n        return EMPTY_ARRAY;\n    if (isArray(val))\n        return val;\n    return [val];\n}\n/**\n * @internal\n * @hidden\n */\nfunction extend(a) {\n    var b = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        b[_i - 1] = arguments[_i];\n    }\n    for (var i = 0; i < b.length; i++) {\n        var current = b[i];\n        for (var key in current)\n            a[key] = current[key];\n    }\n    return a;\n}\n/**\n * @internal\n * @hidden\n */\nfunction isPlainObject(value) {\n    if (value === null || typeof value !== \"object\")\n        return false;\n    var proto = Object.getPrototypeOf(value);\n    return proto === Object.prototype || proto === null;\n}\n/**\n * @internal\n * @hidden\n */\nfunction isMutable(value) {\n    return (value !== null &&\n        typeof value === \"object\" &&\n        !(value instanceof Date) &&\n        !(value instanceof RegExp));\n}\n/**\n * @internal\n * @hidden\n */\nfunction isPrimitive(value, includeDate) {\n    if (includeDate === void 0) { includeDate = true; }\n    if (value === null || value === undefined)\n        return true;\n    if (typeof value === \"string\" ||\n        typeof value === \"number\" ||\n        typeof value === \"boolean\" ||\n        (includeDate && value instanceof Date))\n        return true;\n    return false;\n}\n/**\n * @internal\n * @hidden\n * Freeze a value and return it (if not in production)\n */\nfunction freeze(value) {\n    if (!devMode())\n        return value;\n    return isPrimitive(value) || isObservableArray(value) ? value : Object.freeze(value);\n}\n/**\n * @internal\n * @hidden\n * Recursively freeze a value (if not in production)\n */\nfunction deepFreeze(value) {\n    if (!devMode())\n        return value;\n    freeze(value);\n    if (isPlainObject(value)) {\n        Object.keys(value).forEach(function (propKey) {\n            if (!isPrimitive(value[propKey]) &&\n                !Object.isFrozen(value[propKey])) {\n                deepFreeze(value[propKey]);\n            }\n        });\n    }\n    return value;\n}\n/**\n * @internal\n * @hidden\n */\nfunction isSerializable(value) {\n    return typeof value !== \"function\";\n}\n/**\n * @internal\n * @hidden\n */\nfunction addHiddenFinalProp(object, propName, value) {\n    Object.defineProperty(object, propName, {\n        enumerable: false,\n        writable: false,\n        configurable: true,\n        value: value\n    });\n}\n/**\n * @internal\n * @hidden\n */\nfunction addHiddenWritableProp(object, propName, value) {\n    Object.defineProperty(object, propName, {\n        enumerable: false,\n        writable: true,\n        configurable: true,\n        value: value\n    });\n}\n/**\n * @internal\n * @hidden\n */\nvar EventHandler = /** @class */ (function () {\n    function EventHandler() {\n        this.handlers = [];\n    }\n    Object.defineProperty(EventHandler.prototype, \"hasSubscribers\", {\n        get: function () {\n            return this.handlers.length > 0;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    EventHandler.prototype.register = function (fn, atTheBeginning) {\n        var _this = this;\n        if (atTheBeginning === void 0) { atTheBeginning = false; }\n        if (atTheBeginning) {\n            this.handlers.unshift(fn);\n        }\n        else {\n            this.handlers.push(fn);\n        }\n        return function () {\n            _this.unregister(fn);\n        };\n    };\n    EventHandler.prototype.has = function (fn) {\n        return this.handlers.indexOf(fn) >= 0;\n    };\n    EventHandler.prototype.unregister = function (fn) {\n        var index = this.handlers.indexOf(fn);\n        if (index >= 0) {\n            this.handlers.splice(index, 1);\n        }\n    };\n    EventHandler.prototype.clear = function () {\n        this.handlers.length = 0;\n    };\n    EventHandler.prototype.emit = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        // make a copy just in case it changes\n        var handlers = this.handlers.slice();\n        handlers.forEach(function (f) { return f.apply(void 0, __spread(args)); });\n    };\n    return EventHandler;\n}());\n/**\n * @internal\n * @hidden\n */\nvar EventHandlers = /** @class */ (function () {\n    function EventHandlers() {\n    }\n    EventHandlers.prototype.hasSubscribers = function (event) {\n        var handler = this.eventHandlers && this.eventHandlers[event];\n        return !!handler && handler.hasSubscribers;\n    };\n    EventHandlers.prototype.register = function (event, fn, atTheBeginning) {\n        if (atTheBeginning === void 0) { atTheBeginning = false; }\n        if (!this.eventHandlers) {\n            this.eventHandlers = {};\n        }\n        var handler = this.eventHandlers[event];\n        if (!handler) {\n            handler = this.eventHandlers[event] = new EventHandler();\n        }\n        return handler.register(fn, atTheBeginning);\n    };\n    EventHandlers.prototype.has = function (event, fn) {\n        var handler = this.eventHandlers && this.eventHandlers[event];\n        return !!handler && handler.has(fn);\n    };\n    EventHandlers.prototype.unregister = function (event, fn) {\n        var handler = this.eventHandlers && this.eventHandlers[event];\n        if (handler) {\n            handler.unregister(fn);\n        }\n    };\n    EventHandlers.prototype.clear = function (event) {\n        if (this.eventHandlers) {\n            delete this.eventHandlers[event];\n        }\n    };\n    EventHandlers.prototype.clearAll = function () {\n        this.eventHandlers = undefined;\n    };\n    EventHandlers.prototype.emit = function (event) {\n        var _a;\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var handler = this.eventHandlers && this.eventHandlers[event];\n        if (handler) {\n            (_a = handler).emit.apply(_a, __spread(args));\n        }\n    };\n    return EventHandlers;\n}());\n/**\n * @internal\n * @hidden\n */\nfunction argsToArray(args) {\n    var res = new Array(args.length);\n    for (var i = 0; i < args.length; i++)\n        res[i] = args[i];\n    return res;\n}\n/**\n * @internal\n * @hidden\n */\nfunction invalidateComputed(target, propName) {\n    var atom = getAtom(target, propName);\n    atom.trackAndCompute();\n}\n/**\n * @internal\n * @hidden\n */\nfunction stringStartsWith(str, beginning) {\n    return str.indexOf(beginning) === 0;\n}\n/**\n * @internal\n * @hidden\n */\nvar deprecated = function (id, message) {\n    // skip if running production\n    if (!devMode())\n        return;\n    // warn if hasn't been warned before\n    if (deprecated.ids && !deprecated.ids.hasOwnProperty(id)) {\n        warnError(\"Deprecation warning: \" + message);\n    }\n    // mark as warned to avoid duplicate warn message\n    if (deprecated.ids)\n        deprecated.ids[id] = true;\n};\ndeprecated.ids = {};\n/**\n * @internal\n * @hidden\n */\nfunction warnError(msg) {\n    console.warn(new Error(\"[mobx-state-tree] \" + msg));\n}\n/**\n * @internal\n * @hidden\n */\nfunction isTypeCheckingEnabled() {\n    return (devMode() ||\n        (typeof process !== \"undefined\" && process.env && process.env.ENABLE_TYPE_CHECK === \"true\"));\n}\n/**\n * @internal\n * @hidden\n */\nfunction devMode() {\n    return process.env.NODE_ENV !== \"production\";\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertArg(value, fn, typeName, argNumber) {\n    if (devMode()) {\n        if (!fn(value)) {\n            // istanbul ignore next\n            throw fail$1(\"expected \" + typeName + \" as argument \" + asArray(argNumber).join(\" or \") + \", got \" + value + \" instead\");\n        }\n    }\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertIsFunction(value, argNumber) {\n    assertArg(value, function (fn) { return typeof fn === \"function\"; }, \"function\", argNumber);\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertIsNumber(value, argNumber, min, max) {\n    assertArg(value, function (n) { return typeof n === \"number\"; }, \"number\", argNumber);\n    if (min !== undefined) {\n        assertArg(value, function (n) { return n >= min; }, \"number greater than \" + min, argNumber);\n    }\n    if (max !== undefined) {\n        assertArg(value, function (n) { return n <= max; }, \"number lesser than \" + max, argNumber);\n    }\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertIsString(value, argNumber, canBeEmpty) {\n    if (canBeEmpty === void 0) { canBeEmpty = true; }\n    assertArg(value, function (s) { return typeof s === \"string\"; }, \"string\", argNumber);\n    if (!canBeEmpty) {\n        assertArg(value, function (s) { return s !== \"\"; }, \"not empty string\", argNumber);\n    }\n}\n/**\n * @internal\n * @hidden\n */\nfunction setImmediateWithFallback(fn) {\n    if (typeof queueMicrotask === \"function\") {\n        queueMicrotask(fn);\n    }\n    else if (typeof setImmediate === \"function\") {\n        setImmediate(fn);\n    }\n    else {\n        setTimeout(fn, 1);\n    }\n}\n\n/**\n * See [asynchronous actions](concepts/async-actions.md).\n *\n * @returns The flow as a promise.\n */\nfunction flow(generator) {\n    return createFlowSpawner(generator.name, generator);\n}\n/**\n * @deprecated Not needed since TS3.6.\n * Used for TypeScript to make flows that return a promise return the actual promise result.\n *\n * @param val\n * @returns\n */\nfunction castFlowReturn(val) {\n    return val;\n}\n/**\n * @experimental\n * experimental api - might change on minor/patch releases\n *\n * Convert a promise-returning function to a generator-returning one.\n * This is intended to allow for usage of `yield*` in async actions to\n * retain the promise return type.\n *\n * Example:\n * ```ts\n * function getDataAsync(input: string): Promise<number> { ... }\n * const getDataGen = toGeneratorFunction(getDataAsync);\n *\n * const someModel.actions(self => ({\n *   someAction: flow(function*() {\n *     // value is typed as number\n *     const value = yield* getDataGen(\"input value\");\n *     ...\n *   })\n * }))\n * ```\n */\nfunction toGeneratorFunction(p) {\n    return function () {\n        var _i;\n        var args = [];\n        for (_i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, p.apply(void 0, __spread(args))];\n                case 1: return [2 /*return*/, (_a.sent())];\n            }\n        });\n    };\n}\n/**\n * @experimental\n * experimental api - might change on minor/patch releases\n *\n * Convert a promise to a generator yielding that promise\n * This is intended to allow for usage of `yield*` in async actions to\n * retain the promise return type.\n *\n * Example:\n * ```ts\n * function getDataAsync(input: string): Promise<number> { ... }\n *\n * const someModel.actions(self => ({\n *   someAction: flow(function*() {\n *     // value is typed as number\n *     const value = yield* toGenerator(getDataAsync(\"input value\"));\n *     ...\n *   })\n * }))\n * ```\n */\nfunction toGenerator(p) {\n    return __generator(this, function (_a) {\n        switch (_a.label) {\n            case 0: return [4 /*yield*/, p];\n            case 1: return [2 /*return*/, (_a.sent())];\n        }\n    });\n}\n/**\n * @internal\n * @hidden\n */\nfunction createFlowSpawner(name, generator) {\n    var spawner = function flowSpawner() {\n        // Implementation based on https://github.com/tj/co/blob/master/index.js\n        var runId = getNextActionId();\n        var parentContext = getCurrentActionContext();\n        if (!parentContext) {\n            throw fail$1(\"a mst flow must always have a parent context\");\n        }\n        var parentActionContext = getParentActionContext(parentContext);\n        if (!parentActionContext) {\n            throw fail$1(\"a mst flow must always have a parent action context\");\n        }\n        var contextBase = {\n            name: name,\n            id: runId,\n            tree: parentContext.tree,\n            context: parentContext.context,\n            parentId: parentContext.id,\n            allParentIds: __spread(parentContext.allParentIds, [parentContext.id]),\n            rootId: parentContext.rootId,\n            parentEvent: parentContext,\n            parentActionEvent: parentActionContext\n        };\n        var args = arguments;\n        function wrap(fn, type, arg) {\n            fn.$mst_middleware = spawner.$mst_middleware; // pick up any middleware attached to the flow\n            runWithActionContext(__assign(__assign({}, contextBase), { type: type, args: [arg] }), fn);\n        }\n        return new Promise(function (resolve, reject) {\n            var gen;\n            var init = function asyncActionInit() {\n                gen = generator.apply(null, arguments);\n                onFulfilled(undefined); // kick off the flow\n            };\n            init.$mst_middleware = spawner.$mst_middleware;\n            runWithActionContext(__assign(__assign({}, contextBase), { type: \"flow_spawn\", args: argsToArray(args) }), init);\n            function onFulfilled(res) {\n                var ret;\n                try {\n                    // prettier-ignore\n                    wrap(function (r) { ret = gen.next(r); }, \"flow_resume\", res);\n                }\n                catch (e) {\n                    // prettier-ignore\n                    setImmediateWithFallback(function () {\n                        wrap(function (r) { reject(e); }, \"flow_throw\", e);\n                    });\n                    return;\n                }\n                next(ret);\n                return;\n            }\n            function onRejected(err) {\n                var ret;\n                try {\n                    // prettier-ignore\n                    wrap(function (r) { ret = gen.throw(r); }, \"flow_resume_error\", err); // or yieldError?\n                }\n                catch (e) {\n                    // prettier-ignore\n                    setImmediateWithFallback(function () {\n                        wrap(function (r) { reject(e); }, \"flow_throw\", e);\n                    });\n                    return;\n                }\n                next(ret);\n            }\n            function next(ret) {\n                if (ret.done) {\n                    // prettier-ignore\n                    setImmediateWithFallback(function () {\n                        wrap(function (r) { resolve(r); }, \"flow_return\", ret.value);\n                    });\n                    return;\n                }\n                // TODO: support more type of values? See https://github.com/tj/co/blob/249bbdc72da24ae44076afd716349d2089b31c4c/index.js#L100\n                if (!ret.value || typeof ret.value.then !== \"function\") {\n                    // istanbul ignore next\n                    throw fail$1(\"Only promises can be yielded to `async`, got: \" + ret);\n                }\n                return ret.value.then(onFulfilled, onRejected);\n            }\n        });\n    };\n    return spawner;\n}\n\n/**\n * @internal\n * @hidden\n */\nfunction splitPatch(patch) {\n    if (!(\"oldValue\" in patch))\n        throw fail$1(\"Patches without `oldValue` field cannot be inversed\");\n    return [stripPatch(patch), invertPatch(patch)];\n}\n/**\n * @internal\n * @hidden\n */\nfunction stripPatch(patch) {\n    // strips `oldvalue` information from the patch, so that it becomes a patch conform the json-patch spec\n    // this removes the ability to undo the patch\n    switch (patch.op) {\n        case \"add\":\n            return { op: \"add\", path: patch.path, value: patch.value };\n        case \"remove\":\n            return { op: \"remove\", path: patch.path };\n        case \"replace\":\n            return { op: \"replace\", path: patch.path, value: patch.value };\n    }\n}\nfunction invertPatch(patch) {\n    switch (patch.op) {\n        case \"add\":\n            return {\n                op: \"remove\",\n                path: patch.path\n            };\n        case \"remove\":\n            return {\n                op: \"add\",\n                path: patch.path,\n                value: patch.oldValue\n            };\n        case \"replace\":\n            return {\n                op: \"replace\",\n                path: patch.path,\n                value: patch.oldValue\n            };\n    }\n}\n/**\n * Simple simple check to check it is a number.\n */\nfunction isNumber(x) {\n    return typeof x === \"number\";\n}\n/**\n * Escape slashes and backslashes.\n *\n * http://tools.ietf.org/html/rfc6901\n */\nfunction escapeJsonPath(path) {\n    if (isNumber(path) === true) {\n        return \"\" + path;\n    }\n    if (path.indexOf(\"/\") === -1 && path.indexOf(\"~\") === -1)\n        return path;\n    return path.replace(/~/g, \"~0\").replace(/\\//g, \"~1\");\n}\n/**\n * Unescape slashes and backslashes.\n */\nfunction unescapeJsonPath(path) {\n    return path.replace(/~1/g, \"/\").replace(/~0/g, \"~\");\n}\n/**\n * Generates a json-path compliant json path from path parts.\n *\n * @param path\n * @returns\n */\nfunction joinJsonPath(path) {\n    // `/` refers to property with an empty name, while `` refers to root itself!\n    if (path.length === 0)\n        return \"\";\n    var getPathStr = function (p) { return p.map(escapeJsonPath).join(\"/\"); };\n    if (path[0] === \".\" || path[0] === \"..\") {\n        // relative\n        return getPathStr(path);\n    }\n    else {\n        // absolute\n        return \"/\" + getPathStr(path);\n    }\n}\n/**\n * Splits and decodes a json path into several parts.\n *\n * @param path\n * @returns\n */\nfunction splitJsonPath(path) {\n    // `/` refers to property with an empty name, while `` refers to root itself!\n    var parts = path.split(\"/\").map(unescapeJsonPath);\n    var valid = path === \"\" ||\n        path === \".\" ||\n        path === \"..\" ||\n        stringStartsWith(path, \"/\") ||\n        stringStartsWith(path, \"./\") ||\n        stringStartsWith(path, \"../\");\n    if (!valid) {\n        throw fail$1(\"a json path must be either rooted, empty or relative, but got '\" + path + \"'\");\n    }\n    // '/a/b/c' -> [\"a\", \"b\", \"c\"]\n    // '../../b/c' -> [\"..\", \"..\", \"b\", \"c\"]\n    // '' -> []\n    // '/' -> ['']\n    // './a' -> [\".\", \"a\"]\n    // /./a' -> [\".\", \"a\"] equivalent to './a'\n    if (parts[0] === \"\") {\n        parts.shift();\n    }\n    return parts;\n}\n\nvar SnapshotProcessor = /** @class */ (function (_super) {\n    __extends(SnapshotProcessor, _super);\n    function SnapshotProcessor(_subtype, _processors, name) {\n        var _this = _super.call(this, name || _subtype.name) || this;\n        _this._subtype = _subtype;\n        _this._processors = _processors;\n        return _this;\n    }\n    Object.defineProperty(SnapshotProcessor.prototype, \"flags\", {\n        get: function () {\n            return this._subtype.flags | TypeFlags.SnapshotProcessor;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    SnapshotProcessor.prototype.describe = function () {\n        return \"snapshotProcessor(\" + this._subtype.describe() + \")\";\n    };\n    SnapshotProcessor.prototype.preProcessSnapshot = function (sn) {\n        if (this._processors.preProcessor) {\n            return this._processors.preProcessor.call(null, sn);\n        }\n        return sn;\n    };\n    SnapshotProcessor.prototype.postProcessSnapshot = function (sn) {\n        if (this._processors.postProcessor) {\n            return this._processors.postProcessor.call(null, sn);\n        }\n        return sn;\n    };\n    SnapshotProcessor.prototype._fixNode = function (node) {\n        var _this = this;\n        // the node has to use these methods rather than the original type ones\n        proxyNodeTypeMethods(node.type, this, \"isAssignableFrom\", \"create\");\n        var oldGetSnapshot = node.getSnapshot;\n        node.getSnapshot = function () {\n            return _this.postProcessSnapshot(oldGetSnapshot.call(node));\n        };\n    };\n    SnapshotProcessor.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        var processedInitialValue = isStateTreeNode(initialValue)\n            ? initialValue\n            : this.preProcessSnapshot(initialValue);\n        var node = this._subtype.instantiate(parent, subpath, environment, processedInitialValue);\n        this._fixNode(node);\n        return node;\n    };\n    SnapshotProcessor.prototype.reconcile = function (current, newValue, parent, subpath) {\n        var node = this._subtype.reconcile(current, isStateTreeNode(newValue) ? newValue : this.preProcessSnapshot(newValue), parent, subpath);\n        if (node !== current) {\n            this._fixNode(node);\n        }\n        return node;\n    };\n    SnapshotProcessor.prototype.getSnapshot = function (node, applyPostProcess) {\n        if (applyPostProcess === void 0) { applyPostProcess = true; }\n        var sn = this._subtype.getSnapshot(node);\n        return applyPostProcess ? this.postProcessSnapshot(sn) : sn;\n    };\n    SnapshotProcessor.prototype.isValidSnapshot = function (value, context) {\n        var processedSn = this.preProcessSnapshot(value);\n        return this._subtype.validate(processedSn, context);\n    };\n    SnapshotProcessor.prototype.getSubTypes = function () {\n        return this._subtype;\n    };\n    SnapshotProcessor.prototype.is = function (thing) {\n        var value = isType(thing)\n            ? this._subtype\n            : isStateTreeNode(thing)\n                ? getSnapshot(thing, false)\n                : this.preProcessSnapshot(thing);\n        return this._subtype.validate(value, [{ path: \"\", type: this._subtype }]).length === 0;\n    };\n    return SnapshotProcessor;\n}(BaseType));\nfunction proxyNodeTypeMethods(nodeType, snapshotProcessorType) {\n    var e_1, _a;\n    var methods = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        methods[_i - 2] = arguments[_i];\n    }\n    try {\n        for (var methods_1 = __values(methods), methods_1_1 = methods_1.next(); !methods_1_1.done; methods_1_1 = methods_1.next()) {\n            var method = methods_1_1.value;\n            nodeType[method] = snapshotProcessorType[method].bind(snapshotProcessorType);\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (methods_1_1 && !methods_1_1.done && (_a = methods_1.return)) _a.call(methods_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n}\n/**\n * `types.snapshotProcessor` - Runs a pre/post snapshot processor before/after serializing a given type.\n *\n * Example:\n * ```ts\n * const Todo1 = types.model({ text: types.string })\n * // in the backend the text type must be null when empty\n * interface BackendTodo {\n *     text: string | null\n * }\n * const Todo2 = types.snapshotProcessor(Todo1, {\n *     // from snapshot to instance\n *     preProcessor(sn: BackendTodo) {\n *         return {\n *             text: sn.text || \"\";\n *         }\n *     },\n *     // from instance to snapshot\n *     postProcessor(sn): BackendTodo {\n *         return {\n *             text: !sn.text ? null : sn.text\n *         }\n *     }\n * })\n * ```\n *\n * @param type Type to run the processors over.\n * @param processors Processors to run.\n * @param name Type name, or undefined to inherit the inner type one.\n * @returns\n */\nfunction snapshotProcessor(type, processors, name) {\n    assertIsType(type, 1);\n    if (devMode()) {\n        if (processors.postProcessor && typeof processors.postProcessor !== \"function\") {\n            // istanbul ignore next\n            throw fail(\"postSnapshotProcessor must be a function\");\n        }\n        if (processors.preProcessor && typeof processors.preProcessor !== \"function\") {\n            // istanbul ignore next\n            throw fail(\"preSnapshotProcessor must be a function\");\n        }\n    }\n    return new SnapshotProcessor(type, processors, name);\n}\n\nvar needsIdentifierError = \"Map.put can only be used to store complex values that have an identifier type attribute\";\nfunction tryCollectModelTypes(type, modelTypes) {\n    var e_1, _a;\n    var subtypes = type.getSubTypes();\n    if (subtypes === cannotDetermineSubtype) {\n        return false;\n    }\n    if (subtypes) {\n        var subtypesArray = asArray(subtypes);\n        try {\n            for (var subtypesArray_1 = __values(subtypesArray), subtypesArray_1_1 = subtypesArray_1.next(); !subtypesArray_1_1.done; subtypesArray_1_1 = subtypesArray_1.next()) {\n                var subtype = subtypesArray_1_1.value;\n                if (!tryCollectModelTypes(subtype, modelTypes))\n                    return false;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (subtypesArray_1_1 && !subtypesArray_1_1.done && (_a = subtypesArray_1.return)) _a.call(subtypesArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    if (type instanceof ModelType) {\n        modelTypes.push(type);\n    }\n    return true;\n}\n/**\n * @internal\n * @hidden\n */\nvar MapIdentifierMode;\n(function (MapIdentifierMode) {\n    MapIdentifierMode[MapIdentifierMode[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\n    MapIdentifierMode[MapIdentifierMode[\"YES\"] = 1] = \"YES\";\n    MapIdentifierMode[MapIdentifierMode[\"NO\"] = 2] = \"NO\";\n})(MapIdentifierMode || (MapIdentifierMode = {}));\nvar MSTMap = /** @class */ (function (_super) {\n    __extends(MSTMap, _super);\n    function MSTMap(initialData) {\n        return _super.call(this, initialData, observable.ref.enhancer) || this;\n    }\n    MSTMap.prototype.get = function (key) {\n        // maybe this is over-enthousiastic? normalize numeric keys to strings\n        return _super.prototype.get.call(this, \"\" + key);\n    };\n    MSTMap.prototype.has = function (key) {\n        return _super.prototype.has.call(this, \"\" + key);\n    };\n    MSTMap.prototype.delete = function (key) {\n        return _super.prototype.delete.call(this, \"\" + key);\n    };\n    MSTMap.prototype.set = function (key, value) {\n        return _super.prototype.set.call(this, \"\" + key, value);\n    };\n    MSTMap.prototype.put = function (value) {\n        if (!value)\n            throw fail$1(\"Map.put cannot be used to set empty values\");\n        if (isStateTreeNode(value)) {\n            var node = getStateTreeNode(value);\n            if (devMode()) {\n                if (!node.identifierAttribute) {\n                    throw fail$1(needsIdentifierError);\n                }\n            }\n            if (node.identifier === null) {\n                throw fail$1(needsIdentifierError);\n            }\n            this.set(node.identifier, value);\n            return value;\n        }\n        else if (!isMutable(value)) {\n            throw fail$1(\"Map.put can only be used to store complex values\");\n        }\n        else {\n            var mapNode = getStateTreeNode(this);\n            var mapType = mapNode.type;\n            if (mapType.identifierMode !== MapIdentifierMode.YES) {\n                throw fail$1(needsIdentifierError);\n            }\n            var idAttr = mapType.mapIdentifierAttribute;\n            var id = value[idAttr];\n            if (!isValidIdentifier(id)) {\n                // try again but this time after creating a node for the value\n                // since it might be an optional identifier\n                var newNode = this.put(mapType.getChildType().create(value, mapNode.environment));\n                return this.put(getSnapshot(newNode));\n            }\n            var key = normalizeIdentifier(id);\n            this.set(key, value);\n            return this.get(key);\n        }\n    };\n    return MSTMap;\n}(ObservableMap));\n/**\n * @internal\n * @hidden\n */\nvar MapType = /** @class */ (function (_super) {\n    __extends(MapType, _super);\n    function MapType(name, _subType, hookInitializers) {\n        if (hookInitializers === void 0) { hookInitializers = []; }\n        var _this = _super.call(this, name) || this;\n        _this._subType = _subType;\n        _this.identifierMode = MapIdentifierMode.UNKNOWN;\n        _this.mapIdentifierAttribute = undefined;\n        _this.flags = TypeFlags.Map;\n        _this.hookInitializers = [];\n        _this._determineIdentifierMode();\n        _this.hookInitializers = hookInitializers;\n        return _this;\n    }\n    MapType.prototype.hooks = function (hooks) {\n        var hookInitializers = this.hookInitializers.length > 0 ? this.hookInitializers.concat(hooks) : [hooks];\n        return new MapType(this.name, this._subType, hookInitializers);\n    };\n    MapType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        this._determineIdentifierMode();\n        return createObjectNode(this, parent, subpath, environment, initialValue);\n    };\n    MapType.prototype._determineIdentifierMode = function () {\n        if (this.identifierMode !== MapIdentifierMode.UNKNOWN) {\n            return;\n        }\n        var modelTypes = [];\n        if (tryCollectModelTypes(this._subType, modelTypes)) {\n            var identifierAttribute_1 = undefined;\n            modelTypes.forEach(function (type) {\n                if (type.identifierAttribute) {\n                    if (identifierAttribute_1 && identifierAttribute_1 !== type.identifierAttribute) {\n                        throw fail$1(\"The objects in a map should all have the same identifier attribute, expected '\" + identifierAttribute_1 + \"', but child of type '\" + type.name + \"' declared attribute '\" + type.identifierAttribute + \"' as identifier\");\n                    }\n                    identifierAttribute_1 = type.identifierAttribute;\n                }\n            });\n            if (identifierAttribute_1) {\n                this.identifierMode = MapIdentifierMode.YES;\n                this.mapIdentifierAttribute = identifierAttribute_1;\n            }\n            else {\n                this.identifierMode = MapIdentifierMode.NO;\n            }\n        }\n    };\n    MapType.prototype.initializeChildNodes = function (objNode, initialSnapshot) {\n        if (initialSnapshot === void 0) { initialSnapshot = {}; }\n        var subType = objNode.type._subType;\n        var result = {};\n        Object.keys(initialSnapshot).forEach(function (name) {\n            result[name] = subType.instantiate(objNode, name, undefined, initialSnapshot[name]);\n        });\n        return result;\n    };\n    MapType.prototype.createNewInstance = function (childNodes) {\n        return new MSTMap(childNodes);\n    };\n    MapType.prototype.finalizeNewInstance = function (node, instance) {\n        _interceptReads(instance, node.unbox);\n        var type = node.type;\n        type.hookInitializers.forEach(function (initializer) {\n            var hooks = initializer(instance);\n            Object.keys(hooks).forEach(function (name) {\n                var hook = hooks[name];\n                var actionInvoker = createActionInvoker(instance, name, hook);\n                (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(instance, name, actionInvoker);\n            });\n        });\n        intercept(instance, this.willChange);\n        observe(instance, this.didChange);\n    };\n    MapType.prototype.describe = function () {\n        return \"Map<string, \" + this._subType.describe() + \">\";\n    };\n    MapType.prototype.getChildren = function (node) {\n        // return (node.storedValue as ObservableMap<any>).values()\n        return values(node.storedValue);\n    };\n    MapType.prototype.getChildNode = function (node, key) {\n        var childNode = node.storedValue.get(\"\" + key);\n        if (!childNode)\n            throw fail$1(\"Not a child \" + key);\n        return childNode;\n    };\n    MapType.prototype.willChange = function (change) {\n        var node = getStateTreeNode(change.object);\n        var key = change.name;\n        node.assertWritable({ subpath: key });\n        var mapType = node.type;\n        var subType = mapType._subType;\n        switch (change.type) {\n            case \"update\":\n                {\n                    var newValue = change.newValue;\n                    var oldValue = change.object.get(key);\n                    if (newValue === oldValue)\n                        return null;\n                    typecheckInternal(subType, newValue);\n                    change.newValue = subType.reconcile(node.getChildNode(key), change.newValue, node, key);\n                    mapType.processIdentifier(key, change.newValue);\n                }\n                break;\n            case \"add\":\n                {\n                    typecheckInternal(subType, change.newValue);\n                    change.newValue = subType.instantiate(node, key, undefined, change.newValue);\n                    mapType.processIdentifier(key, change.newValue);\n                }\n                break;\n        }\n        return change;\n    };\n    MapType.prototype.processIdentifier = function (expected, node) {\n        if (this.identifierMode === MapIdentifierMode.YES && node instanceof ObjectNode) {\n            var identifier = node.identifier;\n            if (identifier !== expected)\n                throw fail$1(\"A map of objects containing an identifier should always store the object under their own identifier. Trying to store key '\" + identifier + \"', but expected: '\" + expected + \"'\");\n        }\n    };\n    MapType.prototype.getSnapshot = function (node) {\n        var res = {};\n        node.getChildren().forEach(function (childNode) {\n            res[childNode.subpath] = childNode.snapshot;\n        });\n        return res;\n    };\n    MapType.prototype.processInitialSnapshot = function (childNodes) {\n        var processed = {};\n        Object.keys(childNodes).forEach(function (key) {\n            processed[key] = childNodes[key].getSnapshot();\n        });\n        return processed;\n    };\n    MapType.prototype.didChange = function (change) {\n        var node = getStateTreeNode(change.object);\n        switch (change.type) {\n            case \"update\":\n                return void node.emitPatch({\n                    op: \"replace\",\n                    path: escapeJsonPath(change.name),\n                    value: change.newValue.snapshot,\n                    oldValue: change.oldValue ? change.oldValue.snapshot : undefined\n                }, node);\n            case \"add\":\n                return void node.emitPatch({\n                    op: \"add\",\n                    path: escapeJsonPath(change.name),\n                    value: change.newValue.snapshot,\n                    oldValue: undefined\n                }, node);\n            case \"delete\":\n                // a node got deleted, get the old snapshot and make the node die\n                var oldSnapshot = change.oldValue.snapshot;\n                change.oldValue.die();\n                // emit the patch\n                return void node.emitPatch({\n                    op: \"remove\",\n                    path: escapeJsonPath(change.name),\n                    oldValue: oldSnapshot\n                }, node);\n        }\n    };\n    MapType.prototype.applyPatchLocally = function (node, subpath, patch) {\n        var target = node.storedValue;\n        switch (patch.op) {\n            case \"add\":\n            case \"replace\":\n                target.set(subpath, patch.value);\n                break;\n            case \"remove\":\n                target.delete(subpath);\n                break;\n        }\n    };\n    MapType.prototype.applySnapshot = function (node, snapshot) {\n        typecheckInternal(this, snapshot);\n        var target = node.storedValue;\n        var currentKeys = {};\n        Array.from(target.keys()).forEach(function (key) {\n            currentKeys[key] = false;\n        });\n        if (snapshot) {\n            // Don't use target.replace, as it will throw away all existing items first\n            for (var key in snapshot) {\n                target.set(key, snapshot[key]);\n                currentKeys[\"\" + key] = true;\n            }\n        }\n        Object.keys(currentKeys).forEach(function (key) {\n            if (currentKeys[key] === false)\n                target.delete(key);\n        });\n    };\n    MapType.prototype.getChildType = function () {\n        return this._subType;\n    };\n    MapType.prototype.isValidSnapshot = function (value, context) {\n        var _this = this;\n        if (!isPlainObject(value)) {\n            return typeCheckFailure(context, value, \"Value is not a plain object\");\n        }\n        return flattenTypeErrors(Object.keys(value).map(function (path) {\n            return _this._subType.validate(value[path], getContextForPath(context, path, _this._subType));\n        }));\n    };\n    MapType.prototype.getDefaultSnapshot = function () {\n        return EMPTY_OBJECT;\n    };\n    MapType.prototype.removeChild = function (node, subpath) {\n        node.storedValue.delete(subpath);\n    };\n    __decorate([\n        action\n    ], MapType.prototype, \"applySnapshot\", null);\n    return MapType;\n}(ComplexType));\n/**\n * `types.map` - Creates a key based collection type who's children are all of a uniform declared type.\n * If the type stored in a map has an identifier, it is mandatory to store the child under that identifier in the map.\n *\n * This type will always produce [observable maps](https://mobx.js.org/refguide/map.html)\n *\n * Example:\n * ```ts\n * const Todo = types.model({\n *   id: types.identifier,\n *   task: types.string\n * })\n *\n * const TodoStore = types.model({\n *   todos: types.map(Todo)\n * })\n *\n * const s = TodoStore.create({ todos: {} })\n * unprotect(s)\n * s.todos.set(17, { task: \"Grab coffee\", id: 17 })\n * s.todos.put({ task: \"Grab cookie\", id: 18 }) // put will infer key from the identifier\n * console.log(s.todos.get(17).task) // prints: \"Grab coffee\"\n * ```\n *\n * @param subtype\n * @returns\n */\nfunction map(subtype) {\n    return new MapType(\"map<string, \" + subtype.name + \">\", subtype);\n}\n/**\n * Returns if a given value represents a map type.\n *\n * @param type\n * @returns `true` if it is a map type.\n */\nfunction isMapType(type) {\n    return isType(type) && (type.flags & TypeFlags.Map) > 0;\n}\n\n/**\n * @internal\n * @hidden\n */\nvar ArrayType = /** @class */ (function (_super) {\n    __extends(ArrayType, _super);\n    function ArrayType(name, _subType, hookInitializers) {\n        if (hookInitializers === void 0) { hookInitializers = []; }\n        var _this = _super.call(this, name) || this;\n        _this._subType = _subType;\n        _this.flags = TypeFlags.Array;\n        _this.hookInitializers = [];\n        _this.hookInitializers = hookInitializers;\n        return _this;\n    }\n    ArrayType.prototype.hooks = function (hooks) {\n        var hookInitializers = this.hookInitializers.length > 0 ? this.hookInitializers.concat(hooks) : [hooks];\n        return new ArrayType(this.name, this._subType, hookInitializers);\n    };\n    ArrayType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        return createObjectNode(this, parent, subpath, environment, initialValue);\n    };\n    ArrayType.prototype.initializeChildNodes = function (objNode, snapshot) {\n        if (snapshot === void 0) { snapshot = []; }\n        var subType = objNode.type._subType;\n        var result = {};\n        snapshot.forEach(function (item, index) {\n            var subpath = \"\" + index;\n            result[subpath] = subType.instantiate(objNode, subpath, undefined, item);\n        });\n        return result;\n    };\n    ArrayType.prototype.createNewInstance = function (childNodes) {\n        return observable.array(convertChildNodesToArray(childNodes), mobxShallow);\n    };\n    ArrayType.prototype.finalizeNewInstance = function (node, instance) {\n        _getAdministration(instance).dehancer = node.unbox;\n        var type = node.type;\n        type.hookInitializers.forEach(function (initializer) {\n            var hooks = initializer(instance);\n            Object.keys(hooks).forEach(function (name) {\n                var hook = hooks[name];\n                var actionInvoker = createActionInvoker(instance, name, hook);\n                (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(instance, name, actionInvoker);\n            });\n        });\n        intercept(instance, this.willChange);\n        observe(instance, this.didChange);\n    };\n    ArrayType.prototype.describe = function () {\n        return this._subType.describe() + \"[]\";\n    };\n    ArrayType.prototype.getChildren = function (node) {\n        return node.storedValue.slice();\n    };\n    ArrayType.prototype.getChildNode = function (node, key) {\n        var index = Number(key);\n        if (index < node.storedValue.length)\n            return node.storedValue[index];\n        throw fail$1(\"Not a child: \" + key);\n    };\n    ArrayType.prototype.willChange = function (change) {\n        var node = getStateTreeNode(change.object);\n        node.assertWritable({ subpath: \"\" + change.index });\n        var subType = node.type._subType;\n        var childNodes = node.getChildren();\n        switch (change.type) {\n            case \"update\":\n                {\n                    if (change.newValue === change.object[change.index])\n                        return null;\n                    var updatedNodes = reconcileArrayChildren(node, subType, [childNodes[change.index]], [change.newValue], [change.index]);\n                    if (!updatedNodes) {\n                        return null;\n                    }\n                    change.newValue = updatedNodes[0];\n                }\n                break;\n            case \"splice\":\n                {\n                    var index_1 = change.index, removedCount = change.removedCount, added = change.added;\n                    var addedNodes = reconcileArrayChildren(node, subType, childNodes.slice(index_1, index_1 + removedCount), added, added.map(function (_, i) { return index_1 + i; }));\n                    if (!addedNodes) {\n                        return null;\n                    }\n                    change.added = addedNodes;\n                    // update paths of remaining items\n                    for (var i = index_1 + removedCount; i < childNodes.length; i++) {\n                        childNodes[i].setParent(node, \"\" + (i + added.length - removedCount));\n                    }\n                }\n                break;\n        }\n        return change;\n    };\n    ArrayType.prototype.getSnapshot = function (node) {\n        return node.getChildren().map(function (childNode) { return childNode.snapshot; });\n    };\n    ArrayType.prototype.processInitialSnapshot = function (childNodes) {\n        var processed = [];\n        Object.keys(childNodes).forEach(function (key) {\n            processed.push(childNodes[key].getSnapshot());\n        });\n        return processed;\n    };\n    ArrayType.prototype.didChange = function (change) {\n        var node = getStateTreeNode(change.object);\n        switch (change.type) {\n            case \"update\":\n                return void node.emitPatch({\n                    op: \"replace\",\n                    path: \"\" + change.index,\n                    value: change.newValue.snapshot,\n                    oldValue: change.oldValue ? change.oldValue.snapshot : undefined\n                }, node);\n            case \"splice\":\n                for (var i = change.removedCount - 1; i >= 0; i--)\n                    node.emitPatch({\n                        op: \"remove\",\n                        path: \"\" + (change.index + i),\n                        oldValue: change.removed[i].snapshot\n                    }, node);\n                for (var i = 0; i < change.addedCount; i++)\n                    node.emitPatch({\n                        op: \"add\",\n                        path: \"\" + (change.index + i),\n                        value: node.getChildNode(\"\" + (change.index + i)).snapshot,\n                        oldValue: undefined\n                    }, node);\n                return;\n        }\n    };\n    ArrayType.prototype.applyPatchLocally = function (node, subpath, patch) {\n        var target = node.storedValue;\n        var index = subpath === \"-\" ? target.length : Number(subpath);\n        switch (patch.op) {\n            case \"replace\":\n                target[index] = patch.value;\n                break;\n            case \"add\":\n                target.splice(index, 0, patch.value);\n                break;\n            case \"remove\":\n                target.splice(index, 1);\n                break;\n        }\n    };\n    ArrayType.prototype.applySnapshot = function (node, snapshot) {\n        typecheckInternal(this, snapshot);\n        var target = node.storedValue;\n        target.replace(snapshot);\n    };\n    ArrayType.prototype.getChildType = function () {\n        return this._subType;\n    };\n    ArrayType.prototype.isValidSnapshot = function (value, context) {\n        var _this = this;\n        if (!isArray(value)) {\n            return typeCheckFailure(context, value, \"Value is not an array\");\n        }\n        return flattenTypeErrors(value.map(function (item, index) {\n            return _this._subType.validate(item, getContextForPath(context, \"\" + index, _this._subType));\n        }));\n    };\n    ArrayType.prototype.getDefaultSnapshot = function () {\n        return EMPTY_ARRAY;\n    };\n    ArrayType.prototype.removeChild = function (node, subpath) {\n        node.storedValue.splice(Number(subpath), 1);\n    };\n    __decorate([\n        action\n    ], ArrayType.prototype, \"applySnapshot\", null);\n    return ArrayType;\n}(ComplexType));\n/**\n * `types.array` - Creates an index based collection type who's children are all of a uniform declared type.\n *\n * This type will always produce [observable arrays](https://mobx.js.org/refguide/array.html)\n *\n * Example:\n * ```ts\n * const Todo = types.model({\n *   task: types.string\n * })\n *\n * const TodoStore = types.model({\n *   todos: types.array(Todo)\n * })\n *\n * const s = TodoStore.create({ todos: [] })\n * unprotect(s) // needed to allow modifying outside of an action\n * s.todos.push({ task: \"Grab coffee\" })\n * console.log(s.todos[0]) // prints: \"Grab coffee\"\n * ```\n *\n * @param subtype\n * @returns\n */\nfunction array(subtype) {\n    assertIsType(subtype, 1);\n    return new ArrayType(subtype.name + \"[]\", subtype);\n}\nfunction reconcileArrayChildren(parent, childType, oldNodes, newValues, newPaths) {\n    var nothingChanged = true;\n    for (var i = 0;; i++) {\n        var hasNewNode = i <= newValues.length - 1;\n        var oldNode = oldNodes[i];\n        var newValue = hasNewNode ? newValues[i] : undefined;\n        var newPath = \"\" + newPaths[i];\n        // for some reason, instead of newValue we got a node, fallback to the storedValue\n        // TODO: https://github.com/mobxjs/mobx-state-tree/issues/340#issuecomment-325581681\n        if (isNode(newValue))\n            newValue = newValue.storedValue;\n        if (!oldNode && !hasNewNode) {\n            // both are empty, end\n            break;\n        }\n        else if (!hasNewNode) {\n            // new one does not exists\n            nothingChanged = false;\n            oldNodes.splice(i, 1);\n            if (oldNode instanceof ObjectNode) {\n                // since it is going to be returned by pop/splice/shift better create it before killing it\n                // so it doesn't end up in an undead state\n                oldNode.createObservableInstanceIfNeeded();\n            }\n            oldNode.die();\n            i--;\n        }\n        else if (!oldNode) {\n            // there is no old node, create it\n            // check if already belongs to the same parent. if so, avoid pushing item in. only swapping can occur.\n            if (isStateTreeNode(newValue) && getStateTreeNode(newValue).parent === parent) {\n                // this node is owned by this parent, but not in the reconcilable set, so it must be double\n                throw fail$1(\"Cannot add an object to a state tree if it is already part of the same or another state tree. Tried to assign an object to '\" + parent.path + \"/\" + newPath + \"', but it lives already at '\" + getStateTreeNode(newValue).path + \"'\");\n            }\n            nothingChanged = false;\n            var newNode = valueAsNode(childType, parent, newPath, newValue);\n            oldNodes.splice(i, 0, newNode);\n        }\n        else if (areSame(oldNode, newValue)) {\n            // both are the same, reconcile\n            oldNodes[i] = valueAsNode(childType, parent, newPath, newValue, oldNode);\n        }\n        else {\n            // nothing to do, try to reorder\n            var oldMatch = undefined;\n            // find a possible candidate to reuse\n            for (var j = i; j < oldNodes.length; j++) {\n                if (areSame(oldNodes[j], newValue)) {\n                    oldMatch = oldNodes.splice(j, 1)[0];\n                    break;\n                }\n            }\n            nothingChanged = false;\n            var newNode = valueAsNode(childType, parent, newPath, newValue, oldMatch);\n            oldNodes.splice(i, 0, newNode);\n        }\n    }\n    return nothingChanged ? null : oldNodes;\n}\n/**\n * Convert a value to a node at given parent and subpath. Attempts to reuse old node if possible and given.\n */\nfunction valueAsNode(childType, parent, subpath, newValue, oldNode) {\n    // ensure the value is valid-ish\n    typecheckInternal(childType, newValue);\n    function getNewNode() {\n        // the new value has a MST node\n        if (isStateTreeNode(newValue)) {\n            var childNode = getStateTreeNode(newValue);\n            childNode.assertAlive(EMPTY_OBJECT);\n            // the node lives here\n            if (childNode.parent !== null && childNode.parent === parent) {\n                childNode.setParent(parent, subpath);\n                return childNode;\n            }\n        }\n        // there is old node and new one is a value/snapshot\n        if (oldNode) {\n            return childType.reconcile(oldNode, newValue, parent, subpath);\n        }\n        // nothing to do, create from scratch\n        return childType.instantiate(parent, subpath, undefined, newValue);\n    }\n    var newNode = getNewNode();\n    if (oldNode && oldNode !== newNode) {\n        if (oldNode instanceof ObjectNode) {\n            // since it is going to be returned by pop/splice/shift better create it before killing it\n            // so it doesn't end up in an undead state\n            oldNode.createObservableInstanceIfNeeded();\n        }\n        oldNode.die();\n    }\n    return newNode;\n}\n/**\n * Check if a node holds a value.\n */\nfunction areSame(oldNode, newValue) {\n    // never consider dead old nodes for reconciliation\n    if (!oldNode.isAlive) {\n        return false;\n    }\n    // the new value has the same node\n    if (isStateTreeNode(newValue)) {\n        var newNode = getStateTreeNode(newValue);\n        return newNode.isAlive && newNode === oldNode;\n    }\n    // the provided value is the snapshot of the old node\n    if (oldNode.snapshot === newValue) {\n        return true;\n    }\n    // new value is a snapshot with the correct identifier\n    return (oldNode instanceof ObjectNode &&\n        oldNode.identifier !== null &&\n        oldNode.identifierAttribute &&\n        isPlainObject(newValue) &&\n        oldNode.identifier === normalizeIdentifier(newValue[oldNode.identifierAttribute]) &&\n        oldNode.type.is(newValue));\n}\n/**\n * Returns if a given value represents an array type.\n *\n * @param type\n * @returns `true` if the type is an array type.\n */\nfunction isArrayType(type) {\n    return isType(type) && (type.flags & TypeFlags.Array) > 0;\n}\n\nvar PRE_PROCESS_SNAPSHOT = \"preProcessSnapshot\";\nvar POST_PROCESS_SNAPSHOT = \"postProcessSnapshot\";\nfunction objectTypeToString() {\n    return getStateTreeNode(this).toString();\n}\nvar defaultObjectOptions = {\n    name: \"AnonymousModel\",\n    properties: {},\n    initializers: EMPTY_ARRAY\n};\nfunction toPropertiesObject(declaredProps) {\n    // loop through properties and ensures that all items are types\n    return Object.keys(declaredProps).reduce(function (props, key) {\n        var _a, _b, _c;\n        // warn if user intended a HOOK\n        if (key in Hook)\n            throw fail$1(\"Hook '\" + key + \"' was defined as property. Hooks should be defined as part of the actions\");\n        // the user intended to use a view\n        var descriptor = Object.getOwnPropertyDescriptor(props, key);\n        if (\"get\" in descriptor) {\n            throw fail$1(\"Getters are not supported as properties. Please use views instead\");\n        }\n        // undefined and null are not valid\n        var value = descriptor.value;\n        if (value === null || value === undefined) {\n            throw fail$1(\"The default value of an attribute cannot be null or undefined as the type cannot be inferred. Did you mean `types.maybe(someType)`?\");\n            // its a primitive, convert to its type\n        }\n        else if (isPrimitive(value)) {\n            return Object.assign({}, props, (_a = {},\n                _a[key] = optional(getPrimitiveFactoryFromValue(value), value),\n                _a));\n            // map defaults to empty object automatically for models\n        }\n        else if (value instanceof MapType) {\n            return Object.assign({}, props, (_b = {},\n                _b[key] = optional(value, {}),\n                _b));\n        }\n        else if (value instanceof ArrayType) {\n            return Object.assign({}, props, (_c = {}, _c[key] = optional(value, []), _c));\n            // its already a type\n        }\n        else if (isType(value)) {\n            return props;\n            // its a function, maybe the user wanted a view?\n        }\n        else if (devMode() && typeof value === \"function\") {\n            throw fail$1(\"Invalid type definition for property '\" + key + \"', it looks like you passed a function. Did you forget to invoke it, or did you intend to declare a view / action?\");\n            // no other complex values\n        }\n        else if (devMode() && typeof value === \"object\") {\n            throw fail$1(\"Invalid type definition for property '\" + key + \"', it looks like you passed an object. Try passing another model type or a types.frozen.\");\n            // WTF did you pass in mate?\n        }\n        else {\n            throw fail$1(\"Invalid type definition for property '\" + key + \"', cannot infer a type from a value like '\" + value + \"' (\" + typeof value + \")\");\n        }\n    }, declaredProps);\n}\n/**\n * @internal\n * @hidden\n */\nvar ModelType = /** @class */ (function (_super) {\n    __extends(ModelType, _super);\n    function ModelType(opts) {\n        var _this = _super.call(this, opts.name || defaultObjectOptions.name) || this;\n        _this.flags = TypeFlags.Object;\n        _this.named = function (name) {\n            return _this.cloneAndEnhance({ name: name });\n        };\n        _this.props = function (properties) {\n            return _this.cloneAndEnhance({ properties: properties });\n        };\n        _this.preProcessSnapshot = function (preProcessor) {\n            var currentPreprocessor = _this.preProcessor;\n            if (!currentPreprocessor)\n                return _this.cloneAndEnhance({ preProcessor: preProcessor });\n            else\n                return _this.cloneAndEnhance({\n                    preProcessor: function (snapshot) { return currentPreprocessor(preProcessor(snapshot)); }\n                });\n        };\n        _this.postProcessSnapshot = function (postProcessor) {\n            var currentPostprocessor = _this.postProcessor;\n            if (!currentPostprocessor)\n                return _this.cloneAndEnhance({ postProcessor: postProcessor });\n            else\n                return _this.cloneAndEnhance({\n                    postProcessor: function (snapshot) { return postProcessor(currentPostprocessor(snapshot)); }\n                });\n        };\n        Object.assign(_this, defaultObjectOptions, opts);\n        // ensures that any default value gets converted to its related type\n        _this.properties = toPropertiesObject(_this.properties);\n        freeze(_this.properties); // make sure nobody messes with it\n        _this.propertyNames = Object.keys(_this.properties);\n        _this.identifierAttribute = _this._getIdentifierAttribute();\n        return _this;\n    }\n    ModelType.prototype._getIdentifierAttribute = function () {\n        var identifierAttribute = undefined;\n        this.forAllProps(function (propName, propType) {\n            if (propType.flags & TypeFlags.Identifier) {\n                if (identifierAttribute)\n                    throw fail$1(\"Cannot define property '\" + propName + \"' as object identifier, property '\" + identifierAttribute + \"' is already defined as identifier property\");\n                identifierAttribute = propName;\n            }\n        });\n        return identifierAttribute;\n    };\n    ModelType.prototype.cloneAndEnhance = function (opts) {\n        return new ModelType({\n            name: opts.name || this.name,\n            properties: Object.assign({}, this.properties, opts.properties),\n            initializers: this.initializers.concat(opts.initializers || []),\n            preProcessor: opts.preProcessor || this.preProcessor,\n            postProcessor: opts.postProcessor || this.postProcessor\n        });\n    };\n    ModelType.prototype.actions = function (fn) {\n        var _this = this;\n        var actionInitializer = function (self) {\n            _this.instantiateActions(self, fn(self));\n            return self;\n        };\n        return this.cloneAndEnhance({ initializers: [actionInitializer] });\n    };\n    ModelType.prototype.instantiateActions = function (self, actions) {\n        // check if return is correct\n        if (!isPlainObject(actions))\n            throw fail$1(\"actions initializer should return a plain object containing actions\");\n        // bind actions to the object created\n        Object.keys(actions).forEach(function (name) {\n            // warn if preprocessor was given\n            if (name === PRE_PROCESS_SNAPSHOT)\n                throw fail$1(\"Cannot define action '\" + PRE_PROCESS_SNAPSHOT + \"', it should be defined using 'type.preProcessSnapshot(fn)' instead\");\n            // warn if postprocessor was given\n            if (name === POST_PROCESS_SNAPSHOT)\n                throw fail$1(\"Cannot define action '\" + POST_PROCESS_SNAPSHOT + \"', it should be defined using 'type.postProcessSnapshot(fn)' instead\");\n            var action2 = actions[name];\n            // apply hook composition\n            var baseAction = self[name];\n            if (name in Hook && baseAction) {\n                var specializedAction_1 = action2;\n                action2 = function () {\n                    baseAction.apply(null, arguments);\n                    specializedAction_1.apply(null, arguments);\n                };\n            }\n            // the goal of this is to make sure actions using \"this\" can call themselves,\n            // while still allowing the middlewares to register them\n            var middlewares = action2.$mst_middleware; // make sure middlewares are not lost\n            var boundAction = action2.bind(actions);\n            boundAction.$mst_middleware = middlewares;\n            var actionInvoker = createActionInvoker(self, name, boundAction);\n            actions[name] = actionInvoker;\n            (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(self, name, actionInvoker);\n        });\n    };\n    ModelType.prototype.volatile = function (fn) {\n        var _this = this;\n        if (typeof fn !== \"function\") {\n            throw fail$1(\"You passed an \" + typeof fn + \" to volatile state as an argument, when function is expected\");\n        }\n        var stateInitializer = function (self) {\n            _this.instantiateVolatileState(self, fn(self));\n            return self;\n        };\n        return this.cloneAndEnhance({ initializers: [stateInitializer] });\n    };\n    ModelType.prototype.instantiateVolatileState = function (self, state) {\n        // check views return\n        if (!isPlainObject(state))\n            throw fail$1(\"volatile state initializer should return a plain object containing state\");\n        set(self, state);\n    };\n    ModelType.prototype.extend = function (fn) {\n        var _this = this;\n        var initializer = function (self) {\n            var _a = fn(self), actions = _a.actions, views = _a.views, state = _a.state, rest = __rest(_a, [\"actions\", \"views\", \"state\"]);\n            for (var key in rest)\n                throw fail$1(\"The `extend` function should return an object with a subset of the fields 'actions', 'views' and 'state'. Found invalid key '\" + key + \"'\");\n            if (state)\n                _this.instantiateVolatileState(self, state);\n            if (views)\n                _this.instantiateViews(self, views);\n            if (actions)\n                _this.instantiateActions(self, actions);\n            return self;\n        };\n        return this.cloneAndEnhance({ initializers: [initializer] });\n    };\n    ModelType.prototype.views = function (fn) {\n        var _this = this;\n        var viewInitializer = function (self) {\n            _this.instantiateViews(self, fn(self));\n            return self;\n        };\n        return this.cloneAndEnhance({ initializers: [viewInitializer] });\n    };\n    ModelType.prototype.instantiateViews = function (self, views) {\n        // check views return\n        if (!isPlainObject(views))\n            throw fail$1(\"views initializer should return a plain object containing views\");\n        Object.keys(views).forEach(function (key) {\n            // is this a computed property?\n            var descriptor = Object.getOwnPropertyDescriptor(views, key);\n            if (\"get\" in descriptor) {\n                if (isComputedProp(self, key)) {\n                    var computedValue = _getAdministration(self, key);\n                    // TODO: mobx currently does not allow redefining computes yet, pending #1121\n                    // FIXME: this binds to the internals of mobx!\n                    computedValue.derivation = descriptor.get;\n                    computedValue.scope = self;\n                    if (descriptor.set)\n                        computedValue.setter = action(computedValue.name + \"-setter\", descriptor.set);\n                }\n                else {\n                    computed(self, key, descriptor, true);\n                }\n            }\n            else if (typeof descriptor.value === \"function\") {\n                (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(self, key, descriptor.value);\n            }\n            else {\n                throw fail$1(\"A view member should either be a function or getter based property\");\n            }\n        });\n    };\n    ModelType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        var value = isStateTreeNode(initialValue)\n            ? initialValue\n            : this.applySnapshotPreProcessor(initialValue);\n        return createObjectNode(this, parent, subpath, environment, value);\n        // Optimization: record all prop- view- and action names after first construction, and generate an optimal base class\n        // that pre-reserves all these fields for fast object-member lookups\n    };\n    ModelType.prototype.initializeChildNodes = function (objNode, initialSnapshot) {\n        if (initialSnapshot === void 0) { initialSnapshot = {}; }\n        var type = objNode.type;\n        var result = {};\n        type.forAllProps(function (name, childType) {\n            result[name] = childType.instantiate(objNode, name, undefined, initialSnapshot[name]);\n        });\n        return result;\n    };\n    ModelType.prototype.createNewInstance = function (childNodes) {\n        return observable.object(childNodes, EMPTY_OBJECT, mobxShallow);\n    };\n    ModelType.prototype.finalizeNewInstance = function (node, instance) {\n        addHiddenFinalProp(instance, \"toString\", objectTypeToString);\n        this.forAllProps(function (name) {\n            _interceptReads(instance, name, node.unbox);\n        });\n        this.initializers.reduce(function (self, fn) { return fn(self); }, instance);\n        intercept(instance, this.willChange);\n        observe(instance, this.didChange);\n    };\n    ModelType.prototype.willChange = function (chg) {\n        // TODO: mobx typings don't seem to take into account that newValue can be set even when removing a prop\n        var change = chg;\n        var node = getStateTreeNode(change.object);\n        var subpath = change.name;\n        node.assertWritable({ subpath: subpath });\n        var childType = node.type.properties[subpath];\n        // only properties are typed, state are stored as-is references\n        if (childType) {\n            typecheckInternal(childType, change.newValue);\n            change.newValue = childType.reconcile(node.getChildNode(subpath), change.newValue, node, subpath);\n        }\n        return change;\n    };\n    ModelType.prototype.didChange = function (chg) {\n        // TODO: mobx typings don't seem to take into account that newValue can be set even when removing a prop\n        var change = chg;\n        var childNode = getStateTreeNode(change.object);\n        var childType = childNode.type.properties[change.name];\n        if (!childType) {\n            // don't emit patches for volatile state\n            return;\n        }\n        var oldChildValue = change.oldValue ? change.oldValue.snapshot : undefined;\n        childNode.emitPatch({\n            op: \"replace\",\n            path: escapeJsonPath(change.name),\n            value: change.newValue.snapshot,\n            oldValue: oldChildValue\n        }, childNode);\n    };\n    ModelType.prototype.getChildren = function (node) {\n        var _this = this;\n        var res = [];\n        this.forAllProps(function (name) {\n            res.push(_this.getChildNode(node, name));\n        });\n        return res;\n    };\n    ModelType.prototype.getChildNode = function (node, key) {\n        if (!(key in this.properties))\n            throw fail$1(\"Not a value property: \" + key);\n        var childNode = _getAdministration(node.storedValue, key).value; // TODO: blegh!\n        if (!childNode)\n            throw fail$1(\"Node not available for property \" + key);\n        return childNode;\n    };\n    ModelType.prototype.getSnapshot = function (node, applyPostProcess) {\n        var _this = this;\n        if (applyPostProcess === void 0) { applyPostProcess = true; }\n        var res = {};\n        this.forAllProps(function (name, type) {\n            getAtom(node.storedValue, name).reportObserved();\n            res[name] = _this.getChildNode(node, name).snapshot;\n        });\n        if (applyPostProcess) {\n            return this.applySnapshotPostProcessor(res);\n        }\n        return res;\n    };\n    ModelType.prototype.processInitialSnapshot = function (childNodes) {\n        var processed = {};\n        Object.keys(childNodes).forEach(function (key) {\n            processed[key] = childNodes[key].getSnapshot();\n        });\n        return this.applySnapshotPostProcessor(processed);\n    };\n    ModelType.prototype.applyPatchLocally = function (node, subpath, patch) {\n        if (!(patch.op === \"replace\" || patch.op === \"add\")) {\n            throw fail$1(\"object does not support operation \" + patch.op);\n        }\n        node.storedValue[subpath] = patch.value;\n    };\n    ModelType.prototype.applySnapshot = function (node, snapshot) {\n        var preProcessedSnapshot = this.applySnapshotPreProcessor(snapshot);\n        typecheckInternal(this, preProcessedSnapshot);\n        this.forAllProps(function (name) {\n            node.storedValue[name] = preProcessedSnapshot[name];\n        });\n    };\n    ModelType.prototype.applySnapshotPreProcessor = function (snapshot) {\n        var processor = this.preProcessor;\n        return processor ? processor.call(null, snapshot) : snapshot;\n    };\n    ModelType.prototype.applySnapshotPostProcessor = function (snapshot) {\n        var postProcessor = this.postProcessor;\n        if (postProcessor)\n            return postProcessor.call(null, snapshot);\n        return snapshot;\n    };\n    ModelType.prototype.getChildType = function (propertyName) {\n        assertIsString(propertyName, 1);\n        return this.properties[propertyName];\n    };\n    ModelType.prototype.isValidSnapshot = function (value, context) {\n        var _this = this;\n        var snapshot = this.applySnapshotPreProcessor(value);\n        if (!isPlainObject(snapshot)) {\n            return typeCheckFailure(context, snapshot, \"Value is not a plain object\");\n        }\n        return flattenTypeErrors(this.propertyNames.map(function (key) {\n            return _this.properties[key].validate(snapshot[key], getContextForPath(context, key, _this.properties[key]));\n        }));\n    };\n    ModelType.prototype.forAllProps = function (fn) {\n        var _this = this;\n        this.propertyNames.forEach(function (key) { return fn(key, _this.properties[key]); });\n    };\n    ModelType.prototype.describe = function () {\n        var _this = this;\n        // optimization: cache\n        return (\"{ \" +\n            this.propertyNames\n                .map(function (key) { return key + \": \" + _this.properties[key].describe(); })\n                .join(\"; \") +\n            \" }\");\n    };\n    ModelType.prototype.getDefaultSnapshot = function () {\n        return EMPTY_OBJECT;\n    };\n    ModelType.prototype.removeChild = function (node, subpath) {\n        node.storedValue[subpath] = undefined;\n    };\n    __decorate([\n        action\n    ], ModelType.prototype, \"applySnapshot\", null);\n    return ModelType;\n}(ComplexType));\n/**\n * `types.model` - Creates a new model type by providing a name, properties, volatile state and actions.\n *\n * See the [model type](/concepts/trees#creating-models) description or the [getting started](intro/getting-started.md#getting-started-1) tutorial.\n */\nfunction model() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var name = typeof args[0] === \"string\" ? args.shift() : \"AnonymousModel\";\n    var properties = args.shift() || {};\n    return new ModelType({ name: name, properties: properties });\n}\n/**\n * `types.compose` - Composes a new model from one or more existing model types.\n * This method can be invoked in two forms:\n * Given 2 or more model types, the types are composed into a new Type.\n * Given first parameter as a string and 2 or more model types,\n * the types are composed into a new Type with the given name\n */\nfunction compose() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    // TODO: just join the base type names if no name is provided\n    var hasTypename = typeof args[0] === \"string\";\n    var typeName = hasTypename ? args[0] : \"AnonymousModel\";\n    if (hasTypename) {\n        args.shift();\n    }\n    // check all parameters\n    if (devMode()) {\n        args.forEach(function (type, i) {\n            assertArg(type, isModelType, \"mobx-state-tree model type\", hasTypename ? i + 2 : i + 1);\n        });\n    }\n    return args\n        .reduce(function (prev, cur) {\n        return prev.cloneAndEnhance({\n            name: prev.name + \"_\" + cur.name,\n            properties: cur.properties,\n            initializers: cur.initializers,\n            preProcessor: function (snapshot) {\n                return cur.applySnapshotPreProcessor(prev.applySnapshotPreProcessor(snapshot));\n            },\n            postProcessor: function (snapshot) {\n                return cur.applySnapshotPostProcessor(prev.applySnapshotPostProcessor(snapshot));\n            }\n        });\n    })\n        .named(typeName);\n}\n/**\n * Returns if a given value represents a model type.\n *\n * @param type\n * @returns\n */\nfunction isModelType(type) {\n    return isType(type) && (type.flags & TypeFlags.Object) > 0;\n}\n\n// TODO: implement CoreType using types.custom ?\n/**\n * @internal\n * @hidden\n */\nvar CoreType = /** @class */ (function (_super) {\n    __extends(CoreType, _super);\n    function CoreType(name, flags, checker, initializer) {\n        if (initializer === void 0) { initializer = identity; }\n        var _this = _super.call(this, name) || this;\n        _this.flags = flags;\n        _this.checker = checker;\n        _this.initializer = initializer;\n        _this.flags = flags;\n        return _this;\n    }\n    CoreType.prototype.describe = function () {\n        return this.name;\n    };\n    CoreType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        return createScalarNode(this, parent, subpath, environment, initialValue);\n    };\n    CoreType.prototype.createNewInstance = function (snapshot) {\n        return this.initializer(snapshot);\n    };\n    CoreType.prototype.isValidSnapshot = function (value, context) {\n        if (isPrimitive(value) && this.checker(value)) {\n            return typeCheckSuccess();\n        }\n        var typeName = this.name === \"Date\" ? \"Date or a unix milliseconds timestamp\" : this.name;\n        return typeCheckFailure(context, value, \"Value is not a \" + typeName);\n    };\n    return CoreType;\n}(SimpleType));\n/**\n * `types.string` - Creates a type that can only contain a string value.\n * This type is used for string values by default\n *\n * Example:\n * ```ts\n * const Person = types.model({\n *   firstName: types.string,\n *   lastName: \"Doe\"\n * })\n * ```\n */\n// tslint:disable-next-line:variable-name\nvar string = new CoreType(\"string\", TypeFlags.String, function (v) { return typeof v === \"string\"; });\n/**\n * `types.number` - Creates a type that can only contain a numeric value.\n * This type is used for numeric values by default\n *\n * Example:\n * ```ts\n * const Vector = types.model({\n *   x: types.number,\n *   y: 1.5\n * })\n * ```\n */\n// tslint:disable-next-line:variable-name\nvar number = new CoreType(\"number\", TypeFlags.Number, function (v) { return typeof v === \"number\"; });\n/**\n * `types.integer` - Creates a type that can only contain an integer value.\n * This type is used for integer values by default\n *\n * Example:\n * ```ts\n * const Size = types.model({\n *   width: types.integer,\n *   height: 10\n * })\n * ```\n */\n// tslint:disable-next-line:variable-name\nvar integer = new CoreType(\"integer\", TypeFlags.Integer, function (v) { return isInteger(v); });\n/**\n * `types.boolean` - Creates a type that can only contain a boolean value.\n * This type is used for boolean values by default\n *\n * Example:\n * ```ts\n * const Thing = types.model({\n *   isCool: types.boolean,\n *   isAwesome: false\n * })\n * ```\n */\n// tslint:disable-next-line:variable-name\nvar boolean = new CoreType(\"boolean\", TypeFlags.Boolean, function (v) { return typeof v === \"boolean\"; });\n/**\n * `types.null` - The type of the value `null`\n */\nvar nullType = new CoreType(\"null\", TypeFlags.Null, function (v) { return v === null; });\n/**\n * `types.undefined` - The type of the value `undefined`\n */\nvar undefinedType = new CoreType(\"undefined\", TypeFlags.Undefined, function (v) { return v === undefined; });\nvar _DatePrimitive = new CoreType(\"Date\", TypeFlags.Date, function (v) { return typeof v === \"number\" || v instanceof Date; }, function (v) { return (v instanceof Date ? v : new Date(v)); });\n_DatePrimitive.getSnapshot = function (node) {\n    return node.storedValue.getTime();\n};\n/**\n * `types.Date` - Creates a type that can only contain a javascript Date value.\n *\n * Example:\n * ```ts\n * const LogLine = types.model({\n *   timestamp: types.Date,\n * })\n *\n * LogLine.create({ timestamp: new Date() })\n * ```\n */\nvar DatePrimitive = _DatePrimitive;\n/**\n * @internal\n * @hidden\n */\nfunction getPrimitiveFactoryFromValue(value) {\n    switch (typeof value) {\n        case \"string\":\n            return string;\n        case \"number\":\n            return number; // In the future, isInteger(value) ? integer : number would be interesting, but would be too breaking for now\n        case \"boolean\":\n            return boolean;\n        case \"object\":\n            if (value instanceof Date)\n                return DatePrimitive;\n    }\n    throw fail$1(\"Cannot determine primitive type from value \" + value);\n}\n/**\n * Returns if a given value represents a primitive type.\n *\n * @param type\n * @returns\n */\nfunction isPrimitiveType(type) {\n    return (isType(type) &&\n        (type.flags &\n            (TypeFlags.String |\n                TypeFlags.Number |\n                TypeFlags.Integer |\n                TypeFlags.Boolean |\n                TypeFlags.Date)) >\n            0);\n}\n\n/**\n * @internal\n * @hidden\n */\nvar Literal = /** @class */ (function (_super) {\n    __extends(Literal, _super);\n    function Literal(value) {\n        var _this = _super.call(this, JSON.stringify(value)) || this;\n        _this.flags = TypeFlags.Literal;\n        _this.value = value;\n        return _this;\n    }\n    Literal.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        return createScalarNode(this, parent, subpath, environment, initialValue);\n    };\n    Literal.prototype.describe = function () {\n        return JSON.stringify(this.value);\n    };\n    Literal.prototype.isValidSnapshot = function (value, context) {\n        if (isPrimitive(value) && value === this.value) {\n            return typeCheckSuccess();\n        }\n        return typeCheckFailure(context, value, \"Value is not a literal \" + JSON.stringify(this.value));\n    };\n    return Literal;\n}(SimpleType));\n/**\n * `types.literal` - The literal type will return a type that will match only the exact given type.\n * The given value must be a primitive, in order to be serialized to a snapshot correctly.\n * You can use literal to match exact strings for example the exact male or female string.\n *\n * Example:\n * ```ts\n * const Person = types.model({\n *     name: types.string,\n *     gender: types.union(types.literal('male'), types.literal('female'))\n * })\n * ```\n *\n * @param value The value to use in the strict equal check\n * @returns\n */\nfunction literal(value) {\n    // check that the given value is a primitive\n    assertArg(value, isPrimitive, \"primitive\", 1);\n    return new Literal(value);\n}\n/**\n * Returns if a given value represents a literal type.\n *\n * @param type\n * @returns\n */\nfunction isLiteralType(type) {\n    return isType(type) && (type.flags & TypeFlags.Literal) > 0;\n}\n\nvar Refinement = /** @class */ (function (_super) {\n    __extends(Refinement, _super);\n    function Refinement(name, _subtype, _predicate, _message) {\n        var _this = _super.call(this, name) || this;\n        _this._subtype = _subtype;\n        _this._predicate = _predicate;\n        _this._message = _message;\n        return _this;\n    }\n    Object.defineProperty(Refinement.prototype, \"flags\", {\n        get: function () {\n            return this._subtype.flags | TypeFlags.Refinement;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Refinement.prototype.describe = function () {\n        return this.name;\n    };\n    Refinement.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        // create the child type\n        return this._subtype.instantiate(parent, subpath, environment, initialValue);\n    };\n    Refinement.prototype.isAssignableFrom = function (type) {\n        return this._subtype.isAssignableFrom(type);\n    };\n    Refinement.prototype.isValidSnapshot = function (value, context) {\n        var subtypeErrors = this._subtype.validate(value, context);\n        if (subtypeErrors.length > 0)\n            return subtypeErrors;\n        var snapshot = isStateTreeNode(value) ? getStateTreeNode(value).snapshot : value;\n        if (!this._predicate(snapshot)) {\n            return typeCheckFailure(context, value, this._message(value));\n        }\n        return typeCheckSuccess();\n    };\n    Refinement.prototype.reconcile = function (current, newValue, parent, subpath) {\n        return this._subtype.reconcile(current, newValue, parent, subpath);\n    };\n    Refinement.prototype.getSubTypes = function () {\n        return this._subtype;\n    };\n    return Refinement;\n}(BaseType));\n/**\n * `types.refinement` - Creates a type that is more specific than the base type, e.g. `types.refinement(types.string, value => value.length > 5)` to create a type of strings that can only be longer then 5.\n *\n * @param name\n * @param type\n * @param predicate\n * @returns\n */\nfunction refinement() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var name = typeof args[0] === \"string\" ? args.shift() : isType(args[0]) ? args[0].name : null;\n    var type = args[0];\n    var predicate = args[1];\n    var message = args[2]\n        ? args[2]\n        : function (v) { return \"Value does not respect the refinement predicate\"; };\n    // ensures all parameters are correct\n    assertIsType(type, [1, 2]);\n    assertIsString(name, 1);\n    assertIsFunction(predicate, [2, 3]);\n    assertIsFunction(message, [3, 4]);\n    return new Refinement(name, type, predicate, message);\n}\n/**\n * Returns if a given value is a refinement type.\n *\n * @param type\n * @returns\n */\nfunction isRefinementType(type) {\n    return (type.flags & TypeFlags.Refinement) > 0;\n}\n\n/**\n * `types.enumeration` - Can be used to create an string based enumeration.\n * (note: this methods is just sugar for a union of string literals)\n *\n * Example:\n * ```ts\n * const TrafficLight = types.model({\n *   color: types.enumeration(\"Color\", [\"Red\", \"Orange\", \"Green\"])\n * })\n * ```\n *\n * @param name descriptive name of the enumeration (optional)\n * @param options possible values this enumeration can have\n * @returns\n */\nfunction enumeration(name, options) {\n    var realOptions = typeof name === \"string\" ? options : name;\n    // check all options\n    if (devMode()) {\n        realOptions.forEach(function (option, i) {\n            assertIsString(option, i + 1);\n        });\n    }\n    var type = union.apply(void 0, __spread(realOptions.map(function (option) { return literal(\"\" + option); })));\n    if (typeof name === \"string\")\n        type.name = name;\n    return type;\n}\n\n/**\n * @internal\n * @hidden\n */\nvar Union = /** @class */ (function (_super) {\n    __extends(Union, _super);\n    function Union(name, _types, options) {\n        var _this = _super.call(this, name) || this;\n        _this._types = _types;\n        _this._eager = true;\n        options = __assign({ eager: true, dispatcher: undefined }, options);\n        _this._dispatcher = options.dispatcher;\n        if (!options.eager)\n            _this._eager = false;\n        return _this;\n    }\n    Object.defineProperty(Union.prototype, \"flags\", {\n        get: function () {\n            var result = TypeFlags.Union;\n            this._types.forEach(function (type) {\n                result |= type.flags;\n            });\n            return result;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Union.prototype.isAssignableFrom = function (type) {\n        return this._types.some(function (subType) { return subType.isAssignableFrom(type); });\n    };\n    Union.prototype.describe = function () {\n        return \"(\" + this._types.map(function (factory) { return factory.describe(); }).join(\" | \") + \")\";\n    };\n    Union.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        var type = this.determineType(initialValue, undefined);\n        if (!type)\n            throw fail$1(\"No matching type for union \" + this.describe()); // can happen in prod builds\n        return type.instantiate(parent, subpath, environment, initialValue);\n    };\n    Union.prototype.reconcile = function (current, newValue, parent, subpath) {\n        var type = this.determineType(newValue, current.type);\n        if (!type)\n            throw fail$1(\"No matching type for union \" + this.describe()); // can happen in prod builds\n        return type.reconcile(current, newValue, parent, subpath);\n    };\n    Union.prototype.determineType = function (value, reconcileCurrentType) {\n        // try the dispatcher, if defined\n        if (this._dispatcher) {\n            return this._dispatcher(value);\n        }\n        // find the most accomodating type\n        // if we are using reconciliation try the current node type first (fix for #1045)\n        if (reconcileCurrentType) {\n            if (reconcileCurrentType.is(value)) {\n                return reconcileCurrentType;\n            }\n            return this._types\n                .filter(function (t) { return t !== reconcileCurrentType; })\n                .find(function (type) { return type.is(value); });\n        }\n        else {\n            return this._types.find(function (type) { return type.is(value); });\n        }\n    };\n    Union.prototype.isValidSnapshot = function (value, context) {\n        if (this._dispatcher) {\n            return this._dispatcher(value).validate(value, context);\n        }\n        var allErrors = [];\n        var applicableTypes = 0;\n        for (var i = 0; i < this._types.length; i++) {\n            var type = this._types[i];\n            var errors = type.validate(value, context);\n            if (errors.length === 0) {\n                if (this._eager)\n                    return typeCheckSuccess();\n                else\n                    applicableTypes++;\n            }\n            else {\n                allErrors.push(errors);\n            }\n        }\n        if (applicableTypes === 1)\n            return typeCheckSuccess();\n        return typeCheckFailure(context, value, \"No type is applicable for the union\").concat(flattenTypeErrors(allErrors));\n    };\n    Union.prototype.getSubTypes = function () {\n        return this._types;\n    };\n    return Union;\n}(BaseType));\n/**\n * `types.union` - Create a union of multiple types. If the correct type cannot be inferred unambiguously from a snapshot, provide a dispatcher function of the form `(snapshot) => Type`.\n *\n * @param optionsOrType\n * @param otherTypes\n * @returns\n */\nfunction union(optionsOrType) {\n    var otherTypes = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        otherTypes[_i - 1] = arguments[_i];\n    }\n    var options = isType(optionsOrType) ? undefined : optionsOrType;\n    var types = isType(optionsOrType) ? __spread([optionsOrType], otherTypes) : otherTypes;\n    var name = \"(\" + types.map(function (type) { return type.name; }).join(\" | \") + \")\";\n    // check all options\n    if (devMode()) {\n        if (options) {\n            assertArg(options, function (o) { return isPlainObject(o); }, \"object { eager?: boolean, dispatcher?: Function }\", 1);\n        }\n        types.forEach(function (type, i) {\n            assertIsType(type, options ? i + 2 : i + 1);\n        });\n    }\n    return new Union(name, types, options);\n}\n/**\n * Returns if a given value represents a union type.\n *\n * @param type\n * @returns\n */\nfunction isUnionType(type) {\n    return (type.flags & TypeFlags.Union) > 0;\n}\n\n/**\n * @hidden\n * @internal\n */\nvar OptionalValue = /** @class */ (function (_super) {\n    __extends(OptionalValue, _super);\n    function OptionalValue(_subtype, _defaultValue, optionalValues) {\n        var _this = _super.call(this, _subtype.name) || this;\n        _this._subtype = _subtype;\n        _this._defaultValue = _defaultValue;\n        _this.optionalValues = optionalValues;\n        return _this;\n    }\n    Object.defineProperty(OptionalValue.prototype, \"flags\", {\n        get: function () {\n            return this._subtype.flags | TypeFlags.Optional;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OptionalValue.prototype.describe = function () {\n        return this._subtype.describe() + \"?\";\n    };\n    OptionalValue.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        if (this.optionalValues.indexOf(initialValue) >= 0) {\n            var defaultInstanceOrSnapshot = this.getDefaultInstanceOrSnapshot();\n            return this._subtype.instantiate(parent, subpath, environment, defaultInstanceOrSnapshot);\n        }\n        return this._subtype.instantiate(parent, subpath, environment, initialValue);\n    };\n    OptionalValue.prototype.reconcile = function (current, newValue, parent, subpath) {\n        return this._subtype.reconcile(current, this.optionalValues.indexOf(newValue) < 0 && this._subtype.is(newValue)\n            ? newValue\n            : this.getDefaultInstanceOrSnapshot(), parent, subpath);\n    };\n    OptionalValue.prototype.getDefaultInstanceOrSnapshot = function () {\n        var defaultInstanceOrSnapshot = typeof this._defaultValue === \"function\"\n            ? this._defaultValue()\n            : this._defaultValue;\n        // while static values are already snapshots and checked on types.optional\n        // generator functions must always be rechecked just in case\n        if (typeof this._defaultValue === \"function\") {\n            typecheckInternal(this, defaultInstanceOrSnapshot);\n        }\n        return defaultInstanceOrSnapshot;\n    };\n    OptionalValue.prototype.isValidSnapshot = function (value, context) {\n        // defaulted values can be skipped\n        if (this.optionalValues.indexOf(value) >= 0) {\n            return typeCheckSuccess();\n        }\n        // bounce validation to the sub-type\n        return this._subtype.validate(value, context);\n    };\n    OptionalValue.prototype.isAssignableFrom = function (type) {\n        return this._subtype.isAssignableFrom(type);\n    };\n    OptionalValue.prototype.getSubTypes = function () {\n        return this._subtype;\n    };\n    return OptionalValue;\n}(BaseType));\nfunction checkOptionalPreconditions(type, defaultValueOrFunction) {\n    // make sure we never pass direct instances\n    if (typeof defaultValueOrFunction !== \"function\" && isStateTreeNode(defaultValueOrFunction)) {\n        throw fail$1(\"default value cannot be an instance, pass a snapshot or a function that creates an instance/snapshot instead\");\n    }\n    assertIsType(type, 1);\n    if (devMode()) {\n        // we only check default values if they are passed directly\n        // if they are generator functions they will be checked once they are generated\n        // we don't check generator function results here to avoid generating a node just for type-checking purposes\n        // which might generate side-effects\n        if (typeof defaultValueOrFunction !== \"function\") {\n            typecheckInternal(type, defaultValueOrFunction);\n        }\n    }\n}\n/**\n * `types.optional` - Can be used to create a property with a default value.\n *\n * Depending on the third argument (`optionalValues`) there are two ways of operation:\n * - If the argument is not provided, then if a value is not provided in the snapshot (`undefined` or missing),\n *   it will default to the provided `defaultValue`\n * - If the argument is provided, then if the value in the snapshot matches one of the optional values inside the array then it will\n *   default to the provided `defaultValue`. Additionally, if one of the optional values inside the array is `undefined` then a missing\n *   property is also valid.\n *\n *   Note that it is also possible to include values of the same type as the intended subtype as optional values,\n *   in this case the optional value will be transformed into the `defaultValue` (e.g. `types.optional(types.string, \"unnamed\", [undefined, \"\"])`\n *   will transform the snapshot values `undefined` (and therefore missing) and empty strings into the string `\"unnamed\"` when it gets\n *   instantiated).\n *\n * If `defaultValue` is a function, the function will be invoked for every new instance.\n * Applying a snapshot in which the optional value is one of the optional values (or `undefined`/_not_ present if none are provided) causes the\n * value to be reset.\n *\n * Example:\n * ```ts\n * const Todo = types.model({\n *   title: types.string,\n *   subtitle1: types.optional(types.string, \"\", [null]),\n *   subtitle2: types.optional(types.string, \"\", [null, undefined]),\n *   done: types.optional(types.boolean, false),\n *   created: types.optional(types.Date, () => new Date()),\n * })\n *\n * // if done is missing / undefined it will become false\n * // if created is missing / undefined it will get a freshly generated timestamp\n * // if subtitle1 is null it will default to \"\", but it cannot be missing or undefined\n * // if subtitle2 is null or undefined it will default to \"\"; since it can be undefined it can also be missing\n * const todo = Todo.create({ title: \"Get coffee\", subtitle1: null })\n * ```\n *\n * @param type\n * @param defaultValueOrFunction\n * @param optionalValues an optional array with zero or more primitive values (string, number, boolean, null or undefined)\n *                       that will be converted into the default. `[ undefined ]` is assumed when none is provided\n * @returns\n */\nfunction optional(type, defaultValueOrFunction, optionalValues) {\n    checkOptionalPreconditions(type, defaultValueOrFunction);\n    return new OptionalValue(type, defaultValueOrFunction, optionalValues ? optionalValues : undefinedAsOptionalValues);\n}\nvar undefinedAsOptionalValues = [undefined];\n/**\n * Returns if a value represents an optional type.\n *\n * @template IT\n * @param type\n * @returns\n */\nfunction isOptionalType(type) {\n    return isType(type) && (type.flags & TypeFlags.Optional) > 0;\n}\n\nvar optionalUndefinedType = optional(undefinedType, undefined);\nvar optionalNullType = optional(nullType, null);\n/**\n * `types.maybe` - Maybe will make a type nullable, and also optional.\n * The value `undefined` will be used to represent nullability.\n *\n * @param type\n * @returns\n */\nfunction maybe(type) {\n    assertIsType(type, 1);\n    return union(type, optionalUndefinedType);\n}\n/**\n * `types.maybeNull` - Maybe will make a type nullable, and also optional.\n * The value `null` will be used to represent no value.\n *\n * @param type\n * @returns\n */\nfunction maybeNull(type) {\n    assertIsType(type, 1);\n    return union(type, optionalNullType);\n}\n\nvar Late = /** @class */ (function (_super) {\n    __extends(Late, _super);\n    function Late(name, _definition) {\n        var _this = _super.call(this, name) || this;\n        _this._definition = _definition;\n        return _this;\n    }\n    Object.defineProperty(Late.prototype, \"flags\", {\n        get: function () {\n            return (this._subType ? this._subType.flags : 0) | TypeFlags.Late;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Late.prototype.getSubType = function (mustSucceed) {\n        if (!this._subType) {\n            var t = undefined;\n            try {\n                t = this._definition();\n            }\n            catch (e) {\n                if (e instanceof ReferenceError)\n                    // can happen in strict ES5 code when a definition is self refering\n                    t = undefined;\n                else\n                    throw e;\n            }\n            if (mustSucceed && t === undefined)\n                throw fail$1(\"Late type seems to be used too early, the definition (still) returns undefined\");\n            if (t) {\n                if (devMode() && !isType(t))\n                    throw fail$1(\"Failed to determine subtype, make sure types.late returns a type definition.\");\n                this._subType = t;\n            }\n        }\n        return this._subType;\n    };\n    Late.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        return this.getSubType(true).instantiate(parent, subpath, environment, initialValue);\n    };\n    Late.prototype.reconcile = function (current, newValue, parent, subpath) {\n        return this.getSubType(true).reconcile(current, newValue, parent, subpath);\n    };\n    Late.prototype.describe = function () {\n        var t = this.getSubType(false);\n        return t ? t.name : \"<uknown late type>\";\n    };\n    Late.prototype.isValidSnapshot = function (value, context) {\n        var t = this.getSubType(false);\n        if (!t) {\n            // See #916; the variable the definition closure is pointing to wasn't defined yet, so can't be evaluted yet here\n            return typeCheckSuccess();\n        }\n        return t.validate(value, context);\n    };\n    Late.prototype.isAssignableFrom = function (type) {\n        var t = this.getSubType(false);\n        return t ? t.isAssignableFrom(type) : false;\n    };\n    Late.prototype.getSubTypes = function () {\n        var subtype = this.getSubType(false);\n        return subtype ? subtype : cannotDetermineSubtype;\n    };\n    return Late;\n}(BaseType));\n/**\n * `types.late` - Defines a type that gets implemented later. This is useful when you have to deal with circular dependencies.\n * Please notice that when defining circular dependencies TypeScript isn't smart enough to inference them.\n *\n * Example:\n * ```ts\n *   // TypeScript isn't smart enough to infer self referencing types.\n *  const Node = types.model({\n *       children: types.array(types.late((): IAnyModelType => Node)) // then typecast each array element to Instance<typeof Node>\n *  })\n * ```\n *\n * @param name The name to use for the type that will be returned.\n * @param type A function that returns the type that will be defined.\n * @returns\n */\nfunction late(nameOrType, maybeType) {\n    var name = typeof nameOrType === \"string\" ? nameOrType : \"late(\" + nameOrType.toString() + \")\";\n    var type = typeof nameOrType === \"string\" ? maybeType : nameOrType;\n    // checks that the type is actually a late type\n    if (devMode()) {\n        if (!(typeof type === \"function\" && type.length === 0))\n            throw fail$1(\"Invalid late type, expected a function with zero arguments that returns a type, got: \" +\n                type);\n    }\n    return new Late(name, type);\n}\n/**\n * Returns if a given value represents a late type.\n *\n * @param type\n * @returns\n */\nfunction isLateType(type) {\n    return isType(type) && (type.flags & TypeFlags.Late) > 0;\n}\n\n/**\n * @internal\n * @hidden\n */\nvar Frozen = /** @class */ (function (_super) {\n    __extends(Frozen, _super);\n    function Frozen(subType) {\n        var _this = _super.call(this, subType ? \"frozen(\" + subType.name + \")\" : \"frozen\") || this;\n        _this.subType = subType;\n        _this.flags = TypeFlags.Frozen;\n        return _this;\n    }\n    Frozen.prototype.describe = function () {\n        return \"<any immutable value>\";\n    };\n    Frozen.prototype.instantiate = function (parent, subpath, environment, value) {\n        // create the node\n        return createScalarNode(this, parent, subpath, environment, deepFreeze(value));\n    };\n    Frozen.prototype.isValidSnapshot = function (value, context) {\n        if (!isSerializable(value)) {\n            return typeCheckFailure(context, value, \"Value is not serializable and cannot be frozen\");\n        }\n        if (this.subType)\n            return this.subType.validate(value, context);\n        return typeCheckSuccess();\n    };\n    return Frozen;\n}(SimpleType));\nvar untypedFrozenInstance = new Frozen();\n/**\n * `types.frozen` - Frozen can be used to store any value that is serializable in itself (that is valid JSON).\n * Frozen values need to be immutable or treated as if immutable. They need be serializable as well.\n * Values stored in frozen will snapshotted as-is by MST, and internal changes will not be tracked.\n *\n * This is useful to store complex, but immutable values like vectors etc. It can form a powerful bridge to parts of your application that should be immutable, or that assume data to be immutable.\n *\n * Note: if you want to store free-form state that is mutable, or not serializeable, consider using volatile state instead.\n *\n * Frozen properties can be defined in three different ways\n * 1. `types.frozen(SubType)` - provide a valid MST type and frozen will check if the provided data conforms the snapshot for that type\n * 2. `types.frozen({ someDefaultValue: true})` - provide a primitive value, object or array, and MST will infer the type from that object, and also make it the default value for the field\n * 3. `types.frozen<TypeScriptType>()` - provide a typescript type, to help in strongly typing the field (design time only)\n *\n * Example:\n * ```ts\n * const GameCharacter = types.model({\n *   name: string,\n *   location: types.frozen({ x: 0, y: 0})\n * })\n *\n * const hero = GameCharacter.create({\n *   name: \"Mario\",\n *   location: { x: 7, y: 4 }\n * })\n *\n * hero.location = { x: 10, y: 2 } // OK\n * hero.location.x = 7 // Not ok!\n * ```\n *\n * ```ts\n * type Point = { x: number, y: number }\n *    const Mouse = types.model({\n *         loc: types.frozen<Point>()\n *    })\n * ```\n *\n * @param defaultValueOrType\n * @returns\n */\nfunction frozen(arg) {\n    if (arguments.length === 0)\n        return untypedFrozenInstance;\n    else if (isType(arg))\n        return new Frozen(arg);\n    else\n        return optional(untypedFrozenInstance, arg);\n}\n/**\n * Returns if a given value represents a frozen type.\n *\n * @param type\n * @returns\n */\nfunction isFrozenType(type) {\n    return isType(type) && (type.flags & TypeFlags.Frozen) > 0;\n}\n\nfunction getInvalidationCause(hook) {\n    switch (hook) {\n        case Hook.beforeDestroy:\n            return \"destroy\";\n        case Hook.beforeDetach:\n            return \"detach\";\n        default:\n            return undefined;\n    }\n}\nvar StoredReference = /** @class */ (function () {\n    function StoredReference(value, targetType) {\n        this.targetType = targetType;\n        if (isValidIdentifier(value)) {\n            this.identifier = value;\n        }\n        else if (isStateTreeNode(value)) {\n            var targetNode = getStateTreeNode(value);\n            if (!targetNode.identifierAttribute)\n                throw fail$1(\"Can only store references with a defined identifier attribute.\");\n            var id = targetNode.unnormalizedIdentifier;\n            if (id === null || id === undefined) {\n                throw fail$1(\"Can only store references to tree nodes with a defined identifier.\");\n            }\n            this.identifier = id;\n        }\n        else {\n            throw fail$1(\"Can only store references to tree nodes or identifiers, got: '\" + value + \"'\");\n        }\n    }\n    StoredReference.prototype.updateResolvedReference = function (node) {\n        var normalizedId = normalizeIdentifier(this.identifier);\n        var root = node.root;\n        var lastCacheModification = root.identifierCache.getLastCacheModificationPerId(normalizedId);\n        if (!this.resolvedReference ||\n            this.resolvedReference.lastCacheModification !== lastCacheModification) {\n            var targetType = this.targetType;\n            // reference was initialized with the identifier of the target\n            var target = root.identifierCache.resolve(targetType, normalizedId);\n            if (!target) {\n                throw new InvalidReferenceError(\"[mobx-state-tree] Failed to resolve reference '\" + this.identifier + \"' to type '\" + this.targetType.name + \"' (from node: \" + node.path + \")\");\n            }\n            this.resolvedReference = {\n                node: target,\n                lastCacheModification: lastCacheModification\n            };\n        }\n    };\n    Object.defineProperty(StoredReference.prototype, \"resolvedValue\", {\n        get: function () {\n            this.updateResolvedReference(this.node);\n            return this.resolvedReference.node.value;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return StoredReference;\n}());\n/**\n * @internal\n * @hidden\n */\nvar InvalidReferenceError = /** @class */ (function (_super) {\n    __extends(InvalidReferenceError, _super);\n    function InvalidReferenceError(m) {\n        var _this = _super.call(this, m) || this;\n        Object.setPrototypeOf(_this, InvalidReferenceError.prototype);\n        return _this;\n    }\n    return InvalidReferenceError;\n}(Error));\n/**\n * @internal\n * @hidden\n */\nvar BaseReferenceType = /** @class */ (function (_super) {\n    __extends(BaseReferenceType, _super);\n    function BaseReferenceType(targetType, onInvalidated) {\n        var _this = _super.call(this, \"reference(\" + targetType.name + \")\") || this;\n        _this.targetType = targetType;\n        _this.onInvalidated = onInvalidated;\n        _this.flags = TypeFlags.Reference;\n        return _this;\n    }\n    BaseReferenceType.prototype.describe = function () {\n        return this.name;\n    };\n    BaseReferenceType.prototype.isAssignableFrom = function (type) {\n        return this.targetType.isAssignableFrom(type);\n    };\n    BaseReferenceType.prototype.isValidSnapshot = function (value, context) {\n        return isValidIdentifier(value)\n            ? typeCheckSuccess()\n            : typeCheckFailure(context, value, \"Value is not a valid identifier, which is a string or a number\");\n    };\n    BaseReferenceType.prototype.fireInvalidated = function (cause, storedRefNode, referenceId, refTargetNode) {\n        // to actually invalidate a reference we need an alive parent,\n        // since it is a scalar value (immutable-ish) and we need to change it\n        // from the parent\n        var storedRefParentNode = storedRefNode.parent;\n        if (!storedRefParentNode || !storedRefParentNode.isAlive) {\n            return;\n        }\n        var storedRefParentValue = storedRefParentNode.storedValue;\n        if (!storedRefParentValue) {\n            return;\n        }\n        this.onInvalidated({\n            cause: cause,\n            parent: storedRefParentValue,\n            invalidTarget: refTargetNode ? refTargetNode.storedValue : undefined,\n            invalidId: referenceId,\n            replaceRef: function (newRef) {\n                applyPatch(storedRefNode.root.storedValue, {\n                    op: \"replace\",\n                    value: newRef,\n                    path: storedRefNode.path\n                });\n            },\n            removeRef: function () {\n                if (isModelType(storedRefParentNode.type)) {\n                    this.replaceRef(undefined);\n                }\n                else {\n                    applyPatch(storedRefNode.root.storedValue, {\n                        op: \"remove\",\n                        path: storedRefNode.path\n                    });\n                }\n            }\n        });\n    };\n    BaseReferenceType.prototype.addTargetNodeWatcher = function (storedRefNode, referenceId) {\n        var _this = this;\n        // this will make sure the target node becomes created\n        var refTargetValue = this.getValue(storedRefNode);\n        if (!refTargetValue) {\n            return undefined;\n        }\n        var refTargetNode = getStateTreeNode(refTargetValue);\n        var hookHandler = function (_, refTargetNodeHook) {\n            var cause = getInvalidationCause(refTargetNodeHook);\n            if (!cause) {\n                return;\n            }\n            _this.fireInvalidated(cause, storedRefNode, referenceId, refTargetNode);\n        };\n        var refTargetDetachHookDisposer = refTargetNode.registerHook(Hook.beforeDetach, hookHandler);\n        var refTargetDestroyHookDisposer = refTargetNode.registerHook(Hook.beforeDestroy, hookHandler);\n        return function () {\n            refTargetDetachHookDisposer();\n            refTargetDestroyHookDisposer();\n        };\n    };\n    BaseReferenceType.prototype.watchTargetNodeForInvalidations = function (storedRefNode, identifier, customGetSet) {\n        var _this = this;\n        if (!this.onInvalidated) {\n            return;\n        }\n        var onRefTargetDestroyedHookDisposer;\n        // get rid of the watcher hook when the stored ref node is destroyed\n        // detached is ignored since scalar nodes (where the reference resides) cannot be detached\n        storedRefNode.registerHook(Hook.beforeDestroy, function () {\n            if (onRefTargetDestroyedHookDisposer) {\n                onRefTargetDestroyedHookDisposer();\n            }\n        });\n        var startWatching = function (sync) {\n            // re-create hook in case the stored ref gets reattached\n            if (onRefTargetDestroyedHookDisposer) {\n                onRefTargetDestroyedHookDisposer();\n            }\n            // make sure the target node is actually there and initialized\n            var storedRefParentNode = storedRefNode.parent;\n            var storedRefParentValue = storedRefParentNode && storedRefParentNode.storedValue;\n            if (storedRefParentNode && storedRefParentNode.isAlive && storedRefParentValue) {\n                var refTargetNodeExists = void 0;\n                if (customGetSet) {\n                    refTargetNodeExists = !!customGetSet.get(identifier, storedRefParentValue);\n                }\n                else {\n                    refTargetNodeExists = storedRefNode.root.identifierCache.has(_this.targetType, normalizeIdentifier(identifier));\n                }\n                if (!refTargetNodeExists) {\n                    // we cannot change the reference in sync mode\n                    // since we are in the middle of a reconciliation/instantiation and the change would be overwritten\n                    // for those cases just let the wrong reference be assigned and fail upon usage\n                    // (like current references do)\n                    // this means that effectively this code will only run when it is created from a snapshot\n                    if (!sync) {\n                        _this.fireInvalidated(\"invalidSnapshotReference\", storedRefNode, identifier, null);\n                    }\n                }\n                else {\n                    onRefTargetDestroyedHookDisposer = _this.addTargetNodeWatcher(storedRefNode, identifier);\n                }\n            }\n        };\n        if (storedRefNode.state === NodeLifeCycle.FINALIZED) {\n            // already attached, so the whole tree is ready\n            startWatching(true);\n        }\n        else {\n            if (!storedRefNode.isRoot) {\n                // start watching once the whole tree is ready\n                storedRefNode.root.registerHook(Hook.afterCreationFinalization, function () {\n                    // make sure to attach it so it can start listening\n                    if (storedRefNode.parent) {\n                        storedRefNode.parent.createObservableInstanceIfNeeded();\n                    }\n                });\n            }\n            // start watching once the node is attached somewhere / parent changes\n            storedRefNode.registerHook(Hook.afterAttach, function () {\n                startWatching(false);\n            });\n        }\n    };\n    return BaseReferenceType;\n}(SimpleType));\n/**\n * @internal\n * @hidden\n */\nvar IdentifierReferenceType = /** @class */ (function (_super) {\n    __extends(IdentifierReferenceType, _super);\n    function IdentifierReferenceType(targetType, onInvalidated) {\n        return _super.call(this, targetType, onInvalidated) || this;\n    }\n    IdentifierReferenceType.prototype.getValue = function (storedRefNode) {\n        if (!storedRefNode.isAlive)\n            return undefined;\n        var storedRef = storedRefNode.storedValue;\n        return storedRef.resolvedValue;\n    };\n    IdentifierReferenceType.prototype.getSnapshot = function (storedRefNode) {\n        var ref = storedRefNode.storedValue;\n        return ref.identifier;\n    };\n    IdentifierReferenceType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        var identifier = isStateTreeNode(initialValue)\n            ? getIdentifier(initialValue)\n            : initialValue;\n        var storedRef = new StoredReference(initialValue, this.targetType);\n        var storedRefNode = createScalarNode(this, parent, subpath, environment, storedRef);\n        storedRef.node = storedRefNode;\n        this.watchTargetNodeForInvalidations(storedRefNode, identifier, undefined);\n        return storedRefNode;\n    };\n    IdentifierReferenceType.prototype.reconcile = function (current, newValue, parent, subpath) {\n        if (!current.isDetaching && current.type === this) {\n            var compareByValue = isStateTreeNode(newValue);\n            var ref = current.storedValue;\n            if ((!compareByValue && ref.identifier === newValue) ||\n                (compareByValue && ref.resolvedValue === newValue)) {\n                current.setParent(parent, subpath);\n                return current;\n            }\n        }\n        var newNode = this.instantiate(parent, subpath, undefined, newValue);\n        current.die(); // noop if detaching\n        return newNode;\n    };\n    return IdentifierReferenceType;\n}(BaseReferenceType));\n/**\n * @internal\n * @hidden\n */\nvar CustomReferenceType = /** @class */ (function (_super) {\n    __extends(CustomReferenceType, _super);\n    function CustomReferenceType(targetType, options, onInvalidated) {\n        var _this = _super.call(this, targetType, onInvalidated) || this;\n        _this.options = options;\n        return _this;\n    }\n    CustomReferenceType.prototype.getValue = function (storedRefNode) {\n        if (!storedRefNode.isAlive)\n            return undefined;\n        var referencedNode = this.options.get(storedRefNode.storedValue, storedRefNode.parent ? storedRefNode.parent.storedValue : null);\n        return referencedNode;\n    };\n    CustomReferenceType.prototype.getSnapshot = function (storedRefNode) {\n        return storedRefNode.storedValue;\n    };\n    CustomReferenceType.prototype.instantiate = function (parent, subpath, environment, newValue) {\n        var identifier = isStateTreeNode(newValue)\n            ? this.options.set(newValue, parent ? parent.storedValue : null)\n            : newValue;\n        var storedRefNode = createScalarNode(this, parent, subpath, environment, identifier);\n        this.watchTargetNodeForInvalidations(storedRefNode, identifier, this.options);\n        return storedRefNode;\n    };\n    CustomReferenceType.prototype.reconcile = function (current, newValue, parent, subpath) {\n        var newIdentifier = isStateTreeNode(newValue)\n            ? this.options.set(newValue, current ? current.storedValue : null)\n            : newValue;\n        if (!current.isDetaching &&\n            current.type === this &&\n            current.storedValue === newIdentifier) {\n            current.setParent(parent, subpath);\n            return current;\n        }\n        var newNode = this.instantiate(parent, subpath, undefined, newIdentifier);\n        current.die(); // noop if detaching\n        return newNode;\n    };\n    return CustomReferenceType;\n}(BaseReferenceType));\n/**\n * `types.reference` - Creates a reference to another type, which should have defined an identifier.\n * See also the [reference and identifiers](https://github.com/mobxjs/mobx-state-tree#references-and-identifiers) section.\n */\nfunction reference(subType, options) {\n    assertIsType(subType, 1);\n    if (devMode()) {\n        if (arguments.length === 2 && typeof arguments[1] === \"string\") {\n            // istanbul ignore next\n            throw fail$1(\"References with base path are no longer supported. Please remove the base path.\");\n        }\n    }\n    var getSetOptions = options ? options : undefined;\n    var onInvalidated = options\n        ? options.onInvalidated\n        : undefined;\n    if (getSetOptions && (getSetOptions.get || getSetOptions.set)) {\n        if (devMode()) {\n            if (!getSetOptions.get || !getSetOptions.set) {\n                throw fail$1(\"reference options must either contain both a 'get' and a 'set' method or none of them\");\n            }\n        }\n        return new CustomReferenceType(subType, {\n            get: getSetOptions.get,\n            set: getSetOptions.set\n        }, onInvalidated);\n    }\n    else {\n        return new IdentifierReferenceType(subType, onInvalidated);\n    }\n}\n/**\n * Returns if a given value represents a reference type.\n *\n * @param type\n * @returns\n */\nfunction isReferenceType(type) {\n    return (type.flags & TypeFlags.Reference) > 0;\n}\n/**\n * `types.safeReference` - A safe reference is like a standard reference, except that it accepts the undefined value by default\n * and automatically sets itself to undefined (when the parent is a model) / removes itself from arrays and maps\n * when the reference it is pointing to gets detached/destroyed.\n *\n * The optional options parameter object accepts a parameter named `acceptsUndefined`, which is set to true by default, so it is suitable\n * for model properties.\n * When used inside collections (arrays/maps), it is recommended to set this option to false so it can't take undefined as value,\n * which is usually the desired in those cases.\n *\n * Strictly speaking it is a `types.maybe(types.reference(X))` (when `acceptsUndefined` is set to true, the default) and\n * `types.reference(X)` (when `acceptsUndefined` is set to false), both of them with a customized `onInvalidated` option.\n *\n * @param subType\n * @param options\n * @returns\n */\nfunction safeReference(subType, options) {\n    var refType = reference(subType, __assign(__assign({}, options), { onInvalidated: function (ev) {\n            if (options && options.onInvalidated) {\n                options.onInvalidated(ev);\n            }\n            ev.removeRef();\n        } }));\n    if (options && options.acceptsUndefined === false) {\n        return refType;\n    }\n    else {\n        return maybe(refType);\n    }\n}\n\nvar BaseIdentifierType = /** @class */ (function (_super) {\n    __extends(BaseIdentifierType, _super);\n    function BaseIdentifierType(name, validType) {\n        var _this = _super.call(this, name) || this;\n        _this.validType = validType;\n        _this.flags = TypeFlags.Identifier;\n        return _this;\n    }\n    BaseIdentifierType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        if (!parent || !(parent.type instanceof ModelType))\n            throw fail$1(\"Identifier types can only be instantiated as direct child of a model type\");\n        return createScalarNode(this, parent, subpath, environment, initialValue);\n    };\n    BaseIdentifierType.prototype.reconcile = function (current, newValue, parent, subpath) {\n        // we don't consider detaching here since identifier are scalar nodes, and scalar nodes cannot be detached\n        if (current.storedValue !== newValue)\n            throw fail$1(\"Tried to change identifier from '\" + current.storedValue + \"' to '\" + newValue + \"'. Changing identifiers is not allowed.\");\n        current.setParent(parent, subpath);\n        return current;\n    };\n    BaseIdentifierType.prototype.isValidSnapshot = function (value, context) {\n        if (typeof value !== this.validType) {\n            return typeCheckFailure(context, value, \"Value is not a valid \" + this.describe() + \", expected a \" + this.validType);\n        }\n        return typeCheckSuccess();\n    };\n    return BaseIdentifierType;\n}(SimpleType));\n/**\n * @internal\n * @hidden\n */\nvar IdentifierType = /** @class */ (function (_super) {\n    __extends(IdentifierType, _super);\n    function IdentifierType() {\n        var _this = _super.call(this, \"identifier\", \"string\") || this;\n        _this.flags = TypeFlags.Identifier;\n        return _this;\n    }\n    IdentifierType.prototype.describe = function () {\n        return \"identifier\";\n    };\n    return IdentifierType;\n}(BaseIdentifierType));\n/**\n * @internal\n * @hidden\n */\nvar IdentifierNumberType = /** @class */ (function (_super) {\n    __extends(IdentifierNumberType, _super);\n    function IdentifierNumberType() {\n        return _super.call(this, \"identifierNumber\", \"number\") || this;\n    }\n    IdentifierNumberType.prototype.getSnapshot = function (node) {\n        return node.storedValue;\n    };\n    IdentifierNumberType.prototype.describe = function () {\n        return \"identifierNumber\";\n    };\n    return IdentifierNumberType;\n}(BaseIdentifierType));\n/**\n * `types.identifier` - Identifiers are used to make references, lifecycle events and reconciling works.\n * Inside a state tree, for each type can exist only one instance for each given identifier.\n * For example there couldn't be 2 instances of user with id 1. If you need more, consider using references.\n * Identifier can be used only as type property of a model.\n * This type accepts as parameter the value type of the identifier field that can be either string or number.\n *\n * Example:\n * ```ts\n *  const Todo = types.model(\"Todo\", {\n *      id: types.identifier,\n *      title: types.string\n *  })\n * ```\n *\n * @returns\n */\nvar identifier = new IdentifierType();\n/**\n * `types.identifierNumber` - Similar to `types.identifier`. This one will serialize from / to a number when applying snapshots\n *\n * Example:\n * ```ts\n *  const Todo = types.model(\"Todo\", {\n *      id: types.identifierNumber,\n *      title: types.string\n *  })\n * ```\n *\n * @returns\n */\nvar identifierNumber = new IdentifierNumberType();\n/**\n * Returns if a given value represents an identifier type.\n *\n * @param type\n * @returns\n */\nfunction isIdentifierType(type) {\n    return isType(type) && (type.flags & TypeFlags.Identifier) > 0;\n}\n/**\n * @internal\n * @hidden\n */\nfunction normalizeIdentifier(id) {\n    return \"\" + id;\n}\n/**\n * @internal\n * @hidden\n */\nfunction isValidIdentifier(id) {\n    return typeof id === \"string\" || typeof id === \"number\";\n}\n/**\n * @internal\n * @hidden\n */\nfunction assertIsValidIdentifier(id, argNumber) {\n    assertArg(id, isValidIdentifier, \"string or number (identifier)\", argNumber);\n}\n\n/**\n * `types.custom` - Creates a custom type. Custom types can be used for arbitrary immutable values, that have a serializable representation. For example, to create your own Date representation, Decimal type etc.\n *\n * The signature of the options is:\n * ```ts\n * export interface CustomTypeOptions<S, T> {\n *     // Friendly name\n *     name: string\n *     // given a serialized value and environment, how to turn it into the target type\n *     fromSnapshot(snapshot: S, env: any): T\n *     // return the serialization of the current value\n *     toSnapshot(value: T): S\n *     // if true, this is a converted value, if false, it's a snapshot\n *     isTargetType(value: T | S): value is T\n *     // a non empty string is assumed to be a validation error\n *     getValidationMessage?(snapshot: S): string\n * }\n * ```\n *\n * Example:\n * ```ts\n * const DecimalPrimitive = types.custom<string, Decimal>({\n *     name: \"Decimal\",\n *     fromSnapshot(value: string) {\n *         return new Decimal(value)\n *     },\n *     toSnapshot(value: Decimal) {\n *         return value.toString()\n *     },\n *     isTargetType(value: string | Decimal): boolean {\n *         return value instanceof Decimal\n *     },\n *     getValidationMessage(value: string): string {\n *         if (/^-?\\d+\\.\\d+$/.test(value)) return \"\" // OK\n *         return `'${value}' doesn't look like a valid decimal number`\n *     }\n * })\n *\n * const Wallet = types.model({\n *     balance: DecimalPrimitive\n * })\n * ```\n *\n * @param options\n * @returns\n */\nfunction custom(options) {\n    return new CustomType(options);\n}\n/**\n * @internal\n * @hidden\n */\nvar CustomType = /** @class */ (function (_super) {\n    __extends(CustomType, _super);\n    function CustomType(options) {\n        var _this = _super.call(this, options.name) || this;\n        _this.options = options;\n        _this.flags = TypeFlags.Custom;\n        return _this;\n    }\n    CustomType.prototype.describe = function () {\n        return this.name;\n    };\n    CustomType.prototype.isValidSnapshot = function (value, context) {\n        if (this.options.isTargetType(value))\n            return typeCheckSuccess();\n        var typeError = this.options.getValidationMessage(value);\n        if (typeError) {\n            return typeCheckFailure(context, value, \"Invalid value for type '\" + this.name + \"': \" + typeError);\n        }\n        return typeCheckSuccess();\n    };\n    CustomType.prototype.getSnapshot = function (node) {\n        return this.options.toSnapshot(node.storedValue);\n    };\n    CustomType.prototype.instantiate = function (parent, subpath, environment, initialValue) {\n        var valueToStore = this.options.isTargetType(initialValue)\n            ? initialValue\n            : this.options.fromSnapshot(initialValue, parent && parent.root.environment);\n        return createScalarNode(this, parent, subpath, environment, valueToStore);\n    };\n    CustomType.prototype.reconcile = function (current, value, parent, subpath) {\n        var isSnapshot = !this.options.isTargetType(value);\n        // in theory customs use scalar nodes which cannot be detached, but still...\n        if (!current.isDetaching) {\n            var unchanged = current.type === this &&\n                (isSnapshot ? value === current.snapshot : value === current.storedValue);\n            if (unchanged) {\n                current.setParent(parent, subpath);\n                return current;\n            }\n        }\n        var valueToStore = isSnapshot\n            ? this.options.fromSnapshot(value, parent.root.environment)\n            : value;\n        var newNode = this.instantiate(parent, subpath, undefined, valueToStore);\n        current.die(); // noop if detaching\n        return newNode;\n    };\n    return CustomType;\n}(SimpleType));\n\n// we import the types to re-export them inside types.\nvar types = {\n    enumeration: enumeration,\n    model: model,\n    compose: compose,\n    custom: custom,\n    reference: reference,\n    safeReference: safeReference,\n    union: union,\n    optional: optional,\n    literal: literal,\n    maybe: maybe,\n    maybeNull: maybeNull,\n    refinement: refinement,\n    string: string,\n    boolean: boolean,\n    number: number,\n    integer: integer,\n    Date: DatePrimitive,\n    map: map,\n    array: array,\n    frozen: frozen,\n    identifier: identifier,\n    identifierNumber: identifierNumber,\n    late: late,\n    undefined: undefinedType,\n    null: nullType,\n    snapshotProcessor: snapshotProcessor\n};\n\nexport { addDisposer, addMiddleware, applyAction, applyPatch, applySnapshot, cast, castFlowReturn, castToReferenceSnapshot, castToSnapshot, clone, createActionTrackingMiddleware, createActionTrackingMiddleware2, decorate, destroy, detach, escapeJsonPath, flow, getChildType, getEnv, getIdentifier, getLivelinessChecking, getMembers, getNodeId, getParent, getParentOfType, getPath, getPathParts, getPropertyMembers, getRelativePath, getRoot, getRunningActionContext, getSnapshot, getType, hasParent, hasParentOfType, isActionContextChildOf, isActionContextThisOrChildOf, isAlive, isArrayType, isFrozenType, isIdentifierType, isLateType, isLiteralType, isMapType, isModelType, isOptionalType, isPrimitiveType, isProtected, isReferenceType, isRefinementType, isRoot, isStateTreeNode, isType, isUnionType, isValidReference, joinJsonPath, onAction, onPatch, onSnapshot, process$1 as process, protect, recordActions, recordPatches, resolveIdentifier, resolvePath, setLivelinessChecking, setLivelynessChecking, splitJsonPath, toGenerator, toGeneratorFunction, tryReference, tryResolve, typecheck, types, unescapeJsonPath, unprotect, walk };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,qBAAqB;AAQzB,SAAS,sBAAsB,MAAM;AACjC,uBAAqB;AACzB;AAMA,SAAS,wBAAwB;AAC7B,SAAO;AACX;AAWA,SAAS,sBAAsB,MAAM;AACjC,wBAAsB,IAAI;AAC9B;AAKA,IAAI;AAAA,CACH,SAAUA,OAAM;AACb,EAAAA,MAAK,aAAa,IAAI;AACtB,EAAAA,MAAK,aAAa,IAAI;AACtB,EAAAA,MAAK,2BAA2B,IAAI;AACpC,EAAAA,MAAK,cAAc,IAAI;AACvB,EAAAA,MAAK,eAAe,IAAI;AAC5B,GAAG,SAAS,OAAO,CAAC,EAAE;AAkBtB,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAIA,GAAE,eAAe,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AAC7E,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAEA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEA,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AAC/C,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA,MACxH,UAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,IAAK,KAAI,IAAI,WAAW,CAAC,EAAG,MAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAChE;AAEA,SAAS,YAAY,SAAS,MAAM;AAChC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,EAAG,KAAI;AACV,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEA,SAAS,SAAS,GAAG;AACjB,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,MAAI,EAAG,QAAO,EAAE,KAAK,CAAC;AACtB,MAAI,KAAK,OAAO,EAAE,WAAW,SAAU,QAAO;AAAA,IAC1C,MAAM,WAAY;AACd,UAAI,KAAK,KAAK,EAAE,OAAQ,KAAI;AAC5B,aAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,IAC1C;AAAA,EACJ;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACzF;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI,EAAG,OAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAEA,SAAS,WAAW;AAChB,WAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ;AAC3C,SAAK,GAAG,OAAO,OAAO,UAAU,CAAC,CAAC,CAAC;AACvC,SAAO;AACX;AAQA,SAAS,QAAQ,QAAQ;AACrB,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE;AACpC;AAiBA,SAAS,aAAa,QAAQ,cAAc;AACxC,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE,aAAa,YAAY;AAC7D;AAUA,SAAS,QAAQ,QAAQ,UAAU;AAE/B,wBAAsB,QAAQ,CAAC;AAC/B,mBAAiB,UAAU,CAAC;AAC5B,SAAO,iBAAiB,MAAM,EAAE,QAAQ,QAAQ;AACpD;AAUA,SAAS,WAAW,QAAQ,UAAU;AAElC,wBAAsB,QAAQ,CAAC;AAC/B,mBAAiB,UAAU,CAAC;AAC5B,SAAO,iBAAiB,MAAM,EAAE,WAAW,QAAQ;AACvD;AAWA,SAAS,WAAW,QAAQ,OAAO;AAE/B,wBAAsB,QAAQ,CAAC;AAC/B,YAAU,OAAO,SAAU,GAAG;AAAE,WAAO,OAAO,MAAM;AAAA,EAAU,GAAG,mBAAmB,CAAC;AACrF,mBAAiB,MAAM,EAAE,aAAa,QAAQ,KAAK,CAAC;AACxD;AAgCA,SAAS,cAAc,SAAS,QAAQ;AAEpC,wBAAsB,SAAS,CAAC;AAChC,MAAI,OAAO;AAAA,IACP,SAAS,CAAC;AAAA,IACV,wBAAwB,CAAC;AAAA,EAC7B;AAEA,MAAI,aAAa,CAAC;AAClB,MAAI;AACJ,MAAI,WAAW;AAAA,IACX,IAAI,YAAY;AACZ,aAAO,CAAC,CAAC;AAAA,IACb;AAAA,IACA,IAAI,UAAU;AACV,UAAI,CAAC,WAAW,SAAS;AACrB,mBAAW,UAAU,KAAK,QAAQ,MAAM;AAAA,MAC5C;AACA,aAAO,WAAW;AAAA,IACtB;AAAA,IACA,IAAI,yBAAyB;AACzB,UAAI,CAAC,WAAW,wBAAwB;AACpC,mBAAW,yBAAyB,KAAK,uBAAuB,MAAM;AAAA,MAC1E;AACA,aAAO,WAAW;AAAA,IACtB;AAAA,IACA,IAAI,iBAAiB;AACjB,UAAI,CAAC,WAAW,gBAAgB;AAC5B,mBAAW,iBAAiB,KAAK,uBAAuB,MAAM,EAAE,QAAQ;AAAA,MAC5E;AACA,aAAO,WAAW;AAAA,IACtB;AAAA,IACA,MAAM,WAAY;AACd,UAAI,UAAU;AACV,iBAAS;AACT,mBAAW;AAAA,MACf;AAAA,IACJ;AAAA,IACA,QAAQ,WAAY;AAChB,UAAI;AACA;AACJ,iBAAW,QAAQ,SAAS,SAAU,OAAO,cAAc;AAEvD,YAAI,UAAU,CAAC,OAAO,OAAO,cAAc,wBAAwB,CAAC,GAAG;AACnE;AAAA,QACJ;AACA,aAAK,QAAQ,KAAK,KAAK;AACvB,aAAK,uBAAuB,QAAQ,YAAY;AAEhD,mBAAW,UAAU;AACrB,mBAAW,iBAAiB;AAC5B,mBAAW,yBAAyB;AAAA,MACxC,CAAC;AAAA,IACL;AAAA,IACA,QAAQ,SAAU,QAAQ;AACtB,iBAAW,UAAU,SAAS,KAAK,OAAO;AAAA,IAC9C;AAAA,IACA,MAAM,SAAU,QAAQ;AACpB,iBAAW,UAAU,SAAS,KAAK,sBAAsB;AAAA,IAC7D;AAAA,EACJ;AACA,WAAS,OAAO;AAChB,SAAO;AACX;AAMA,SAAS,QAAQ,QAAQ;AAErB,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,OAAO,iBAAiB,MAAM;AAClC,MAAI,CAAC,KAAK;AACN,UAAM,OAAO,6CAA6C;AAC9D,OAAK,sBAAsB;AAC/B;AAyBA,SAAS,UAAU,QAAQ;AAEvB,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,OAAO,iBAAiB,MAAM;AAClC,MAAI,CAAC,KAAK;AACN,UAAM,OAAO,+CAA+C;AAChE,OAAK,sBAAsB;AAC/B;AAIA,SAAS,YAAY,QAAQ;AACzB,SAAO,iBAAiB,MAAM,EAAE;AACpC;AAQA,SAAS,cAAc,QAAQ,UAAU;AAErC,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE,cAAc,QAAQ;AAC1D;AASA,SAAS,YAAY,QAAQ,kBAAkB;AAC3C,MAAI,qBAAqB,QAAQ;AAAE,uBAAmB;AAAA,EAAM;AAE5D,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,OAAO,iBAAiB,MAAM;AAClC,MAAI;AACA,WAAO,KAAK;AAChB,SAAO,OAAO,KAAK,KAAK,YAAY,MAAM,KAAK,CAAC;AACpD;AAQA,SAAS,UAAU,QAAQ,OAAO;AAC9B,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAG;AAEnC,wBAAsB,QAAQ,CAAC;AAC/B,iBAAe,OAAO,GAAG,CAAC;AAC1B,MAAI,SAAS,iBAAiB,MAAM,EAAE;AACtC,SAAO,QAAQ;AACX,QAAI,EAAE,UAAU;AACZ,aAAO;AACX,aAAS,OAAO;AAAA,EACpB;AACA,SAAO;AACX;AAcA,SAAS,UAAU,QAAQ,OAAO;AAC9B,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAG;AAEnC,wBAAsB,QAAQ,CAAC;AAC/B,iBAAe,OAAO,GAAG,CAAC;AAC1B,MAAI,IAAI;AACR,MAAI,SAAS,iBAAiB,MAAM,EAAE;AACtC,SAAO,QAAQ;AACX,QAAI,EAAE,MAAM;AACR,aAAO,OAAO;AAClB,aAAS,OAAO;AAAA,EACpB;AACA,QAAM,OAAO,kCAAkC,iBAAiB,MAAM,IAAI,eAAe,KAAK;AAClG;AAQA,SAAS,gBAAgB,QAAQ,MAAM;AAEnC,wBAAsB,QAAQ,CAAC;AAC/B,eAAa,MAAM,CAAC;AACpB,MAAI,SAAS,iBAAiB,MAAM,EAAE;AACtC,SAAO,QAAQ;AACX,QAAI,KAAK,GAAG,OAAO,WAAW;AAC1B,aAAO;AACX,aAAS,OAAO;AAAA,EACpB;AACA,SAAO;AACX;AAQA,SAAS,gBAAgB,QAAQ,MAAM;AAEnC,wBAAsB,QAAQ,CAAC;AAC/B,eAAa,MAAM,CAAC;AACpB,MAAI,SAAS,iBAAiB,MAAM,EAAE;AACtC,SAAO,QAAQ;AACX,QAAI,KAAK,GAAG,OAAO,WAAW;AAC1B,aAAO,OAAO;AAClB,aAAS,OAAO;AAAA,EACpB;AACA,QAAM,OAAO,kCAAkC,iBAAiB,MAAM,IAAI,kBAAkB;AAChG;AAUA,SAAS,QAAQ,QAAQ;AAErB,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE,KAAK;AACzC;AAOA,SAAS,QAAQ,QAAQ;AAErB,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE;AACpC;AAOA,SAAS,aAAa,QAAQ;AAE1B,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,cAAc,iBAAiB,MAAM,EAAE,IAAI;AACtD;AAOA,SAAS,OAAO,QAAQ;AAEpB,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE;AACpC;AASA,SAAS,YAAY,QAAQ,MAAM;AAE/B,wBAAsB,QAAQ,CAAC;AAC/B,iBAAe,MAAM,CAAC;AACtB,MAAI,OAAO,kBAAkB,iBAAiB,MAAM,GAAG,IAAI;AAC3D,SAAO,OAAO,KAAK,QAAQ;AAC/B;AAUA,SAAS,kBAAkB,MAAM,QAAQC,aAAY;AAEjD,eAAa,MAAM,CAAC;AACpB,wBAAsB,QAAQ,CAAC;AAC/B,0BAAwBA,aAAY,CAAC;AACrC,MAAI,OAAO,iBAAiB,MAAM,EAAE,KAAK,gBAAgB,QAAQ,MAAM,oBAAoBA,WAAU,CAAC;AACtG,SAAO,OAAO,KAAK,QAAQ;AAC/B;AAQA,SAAS,cAAc,QAAQ;AAE3B,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE;AACpC;AASA,SAAS,aAAa,QAAQ,cAAc;AACxC,MAAI,iBAAiB,QAAQ;AAAE,mBAAe;AAAA,EAAM;AACpD,MAAI;AACA,QAAI,OAAO,OAAO;AAClB,QAAI,SAAS,UAAa,SAAS,MAAM;AACrC,aAAO;AAAA,IACX,WACS,gBAAgB,IAAI,GAAG;AAC5B,UAAI,CAAC,cAAc;AACf,eAAO;AAAA,MACX,OACK;AACD,eAAO,QAAQ,IAAI,IAAI,OAAO;AAAA,MAClC;AAAA,IACJ,OACK;AACD,YAAM,OAAO,mEAAmE;AAAA,IACpF;AAAA,EACJ,SACO,GAAG;AACN,QAAI,aAAa,uBAAuB;AACpC,aAAO;AAAA,IACX;AACA,UAAM;AAAA,EACV;AACJ;AAQA,SAAS,iBAAiB,QAAQ,cAAc;AAC5C,MAAI,iBAAiB,QAAQ;AAAE,mBAAe;AAAA,EAAM;AACpD,MAAI;AACA,QAAI,OAAO,OAAO;AAClB,QAAI,SAAS,UAAa,SAAS,MAAM;AACrC,aAAO;AAAA,IACX,WACS,gBAAgB,IAAI,GAAG;AAC5B,aAAO,eAAe,QAAQ,IAAI,IAAI;AAAA,IAC1C,OACK;AACD,YAAM,OAAO,mEAAmE;AAAA,IACpF;AAAA,EACJ,SACO,GAAG;AACN,QAAI,aAAa,uBAAuB;AACpC,aAAO;AAAA,IACX;AACA,UAAM;AAAA,EACV;AACJ;AAQA,SAAS,WAAW,QAAQ,MAAM;AAE9B,wBAAsB,QAAQ,CAAC;AAC/B,iBAAe,MAAM,CAAC;AACtB,MAAI,OAAO,kBAAkB,iBAAiB,MAAM,GAAG,MAAM,KAAK;AAClE,MAAI,SAAS;AACT,WAAO;AACX,MAAI;AACA,WAAO,KAAK;AAAA,EAChB,SACO,GAAG;AAGN,WAAO;AAAA,EACX;AACJ;AASA,SAAS,gBAAgB,MAAM,QAAQ;AAEnC,wBAAsB,MAAM,CAAC;AAC7B,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,4BAA4B,iBAAiB,IAAI,GAAG,iBAAiB,MAAM,CAAC;AACvF;AAWA,SAAS,MAAM,QAAQ,iBAAiB;AACpC,MAAI,oBAAoB,QAAQ;AAAE,sBAAkB;AAAA,EAAM;AAE1D,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,OAAO,iBAAiB,MAAM;AAClC,SAAO,KAAK,KAAK,OAAO,KAAK,UAAU,oBAAoB,OACrD,KAAK,KAAK,cACV,oBAAoB,QAChB,SACA,eAAe;AAC7B;AAIA,SAAS,OAAO,QAAQ;AAEpB,wBAAsB,QAAQ,CAAC;AAC/B,mBAAiB,MAAM,EAAE,OAAO;AAChC,SAAO;AACX;AAIA,SAAS,QAAQ,QAAQ;AAErB,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,OAAO,iBAAiB,MAAM;AAClC,MAAI,KAAK;AACL,SAAK,IAAI;AAAA;AAET,SAAK,OAAO,YAAY,KAAK,OAAO;AAC5C;AAUA,SAAS,QAAQ,QAAQ;AAErB,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE;AACpC;AA6BA,SAAS,YAAY,QAAQ,UAAU;AAEnC,wBAAsB,QAAQ,CAAC;AAC/B,mBAAiB,UAAU,CAAC;AAC5B,MAAI,OAAO,iBAAiB,MAAM;AAClC,OAAK,YAAY,QAAQ;AACzB,SAAO;AACX;AAaA,SAAS,OAAO,QAAQ;AAEpB,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,OAAO,iBAAiB,MAAM;AAClC,MAAI,MAAM,KAAK,KAAK;AACpB,MAAI,CAAC;AACD,WAAO;AACX,SAAO;AACX;AAIA,SAAS,KAAK,QAAQ,WAAW;AAE7B,wBAAsB,QAAQ,CAAC;AAC/B,mBAAiB,WAAW,CAAC;AAC7B,MAAI,OAAO,iBAAiB,MAAM;AAElC,OAAK,YAAY,EAAE,QAAQ,SAAU,OAAO;AACxC,QAAI,gBAAgB,MAAM,WAAW;AACjC,WAAK,MAAM,aAAa,SAAS;AAAA,EACzC,CAAC;AACD,YAAU,KAAK,WAAW;AAC9B;AAOA,SAAS,mBAAmB,YAAY;AACpC,MAAI;AACJ,MAAI,gBAAgB,UAAU,GAAG;AAC7B,WAAO,QAAQ,UAAU;AAAA,EAC7B,OACK;AACD,WAAO;AAAA,EACX;AACA,YAAU,MAAM,SAAU,GAAG;AAAE,WAAO,YAAY,CAAC;AAAA,EAAG,GAAG,gCAAgC,CAAC;AAC1F,SAAO;AAAA,IACH,MAAM,KAAK;AAAA,IACX,YAAY,SAAS,CAAC,GAAG,KAAK,UAAU;AAAA,EAC5C;AACJ;AAOA,SAAS,WAAW,QAAQ;AACxB,MAAI,OAAO,iBAAiB,MAAM,EAAE;AACpC,MAAI,YAAY,SAAS,SAAS,CAAC,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC;AACzG,MAAI,QAAQ,OAAO,oBAAoB,MAAM;AAC7C,QAAM,QAAQ,SAAU,KAAK;AACzB,QAAI,OAAO,UAAU;AACjB;AACJ,QAAI,aAAa,OAAO,yBAAyB,QAAQ,GAAG;AAC5D,QAAI,WAAW,KAAK;AAChB,UAAI,eAAe,QAAQ,GAAG;AAC1B,kBAAU,MAAM,KAAK,GAAG;AAAA;AAExB,kBAAU,SAAS,KAAK,GAAG;AAC/B;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,iBAAiB;AAClC,gBAAU,QAAQ,KAAK,GAAG;AAAA,aACrB,iBAAiB,QAAQ,GAAG;AACjC,gBAAU,SAAS,KAAK,GAAG;AAAA;AAE3B,gBAAU,MAAM,KAAK,GAAG;AAAA,EAChC,CAAC;AACD,SAAO;AACX;AA8BA,SAAS,KAAK,oBAAoB;AAC9B,SAAO;AACX;AA4BA,SAAS,eAAe,oBAAoB;AACxC,SAAO;AACX;AA6BA,SAAS,wBAAwB,UAAU;AACvC,SAAO;AACX;AAUA,SAAS,UAAU,QAAQ;AACvB,wBAAsB,QAAQ,CAAC;AAC/B,SAAO,iBAAiB,MAAM,EAAE;AACpC;AAMA,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASC,UAAS,MAAM,QAAQ,SAAS,aAAa;AAClD,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,SAAS,cAAc;AAC5B,WAAK,cAAc;AACnB,WAAK,cAAc,QAAQ,OAAO;AAAA,IACtC;AACA,WAAO,eAAeA,UAAS,WAAW,WAAW;AAAA,MACjD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,oBAAoB;AAAA,MAC1D,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,iBAAiB;AAAA,MACvD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,SAAS;AAAA,MAC/C,KAAK,WAAY;AACb,eAAO,KAAK,KAAK,SAAS,IAAI;AAAA,MAClC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,SAAS;AAAA,MAC/C,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,KAAK,SAAU,KAAK;AAChB,YAAI,WAAW,KAAK;AACpB,aAAK,SAAS;AACd,YAAIC,WAAU,KAAK;AACnB,YAAI,KAAK,aAAa,aAAaA,UAAS;AACxC,eAAK,UAAU,cAAc;AAAA,QACjC;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAD,UAAS,UAAU,mBAAmB,SAAU,MAAM;AAClD,UAAI,KAAK,kBAAkB;AACvB,aAAK,iBAAiB,KAAK,MAAM,MAAM,IAAI;AAAA,MAC/C;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,MAAM,aAAa;AAC3D,UAAI,CAAC,KAAK,kBAAkB;AACxB,aAAK,mBAAmB,IAAI,cAAc;AAAA,MAC9C;AACA,aAAO,KAAK,iBAAiB,SAAS,MAAM,WAAW;AAAA,IAC3D;AACA,WAAO,eAAeA,UAAS,WAAW,UAAU;AAAA,MAChD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,UAAS,UAAU,gBAAgB,SAAU,QAAQ,SAAS;AAC1D,WAAK,UAAU;AACf,WAAK,WAAW;AAChB,WAAK,kBAAkB;AACvB,UAAI,KAAK,UAAU;AACf,aAAK,SAAS,cAAc;AAAA,MAChC;AAAA,IACJ;AACA,WAAO,eAAeA,UAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI9C,KAAK,WAAY;AACb,eAAO,KAAK,eAAe,IAAI;AAAA,MACnC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,UAAS,UAAU,iBAAiB,SAAU,gBAAgB;AAC1D,UAAI,gBAAgB;AAChB,YAAI,CAAC,KAAK,UAAU;AAChB,eAAK,WAAW,WAAW,MAAM;AAAA,QACrC;AACA,aAAK,SAAS,eAAe;AAAA,MACjC;AACA,UAAI,CAAC,KAAK;AACN,eAAO;AAEX,UAAI,KAAK,oBAAoB,QAAW;AACpC,aAAK,kBAAkB,CAAC,KAAK,WAAW,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC7E;AACA,aAAO,KAAK,OAAO,eAAe,cAAc,IAAI,MAAM,KAAK;AAAA,IACnE;AACA,WAAO,eAAeA,UAAS,WAAW,UAAU;AAAA,MAChD,KAAK,WAAY;AACb,eAAO,KAAK,WAAW;AAAA,MAC3B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,WAAW;AAAA,MACjD,KAAK,WAAY;AACb,eAAO,KAAK,UAAU,cAAc;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,eAAe;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK,UAAU,cAAc;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,qBAAqB;AAAA,MAC3D,KAAK,WAAY;AACb,YAAI,CAAC,KAAK,WAAW;AACjB,eAAK,YAAY,WAAW,OAAO;AAAA,QACvC;AACA,aAAK,UAAU,eAAe;AAC9B,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,UAAS,UAAU,uBAAuB,SAAU,eAAe;AAC/D,UAAI,QAAQ,GAAG;AACX,YAAI,CAAC,KAAK,SAAS;AAEf,gBAAM,KAAK,+EAA+E;AAAA,QAC9F;AAAA,MACJ;AAEA,UAAI,KAAK,UAAU,cAAc,SAAS;AACtC,YAAI,KAAK,QAAQ;AACb,cAAI,KAAK,OAAO,UAAU,cAAc,WAAW;AAE/C;AAAA,UACJ;AACA,eAAK,SAAS,KAAK,WAAW;AAAA,QAClC;AACA,aAAK,QAAQ,cAAc;AAC3B,YAAI,eAAe;AACf,wBAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,oBAAoB,WAAY;AAC/C,UAAI,KAAK,kBAAkB;AACvB,aAAK,iBAAiB,SAAS;AAAA,MACnC;AACA,WAAK,oBAAoB,KAAK;AAC9B,WAAK,iBAAiB,KAAK,eAAe,KAAK;AAC/C,WAAK,cAAc,MAAM,EAAE;AAC3B,WAAK,QAAQ,cAAc;AAAA,IAC/B;AACA,IAAAA,UAAS,UAAU,iBAAiB,WAAY;AAC5C,WAAK,SAAS,KAAK,aAAa;AAAA,IACpC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAMF,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,cAAUE,aAAY,MAAM;AAC5B,aAASA,YAAW,YAAY,QAAQ,SAAS,aAAa,iBAAiB;AAC3E,UAAI,QAAQ,OAAO,KAAK,MAAM,YAAY,QAAQ,SAAS,WAAW,KAAK;AAC3E,UAAI;AACA,cAAM,cAAc,WAAW,kBAAkB,eAAe;AAAA,MACpE,SACO,GAAG;AAEN,cAAM,QAAQ,cAAc;AAC5B,cAAM;AAAA,MACV;AACA,YAAM,QAAQ,cAAc;AAI5B,YAAM,iBAAiB;AACvB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,YAAW,WAAW,QAAQ;AAAA,MAChD,KAAK,WAAY;AAEb,YAAI,CAAC,KAAK;AACN,gBAAM,OAAO,wCAAwC;AACzD,eAAO,KAAK,OAAO;AAAA,MACvB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,YAAY,SAAU,WAAW,SAAS;AAC3D,UAAI,gBAAgB,KAAK,WAAW;AACpC,UAAI,iBAAiB,KAAK,YAAY;AACtC,UAAI,CAAC,iBAAiB,CAAC,gBAAgB;AACnC;AAAA,MACJ;AACA,UAAI,QAAQ,GAAG;AACX,YAAI,CAAC,SAAS;AAEV,gBAAM,OAAO,oCAAoC;AAAA,QACrD;AACA,YAAI,CAAC,WAAW;AAEZ,gBAAM,OAAO,mCAAmC;AAAA,QACpD;AACA,YAAI,eAAe;AAEf,gBAAM,OAAO,2DAA2D;AAAA,QAC5E;AAAA,MACJ;AACA,WAAK,cAAc;AACnB,WAAK,cAAc,KAAK,QAAQ,OAAO;AAAA,IAC3C;AACA,WAAO,eAAeA,YAAW,WAAW,YAAY;AAAA,MACpD,KAAK,WAAY;AACb,eAAO,OAAO,KAAK,YAAY,CAAC;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK,KAAK,YAAY,IAAI;AAAA,IACrC;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,UAAI,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,kBAAkB;AAC9D,aAAO,KAAK,KAAK,OAAO,MAAM,QAAQ,KAAK,UAAU,KAAK;AAAA,IAC9D;AACA,IAAAA,YAAW,UAAU,MAAM,WAAY;AACnC,UAAI,CAAC,KAAK,WAAW,KAAK,UAAU,cAAc;AAC9C;AACJ,WAAK,WAAW;AAChB,WAAK,cAAc;AAAA,IACvB;AACA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,WAAK,qBAAqB;AAAA,IAC9B;AACA,IAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,WAAK,eAAe;AAAA,IACxB;AACA,IAAAA,YAAW,UAAU,gBAAgB,WAAY;AAC7C,WAAK,kBAAkB;AAAA,IAC3B;AACA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM;AAC5C,WAAK,iBAAiB,IAAI;AAAA,IAC9B;AACA,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,YAAW,WAAW,OAAO,IAAI;AACpC,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAEV,IAAI,aAAa;AACjB,IAAI,0BAA0B;AAAA,EAC1B,SAAS,SAAU,GAAG;AAClB,UAAM;AAAA,EACV;AACJ;AAKA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,YAAW,aAAa,QAAQ,SAAS,aAAa,cAAc;AACzE,UAAI,QAAQ,OAAO,KAAK,MAAM,aAAa,QAAQ,SAAS,WAAW,KAAK;AAC5E,YAAM,SAAS,EAAE;AACjB,YAAM,sBAAsB;AAC5B,YAAM,aAAa;AACnB,YAAM,mBAAmB;AACzB,YAAM,uBAAuB;AAC7B,YAAM,2BAA2B;AACjC,YAAM,gCAAgC;AACtC,YAAM,QAAQ,MAAM,MAAM,KAAK,KAAK;AACpC,YAAM,mBAAmB,OAAO,YAAY;AAC5C,YAAM,sBAAsB,YAAY;AACxC,UAAI,CAAC,QAAQ;AACT,cAAM,kBAAkB,IAAI,gBAAgB;AAAA,MAChD;AACA,YAAM,cAAc,YAAY,qBAAqB,OAAO,MAAM,gBAAgB;AAGlF,YAAM,aAAa;AACnB,YAAM,yBAAyB;AAC/B,UAAI,MAAM,uBAAuB,MAAM,kBAAkB;AACrD,YAAI,KAAK,MAAM,iBAAiB,MAAM,mBAAmB;AACzD,YAAI,OAAO,QAAW;AAElB,cAAI,YAAY,MAAM,YAAY,MAAM,mBAAmB;AAC3D,cAAI,WAAW;AACX,iBAAK,UAAU;AAAA,UACnB;AAAA,QACJ;AACA,YAAI,OAAO,OAAO,YAAY,OAAO,OAAO,UAAU;AAClD,gBAAM,OAAO,0BAA0B,MAAM,sBAAsB,iBAAiB,MAAM,KAAK,OAAO,gCAAgC;AAAA,QAC1I;AAEA,cAAM,aAAa,oBAAoB,EAAE;AACzC,cAAM,yBAAyB;AAAA,MACnC;AACA,UAAI,CAAC,QAAQ;AACT,cAAM,gBAAgB,eAAe,KAAK;AAAA,MAC9C,OACK;AACD,eAAO,KAAK,gBAAgB,eAAe,KAAK;AAAA,MACpD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,SAAS;AACnD,WAAK,iCAAiC;AACtC,WAAK,cAAc,OAAO;AAAA,IAC9B;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,UAAU;AACrD,WAAK,iCAAiC;AACtC,WAAK,eAAe,QAAQ;AAAA,IAChC;AACA,IAAAA,YAAW,UAAU,mCAAmC,WAAY;AAChE,UAAI,KAAK,6BAA6B,GAAuB;AACzD,aAAK,yBAAyB;AAAA,MAClC;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,2BAA2B,WAAY;AACxD,UAAI,KAAK;AACT,UAAI,QAAQ,GAAG;AACX,YAAI,KAAK,UAAU,cAAc,cAAc;AAE3C,gBAAM,OAAO,kGAAkG;AAAA,QACnH;AAAA,MACJ;AACA,WAAK,2BAA2B;AAGhC,UAAI,cAAc,CAAC;AACnB,UAAI,SAAS,KAAK;AAKlB,aAAO,UACH,OAAO,6BAA6B,GAAuB;AAC3D,oBAAY,QAAQ,MAAM;AAC1B,iBAAS,OAAO;AAAA,MACpB;AACA,UAAI;AAEA,iBAAS,gBAAgB,SAAS,WAAW,GAAG,kBAAkB,cAAc,KAAK,GAAG,CAAC,gBAAgB,MAAM,kBAAkB,cAAc,KAAK,GAAG;AACnJ,cAAI,IAAI,gBAAgB;AACxB,YAAE,iCAAiC;AAAA,QACvC;AAAA,MACJ,SACO,OAAO;AAAE,cAAM,EAAE,OAAO,MAAM;AAAA,MAAG,UACxC;AACI,YAAI;AACA,cAAI,mBAAmB,CAAC,gBAAgB,SAAS,KAAK,cAAc,QAAS,IAAG,KAAK,aAAa;AAAA,QACtG,UACA;AAAU,cAAI,IAAK,OAAM,IAAI;AAAA,QAAO;AAAA,MACxC;AACA,UAAI,OAAO,KAAK;AAChB,UAAI;AACA,aAAK,cAAc,KAAK,kBAAkB,KAAK,WAAW;AAC1D,aAAK,QAAQ;AACb,aAAK,mBAAmB;AACxB,aAAK,oBAAoB,MAAM,KAAK,WAAW;AAAA,MACnD,SACO,GAAG;AAEN,aAAK,QAAQ,cAAc;AAC3B,cAAM;AAAA,MACV,UACA;AACI,aAAK,mBAAmB;AAAA,MAC5B;AACA,WAAK,2BAA2B;AAGhC,yBAAmB,MAAM,UAAU;AACnC,UAAI,KAAK;AACL,aAAK,qBAAqB;AAC9B,WAAK,cAAc;AACnB,WAAK,QAAQ,cAAc;AAC3B,WAAK,SAAS,KAAK,WAAW;AAC9B,WAAK,iBAAiB;AAAA,IAC1B;AACA,WAAO,eAAeA,YAAW,WAAW,QAAQ;AAAA,MAChD,KAAK,WAAY;AACb,YAAI,SAAS,KAAK;AAClB,eAAO,SAAS,OAAO,OAAO;AAAA,MAClC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,UAAI,CAAC,KAAK;AACN;AAEJ,WAAK,SAAS,KAAK,YAAY;AAC/B,UAAI,gBAAgB,KAAK;AACzB,WAAK,QAAQ,cAAc;AAC3B,UAAI,OAAO,KAAK;AAChB,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,KAAK,gBAAgB,WAAW,IAAI;AACrD,UAAI;AACA,aAAK,OAAO,YAAY,KAAK,OAAO;AACpC,aAAK,cAAc,MAAM,EAAE;AAC3B,aAAK,cAAc;AACnB,aAAK,kBAAkB;AAAA,MAC3B,UACA;AACI,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,WAAW,SAAS;AAC3D,UAAI,gBAAgB,cAAc,KAAK;AACvC,UAAI,iBAAiB,YAAY,KAAK;AACtC,UAAI,CAAC,iBAAiB,CAAC,gBAAgB;AACnC;AAAA,MACJ;AACA,UAAI,QAAQ,GAAG;AACX,YAAI,CAAC,SAAS;AAEV,gBAAM,OAAO,oCAAoC;AAAA,QACrD;AACA,YAAI,CAAC,WAAW;AAEZ,gBAAM,OAAO,uCAAuC;AAAA,QACxD;AACA,YAAI,KAAK,UAAU,eAAe;AAC9B,gBAAM,OAAO,iEAAiE,OAAO,eAAe,UAAU,OAAO,MAAM,UAAU,IAAI;AAAA,QAC7I;AACA,YAAI,CAAC,KAAK,UAAU,UAAU,SAAS,MAAM;AACzC,gBAAM,OAAO,kEAAkE,OAAO,eAAe,UAAU,OAAO,MAAM,UAAU,GAAG;AAAA,QAC7I;AACA,YAAI,CAAC,KAAK,UACN,CAAC,CAAC,KAAK,eACP,KAAK,gBAAgB,UAAU,KAAK,aAAa;AACjD,gBAAM,OAAO,qGAAqG;AAAA,QACtH;AAAA,MACJ;AACA,UAAI,eAAe;AAEf,aAAK,cAAc;AACnB,kBAAU,KAAK,gBAAgB,WAAW,IAAI;AAC9C,aAAK,cAAc,WAAW,OAAO;AACrC,aAAK,SAAS,KAAK,WAAW;AAAA,MAClC,WACS,gBAAgB;AAErB,aAAK,cAAc,KAAK,QAAQ,OAAO;AAAA,MAC3C;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM;AAC5C,UAAI,QAAQ;AACZ,WAAK,iBAAiB,IAAI;AAC1B,UAAI,KAAK,KAAK,eACV,OAAO,KAAK,gBAAgB,YAC5B,KAAK,YAAY,IAAI;AACzB,UAAI,OAAO,OAAO,YAAY;AAE1B,YAAI,iCAAkC;AAClC,0CAAiC,WAAY;AACzC,eAAG,MAAM,MAAM,WAAW;AAAA,UAC9B,CAAC;AAAA,QACL,OACK;AACD,aAAG,MAAM,KAAK,WAAW;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,eAAeA,YAAW,WAAW,YAAY;AAAA;AAAA,MAEpD,KAAK,WAAY;AACb,eAAO,OAAO,KAAK,YAAY,CAAC;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AAED,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,UAAI,CAAC,KAAK;AACN,eAAO,KAAK;AAChB,aAAO,KAAK,6BAA6B,IACnC,KAAK,mBAAmB,IACxB,KAAK,0BAA0B;AAAA,IACzC;AACA,IAAAA,YAAW,UAAU,qBAAqB,WAAY;AAClD,aAAO,KAAK,KAAK,YAAY,IAAI;AAAA,IACrC;AACA,IAAAA,YAAW,UAAU,4BAA4B,WAAY;AACzD,UAAI,CAAC,KAAK,+BAA+B;AACrC,YAAI,OAAO,KAAK;AAChB,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,KAAK;AACpB,aAAK,yBAAyB,KAAK,uBAAuB,YAAY,QAAQ;AAC9E,aAAK,gCAAgC;AAAA,MACzC;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,kBAAkB,WAAY;AAC/C,UAAI,KAAK;AACL,eAAO;AACX,UAAI,KAAK;AACL,eAAO;AACX,aAAO,KAAK,OAAO,gBAAgB;AAAA,IACvC;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,SAAS;AAClD,UAAIC,sBAAqB,sBAAsB;AAC/C,UAAI,CAAC,KAAK,WAAWA,wBAAuB,UAAU;AAClD,YAAI,QAAQ,KAAK,qBAAqB,OAAO;AAC7C,gBAAQA,qBAAoB;AAAA,UACxB,KAAK;AACD,kBAAM,OAAO,KAAK;AAAA,UACtB,KAAK;AACD,sBAAU,KAAK;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AACA,IAAAD,YAAW,UAAU,uBAAuB,SAAU,SAAS;AAC3D,UAAI,cAAc,KAAK,eAAe,KAAK,KAAK,KAAK,iBAAiB;AACtE,UAAI,UAAW,QAAQ,WAAW,eAAe,QAAQ,OAAO,KAAM;AACtE,UAAI,gBAAgB,QAAQ,iBAAiB,wBAAwB;AAErE,UAAI,iBAAiB,cAAc,SAAS,YAAY,cAAc,mBAAmB;AACrF,wBAAgB,cAAc;AAAA,MAClC;AACA,UAAI,iBAAiB;AACrB,UAAI,iBAAiB,cAAc,QAAQ,MAAM;AAE7C,YAAI,aAAc,iBAAiB,cAAc,WAAW,QAAQ,cAAc,OAAO,KACrF;AACJ,yBAAiB,aAAa,MAAM,cAAc,OAAO;AAAA,MAC7D;AACA,aAAO,yGAAyG,KAAK,KAAK,OAAO,0BAA0B,cAAc,kBAAkB,UAAU,iBAAiB,iBAAiB;AAAA,IAC3O;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,SAAS;AACnD,WAAK,YAAY;AAAA,QACb;AAAA,MACJ,CAAC;AACD,WAAK,aAAa;AAClB,UAAI;AACA,eAAO,KAAK,6BAA6B,IACnC,KAAK,KAAK,aAAa,MAAM,OAAO,IACpC,KAAK,YAAY,OAAO;AAAA,MAClC,UACA;AACI,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,WAAK,YAAY,YAAY;AAC7B,WAAK,aAAa;AAClB,UAAI;AACA,eAAO,KAAK,6BAA6B,IACnC,KAAK,KAAK,YAAY,IAAI,IAC1B,yBAAyB,KAAK,WAAW;AAAA,MACnD,UACA;AACI,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,cAAc;AACxD,aAAO,KAAK,KAAK,aAAa,YAAY;AAAA,IAC9C;AACA,WAAO,eAAeA,YAAW,WAAW,eAAe;AAAA,MACvD,KAAK,WAAY;AACb,eAAO,KAAK,KAAK;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,iBAAiB,SAAU,SAAS;AACrD,WAAK,YAAY,OAAO;AACxB,UAAI,CAAC,KAAK,gBAAgB,KAAK,KAAK,aAAa;AAC7C,cAAM,OAAO,oBAAoB,OAAO,yEAAyE;AAAA,MACrH;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,SAAS;AAClD,WAAK,KAAK,YAAY,MAAM,OAAO;AAAA,IACvC;AAEA,IAAAA,YAAW,UAAU,QAAQ,SAAU,WAAW;AAC9C,UAAI,CAAC;AACD,eAAO;AACX,WAAK,YAAY;AAAA,QACb,SAAS,UAAU,WAAW,UAAU;AAAA,MAC5C,CAAC;AACD,aAAO,KAAK,aAAa,UAAU,QAAQ;AAAA,IAC/C;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,UAAI,QAAQ,KAAK,UAAU,KAAK,OAAO,KAAK,kBAAkB;AAC9D,UAAIJ,cAAa,KAAK,aAAa,UAAU,KAAK,aAAa,MAAM;AACrE,aAAO,KAAK,KAAK,OAAO,MAAM,OAAOA,eAAc,KAAK,UAAU,KAAK;AAAA,IAC3E;AACA,IAAAI,YAAW,UAAU,mBAAmB,WAAY;AAChD,UAAI,QAAQ;AACZ,WAAK,qBAAqB,WAAY;AAClC,YAAI,KAAK;AACT,YAAI;AACA,mBAAS,KAAK,SAAS,MAAM,YAAY,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AACnF,gBAAI,QAAQ,GAAG;AACf,kBAAM,iBAAiB;AAAA,UAC3B;AAAA,QACJ,SACO,OAAO;AAAE,gBAAM,EAAE,OAAO,MAAM;AAAA,QAAG,UACxC;AACI,cAAI;AACA,gBAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,QAAS,IAAG,KAAK,EAAE;AAAA,UACtD,UACA;AAAU,gBAAI,IAAK,OAAM,IAAI;AAAA,UAAO;AAAA,QACxC;AACA,cAAM,iBAAiB,KAAK,yBAAyB;AAAA,MACzD,CAAC;AAAA,IACL;AACA,IAAAA,YAAW,UAAU,SAAS,WAAY;AACtC,UAAI,CAAC,KAAK;AACN,cAAM,OAAO,2CAA2C;AAC5D,WAAK,YAAY;AAAA,IACrB;AACA,IAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,UAAI,OAAO;AACX,WAAK,gBAAgB,oBAAoB,KAAK,aAAa,kBAAkB,SAAU,SAAS;AAC5F,gBAAQ,QAAQ,SAAU,OAAO;AAC7B,cAAI,QAAQ,cAAc,MAAM,IAAI;AACpC,cAAI,OAAO,uBAAuB,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;AAC1D,eAAK,kBAAkB,MAAM,MAAM,SAAS,CAAC,GAAG,KAAK;AAAA,QACzD,CAAC;AAAA,MACL,CAAC;AACD,WAAK,iBAAiB,oBAAoB,KAAK,aAAa,mBAAmB,SAAU,UAAU;AAE/F,YAAI,aAAa,KAAK;AAClB;AAEJ,eAAO,KAAK,KAAK,cAAc,MAAM,QAAQ;AAAA,MACjD,CAAC;AACD,yBAAmB,KAAK,aAAa,aAAa,IAAI;AACtD,yBAAmB,KAAK,aAAa,UAAU,MAAM;AAAA,IACzD;AACA,IAAAA,YAAW,UAAU,MAAM,WAAY;AACnC,UAAI,CAAC,KAAK,WAAW,KAAK,UAAU,cAAc;AAC9C;AACJ,WAAK,WAAW;AAChB,WAAK,cAAc;AAAA,IACvB;AACA,IAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,UAAI,KAAK,6BAA6B,GAAuB;AACzD;AAAA,MACJ;AACA,WAAK,YAAY,EAAE,QAAQ,SAAU,MAAM;AACvC,aAAK,WAAW;AAAA,MACpB,CAAC;AAGD,WAAK,eAAe;AACpB,WAAK;AAAA,QAAoB;AAAA;AAAA,MAAuB;AAChD,WAAK;AAAA,QAAqB;AAAA;AAAA,MAAuB;AAAA,IACrD;AACA,IAAAA,YAAW,UAAU,gBAAgB,WAAY;AAE7C,WAAK,YAAY,EAAE,QAAQ,SAAU,MAAM;AACvC,aAAK,cAAc;AAAA,MACvB,CAAC;AACD,WAAK,KAAK,gBAAgB,WAAW,IAAI;AAEzC,UAAI,WAAW,KAAK;AACpB,WAAK,qBAAqB;AAC1B,WAAK,wBAAwB;AAC7B,WAAK,kBAAkB;AAAA,IAC3B;AACA,IAAAA,YAAW,UAAU,aAAa,SAAU,UAAU;AAClD,WAAK,qBAAqB;AAC1B,aAAO,KAAK,wBAAwB,YAA2B,QAAQ;AAAA,IAC3E;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,UAAU;AACpD,WAAK,oBAAoB,YAA2B,QAAQ;AAAA,IAChE;AACA,IAAAA,YAAW,UAAU,UAAU,SAAU,SAAS;AAC9C,aAAO,KAAK,wBAAwB,SAAqB,OAAO;AAAA,IACpE;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,WAAW,QAAQ;AAC1D,UAAI,KAAK;AAAA,QAA8B;AAAA;AAAA,MAAmB,GAAG;AACzD,YAAI,iBAAiB,OAAO,CAAC,GAAG,WAAW;AAAA,UACvC,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,MAAM,IAAI,MAAM,UAAU;AAAA;AAAA,QACjE,CAAC;AACD,YAAI,KAAK,OAAO,WAAW,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAClF,aAAK,oBAAoB,SAAqB,OAAO,YAAY;AAAA,MACrE;AACA,UAAI,KAAK;AACL,aAAK,OAAO,UAAU,WAAW,MAAM;AAAA,IAC/C;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,UAAU;AACnD,aAAO,KAAK,mBAAmB,WAAyB,QAAQ;AAAA,IACpE;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,UAAU;AACnD,UAAI,CAAC,KAAK,YAAY,QAAQ,GAAG;AAC7B,aAAK,wBAAwB,WAAyB,UAAU,IAAI;AACpE;AAAA,MACJ;AACA,YAAM,OAAO,mEAAmE;AAAA,IACpF;AACA,IAAAA,YAAW,UAAU,iBAAiB,SAAU,UAAU;AACtD,UAAI,CAAC,KAAK,mBAAmB,WAAyB,QAAQ,GAAG;AAC7D,cAAM,OAAO,mEAAmE;AAAA,MACpF;AACA,WAAK,0BAA0B,WAAyB,QAAQ;AAAA,IACpE;AACA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,YAAY;AAC1D,UAAI,KAAK,aAAa;AAClB,YAAI,QAAQ,KAAK,YAAY,QAAQ,UAAU;AAC/C,YAAI,SAAS,GAAG;AACZ,eAAK,YAAY,OAAO,OAAO,CAAC;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,SAAS,cAAc;AAClE,UAAI,QAAQ;AACZ,UAAI,iBAAiB,QAAQ;AAAE,uBAAe;AAAA,MAAM;AACpD,UAAI,aAAa,EAAE,SAAkB,aAA2B;AAChE,UAAI,CAAC,KAAK;AACN,aAAK,cAAc,CAAC,UAAU;AAAA;AAE9B,aAAK,YAAY,KAAK,UAAU;AACpC,aAAO,WAAY;AACf,cAAM,iBAAiB,UAAU;AAAA,MACrC;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,SAAS,OAAO;AAC/D,WAAK,eAAe;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,WAAK,iCAAiC;AACtC,WAAK,KAAK,kBAAkB,MAAM,SAAS,KAAK;AAAA,IACpD;AACA,IAAAA,YAAW,UAAU,uBAAuB,WAAY;AACpD,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,sBAAsB;AAC5B,YAAI,mBAAmB,SAAS,WAAY;AAAE,iBAAO,MAAM;AAAA,QAAU,GAAG,SAAU,UAAU;AAAE,iBAAO,MAAM,aAAa,QAAQ;AAAA,QAAG,GAAG,uBAAuB;AAC7J,aAAK,YAAY,gBAAgB;AACjC,aAAK,uBAAuB;AAAA,MAChC;AAAA,IACJ;AAEA,IAAAA,YAAW,UAAU,gCAAgC,SAAU,OAAO;AAClE,aAAO,CAAC,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,eAAe,KAAK;AAAA,IAC9E;AACA,IAAAA,YAAW,UAAU,0BAA0B,SAAU,OAAO,cAAc,gBAAgB;AAC1F,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB;AAAA,MAAO;AACzD,UAAI,CAAC,KAAK,iBAAiB;AACvB,aAAK,kBAAkB,IAAI,cAAc;AAAA,MAC7C;AACA,aAAO,KAAK,gBAAgB,SAAS,OAAO,cAAc,cAAc;AAAA,IAC5E;AACA,IAAAA,YAAW,UAAU,qBAAqB,SAAU,OAAO,cAAc;AACrE,aAAO,CAAC,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,IAAI,OAAO,YAAY;AAAA,IACjF;AACA,IAAAA,YAAW,UAAU,4BAA4B,SAAU,OAAO,cAAc;AAC5E,UAAI,KAAK,iBAAiB;AACtB,aAAK,gBAAgB,WAAW,OAAO,YAAY;AAAA,MACvD;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,sBAAsB,SAAU,OAAO;AACxD,UAAI;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MAC/B;AACA,UAAI,KAAK,iBAAiB;AACtB,SAAC,KAAK,KAAK,iBAAiB,KAAK,MAAM,IAAI,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;AAAA,MACtE;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,uBAAuB,SAAU,OAAO;AACzD,UAAI,KAAK,iBAAiB;AACtB,aAAK,gBAAgB,MAAM,KAAK;AAAA,MACpC;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,0BAA0B,WAAY;AACvD,UAAI,KAAK,iBAAiB;AACtB,aAAK,gBAAgB,SAAS;AAAA,MAClC;AAAA,IACJ;AACA,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,YAAW,WAAW,4BAA4B,IAAI;AACzD,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,YAAW,WAAW,YAAY,IAAI;AACzC,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,YAAW,WAAW,UAAU,IAAI;AACvC,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,YAAW,WAAW,OAAO,IAAI;AACpC,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAMV,IAAI;AAAA,CACH,SAAUE,YAAW;AAClB,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,SAAS,IAAI,EAAE,IAAI;AACvC,EAAAA,WAAUA,WAAU,OAAO,IAAI,EAAE,IAAI;AACrC,EAAAA,WAAUA,WAAU,KAAK,IAAI,EAAE,IAAI;AACnC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,GAAG,IAAI;AACvC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,GAAG,IAAI;AACvC,EAAAA,WAAUA,WAAU,UAAU,IAAI,GAAG,IAAI;AACzC,EAAAA,WAAUA,WAAU,WAAW,IAAI,IAAI,IAAI;AAC3C,EAAAA,WAAUA,WAAU,YAAY,IAAI,IAAI,IAAI;AAC5C,EAAAA,WAAUA,WAAU,MAAM,IAAI,IAAI,IAAI;AACtC,EAAAA,WAAUA,WAAU,YAAY,IAAI,IAAI,IAAI;AAC5C,EAAAA,WAAUA,WAAU,OAAO,IAAI,KAAK,IAAI;AACxC,EAAAA,WAAUA,WAAU,MAAM,IAAI,KAAK,IAAI;AACvC,EAAAA,WAAUA,WAAU,WAAW,IAAI,KAAK,IAAI;AAC5C,EAAAA,WAAUA,WAAU,SAAS,IAAI,MAAM,IAAI;AAC3C,EAAAA,WAAUA,WAAU,QAAQ,IAAI,MAAM,IAAI;AAC1C,EAAAA,WAAUA,WAAU,mBAAmB,IAAI,MAAM,IAAI;AACzD,GAAG,cAAc,YAAY,CAAC,EAAE;AAKhC,IAAI,yBAAyB;AAO7B,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASC,UAAS,MAAM;AACpB,WAAK,SAAS;AACd,WAAK,OAAO;AAAA,IAChB;AACA,IAAAA,UAAS,UAAU,SAAS,SAAU,UAAU,aAAa;AACzD,wBAAkB,MAAM,QAAQ;AAChC,aAAO,KAAK,YAAY,MAAM,IAAI,aAAa,QAAQ,EAAE;AAAA,IAC7D;AACA,IAAAA,UAAS,UAAU,cAAc,SAAU,MAAM,kBAAkB;AAE/D,YAAM,OAAO,sBAAsB;AAAA,IACvC;AACA,IAAAA,UAAS,UAAU,mBAAmB,SAAU,MAAM;AAClD,aAAO,SAAS;AAAA,IACpB;AACA,IAAAA,UAAS,UAAU,WAAW,SAAU,OAAO,SAAS;AACpD,UAAI,OAAO,qBAAqB,KAAK;AACrC,UAAI,MAAM;AACN,YAAI,YAAY,QAAQ,KAAK;AAC7B,eAAO,KAAK,iBAAiB,SAAS,IAChC,iBAAiB,IACjB,iBAAiB,SAAS,KAAK;AAAA,MAEzC;AACA,aAAO,KAAK,gBAAgB,OAAO,OAAO;AAAA,IAC9C;AACA,IAAAA,UAAS,UAAU,KAAK,SAAU,OAAO;AACrC,aAAO,KAAK,SAAS,OAAO,CAAC,EAAE,MAAM,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,WAAW;AAAA,IACvE;AACA,WAAO,eAAeA,UAAS,WAAW,QAAQ;AAAA,MAC9C,KAAK,WAAY;AAEb,cAAM,OAAO,uJAAuJ;AAAA,MACxK;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,kBAAkB;AAAA,MACxD,KAAK,WAAY;AAEb,cAAM,OAAO,2KAA2K;AAAA,MAC5L;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,gBAAgB;AAAA,MACtD,KAAK,WAAY;AAEb,cAAM,OAAO,uKAAuK;AAAA,MACxL;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,gBAAgB;AAAA,MACtD,KAAK,WAAY;AAEb,cAAM,OAAO,uKAAuK;AAAA,MACxL;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,UAAS,WAAW,UAAU,IAAI;AACrC,WAAOA;AAAA,EACX,EAAE;AAAA;AAOF,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,aAAY,MAAM;AACvB,aAAO,OAAO,KAAK,MAAM,IAAI,KAAK;AAAA,IACtC;AACA,IAAAA,aAAY,UAAU,SAAS,SAAU,UAAU,aAAa;AAC5D,UAAI,aAAa,QAAQ;AAAE,mBAAW,KAAK,mBAAmB;AAAA,MAAG;AACjE,aAAO,OAAO,UAAU,OAAO,KAAK,MAAM,UAAU,WAAW;AAAA,IACnE;AACA,IAAAA,aAAY,UAAU,WAAW,SAAU,MAAM;AAC7C,WAAK,iCAAiC;AACtC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,aAAY,UAAU,qBAAqB,SAAU,SAAS,UAAU;AACpE,UAAI,QAAQ;AACR,eAAO;AACX,UAAI,QAAQ,aAAa,UAAU;AAE/B,eAAO;AAAA,MACX;AACA,UAAI,gBAAgB,QAAQ,KAAK,iBAAiB,QAAQ,MAAM,SAAS;AAErE,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,SAAS,QACjB,UAAU,QAAQ,KAClB,CAAC,gBAAgB,QAAQ,MACxB,CAAC,QAAQ,uBACN,QAAQ,eACJ,oBAAoB,SAAS,QAAQ,mBAAmB,CAAC,IAAI;AAGrE,gBAAQ,cAAc,QAAQ;AAC9B,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,IAAAA,aAAY,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AAC5E,UAAI,iBAAiB,KAAK,mBAAmB,SAAS,QAAQ;AAC9D,UAAI,gBAAgB;AAChB,gBAAQ,UAAU,QAAQ,OAAO;AACjC,eAAO;AAAA,MACX;AAEA,cAAQ,IAAI;AAEZ,UAAI,gBAAgB,QAAQ,KAAK,KAAK,iBAAiB,QAAQ,QAAQ,CAAC,GAAG;AAEvE,YAAI,UAAU,iBAAiB,QAAQ;AACvC,gBAAQ,UAAU,QAAQ,OAAO;AACjC,eAAO;AAAA,MACX;AAEA,aAAO,KAAK,YAAY,QAAQ,SAAS,QAAW,QAAQ;AAAA,IAChE;AACA,IAAAA,aAAY,UAAU,cAAc,WAAY;AAC5C,aAAO;AAAA,IACX;AACA,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,aAAY,WAAW,UAAU,IAAI;AACxC,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAKV,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,cAAa;AAClB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,YAAW,UAAU,oBAAoB,SAAU,UAAU;AACzD,aAAO;AAAA,IACX;AACA,IAAAA,YAAW,UAAU,WAAW,SAAU,MAAM;AAM5C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,MAAM;AAC/C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AAE3E,UAAI,CAAC,QAAQ,eAAe,QAAQ,SAAS,QAAQ,QAAQ,gBAAgB,UAAU;AACnF,eAAO;AAAA,MACX;AACA,UAAI,MAAM,KAAK,YAAY,QAAQ,SAAS,QAAW,QAAQ;AAC/D,cAAQ,IAAI;AACZ,aAAO;AAAA,IACX;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAOV,SAAS,OAAO,OAAO;AACnB,SAAO,OAAO,UAAU,YAAY,SAAS,MAAM,WAAW;AAClE;AAKA,SAAS,aAAa,MAAM,WAAW;AACnC,YAAU,MAAM,QAAQ,wBAAwB,SAAS;AAC7D;AAEA,IAAI,iBAAiB,oBAAI,IAAI;AAe7B,SAAS,+BAA+B,OAAO;AAC3C,SAAO,SAAS,yBAAyB,MAAM,MAAM,OAAO;AACxD,YAAQ,KAAK,MAAM;AAAA,MACf,KAAK,UAAU;AACX,YAAI,CAAC,MAAM,UAAU,MAAM,OAAO,IAAI,MAAM,MAAM;AAC9C,cAAI,UAAU,MAAM,QAAQ,IAAI;AAChC,gBAAM,SAAS,MAAM,OAAO;AAC5B,yBAAe,IAAI,KAAK,IAAI;AAAA,YACxB;AAAA,YACA;AAAA,YACA,OAAO;AAAA,UACX,CAAC;AACD,cAAI;AACA,gBAAI,MAAM,KAAK,IAAI;AACnB,kBAAM,UAAU,MAAM,OAAO;AAC7B,gBAAI,eAAe,IAAI,KAAK,EAAE,EAAE,UAAU,OAAO;AAC7C,6BAAe,OAAO,KAAK,EAAE;AAC7B,oBAAM,UAAU,MAAM,SAAS,GAAG;AAAA,YACtC;AACA,mBAAO;AAAA,UACX,SACO,GAAG;AACN,2BAAe,OAAO,KAAK,EAAE;AAC7B,kBAAM,OAAO,MAAM,SAAS,CAAC;AAC7B,kBAAM;AAAA,UACV;AAAA,QACJ,OACK;AACD,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ;AAAA,MACA,KAAK,cAAc;AACf,YAAI,OAAO,eAAe,IAAI,KAAK,MAAM;AACzC,aAAK,QAAQ;AACb,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL,KAAK,qBAAqB;AACtB,YAAI,OAAO,eAAe,IAAI,KAAK,MAAM;AACzC,cAAM,SAAS,MAAM,KAAK,OAAO;AACjC,YAAI;AACA,iBAAO,KAAK,IAAI;AAAA,QACpB,UACA;AACI,gBAAM,UAAU,MAAM,KAAK,OAAO;AAAA,QACtC;AAAA,MACJ;AAAA,MACA,KAAK,cAAc;AACf,YAAI,OAAO,eAAe,IAAI,KAAK,MAAM;AACzC,uBAAe,OAAO,KAAK,MAAM;AACjC,cAAM,OAAO,MAAM,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC;AAC7C,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA,MACA,KAAK,eAAe;AAChB,YAAI,OAAO,eAAe,IAAI,KAAK,MAAM;AACzC,uBAAe,OAAO,KAAK,MAAM;AACjC,cAAM,UAAU,MAAM,KAAK,SAAS,KAAK,KAAK,CAAC,CAAC;AAChD,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASC,eAAc,OAAO,MAAM;AAChC,WAAK,QAAQ;AACb,WAAK,OAAO;AACZ,WAAK,eAAe;AACpB,WAAK,UAAU;AACf,UAAI,OAAO;AACP,cAAM,QAAQ,IAAI;AAAA,MACtB;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,SAAS,SAAU,OAAO;AAC9C,UAAI,KAAK,SAAS;AACd,aAAK,UAAU;AACf,YAAI,KAAK,OAAO;AACZ,eAAK,MAAM,SAAS,KAAK,MAAM,KAAK;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,WAAK;AAAA,IACT;AACA,IAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,WAAK;AAAA,IACT;AACA,WAAO,eAAeA,eAAc,WAAW,mBAAmB;AAAA,MAC9D,KAAK,WAAY;AACb,eAAO,KAAK,eAAe;AAAA,MAC/B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAOA;AAAA,EACX,EAAE;AAAA;AAwBF,SAAS,gCAAgC,iBAAiB;AACtD,MAAIC,kBAAiB,oBAAI,QAAQ;AACjC,SAAO,SAAS,yBAAyB,MAAM,MAAM;AAEjD,QAAI,sBAAsB,KAAK,oBACzBA,gBAAe,IAAI,KAAK,iBAAiB,IACzC;AACN,QAAI,KAAK,SAAS,UAAU;AACxB,UAAI,UAAU,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG;AAAA;AAAA,QAEvC,KAAK,uBAAuB,oBAAoB,KAAK;AAAA,QAAK,YAAY,uBAAuB,oBAAoB;AAAA,MAAK,CAAC;AAC3H,UAAI,eAAe,CAAC,gBAAgB,UAAU,gBAAgB,OAAO,OAAO;AAC5E,UAAI,QAAQ,eAAe,kBAAkB;AAC7C,UAAI,gBAAgB,IAAI,cAAc,OAAO,OAAO;AACpD,MAAAA,gBAAe,IAAI,MAAM,aAAa;AACtC,UAAI,MAAM;AACV,UAAI;AACA,cAAM,KAAK,IAAI;AAAA,MACnB,SACO,GAAG;AACN,sBAAc,OAAO,CAAC;AACtB,cAAM;AAAA,MACV;AACA,UAAI,CAAC,cAAc,iBAAiB;AAEhC,sBAAc,OAAO;AAAA,MACzB;AACA,aAAO;AAAA,IACX,OACK;AACD,UAAI,CAAC,qBAAqB;AACtB,eAAO,KAAK,IAAI;AAAA,MACpB;AACA,cAAQ,KAAK,MAAM;AAAA,QACf,KAAK,cAAc;AACf,8BAAoB,gBAAgB;AACpC,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,QACA,KAAK;AAAA,QACL,KAAK,qBAAqB;AACtB,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,QACA,KAAK,cAAc;AACf,cAAI,QAAQ,KAAK,KAAK,CAAC;AACvB,cAAI;AACA,mBAAO,KAAK,IAAI;AAAA,UACpB,UACA;AACI,gCAAoB,gBAAgB;AACpC,gBAAI,CAAC,oBAAoB,iBAAiB;AACtC,kCAAoB,OAAO,KAAK;AAAA,YACpC;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,KAAK,eAAe;AAChB,cAAI;AACA,mBAAO,KAAK,IAAI;AAAA,UACpB,UACA;AACI,gCAAoB,gBAAgB;AACpC,gBAAI,CAAC,oBAAoB,iBAAiB;AACtC,kCAAoB,OAAO;AAAA,YAC/B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,kBAAkB,MAAM,YAAY,OAAO,KAAK;AACrD,MAAI,eAAe;AACf,WAAO,EAAE,WAAW,IAAI,QAAQ,EAAE;AACtC,MAAI,YAAY,GAAG;AACf,WAAO;AAGX,MAAI,gBAAgB,GAAG;AACnB,WAAO,2BAA2B,eAAe,QAAQ,GAAG,EAAE,OAAO,GAAG;AAC5E,MAAI,OAAO,QAAQ;AACf,WAAO,2BAA2B,YAAY;AAClD,MAAI,OAAO,QAAQ,YAAY,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,GAAG;AAC9D,WAAO,2BAA2B,cAAe,OAAO,IAAI,eAAe,IAAI,YAAY,QACvF,oBAAoB,GAAG;AAC/B,MAAI;AAGA,SAAK,UAAU,GAAG;AAClB,WAAO;AAAA,EACX,SACO,GAAG;AACN,WAAO,2BAA2B,KAAK,CAAC;AAAA,EAC5C;AACJ;AACA,SAAS,oBAAoB,KAAK,OAAO;AACrC,MAAI,SAAS,OAAO,UAAU,YAAY,eAAe;AACrD,WAAO,IAAI,KAAK,MAAM,WAAW,CAAC;AACtC,SAAO;AACX;AACA,SAAS,2BAA2B,UAAU;AAC1C,SAAO;AAAA,IACH,qBAAqB;AAAA,IACrB,MAAM;AAAA,EACV;AACJ;AASA,SAAS,YAAY,QAAQ,SAAS;AAElC,wBAAsB,QAAQ,CAAC;AAC/B,YAAU,SAAS,SAAU,GAAG;AAAE,WAAO,OAAO,MAAM;AAAA,EAAU,GAAG,mBAAmB,CAAC;AACvF,cAAY,WAAY;AACpB,YAAQ,OAAO,EAAE,QAAQ,SAAUC,SAAQ;AAAE,aAAO,gBAAgB,QAAQA,OAAM;AAAA,IAAG,CAAC;AAAA,EAC1F,CAAC;AACL;AACA,SAAS,gBAAgB,QAAQA,SAAQ;AACrC,MAAI,iBAAiB,WAAW,QAAQA,QAAO,QAAQ,EAAE;AACzD,MAAI,CAAC;AACD,UAAM,OAAO,2BAA2BA,QAAO,QAAQ,GAAG;AAC9D,MAAI,OAAO,iBAAiB,cAAc;AAE1C,MAAIA,QAAO,SAAS,kBAAkB;AAClC,WAAO,WAAW,KAAK,MAAM,gBAAgBA,QAAO,KAAK,CAAC,CAAC;AAAA,EAC/D;AACA,MAAIA,QAAO,SAAS,mBAAmB;AACnC,WAAO,cAAc,KAAK,MAAM,gBAAgBA,QAAO,KAAK,CAAC,CAAC;AAAA,EAClE;AACA,MAAI,EAAE,OAAO,eAAeA,QAAO,IAAI,MAAM;AACzC,UAAM,OAAO,aAAaA,QAAO,OAAO,0BAA0B,KAAK,OAAO,GAAG;AACrF,SAAO,eAAeA,QAAO,IAAI,EAAE,MAAM,gBAAgBA,QAAO,OAAOA,QAAO,KAAK,IAAI,SAAU,GAAG;AAAE,WAAO,oBAAoB,MAAM,CAAC;AAAA,EAAG,CAAC,IAAI,CAAC,CAAC;AACtJ;AA0BA,SAAS,cAAc,SAAS,QAAQ;AAEpC,wBAAsB,SAAS,CAAC;AAChC,MAAI,UAAU,CAAC;AACf,MAAI,WAAW,SAAU,MAAM;AAC3B,QAAI,aAAa,SAAS,OAAO,MAAM,wBAAwB,CAAC,IAAI;AACpE,QAAI,YAAY;AACZ,cAAQ,KAAK,IAAI;AAAA,IACrB;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,WAAW;AAAA,IACX;AAAA,IACA,IAAI,YAAY;AACZ,aAAO,CAAC,CAAC;AAAA,IACb;AAAA,IACA,MAAM,WAAY;AACd,UAAI,UAAU;AACV,iBAAS;AACT,mBAAW;AAAA,MACf;AAAA,IACJ;AAAA,IACA,QAAQ,WAAY;AAChB,UAAI;AACA;AACJ,iBAAW,SAAS,SAAS,QAAQ;AAAA,IACzC;AAAA,IACA,QAAQ,SAAU,QAAQ;AACtB,kBAAY,QAAQ,OAAO;AAAA,IAC/B;AAAA,EACJ;AACA,WAAS,OAAO;AAChB,SAAO;AACX;AAuCA,SAAS,SAAS,QAAQ,UAAU,aAAa;AAC7C,MAAI,gBAAgB,QAAQ;AAAE,kBAAc;AAAA,EAAO;AAEnD,wBAAsB,QAAQ,CAAC;AAC/B,MAAI,QAAQ,GAAG;AACX,QAAI,CAAC,OAAO,MAAM;AACd,gBAAU,8IAA8I;AAC5J,QAAI,CAAC,YAAY,MAAM;AACnB,gBAAU,+IAA+I;AAAA,EACjK;AACA,SAAO,cAAc,QAAQ,SAAS,QAAQ,SAAS,MAAM;AACzD,QAAI,QAAQ,SAAS,YAAY,QAAQ,OAAO,QAAQ,QAAQ;AAC5D,UAAI,eAAe,iBAAiB,QAAQ,OAAO;AACnD,UAAI,OAAO;AAAA,QACP,MAAM,QAAQ;AAAA,QACd,MAAM,4BAA4B,iBAAiB,MAAM,GAAG,YAAY;AAAA,QACxE,MAAM,QAAQ,KAAK,IAAI,SAAU,KAAK,OAAO;AACzC,iBAAO,kBAAkB,cAAc,QAAQ,MAAM,OAAO,GAAG;AAAA,QACnE,CAAC;AAAA,MACL;AACA,UAAI,aAAa;AACb,YAAI,MAAM,KAAK,OAAO;AACtB,iBAAS,IAAI;AACb,eAAO;AAAA,MACX,OACK;AACD,iBAAS,IAAI;AACb,eAAO,KAAK,OAAO;AAAA,MACvB;AAAA,IACJ,OACK;AACD,aAAO,KAAK,OAAO;AAAA,IACvB;AAAA,EACJ,CAAC;AACL;AAEA,IAAI,eAAe;AACnB,IAAI;AAKJ,SAAS,0BAA0B;AAC/B,SAAO;AACX;AAKA,SAAS,kBAAkB;AACvB,SAAO;AACX;AAMA,SAAS,qBAAqB,SAAS,IAAI;AACvC,MAAI,OAAO,iBAAiB,QAAQ,OAAO;AAC3C,MAAI,QAAQ,SAAS,UAAU;AAC3B,SAAK,YAAY;AAAA,MACb,eAAe;AAAA,IACnB,CAAC;AAAA,EACL;AACA,MAAI,sBAAsB,KAAK;AAC/B,OAAK,mBAAmB;AACxB,MAAI,kBAAkB;AACtB,yBAAuB;AACvB,MAAI;AACA,WAAO,eAAe,MAAM,SAAS,EAAE;AAAA,EAC3C,UACA;AACI,2BAAuB;AACvB,SAAK,mBAAmB;AAAA,EAC5B;AACJ;AAKA,SAAS,uBAAuB,eAAe;AAC3C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,cAAc,SAAS;AACvB,WAAO;AACX,SAAO,cAAc;AACzB;AAKA,SAAS,oBAAoB,QAAQ,MAAM,IAAI;AAC3C,MAAI,MAAM,WAAY;AAClB,QAAI,KAAK,gBAAgB;AACzB,QAAI,gBAAgB;AACpB,QAAI,sBAAsB,uBAAuB,aAAa;AAC9D,WAAO,qBAAqB;AAAA,MACxB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,MAAM,YAAY,SAAS;AAAA,MAC3B,SAAS;AAAA,MACT,MAAM,QAAQ,MAAM;AAAA,MACpB,QAAQ,gBAAgB,cAAc,SAAS;AAAA,MAC/C,UAAU,gBAAgB,cAAc,KAAK;AAAA,MAC7C,cAAc,gBACR,SAAS,cAAc,cAAc,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC;AAAA,MAClE,aAAa;AAAA,MACb,mBAAmB;AAAA,IACvB,GAAG,EAAE;AAAA,EACT;AACA,MAAI,eAAe;AACnB,SAAO;AACX;AAWA,SAAS,cAAc,QAAQ,SAAS,cAAc;AAClD,MAAI,iBAAiB,QAAQ;AAAE,mBAAe;AAAA,EAAM;AACpD,MAAI,OAAO,iBAAiB,MAAM;AAClC,MAAI,QAAQ,GAAG;AACX,QAAI,CAAC,KAAK,qBAAqB;AAC3B,gBAAU,oLAAoL;AAAA,IAClM;AAAA,EACJ;AACA,SAAO,KAAK,cAAc,SAAS,YAAY;AACnD;AAwBA,SAAS,SAAS,SAAS,IAAI,cAAc;AACzC,MAAI,iBAAiB,QAAQ;AAAE,mBAAe;AAAA,EAAM;AACpD,MAAI,aAAa,EAAE,SAAkB,aAA2B;AAChE,KAAG,kBAAkB,GAAG,mBAAmB,CAAC;AAC5C,KAAG,gBAAgB,KAAK,UAAU;AAClC,SAAO;AACX;AACA,IAAI;AAAA;AAAA,EAAsC,WAAY;AAClD,aAASC,sBAAqB,MAAM,IAAI;AACpC,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,cAAc,CAAC;AAEpB,UAAI,GAAG,iBAAiB;AACpB,aAAK,YAAY,KAAK,GAAG,eAAe;AAAA,MAC5C;AACA,UAAI,IAAI;AAER,aAAO,GAAG;AACN,YAAI,EAAE;AACF,eAAK,YAAY,KAAK,EAAE,WAAW;AACvC,YAAI,EAAE;AAAA,MACV;AAAA,IACJ;AACA,WAAO,eAAeA,sBAAqB,WAAW,WAAW;AAAA,MAC7D,KAAK,WAAY;AACb,eAAO,KAAK,YAAY,UAAU;AAAA,MACtC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,sBAAqB,UAAU,oBAAoB,WAAY;AAC3D,UAAIC,SAAQ,KAAK,YAAY,KAAK,UAAU;AAC5C,UAAI,CAACA;AACD,eAAO;AACX,UAAI,OAAOA,OAAM,KAAK,cAAc;AACpC,UAAI,CAAC,MAAM;AACP,aAAK;AACL,aAAK,eAAe;AACpB,eAAO,KAAK,kBAAkB;AAAA,MAClC;AACA,aAAO;AAAA,IACX;AACA,WAAOD;AAAA,EACX,EAAE;AAAA;AACF,SAAS,eAAe,MAAM,UAAU,YAAY;AAChD,MAAI,cAAc,IAAI,qBAAqB,MAAM,UAAU;AAE3D,MAAI,YAAY;AACZ,WAAO,OAAO,UAAU,EAAE,MAAM,MAAM,SAAS,IAAI;AACvD,MAAI,SAAS;AACb,WAAS,kBAAkB,MAAM;AAC7B,QAAI,aAAa,YAAY,kBAAkB;AAC/C,QAAI,UAAU,cAAc,WAAW;AACvC,QAAI,CAAC,SAAS;AACV,aAAO,OAAO,UAAU,EAAE,MAAM,MAAM,KAAK,IAAI;AAAA,IACnD;AAEA,QAAI,CAAC,WAAW,gBAAgB,KAAK,KAAK,IAAI,GAAG;AAC7C,aAAO,kBAAkB,IAAI;AAAA,IACjC;AACA,QAAI,cAAc;AAClB,aAAS,KAAK,OAAO,UAAU;AAC3B,oBAAc;AAKd,eAAS,kBAAkB,KAAK;AAChC,UAAI,UAAU;AACV,iBAAS,SAAS,MAAM;AAAA,MAC5B;AAAA,IACJ;AACA,QAAI,eAAe;AACnB,aAAS,MAAM,OAAO;AAClB,qBAAe;AAGf,eAAS;AAAA,IACb;AACA,YAAQ,MAAM,MAAM,KAAK;AACzB,QAAI,QAAQ,GAAG;AACX,UAAI,CAAC,eAAe,CAAC,cAAc;AAC/B,YAAI,QAAQ,iBAAiB,KAAK,IAAI;AACtC,cAAM,OAAO,uEAAuE,QAAQ,OAAO,uBAAwB,KAAK,OAAO,oBAAqB,MAAM,KAAK,OAAO,eAAe;AAAA,MACjM,WACS,eAAe,cAAc;AAClC,YAAI,QAAQ,iBAAiB,KAAK,IAAI;AACtC,cAAM,OAAO,2DAA2D,QAAQ,OAAO,uBAAwB,KAAK,OAAO,oBAAqB,MAAM,KAAK,OAAO,gBAAgB;AAAA,MACtL;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAO,kBAAkB,QAAQ;AACrC;AAKA,SAAS,0BAA0B;AAC/B,MAAI,UAAU,wBAAwB;AACtC,SAAO,WAAW,QAAQ,SAAS,UAAU;AACzC,cAAU,QAAQ;AAAA,EACtB;AACA,SAAO;AACX;AACA,SAAS,8BAA8B,eAAe,cAAc,aAAa;AAC7E,MAAI,WAAW,OAAO,iBAAiB,WAAW,eAAe,aAAa;AAC9E,MAAI,UAAU,cACR,gBACA,cAAc;AACpB,SAAO,SAAS;AACZ,QAAI,QAAQ,OAAO,UAAU;AACzB,aAAO;AAAA,IACX;AACA,cAAU,QAAQ;AAAA,EACtB;AACA,SAAO;AACX;AAIA,SAAS,uBAAuB,eAAe,QAAQ;AACnD,SAAO,8BAA8B,eAAe,QAAQ,KAAK;AACrE;AAIA,SAAS,6BAA6B,eAAe,cAAc;AAC/D,SAAO,8BAA8B,eAAe,cAAc,IAAI;AAC1E;AAEA,SAAS,cAAc,OAAO;AAC1B,MAAI;AACA,WAAO,KAAK,UAAU,KAAK;AAAA,EAC/B,SACO,GAAG;AAEN,WAAO,sBAAsB,IAAI;AAAA,EACrC;AACJ;AAKA,SAAS,iBAAiB,OAAO;AAC7B,SAAO,OAAO,UAAU,aAClB,eAAe,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MACrD,gBAAgB,KAAK,IACjB,MAAM,QAAQ,MACd,MAAM,cAAc,KAAK,IAAI;AAC3C;AACA,SAAS,kBAAkB,eAAe;AACtC,SAAO,cAAc,SAAS,MACxB,gBACA,cAAc,UAAU,GAAG,GAAG,IAAI,WAAW,cAAc,UAAU,cAAc,SAAS,CAAC;AACvG;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,QAAQ,MAAM;AAClB,MAAI,OAAO,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE;AACnD,MAAI,WAAW,MAAM,QAChB,IAAI,SAAU,IAAI;AACnB,QAAI,OAAO,GAAG;AACd,WAAO;AAAA,EACX,CAAC,EACI,OAAO,SAAU,MAAM;AAAE,WAAO,KAAK,SAAS;AAAA,EAAG,CAAC,EAClD,KAAK,GAAG;AACb,MAAI,aAAa,SAAS,SAAS,IAAI,eAAgB,WAAW,OAAQ;AAC1E,MAAI,kBAAkB,gBAAgB,KAAK,IACrC,mBAAmB,iBAAiB,KAAK,EAAE,KAAK,OAAO,MACvD,YAAY,KAAK,IACb,UACA;AACV,MAAI,uBAAuB,QAAQ,gBAAgB,KAAK,KAAK,KAAK,GAAG,iBAAiB,KAAK,EAAE,QAAQ;AACrG,SAAQ,KAAK,aAAa,kBAAkB,MAAM,iBAAiB,KAAK,IAAI,yBAAyB,OAAO,eAAe,KAAK,OAAO,MAAM,OACxI,MAAM,UAAU,OAAO,MAAM,UAAU,MAAM,OAC7C,OACK,gBAAgB,IAAI,KAAK,YAAY,KAAK,IACtC,MACA,gCAAgC,KAAK,OAAO,2BAA2B,KAAK,SAAS,IAAI,gBACtF,uBACK,uFACA,MACZ;AACd;AAKA,SAAS,kBAAkB,SAAS,MAAM,MAAM;AAC5C,SAAO,QAAQ,OAAO,CAAC,EAAE,MAAY,KAAW,CAAC,CAAC;AACtD;AAKA,SAAS,mBAAmB;AACxB,SAAO;AACX;AAKA,SAAS,iBAAiB,SAAS,OAAO,SAAS;AAC/C,SAAO,CAAC,EAAE,SAAkB,OAAc,QAAiB,CAAC;AAChE;AAKA,SAAS,kBAAkB,QAAQ;AAC/B,SAAO,OAAO,OAAO,SAAU,GAAG,GAAG;AAAE,WAAO,EAAE,OAAO,CAAC;AAAA,EAAG,GAAG,CAAC,CAAC;AACpE;AAMA,SAAS,kBAAkB,MAAM,OAAO;AAEpC,MAAI,sBAAsB,GAAG;AACzB,cAAU,MAAM,KAAK;AAAA,EACzB;AACJ;AASA,SAAS,UAAU,MAAM,OAAO;AAC5B,MAAI,SAAS,KAAK,SAAS,OAAO,CAAC,EAAE,MAAM,IAAI,KAAW,CAAC,CAAC;AAC5D,MAAI,OAAO,SAAS,GAAG;AACnB,UAAM,OAAO,yBAAyB,MAAM,OAAO,MAAM,CAAC;AAAA,EAC9D;AACJ;AACA,SAAS,yBAAyB,MAAM,OAAO,QAAQ;AACnD,MAAI,OAAO,WAAW,GAAG;AACrB,WAAO;AAAA,EACX;AACA,SAAQ,4BAA4B,kBAAkB,iBAAiB,KAAK,CAAC,IAAI,UAAU,KAAK,OAAO,eAAe,OAAO,IAAI,aAAa,EAAE,KAAK,QAAQ;AACjK;AAEA,IAAI,oBAAoB;AAKxB,IAAI;AAAA;AAAA,EAAiC,WAAY;AAC7C,aAASE,mBAAkB;AACvB,WAAK,UAAU;AAEf,WAAK,QAAQ,WAAW,IAAI;AAG5B,WAAK,6BAA6B,WAAW,IAAI;AAAA,IACrD;AACA,IAAAA,iBAAgB,UAAU,mCAAmC,SAAUf,aAAY;AAC/E,UAAI,MAAM,KAAK,2BAA2B,IAAIA,WAAU;AAExD,WAAK,2BAA2B,IAAIA,aAAY,QAAQ,SAAY,IAAI,MAAM,CAAC;AAAA,IACnF;AACA,IAAAe,iBAAgB,UAAU,gCAAgC,SAAUf,aAAY;AAC5E,UAAI,iBAAiB,KAAK,2BAA2B,IAAIA,WAAU,KAAK;AACxE,aAAO,KAAK,UAAU,MAAM;AAAA,IAChC;AACA,IAAAe,iBAAgB,UAAU,iBAAiB,SAAU,MAAM,iBAAiB;AACxE,UAAI,oBAAoB,QAAQ;AAAE,0BAAkB;AAAA,MAAM;AAC1D,UAAI,KAAK,qBAAqB;AAC1B,YAAIf,cAAa,KAAK;AACtB,YAAI,CAAC,KAAK,MAAM,IAAIA,WAAU,GAAG;AAC7B,eAAK,MAAM,IAAIA,aAAY,WAAW,MAAM,CAAC,GAAG,WAAW,CAAC;AAAA,QAChE;AACA,YAAIgB,OAAM,KAAK,MAAM,IAAIhB,WAAU;AACnC,YAAIgB,KAAI,QAAQ,IAAI,MAAM;AACtB,gBAAM,OAAO,oBAAoB;AACrC,QAAAA,KAAI,KAAK,IAAI;AACb,YAAI,iBAAiB;AACjB,eAAK,iCAAiChB,WAAU;AAAA,QACpD;AAAA,MACJ;AAAA,IACJ;AACA,IAAAe,iBAAgB,UAAU,aAAa,SAAU,MAAM;AACnD,UAAI,QAAQ;AACZ,aAAO,KAAK,gBAAgB,KAAK,EAAE,QAAQ,SAAU,OAAO;AACxD,eAAO,MAAM,QAAQ,SAAU,OAAO;AAClC,gBAAM,eAAe,KAAK;AAAA,QAC9B,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,MAAM;AACnD,UAAI,KAAK,qBAAqB;AAC1B,YAAI,KAAK,KAAK;AACd,YAAIC,OAAM,KAAK,MAAM,IAAI,EAAE;AAC3B,YAAIA,MAAK;AACL,UAAAA,KAAI,OAAO,IAAI;AAEf,cAAI,CAACA,KAAI,QAAQ;AACb,iBAAK,MAAM,OAAO,EAAE;AAAA,UACxB;AACA,eAAK,iCAAiC,KAAK,UAAU;AAAA,QACzD;AAAA,MACJ;AAAA,IACJ;AACA,IAAAD,iBAAgB,UAAU,aAAa,SAAU,MAAM;AACnD,UAAI,QAAQ;AACZ,UAAI,MAAM,IAAIA,iBAAgB;AAC9B,UAAI,WAAW,KAAK;AACpB,cAAQ,KAAK,KAAK,EAAE,QAAQ,SAAU,IAAI;AACtC,YAAI,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAChD,YAAI,WAAW;AACf,iBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,cAAI,MAAM,CAAC,EAAE,KAAK,QAAQ,QAAQ,MAAM,GAAG;AACvC,gBAAI,eAAe,MAAM,CAAC,GAAG,KAAK;AAClC,kBAAM,OAAO,GAAG,CAAC;AACjB,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,UAAU;AACV,gBAAM,iCAAiC,EAAE;AAAA,QAC7C;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,iBAAgB,UAAU,MAAM,SAAU,MAAMf,aAAY;AACxD,UAAIgB,OAAM,KAAK,MAAM,IAAIhB,WAAU;AACnC,UAAI,CAACgB;AACD,eAAO;AACX,aAAOA,KAAI,KAAK,SAAU,WAAW;AAAE,eAAO,KAAK,iBAAiB,UAAU,IAAI;AAAA,MAAG,CAAC;AAAA,IAC1F;AACA,IAAAD,iBAAgB,UAAU,UAAU,SAAU,MAAMf,aAAY;AAC5D,UAAIgB,OAAM,KAAK,MAAM,IAAIhB,WAAU;AACnC,UAAI,CAACgB;AACD,eAAO;AACX,UAAI,UAAUA,KAAI,OAAO,SAAU,WAAW;AAAE,eAAO,KAAK,iBAAiB,UAAU,IAAI;AAAA,MAAG,CAAC;AAC/F,cAAQ,QAAQ,QAAQ;AAAA,QACpB,KAAK;AACD,iBAAO;AAAA,QACX,KAAK;AACD,iBAAO,QAAQ,CAAC;AAAA,QACpB;AACI,gBAAM,OAAO,yCAAyC,KAAK,OAAO,iBAAiBhB,cAAa,oDAAoD,QAC/I,IAAI,SAAU,GAAG;AAAE,mBAAO,EAAE;AAAA,UAAM,CAAC,EACnC,KAAK,IAAI,CAAC;AAAA,MACvB;AAAA,IACJ;AACA,WAAOe;AAAA,EACX,EAAE;AAAA;AAMF,SAAS,iBAAiB,MAAM,QAAQ,SAAS,aAAa,cAAc;AACxE,MAAI,eAAe,qBAAqB,YAAY;AACpD,MAAI,cAAc;AACd,QAAI,aAAa,QAAQ;AAErB,YAAM,OAAO,kIAAkI,SAAS,OAAO,OAAO,MAAM,MAAM,UAAU,iCAAiC,aAAa,OAAO,GAAG;AAAA,IACxP;AACA,QAAI,QAAQ;AACR,mBAAa,UAAU,QAAQ,OAAO;AAAA,IAC1C;AAEA,WAAO;AAAA,EACX;AAEA,SAAO,IAAI,WAAW,MAAM,QAAQ,SAAS,aAAa,YAAY;AAC1E;AAKA,SAAS,iBAAiB,MAAM,QAAQ,SAAS,aAAa,cAAc;AACxE,SAAO,IAAI,WAAW,MAAM,QAAQ,SAAS,aAAa,YAAY;AAC1E;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,iBAAiB,cAAc,iBAAiB;AAC3D;AAMA,IAAI;AAAA,CACH,SAAUE,gBAAe;AACtB,EAAAA,eAAcA,eAAc,cAAc,IAAI,CAAC,IAAI;AACnD,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9C,EAAAA,eAAcA,eAAc,WAAW,IAAI,CAAC,IAAI;AAChD,EAAAA,eAAcA,eAAc,WAAW,IAAI,CAAC,IAAI;AAChD,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC/C,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AASxC,SAAS,gBAAgB,OAAO;AAC5B,SAAO,CAAC,EAAE,SAAS,MAAM;AAC7B;AAKA,SAAS,sBAAsB,OAAO,WAAW;AAC7C,YAAU,OAAO,iBAAiB,wBAAwB,SAAS;AACvE;AAKA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,CAAC,gBAAgB,KAAK,GAAG;AAEzB,UAAM,OAAO,WAAW,QAAQ,iBAAiB;AAAA,EACrD;AACA,SAAO,MAAM;AACjB;AAKA,SAAS,qBAAqB,OAAO;AACjC,SAAQ,SAAS,MAAM,aAAc;AACzC;AAKA,SAAS,SAAS;AACd,SAAO,iBAAiB,IAAI,EAAE;AAClC;AACA,IAAI,YAAY,SAAU,GAAG;AAAE,SAAO;AAAM;AAK5C,SAAS,4BAA4B,MAAM,QAAQ;AAE/C,MAAI,KAAK,SAAS,OAAO,MAAM;AAC3B,UAAM,OAAO,8CAA8C,OAAO,YAAY,SAAS,wCAAwC;AAAA,EACnI;AACA,MAAI,YAAY,cAAc,KAAK,IAAI;AACvC,MAAI,cAAc,cAAc,OAAO,IAAI;AAC3C,MAAI,SAAS;AACb,SAAO,SAAS,UAAU,QAAQ,UAAU;AACxC,QAAI,UAAU,MAAM,MAAM,YAAY,MAAM;AACxC;AAAA,EACR;AAEA,SAAQ,UAAU,MAAM,MAAM,EAAE,IAAI,SAAS,EAAE,KAAK,GAAG,IAAI,aAAa,YAAY,MAAM,MAAM,CAAC;AACrG;AAKA,SAAS,kBAAkB,MAAM,MAAM,oBAAoB;AACvD,MAAI,uBAAuB,QAAQ;AAAE,yBAAqB;AAAA,EAAM;AAChE,SAAO,uBAAuB,MAAM,cAAc,IAAI,GAAG,kBAAkB;AAC/E;AAKA,SAAS,uBAAuB,MAAM,WAAW,oBAAoB;AACjE,MAAI,uBAAuB,QAAQ;AAAE,yBAAqB;AAAA,EAAM;AAChE,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,OAAO,UAAU,CAAC;AACtB,QAAI,SAAS,MAAM;AACf,gBAAU,QAAQ;AAClB,UAAI;AACA;AAAA,IACR,WACS,SAAS,KAAK;AACnB;AAAA,IACJ,WACS,SAAS;AACd,UAAI,mBAAmB,YAAY;AAG/B,YAAI;AACA,cAAI,QAAQ,QAAQ;AACpB,cAAI,gBAAgB,KAAK,GAAG;AACxB,sBAAU,iBAAiB,KAAK;AAAA,UAEpC;AAAA,QACJ,SACO,GAAG;AACN,cAAI,CAAC,oBAAoB;AACrB,mBAAO;AAAA,UACX;AACA,gBAAM;AAAA,QACV;AAAA,MACJ;AACA,UAAI,mBAAmB,YAAY;AAC/B,YAAI,UAAU,QAAQ,aAAa,IAAI;AACvC,YAAI,SAAS;AACT,oBAAU,QAAQ,aAAa,IAAI;AACnC,cAAI;AACA;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AACA,QAAI;AACA,YAAM,OAAO,wBAAwB,OAAO,iBAAiB,aAAa,UAAU,MAAM,GAAG,CAAC,CAAC,KAAK,OAAO,wBAAwB,aAAa,SAAS,IAAI,GAAG;AAAA;AAEhK,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAKA,SAAS,yBAAyB,YAAY;AAC1C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,OAAO,KAAK,UAAU;AACjC,MAAI,CAAC,KAAK;AACN,WAAO;AACX,MAAI,SAAS,IAAI,MAAM,KAAK,MAAM;AAClC,OAAK,QAAQ,SAAU,KAAK,OAAO;AAC/B,WAAO,KAAK,IAAI,WAAW,GAAG;AAAA,EAClC,CAAC;AACD,SAAO;AACX;AAWA,IAAI,sBAAsB;AAW1B,SAAS,UAAU,aAAa;AAC5B,aAAW,WAAW,+CAA+C,mBAAmB;AACxF,SAAO,KAAK,WAAW;AAC3B;AAMA,IAAI,cAAc,OAAO,OAAO,CAAC,CAAC;AAKlC,IAAI,eAAe,OAAO,OAAO,CAAC,CAAC;AAKnC,IAAI,cAAc,OAAO,UAAU,WAAW,EAAE,MAAM,MAAM,IAAI,EAAE,MAAM,OAAO,OAAO,MAAM;AAC5F,OAAO,OAAO,WAAW;AAKzB,SAAS,OAAO,SAAS;AACrB,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAiB;AACrD,SAAO,IAAI,MAAM,uBAAuB,OAAO;AACnD;AAKA,SAAS,SAAS,GAAG;AACjB,SAAO;AACX;AAOA,IAAI,YAAY,OAAO,aACnB,SAAU,OAAO;AACb,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM;AACjF;AAKJ,SAAS,QAAQ,KAAK;AAClB,SAAO,MAAM,QAAQ,GAAG,KAAK,kBAAkB,GAAG;AACtD;AAKA,SAAS,QAAQ,KAAK;AAClB,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,GAAG;AACX,WAAO;AACX,SAAO,CAAC,GAAG;AACf;AAKA,SAAS,OAAO,GAAG;AACf,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,MAAE,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC5B;AACA,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,QAAI,UAAU,EAAE,CAAC;AACjB,aAAS,OAAO;AACZ,QAAE,GAAG,IAAI,QAAQ,GAAG;AAAA,EAC5B;AACA,SAAO;AACX;AAKA,SAAS,cAAc,OAAO;AAC1B,MAAI,UAAU,QAAQ,OAAO,UAAU;AACnC,WAAO;AACX,MAAI,QAAQ,OAAO,eAAe,KAAK;AACvC,SAAO,UAAU,OAAO,aAAa,UAAU;AACnD;AAKA,SAAS,UAAU,OAAO;AACtB,SAAQ,UAAU,QACd,OAAO,UAAU,YACjB,EAAE,iBAAiB,SACnB,EAAE,iBAAiB;AAC3B;AAKA,SAAS,YAAY,OAAO,aAAa;AACrC,MAAI,gBAAgB,QAAQ;AAAE,kBAAc;AAAA,EAAM;AAClD,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,MAAI,OAAO,UAAU,YACjB,OAAO,UAAU,YACjB,OAAO,UAAU,aAChB,eAAe,iBAAiB;AACjC,WAAO;AACX,SAAO;AACX;AAMA,SAAS,OAAO,OAAO;AACnB,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,SAAO,YAAY,KAAK,KAAK,kBAAkB,KAAK,IAAI,QAAQ,OAAO,OAAO,KAAK;AACvF;AAMA,SAAS,WAAW,OAAO;AACvB,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,SAAO,KAAK;AACZ,MAAI,cAAc,KAAK,GAAG;AACtB,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,SAAS;AAC1C,UAAI,CAAC,YAAY,MAAM,OAAO,CAAC,KAC3B,CAAC,OAAO,SAAS,MAAM,OAAO,CAAC,GAAG;AAClC,mBAAW,MAAM,OAAO,CAAC;AAAA,MAC7B;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAKA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,UAAU;AAC5B;AAKA,SAAS,mBAAmB,QAAQ,UAAU,OAAO;AACjD,SAAO,eAAe,QAAQ,UAAU;AAAA,IACpC,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,IACd;AAAA,EACJ,CAAC;AACL;AAKA,SAAS,sBAAsB,QAAQ,UAAU,OAAO;AACpD,SAAO,eAAe,QAAQ,UAAU;AAAA,IACpC,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,cAAc;AAAA,IACd;AAAA,EACJ,CAAC;AACL;AAKA,IAAI;AAAA;AAAA,EAA8B,WAAY;AAC1C,aAASC,gBAAe;AACpB,WAAK,WAAW,CAAC;AAAA,IACrB;AACA,WAAO,eAAeA,cAAa,WAAW,kBAAkB;AAAA,MAC5D,KAAK,WAAY;AACb,eAAO,KAAK,SAAS,SAAS;AAAA,MAClC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,cAAa,UAAU,WAAW,SAAU,IAAI,gBAAgB;AAC5D,UAAI,QAAQ;AACZ,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB;AAAA,MAAO;AACzD,UAAI,gBAAgB;AAChB,aAAK,SAAS,QAAQ,EAAE;AAAA,MAC5B,OACK;AACD,aAAK,SAAS,KAAK,EAAE;AAAA,MACzB;AACA,aAAO,WAAY;AACf,cAAM,WAAW,EAAE;AAAA,MACvB;AAAA,IACJ;AACA,IAAAA,cAAa,UAAU,MAAM,SAAU,IAAI;AACvC,aAAO,KAAK,SAAS,QAAQ,EAAE,KAAK;AAAA,IACxC;AACA,IAAAA,cAAa,UAAU,aAAa,SAAU,IAAI;AAC9C,UAAI,QAAQ,KAAK,SAAS,QAAQ,EAAE;AACpC,UAAI,SAAS,GAAG;AACZ,aAAK,SAAS,OAAO,OAAO,CAAC;AAAA,MACjC;AAAA,IACJ;AACA,IAAAA,cAAa,UAAU,QAAQ,WAAY;AACvC,WAAK,SAAS,SAAS;AAAA,IAC3B;AACA,IAAAA,cAAa,UAAU,OAAO,WAAY;AACtC,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AAEA,UAAI,WAAW,KAAK,SAAS,MAAM;AACnC,eAAS,QAAQ,SAAU,GAAG;AAAE,eAAO,EAAE,MAAM,QAAQ,SAAS,IAAI,CAAC;AAAA,MAAG,CAAC;AAAA,IAC7E;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAKF,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASC,iBAAgB;AAAA,IACzB;AACA,IAAAA,eAAc,UAAU,iBAAiB,SAAU,OAAO;AACtD,UAAI,UAAU,KAAK,iBAAiB,KAAK,cAAc,KAAK;AAC5D,aAAO,CAAC,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,IAAAA,eAAc,UAAU,WAAW,SAAU,OAAO,IAAI,gBAAgB;AACpE,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB;AAAA,MAAO;AACzD,UAAI,CAAC,KAAK,eAAe;AACrB,aAAK,gBAAgB,CAAC;AAAA,MAC1B;AACA,UAAI,UAAU,KAAK,cAAc,KAAK;AACtC,UAAI,CAAC,SAAS;AACV,kBAAU,KAAK,cAAc,KAAK,IAAI,IAAI,aAAa;AAAA,MAC3D;AACA,aAAO,QAAQ,SAAS,IAAI,cAAc;AAAA,IAC9C;AACA,IAAAA,eAAc,UAAU,MAAM,SAAU,OAAO,IAAI;AAC/C,UAAI,UAAU,KAAK,iBAAiB,KAAK,cAAc,KAAK;AAC5D,aAAO,CAAC,CAAC,WAAW,QAAQ,IAAI,EAAE;AAAA,IACtC;AACA,IAAAA,eAAc,UAAU,aAAa,SAAU,OAAO,IAAI;AACtD,UAAI,UAAU,KAAK,iBAAiB,KAAK,cAAc,KAAK;AAC5D,UAAI,SAAS;AACT,gBAAQ,WAAW,EAAE;AAAA,MACzB;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,QAAQ,SAAU,OAAO;AAC7C,UAAI,KAAK,eAAe;AACpB,eAAO,KAAK,cAAc,KAAK;AAAA,MACnC;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,WAAW,WAAY;AAC3C,WAAK,gBAAgB;AAAA,IACzB;AACA,IAAAA,eAAc,UAAU,OAAO,SAAU,OAAO;AAC5C,UAAI;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MAC/B;AACA,UAAI,UAAU,KAAK,iBAAiB,KAAK,cAAc,KAAK;AAC5D,UAAI,SAAS;AACT,SAAC,KAAK,SAAS,KAAK,MAAM,IAAI,SAAS,IAAI,CAAC;AAAA,MAChD;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAKF,SAAS,YAAY,MAAM;AACvB,MAAI,MAAM,IAAI,MAAM,KAAK,MAAM;AAC/B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ;AAC7B,QAAI,CAAC,IAAI,KAAK,CAAC;AACnB,SAAO;AACX;AAKA,SAAS,mBAAmB,QAAQ,UAAU;AAC1C,MAAI,OAAO,QAAQ,QAAQ,QAAQ;AACnC,OAAK,gBAAgB;AACzB;AAKA,SAAS,iBAAiB,KAAK,WAAW;AACtC,SAAO,IAAI,QAAQ,SAAS,MAAM;AACtC;AAKA,IAAI,aAAa,SAAU,IAAI,SAAS;AAEpC,MAAI,CAAC,QAAQ;AACT;AAEJ,MAAI,WAAW,OAAO,CAAC,WAAW,IAAI,eAAe,EAAE,GAAG;AACtD,cAAU,0BAA0B,OAAO;AAAA,EAC/C;AAEA,MAAI,WAAW;AACX,eAAW,IAAI,EAAE,IAAI;AAC7B;AACA,WAAW,MAAM,CAAC;AAKlB,SAAS,UAAU,KAAK;AACpB,UAAQ,KAAK,IAAI,MAAM,uBAAuB,GAAG,CAAC;AACtD;AAKA,SAAS,wBAAwB;AAC7B,SAAQ,QAAQ,KACX,OAAO,YAAY,eAAe,QAAQ,OAAO,QAAQ,IAAI,sBAAsB;AAC5F;AAKA,SAAS,UAAU;AACf,SAAO;AACX;AAKA,SAAS,UAAU,OAAO,IAAI,UAAU,WAAW;AAC/C,MAAI,QAAQ,GAAG;AACX,QAAI,CAAC,GAAG,KAAK,GAAG;AAEZ,YAAM,OAAO,cAAc,WAAW,kBAAkB,QAAQ,SAAS,EAAE,KAAK,MAAM,IAAI,WAAW,QAAQ,UAAU;AAAA,IAC3H;AAAA,EACJ;AACJ;AAKA,SAAS,iBAAiB,OAAO,WAAW;AACxC,YAAU,OAAO,SAAU,IAAI;AAAE,WAAO,OAAO,OAAO;AAAA,EAAY,GAAG,YAAY,SAAS;AAC9F;AAKA,SAAS,eAAe,OAAO,WAAW,KAAK,KAAK;AAChD,YAAU,OAAO,SAAU,GAAG;AAAE,WAAO,OAAO,MAAM;AAAA,EAAU,GAAG,UAAU,SAAS;AACpF,MAAI,QAAQ,QAAW;AACnB,cAAU,OAAO,SAAU,GAAG;AAAE,aAAO,KAAK;AAAA,IAAK,GAAG,yBAAyB,KAAK,SAAS;AAAA,EAC/F;AACA,MAAI,QAAQ,QAAW;AACnB,cAAU,OAAO,SAAU,GAAG;AAAE,aAAO,KAAK;AAAA,IAAK,GAAG,wBAAwB,KAAK,SAAS;AAAA,EAC9F;AACJ;AAKA,SAAS,eAAe,OAAO,WAAW,YAAY;AAClD,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAM;AAChD,YAAU,OAAO,SAAU,GAAG;AAAE,WAAO,OAAO,MAAM;AAAA,EAAU,GAAG,UAAU,SAAS;AACpF,MAAI,CAAC,YAAY;AACb,cAAU,OAAO,SAAU,GAAG;AAAE,aAAO,MAAM;AAAA,IAAI,GAAG,oBAAoB,SAAS;AAAA,EACrF;AACJ;AAKA,SAAS,yBAAyB,IAAI;AAClC,MAAI,OAAO,mBAAmB,YAAY;AACtC,mBAAe,EAAE;AAAA,EACrB,WACS,OAAO,iBAAiB,YAAY;AACzC,iBAAa,EAAE;AAAA,EACnB,OACK;AACD,eAAW,IAAI,CAAC;AAAA,EACpB;AACJ;AAOA,SAAS,KAAK,WAAW;AACrB,SAAO,kBAAkB,UAAU,MAAM,SAAS;AACtD;AAQA,SAAS,eAAe,KAAK;AACzB,SAAO;AACX;AAuBA,SAAS,oBAAoB,GAAG;AAC5B,SAAO,WAAY;AACf,QAAI;AACJ,QAAI,OAAO,CAAC;AACZ,SAAK,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AACtC,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AAAG,iBAAO,CAAC,GAAa,EAAE,MAAM,QAAQ,SAAS,IAAI,CAAC,CAAC;AAAA,QAC5D,KAAK;AAAG,iBAAO,CAAC,GAAe,GAAG,KAAK,CAAE;AAAA,MAC7C;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAsBA,SAAS,YAAY,GAAG;AACpB,SAAO,YAAY,MAAM,SAAU,IAAI;AACnC,YAAQ,GAAG,OAAO;AAAA,MACd,KAAK;AAAG,eAAO,CAAC,GAAa,CAAC;AAAA,MAC9B,KAAK;AAAG,eAAO,CAAC,GAAe,GAAG,KAAK,CAAE;AAAA,IAC7C;AAAA,EACJ,CAAC;AACL;AAKA,SAAS,kBAAkB,MAAM,WAAW;AACxC,MAAI,UAAU,SAAS,cAAc;AAEjC,QAAI,QAAQ,gBAAgB;AAC5B,QAAI,gBAAgB,wBAAwB;AAC5C,QAAI,CAAC,eAAe;AAChB,YAAM,OAAO,8CAA8C;AAAA,IAC/D;AACA,QAAI,sBAAsB,uBAAuB,aAAa;AAC9D,QAAI,CAAC,qBAAqB;AACtB,YAAM,OAAO,qDAAqD;AAAA,IACtE;AACA,QAAI,cAAc;AAAA,MACd;AAAA,MACA,IAAI;AAAA,MACJ,MAAM,cAAc;AAAA,MACpB,SAAS,cAAc;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,cAAc,SAAS,cAAc,cAAc,CAAC,cAAc,EAAE,CAAC;AAAA,MACrE,QAAQ,cAAc;AAAA,MACtB,aAAa;AAAA,MACb,mBAAmB;AAAA,IACvB;AACA,QAAI,OAAO;AACX,aAAS,KAAK,IAAI,MAAM,KAAK;AACzB,SAAG,kBAAkB,QAAQ;AAC7B,2BAAqB,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,MAAY,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;AAAA,IAC7F;AACA,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,UAAI;AACJ,UAAI,OAAO,SAAS,kBAAkB;AAClC,cAAM,UAAU,MAAM,MAAM,SAAS;AACrC,oBAAY,MAAS;AAAA,MACzB;AACA,WAAK,kBAAkB,QAAQ;AAC/B,2BAAqB,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,MAAM,cAAc,MAAM,YAAY,IAAI,EAAE,CAAC,GAAG,IAAI;AAC/G,eAAS,YAAY,KAAK;AACtB,YAAI;AACJ,YAAI;AAEA,eAAK,SAAU,GAAG;AAAE,kBAAM,IAAI,KAAK,CAAC;AAAA,UAAG,GAAG,eAAe,GAAG;AAAA,QAChE,SACO,GAAG;AAEN,mCAAyB,WAAY;AACjC,iBAAK,SAAU,GAAG;AAAE,qBAAO,CAAC;AAAA,YAAG,GAAG,cAAc,CAAC;AAAA,UACrD,CAAC;AACD;AAAA,QACJ;AACA,aAAK,GAAG;AACR;AAAA,MACJ;AACA,eAAS,WAAW,KAAK;AACrB,YAAI;AACJ,YAAI;AAEA,eAAK,SAAU,GAAG;AAAE,kBAAM,IAAI,MAAM,CAAC;AAAA,UAAG,GAAG,qBAAqB,GAAG;AAAA,QACvE,SACO,GAAG;AAEN,mCAAyB,WAAY;AACjC,iBAAK,SAAU,GAAG;AAAE,qBAAO,CAAC;AAAA,YAAG,GAAG,cAAc,CAAC;AAAA,UACrD,CAAC;AACD;AAAA,QACJ;AACA,aAAK,GAAG;AAAA,MACZ;AACA,eAAS,KAAK,KAAK;AACf,YAAI,IAAI,MAAM;AAEV,mCAAyB,WAAY;AACjC,iBAAK,SAAU,GAAG;AAAE,sBAAQ,CAAC;AAAA,YAAG,GAAG,eAAe,IAAI,KAAK;AAAA,UAC/D,CAAC;AACD;AAAA,QACJ;AAEA,YAAI,CAAC,IAAI,SAAS,OAAO,IAAI,MAAM,SAAS,YAAY;AAEpD,gBAAM,OAAO,mDAAmD,GAAG;AAAA,QACvE;AACA,eAAO,IAAI,MAAM,KAAK,aAAa,UAAU;AAAA,MACjD;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAMA,SAAS,WAAW,OAAO;AACvB,MAAI,EAAE,cAAc;AAChB,UAAM,OAAO,qDAAqD;AACtE,SAAO,CAAC,WAAW,KAAK,GAAG,YAAY,KAAK,CAAC;AACjD;AAKA,SAAS,WAAW,OAAO;AAGvB,UAAQ,MAAM,IAAI;AAAA,IACd,KAAK;AACD,aAAO,EAAE,IAAI,OAAO,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM;AAAA,IAC7D,KAAK;AACD,aAAO,EAAE,IAAI,UAAU,MAAM,MAAM,KAAK;AAAA,IAC5C,KAAK;AACD,aAAO,EAAE,IAAI,WAAW,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM;AAAA,EACrE;AACJ;AACA,SAAS,YAAY,OAAO;AACxB,UAAQ,MAAM,IAAI;AAAA,IACd,KAAK;AACD,aAAO;AAAA,QACH,IAAI;AAAA,QACJ,MAAM,MAAM;AAAA,MAChB;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,IAAI;AAAA,QACJ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,MACjB;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,IAAI;AAAA,QACJ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,MACjB;AAAA,EACR;AACJ;AAIA,SAAS,SAAS,GAAG;AACjB,SAAO,OAAO,MAAM;AACxB;AAMA,SAAS,eAAe,MAAM;AAC1B,MAAI,SAAS,IAAI,MAAM,MAAM;AACzB,WAAO,KAAK;AAAA,EAChB;AACA,MAAI,KAAK,QAAQ,GAAG,MAAM,MAAM,KAAK,QAAQ,GAAG,MAAM;AAClD,WAAO;AACX,SAAO,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI;AACvD;AAIA,SAAS,iBAAiB,MAAM;AAC5B,SAAO,KAAK,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AACtD;AAOA,SAAS,aAAa,MAAM;AAExB,MAAI,KAAK,WAAW;AAChB,WAAO;AACX,MAAI,aAAa,SAAU,GAAG;AAAE,WAAO,EAAE,IAAI,cAAc,EAAE,KAAK,GAAG;AAAA,EAAG;AACxE,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM;AAErC,WAAO,WAAW,IAAI;AAAA,EAC1B,OACK;AAED,WAAO,MAAM,WAAW,IAAI;AAAA,EAChC;AACJ;AAOA,SAAS,cAAc,MAAM;AAEzB,MAAI,QAAQ,KAAK,MAAM,GAAG,EAAE,IAAI,gBAAgB;AAChD,MAAI,QAAQ,SAAS,MACjB,SAAS,OACT,SAAS,QACT,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,IAAI,KAC3B,iBAAiB,MAAM,KAAK;AAChC,MAAI,CAAC,OAAO;AACR,UAAM,OAAO,oEAAoE,OAAO,GAAG;AAAA,EAC/F;AAOA,MAAI,MAAM,CAAC,MAAM,IAAI;AACjB,UAAM,MAAM;AAAA,EAChB;AACA,SAAO;AACX;AAEA,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,UAAU,aAAa,MAAM;AACpD,UAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ,SAAS,IAAI,KAAK;AACxD,YAAM,WAAW;AACjB,YAAM,cAAc;AACpB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,mBAAkB,WAAW,SAAS;AAAA,MACxD,KAAK,WAAY;AACb,eAAO,KAAK,SAAS,QAAQ,UAAU;AAAA,MAC3C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,UAAU,WAAW,WAAY;AAC/C,aAAO,uBAAuB,KAAK,SAAS,SAAS,IAAI;AAAA,IAC7D;AACA,IAAAA,mBAAkB,UAAU,qBAAqB,SAAU,IAAI;AAC3D,UAAI,KAAK,YAAY,cAAc;AAC/B,eAAO,KAAK,YAAY,aAAa,KAAK,MAAM,EAAE;AAAA,MACtD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,sBAAsB,SAAU,IAAI;AAC5D,UAAI,KAAK,YAAY,eAAe;AAChC,eAAO,KAAK,YAAY,cAAc,KAAK,MAAM,EAAE;AAAA,MACvD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,WAAW,SAAU,MAAM;AACnD,UAAI,QAAQ;AAEZ,2BAAqB,KAAK,MAAM,MAAM,oBAAoB,QAAQ;AAClE,UAAI,iBAAiB,KAAK;AAC1B,WAAK,cAAc,WAAY;AAC3B,eAAO,MAAM,oBAAoB,eAAe,KAAK,IAAI,CAAC;AAAA,MAC9D;AAAA,IACJ;AACA,IAAAA,mBAAkB,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAC5F,UAAI,wBAAwB,gBAAgB,YAAY,IAClD,eACA,KAAK,mBAAmB,YAAY;AAC1C,UAAI,OAAO,KAAK,SAAS,YAAY,QAAQ,SAAS,aAAa,qBAAqB;AACxF,WAAK,SAAS,IAAI;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AAClF,UAAI,OAAO,KAAK,SAAS,UAAU,SAAS,gBAAgB,QAAQ,IAAI,WAAW,KAAK,mBAAmB,QAAQ,GAAG,QAAQ,OAAO;AACrI,UAAI,SAAS,SAAS;AAClB,aAAK,SAAS,IAAI;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,cAAc,SAAU,MAAM,kBAAkB;AACxE,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB;AAAA,MAAM;AAC5D,UAAI,KAAK,KAAK,SAAS,YAAY,IAAI;AACvC,aAAO,mBAAmB,KAAK,oBAAoB,EAAE,IAAI;AAAA,IAC7D;AACA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACpE,UAAI,cAAc,KAAK,mBAAmB,KAAK;AAC/C,aAAO,KAAK,SAAS,SAAS,aAAa,OAAO;AAAA,IACtD;AACA,IAAAA,mBAAkB,UAAU,cAAc,WAAY;AAClD,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,mBAAkB,UAAU,KAAK,SAAU,OAAO;AAC9C,UAAI,QAAQ,OAAO,KAAK,IAClB,KAAK,WACL,gBAAgB,KAAK,IACjB,YAAY,OAAO,KAAK,IACxB,KAAK,mBAAmB,KAAK;AACvC,aAAO,KAAK,SAAS,SAAS,OAAO,CAAC,EAAE,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,EAAE,WAAW;AAAA,IACzF;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AACV,SAAS,qBAAqB,UAAU,uBAAuB;AAC3D,MAAI,KAAK;AACT,MAAI,UAAU,CAAC;AACf,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,YAAQ,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAClC;AACA,MAAI;AACA,aAAS,YAAY,SAAS,OAAO,GAAG,cAAc,UAAU,KAAK,GAAG,CAAC,YAAY,MAAM,cAAc,UAAU,KAAK,GAAG;AACvH,UAAI,SAAS,YAAY;AACzB,eAAS,MAAM,IAAI,sBAAsB,MAAM,EAAE,KAAK,qBAAqB;AAAA,IAC/E;AAAA,EACJ,SACO,OAAO;AAAE,UAAM,EAAE,OAAO,MAAM;AAAA,EAAG,UACxC;AACI,QAAI;AACA,UAAI,eAAe,CAAC,YAAY,SAAS,KAAK,UAAU,QAAS,IAAG,KAAK,SAAS;AAAA,IACtF,UACA;AAAU,UAAI,IAAK,OAAM,IAAI;AAAA,IAAO;AAAA,EACxC;AACJ;AAgCA,SAAS,kBAAkB,MAAM,YAAY,MAAM;AAC/C,eAAa,MAAM,CAAC;AACpB,MAAI,QAAQ,GAAG;AACX,QAAI,WAAW,iBAAiB,OAAO,WAAW,kBAAkB,YAAY;AAE5E,YAAM,KAAK,0CAA0C;AAAA,IACzD;AACA,QAAI,WAAW,gBAAgB,OAAO,WAAW,iBAAiB,YAAY;AAE1E,YAAM,KAAK,yCAAyC;AAAA,IACxD;AAAA,EACJ;AACA,SAAO,IAAI,kBAAkB,MAAM,YAAY,IAAI;AACvD;AAEA,IAAI,uBAAuB;AAC3B,SAAS,qBAAqB,MAAM,YAAY;AAC5C,MAAI,KAAK;AACT,MAAI,WAAW,KAAK,YAAY;AAChC,MAAI,aAAa,wBAAwB;AACrC,WAAO;AAAA,EACX;AACA,MAAI,UAAU;AACV,QAAI,gBAAgB,QAAQ,QAAQ;AACpC,QAAI;AACA,eAAS,kBAAkB,SAAS,aAAa,GAAG,oBAAoB,gBAAgB,KAAK,GAAG,CAAC,kBAAkB,MAAM,oBAAoB,gBAAgB,KAAK,GAAG;AACjK,YAAI,UAAU,kBAAkB;AAChC,YAAI,CAAC,qBAAqB,SAAS,UAAU;AACzC,iBAAO;AAAA,MACf;AAAA,IACJ,SACO,OAAO;AAAE,YAAM,EAAE,OAAO,MAAM;AAAA,IAAG,UACxC;AACI,UAAI;AACA,YAAI,qBAAqB,CAAC,kBAAkB,SAAS,KAAK,gBAAgB,QAAS,IAAG,KAAK,eAAe;AAAA,MAC9G,UACA;AAAU,YAAI,IAAK,OAAM,IAAI;AAAA,MAAO;AAAA,IACxC;AAAA,EACJ;AACA,MAAI,gBAAgB,WAAW;AAC3B,eAAW,KAAK,IAAI;AAAA,EACxB;AACA,SAAO;AACX;AAKA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAC1B,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtD,EAAAA,mBAAkBA,mBAAkB,KAAK,IAAI,CAAC,IAAI;AAClD,EAAAA,mBAAkBA,mBAAkB,IAAI,IAAI,CAAC,IAAI;AACrD,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAI;AAAA;AAAA,EAAwB,SAAU,QAAQ;AAC1C,cAAUC,SAAQ,MAAM;AACxB,aAASA,QAAO,aAAa;AACzB,aAAO,OAAO,KAAK,MAAM,aAAa,WAAW,IAAI,QAAQ,KAAK;AAAA,IACtE;AACA,IAAAA,QAAO,UAAU,MAAM,SAAU,KAAK;AAElC,aAAO,OAAO,UAAU,IAAI,KAAK,MAAM,KAAK,GAAG;AAAA,IACnD;AACA,IAAAA,QAAO,UAAU,MAAM,SAAU,KAAK;AAClC,aAAO,OAAO,UAAU,IAAI,KAAK,MAAM,KAAK,GAAG;AAAA,IACnD;AACA,IAAAA,QAAO,UAAU,SAAS,SAAU,KAAK;AACrC,aAAO,OAAO,UAAU,OAAO,KAAK,MAAM,KAAK,GAAG;AAAA,IACtD;AACA,IAAAA,QAAO,UAAU,MAAM,SAAU,KAAK,OAAO;AACzC,aAAO,OAAO,UAAU,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,IAC1D;AACA,IAAAA,QAAO,UAAU,MAAM,SAAU,OAAO;AACpC,UAAI,CAAC;AACD,cAAM,OAAO,4CAA4C;AAC7D,UAAI,gBAAgB,KAAK,GAAG;AACxB,YAAI,OAAO,iBAAiB,KAAK;AACjC,YAAI,QAAQ,GAAG;AACX,cAAI,CAAC,KAAK,qBAAqB;AAC3B,kBAAM,OAAO,oBAAoB;AAAA,UACrC;AAAA,QACJ;AACA,YAAI,KAAK,eAAe,MAAM;AAC1B,gBAAM,OAAO,oBAAoB;AAAA,QACrC;AACA,aAAK,IAAI,KAAK,YAAY,KAAK;AAC/B,eAAO;AAAA,MACX,WACS,CAAC,UAAU,KAAK,GAAG;AACxB,cAAM,OAAO,kDAAkD;AAAA,MACnE,OACK;AACD,YAAI,UAAU,iBAAiB,IAAI;AACnC,YAAI,UAAU,QAAQ;AACtB,YAAI,QAAQ,mBAAmB,kBAAkB,KAAK;AAClD,gBAAM,OAAO,oBAAoB;AAAA,QACrC;AACA,YAAI,SAAS,QAAQ;AACrB,YAAI,KAAK,MAAM,MAAM;AACrB,YAAI,CAAC,kBAAkB,EAAE,GAAG;AAGxB,cAAI,UAAU,KAAK,IAAI,QAAQ,aAAa,EAAE,OAAO,OAAO,QAAQ,WAAW,CAAC;AAChF,iBAAO,KAAK,IAAI,YAAY,OAAO,CAAC;AAAA,QACxC;AACA,YAAI,MAAM,oBAAoB,EAAE;AAChC,aAAK,IAAI,KAAK,KAAK;AACnB,eAAO,KAAK,IAAI,GAAG;AAAA,MACvB;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,aAAa;AAAA;AAKf,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC3C,cAAUC,UAAS,MAAM;AACzB,aAASA,SAAQ,MAAM,UAAU,kBAAkB;AAC/C,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB,CAAC;AAAA,MAAG;AAC1D,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,WAAW;AACjB,YAAM,iBAAiB,kBAAkB;AACzC,YAAM,yBAAyB;AAC/B,YAAM,QAAQ,UAAU;AACxB,YAAM,mBAAmB,CAAC;AAC1B,YAAM,yBAAyB;AAC/B,YAAM,mBAAmB;AACzB,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,QAAQ,SAAU,OAAO;AACvC,UAAI,mBAAmB,KAAK,iBAAiB,SAAS,IAAI,KAAK,iBAAiB,OAAO,KAAK,IAAI,CAAC,KAAK;AACtG,aAAO,IAAIA,SAAQ,KAAK,MAAM,KAAK,UAAU,gBAAgB;AAAA,IACjE;AACA,IAAAA,SAAQ,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAClF,WAAK,yBAAyB;AAC9B,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC5E;AACA,IAAAA,SAAQ,UAAU,2BAA2B,WAAY;AACrD,UAAI,KAAK,mBAAmB,kBAAkB,SAAS;AACnD;AAAA,MACJ;AACA,UAAI,aAAa,CAAC;AAClB,UAAI,qBAAqB,KAAK,UAAU,UAAU,GAAG;AACjD,YAAI,wBAAwB;AAC5B,mBAAW,QAAQ,SAAU,MAAM;AAC/B,cAAI,KAAK,qBAAqB;AAC1B,gBAAI,yBAAyB,0BAA0B,KAAK,qBAAqB;AAC7E,oBAAM,OAAO,mFAAmF,wBAAwB,2BAA2B,KAAK,OAAO,2BAA2B,KAAK,sBAAsB,iBAAiB;AAAA,YAC1O;AACA,oCAAwB,KAAK;AAAA,UACjC;AAAA,QACJ,CAAC;AACD,YAAI,uBAAuB;AACvB,eAAK,iBAAiB,kBAAkB;AACxC,eAAK,yBAAyB;AAAA,QAClC,OACK;AACD,eAAK,iBAAiB,kBAAkB;AAAA,QAC5C;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,SAAQ,UAAU,uBAAuB,SAAU,SAAS,iBAAiB;AACzE,UAAI,oBAAoB,QAAQ;AAAE,0BAAkB,CAAC;AAAA,MAAG;AACxD,UAAI,UAAU,QAAQ,KAAK;AAC3B,UAAI,SAAS,CAAC;AACd,aAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,MAAM;AACjD,eAAO,IAAI,IAAI,QAAQ,YAAY,SAAS,MAAM,QAAW,gBAAgB,IAAI,CAAC;AAAA,MACtF,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,oBAAoB,SAAU,YAAY;AACxD,aAAO,IAAI,OAAO,UAAU;AAAA,IAChC;AACA,IAAAA,SAAQ,UAAU,sBAAsB,SAAU,MAAM,UAAU;AAC9D,qBAAgB,UAAU,KAAK,KAAK;AACpC,UAAI,OAAO,KAAK;AAChB,WAAK,iBAAiB,QAAQ,SAAU,aAAa;AACjD,YAAI,QAAQ,YAAY,QAAQ;AAChC,eAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,MAAM;AACvC,cAAI,OAAO,MAAM,IAAI;AACrB,cAAI,gBAAgB,oBAAoB,UAAU,MAAM,IAAI;AAC5D,WAAC,CAAC,QAAQ,IAAI,qBAAqB,uBAAuB,UAAU,MAAM,aAAa;AAAA,QAC3F,CAAC;AAAA,MACL,CAAC;AACD,gBAAU,UAAU,KAAK,UAAU;AACnC,cAAQ,UAAU,KAAK,SAAS;AAAA,IACpC;AACA,IAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,aAAO,iBAAiB,KAAK,SAAS,SAAS,IAAI;AAAA,IACvD;AACA,IAAAA,SAAQ,UAAU,cAAc,SAAU,MAAM;AAE5C,aAAO,OAAO,KAAK,WAAW;AAAA,IAClC;AACA,IAAAA,SAAQ,UAAU,eAAe,SAAU,MAAM,KAAK;AAClD,UAAI,YAAY,KAAK,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,CAAC;AACD,cAAM,OAAO,iBAAiB,GAAG;AACrC,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,aAAa,SAAU,QAAQ;AAC7C,UAAI,OAAO,iBAAiB,OAAO,MAAM;AACzC,UAAI,MAAM,OAAO;AACjB,WAAK,eAAe,EAAE,SAAS,IAAI,CAAC;AACpC,UAAI,UAAU,KAAK;AACnB,UAAI,UAAU,QAAQ;AACtB,cAAQ,OAAO,MAAM;AAAA,QACjB,KAAK;AACD;AACI,gBAAI,WAAW,OAAO;AACtB,gBAAI,WAAW,OAAO,OAAO,IAAI,GAAG;AACpC,gBAAI,aAAa;AACb,qBAAO;AACX,8BAAkB,SAAS,QAAQ;AACnC,mBAAO,WAAW,QAAQ,UAAU,KAAK,aAAa,GAAG,GAAG,OAAO,UAAU,MAAM,GAAG;AACtF,oBAAQ,kBAAkB,KAAK,OAAO,QAAQ;AAAA,UAClD;AACA;AAAA,QACJ,KAAK;AACD;AACI,8BAAkB,SAAS,OAAO,QAAQ;AAC1C,mBAAO,WAAW,QAAQ,YAAY,MAAM,KAAK,QAAW,OAAO,QAAQ;AAC3E,oBAAQ,kBAAkB,KAAK,OAAO,QAAQ;AAAA,UAClD;AACA;AAAA,MACR;AACA,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,oBAAoB,SAAU,UAAU,MAAM;AAC5D,UAAI,KAAK,mBAAmB,kBAAkB,OAAO,gBAAgB,YAAY;AAC7E,YAAIvB,cAAa,KAAK;AACtB,YAAIA,gBAAe;AACf,gBAAM,OAAO,+HAA+HA,cAAa,uBAAuB,WAAW,GAAG;AAAA,MACtM;AAAA,IACJ;AACA,IAAAuB,SAAQ,UAAU,cAAc,SAAU,MAAM;AAC5C,UAAI,MAAM,CAAC;AACX,WAAK,YAAY,EAAE,QAAQ,SAAU,WAAW;AAC5C,YAAI,UAAU,OAAO,IAAI,UAAU;AAAA,MACvC,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,yBAAyB,SAAU,YAAY;AAC7D,UAAI,YAAY,CAAC;AACjB,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC3C,kBAAU,GAAG,IAAI,WAAW,GAAG,EAAE,YAAY;AAAA,MACjD,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,YAAY,SAAU,QAAQ;AAC5C,UAAI,OAAO,iBAAiB,OAAO,MAAM;AACzC,cAAQ,OAAO,MAAM;AAAA,QACjB,KAAK;AACD,iBAAO,KAAK,KAAK,UAAU;AAAA,YACvB,IAAI;AAAA,YACJ,MAAM,eAAe,OAAO,IAAI;AAAA,YAChC,OAAO,OAAO,SAAS;AAAA,YACvB,UAAU,OAAO,WAAW,OAAO,SAAS,WAAW;AAAA,UAC3D,GAAG,IAAI;AAAA,QACX,KAAK;AACD,iBAAO,KAAK,KAAK,UAAU;AAAA,YACvB,IAAI;AAAA,YACJ,MAAM,eAAe,OAAO,IAAI;AAAA,YAChC,OAAO,OAAO,SAAS;AAAA,YACvB,UAAU;AAAA,UACd,GAAG,IAAI;AAAA,QACX,KAAK;AAED,cAAI,cAAc,OAAO,SAAS;AAClC,iBAAO,SAAS,IAAI;AAEpB,iBAAO,KAAK,KAAK,UAAU;AAAA,YACvB,IAAI;AAAA,YACJ,MAAM,eAAe,OAAO,IAAI;AAAA,YAChC,UAAU;AAAA,UACd,GAAG,IAAI;AAAA,MACf;AAAA,IACJ;AACA,IAAAA,SAAQ,UAAU,oBAAoB,SAAU,MAAM,SAAS,OAAO;AAClE,UAAI,SAAS,KAAK;AAClB,cAAQ,MAAM,IAAI;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,IAAI,SAAS,MAAM,KAAK;AAC/B;AAAA,QACJ,KAAK;AACD,iBAAO,OAAO,OAAO;AACrB;AAAA,MACR;AAAA,IACJ;AACA,IAAAA,SAAQ,UAAU,gBAAgB,SAAU,MAAM,UAAU;AACxD,wBAAkB,MAAM,QAAQ;AAChC,UAAI,SAAS,KAAK;AAClB,UAAI,cAAc,CAAC;AACnB,YAAM,KAAK,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAUC,MAAK;AAC7C,oBAAYA,IAAG,IAAI;AAAA,MACvB,CAAC;AACD,UAAI,UAAU;AAEV,iBAAS,OAAO,UAAU;AACtB,iBAAO,IAAI,KAAK,SAAS,GAAG,CAAC;AAC7B,sBAAY,KAAK,GAAG,IAAI;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,KAAK,WAAW,EAAE,QAAQ,SAAUA,MAAK;AAC5C,YAAI,YAAYA,IAAG,MAAM;AACrB,iBAAO,OAAOA,IAAG;AAAA,MACzB,CAAC;AAAA,IACL;AACA,IAAAD,SAAQ,UAAU,eAAe,WAAY;AACzC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,SAAQ,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC1D,UAAI,QAAQ;AACZ,UAAI,CAAC,cAAc,KAAK,GAAG;AACvB,eAAO,iBAAiB,SAAS,OAAO,6BAA6B;AAAA,MACzE;AACA,aAAO,kBAAkB,OAAO,KAAK,KAAK,EAAE,IAAI,SAAU,MAAM;AAC5D,eAAO,MAAM,SAAS,SAAS,MAAM,IAAI,GAAG,kBAAkB,SAAS,MAAM,MAAM,QAAQ,CAAC;AAAA,MAChG,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,SAAQ,UAAU,qBAAqB,WAAY;AAC/C,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,cAAc,SAAU,MAAM,SAAS;AACrD,WAAK,YAAY,OAAO,OAAO;AAAA,IACnC;AACA,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,SAAQ,WAAW,iBAAiB,IAAI;AAC3C,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AA4Bb,SAAS,IAAI,SAAS;AAClB,SAAO,IAAI,QAAQ,iBAAiB,QAAQ,OAAO,KAAK,OAAO;AACnE;AAOA,SAAS,UAAU,MAAM;AACrB,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,OAAO;AAC1D;AAMA,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUE,YAAW,MAAM;AAC3B,aAASA,WAAU,MAAM,UAAU,kBAAkB;AACjD,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB,CAAC;AAAA,MAAG;AAC1D,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,WAAW;AACjB,YAAM,QAAQ,UAAU;AACxB,YAAM,mBAAmB,CAAC;AAC1B,YAAM,mBAAmB;AACzB,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,QAAQ,SAAU,OAAO;AACzC,UAAI,mBAAmB,KAAK,iBAAiB,SAAS,IAAI,KAAK,iBAAiB,OAAO,KAAK,IAAI,CAAC,KAAK;AACtG,aAAO,IAAIA,WAAU,KAAK,MAAM,KAAK,UAAU,gBAAgB;AAAA,IACnE;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AACpF,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC5E;AACA,IAAAA,WAAU,UAAU,uBAAuB,SAAU,SAAS,UAAU;AACpE,UAAI,aAAa,QAAQ;AAAE,mBAAW,CAAC;AAAA,MAAG;AAC1C,UAAI,UAAU,QAAQ,KAAK;AAC3B,UAAI,SAAS,CAAC;AACd,eAAS,QAAQ,SAAU,MAAM,OAAO;AACpC,YAAI,UAAU,KAAK;AACnB,eAAO,OAAO,IAAI,QAAQ,YAAY,SAAS,SAAS,QAAW,IAAI;AAAA,MAC3E,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,oBAAoB,SAAU,YAAY;AAC1D,aAAO,WAAW,MAAM,yBAAyB,UAAU,GAAG,WAAW;AAAA,IAC7E;AACA,IAAAA,WAAU,UAAU,sBAAsB,SAAU,MAAM,UAAU;AAChE,wBAAmB,QAAQ,EAAE,WAAW,KAAK;AAC7C,UAAI,OAAO,KAAK;AAChB,WAAK,iBAAiB,QAAQ,SAAU,aAAa;AACjD,YAAI,QAAQ,YAAY,QAAQ;AAChC,eAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,MAAM;AACvC,cAAI,OAAO,MAAM,IAAI;AACrB,cAAI,gBAAgB,oBAAoB,UAAU,MAAM,IAAI;AAC5D,WAAC,CAAC,QAAQ,IAAI,qBAAqB,uBAAuB,UAAU,MAAM,aAAa;AAAA,QAC3F,CAAC;AAAA,MACL,CAAC;AACD,gBAAU,UAAU,KAAK,UAAU;AACnC,cAAQ,UAAU,KAAK,SAAS;AAAA,IACpC;AACA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,aAAO,KAAK,SAAS,SAAS,IAAI;AAAA,IACtC;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,MAAM;AAC9C,aAAO,KAAK,YAAY,MAAM;AAAA,IAClC;AACA,IAAAA,WAAU,UAAU,eAAe,SAAU,MAAM,KAAK;AACpD,UAAI,QAAQ,OAAO,GAAG;AACtB,UAAI,QAAQ,KAAK,YAAY;AACzB,eAAO,KAAK,YAAY,KAAK;AACjC,YAAM,OAAO,kBAAkB,GAAG;AAAA,IACtC;AACA,IAAAA,WAAU,UAAU,aAAa,SAAU,QAAQ;AAC/C,UAAI,OAAO,iBAAiB,OAAO,MAAM;AACzC,WAAK,eAAe,EAAE,SAAS,KAAK,OAAO,MAAM,CAAC;AAClD,UAAI,UAAU,KAAK,KAAK;AACxB,UAAI,aAAa,KAAK,YAAY;AAClC,cAAQ,OAAO,MAAM;AAAA,QACjB,KAAK;AACD;AACI,gBAAI,OAAO,aAAa,OAAO,OAAO,OAAO,KAAK;AAC9C,qBAAO;AACX,gBAAI,eAAe,uBAAuB,MAAM,SAAS,CAAC,WAAW,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,QAAQ,GAAG,CAAC,OAAO,KAAK,CAAC;AACtH,gBAAI,CAAC,cAAc;AACf,qBAAO;AAAA,YACX;AACA,mBAAO,WAAW,aAAa,CAAC;AAAA,UACpC;AACA;AAAA,QACJ,KAAK;AACD;AACI,gBAAI,UAAU,OAAO,OAAO,eAAe,OAAO,cAAc,QAAQ,OAAO;AAC/E,gBAAI,aAAa,uBAAuB,MAAM,SAAS,WAAW,MAAM,SAAS,UAAU,YAAY,GAAG,OAAO,MAAM,IAAI,SAAU,GAAGC,IAAG;AAAE,qBAAO,UAAUA;AAAA,YAAG,CAAC,CAAC;AACnK,gBAAI,CAAC,YAAY;AACb,qBAAO;AAAA,YACX;AACA,mBAAO,QAAQ;AAEf,qBAAS,IAAI,UAAU,cAAc,IAAI,WAAW,QAAQ,KAAK;AAC7D,yBAAW,CAAC,EAAE,UAAU,MAAM,MAAM,IAAI,MAAM,SAAS,aAAa;AAAA,YACxE;AAAA,UACJ;AACA;AAAA,MACR;AACA,aAAO;AAAA,IACX;AACA,IAAAD,WAAU,UAAU,cAAc,SAAU,MAAM;AAC9C,aAAO,KAAK,YAAY,EAAE,IAAI,SAAU,WAAW;AAAE,eAAO,UAAU;AAAA,MAAU,CAAC;AAAA,IACrF;AACA,IAAAA,WAAU,UAAU,yBAAyB,SAAU,YAAY;AAC/D,UAAI,YAAY,CAAC;AACjB,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC3C,kBAAU,KAAK,WAAW,GAAG,EAAE,YAAY,CAAC;AAAA,MAChD,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,YAAY,SAAU,QAAQ;AAC9C,UAAI,OAAO,iBAAiB,OAAO,MAAM;AACzC,cAAQ,OAAO,MAAM;AAAA,QACjB,KAAK;AACD,iBAAO,KAAK,KAAK,UAAU;AAAA,YACvB,IAAI;AAAA,YACJ,MAAM,KAAK,OAAO;AAAA,YAClB,OAAO,OAAO,SAAS;AAAA,YACvB,UAAU,OAAO,WAAW,OAAO,SAAS,WAAW;AAAA,UAC3D,GAAG,IAAI;AAAA,QACX,KAAK;AACD,mBAAS,IAAI,OAAO,eAAe,GAAG,KAAK,GAAG;AAC1C,iBAAK,UAAU;AAAA,cACX,IAAI;AAAA,cACJ,MAAM,MAAM,OAAO,QAAQ;AAAA,cAC3B,UAAU,OAAO,QAAQ,CAAC,EAAE;AAAA,YAChC,GAAG,IAAI;AACX,mBAAS,IAAI,GAAG,IAAI,OAAO,YAAY;AACnC,iBAAK,UAAU;AAAA,cACX,IAAI;AAAA,cACJ,MAAM,MAAM,OAAO,QAAQ;AAAA,cAC3B,OAAO,KAAK,aAAa,MAAM,OAAO,QAAQ,EAAE,EAAE;AAAA,cAClD,UAAU;AAAA,YACd,GAAG,IAAI;AACX;AAAA,MACR;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,oBAAoB,SAAU,MAAM,SAAS,OAAO;AACpE,UAAI,SAAS,KAAK;AAClB,UAAI,QAAQ,YAAY,MAAM,OAAO,SAAS,OAAO,OAAO;AAC5D,cAAQ,MAAM,IAAI;AAAA,QACd,KAAK;AACD,iBAAO,KAAK,IAAI,MAAM;AACtB;AAAA,QACJ,KAAK;AACD,iBAAO,OAAO,OAAO,GAAG,MAAM,KAAK;AACnC;AAAA,QACJ,KAAK;AACD,iBAAO,OAAO,OAAO,CAAC;AACtB;AAAA,MACR;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,MAAM,UAAU;AAC1D,wBAAkB,MAAM,QAAQ;AAChC,UAAI,SAAS,KAAK;AAClB,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,WAAU,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC5D,UAAI,QAAQ;AACZ,UAAI,CAAC,QAAQ,KAAK,GAAG;AACjB,eAAO,iBAAiB,SAAS,OAAO,uBAAuB;AAAA,MACnE;AACA,aAAO,kBAAkB,MAAM,IAAI,SAAU,MAAM,OAAO;AACtD,eAAO,MAAM,SAAS,SAAS,MAAM,kBAAkB,SAAS,KAAK,OAAO,MAAM,QAAQ,CAAC;AAAA,MAC/F,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,WAAU,UAAU,qBAAqB,WAAY;AACjD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,MAAM,SAAS;AACvD,WAAK,YAAY,OAAO,OAAO,OAAO,GAAG,CAAC;AAAA,IAC9C;AACA,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,WAAU,WAAW,iBAAiB,IAAI;AAC7C,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAyBb,SAAS,MAAM,SAAS;AACpB,eAAa,SAAS,CAAC;AACvB,SAAO,IAAI,UAAU,QAAQ,OAAO,MAAM,OAAO;AACrD;AACA,SAAS,uBAAuB,QAAQ,WAAW,UAAU,WAAW,UAAU;AAC9E,MAAI,iBAAiB;AACrB,WAAS,IAAI,KAAI,KAAK;AAClB,QAAI,aAAa,KAAK,UAAU,SAAS;AACzC,QAAI,UAAU,SAAS,CAAC;AACxB,QAAI,WAAW,aAAa,UAAU,CAAC,IAAI;AAC3C,QAAI,UAAU,KAAK,SAAS,CAAC;AAG7B,QAAI,OAAO,QAAQ;AACf,iBAAW,SAAS;AACxB,QAAI,CAAC,WAAW,CAAC,YAAY;AAEzB;AAAA,IACJ,WACS,CAAC,YAAY;AAElB,uBAAiB;AACjB,eAAS,OAAO,GAAG,CAAC;AACpB,UAAI,mBAAmB,YAAY;AAG/B,gBAAQ,iCAAiC;AAAA,MAC7C;AACA,cAAQ,IAAI;AACZ;AAAA,IACJ,WACS,CAAC,SAAS;AAGf,UAAI,gBAAgB,QAAQ,KAAK,iBAAiB,QAAQ,EAAE,WAAW,QAAQ;AAE3E,cAAM,OAAO,iIAAiI,OAAO,OAAO,MAAM,UAAU,iCAAiC,iBAAiB,QAAQ,EAAE,OAAO,GAAG;AAAA,MACtP;AACA,uBAAiB;AACjB,UAAI,UAAU,YAAY,WAAW,QAAQ,SAAS,QAAQ;AAC9D,eAAS,OAAO,GAAG,GAAG,OAAO;AAAA,IACjC,WACS,QAAQ,SAAS,QAAQ,GAAG;AAEjC,eAAS,CAAC,IAAI,YAAY,WAAW,QAAQ,SAAS,UAAU,OAAO;AAAA,IAC3E,OACK;AAED,UAAI,WAAW;AAEf,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAI,QAAQ,SAAS,CAAC,GAAG,QAAQ,GAAG;AAChC,qBAAW,SAAS,OAAO,GAAG,CAAC,EAAE,CAAC;AAClC;AAAA,QACJ;AAAA,MACJ;AACA,uBAAiB;AACjB,UAAI,UAAU,YAAY,WAAW,QAAQ,SAAS,UAAU,QAAQ;AACxE,eAAS,OAAO,GAAG,GAAG,OAAO;AAAA,IACjC;AAAA,EACJ;AACA,SAAO,iBAAiB,OAAO;AACnC;AAIA,SAAS,YAAY,WAAW,QAAQ,SAAS,UAAU,SAAS;AAEhE,oBAAkB,WAAW,QAAQ;AACrC,WAAS,aAAa;AAElB,QAAI,gBAAgB,QAAQ,GAAG;AAC3B,UAAI,YAAY,iBAAiB,QAAQ;AACzC,gBAAU,YAAY,YAAY;AAElC,UAAI,UAAU,WAAW,QAAQ,UAAU,WAAW,QAAQ;AAC1D,kBAAU,UAAU,QAAQ,OAAO;AACnC,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,SAAS;AACT,aAAO,UAAU,UAAU,SAAS,UAAU,QAAQ,OAAO;AAAA,IACjE;AAEA,WAAO,UAAU,YAAY,QAAQ,SAAS,QAAW,QAAQ;AAAA,EACrE;AACA,MAAI,UAAU,WAAW;AACzB,MAAI,WAAW,YAAY,SAAS;AAChC,QAAI,mBAAmB,YAAY;AAG/B,cAAQ,iCAAiC;AAAA,IAC7C;AACA,YAAQ,IAAI;AAAA,EAChB;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,SAAS,UAAU;AAEhC,MAAI,CAAC,QAAQ,SAAS;AAClB,WAAO;AAAA,EACX;AAEA,MAAI,gBAAgB,QAAQ,GAAG;AAC3B,QAAI,UAAU,iBAAiB,QAAQ;AACvC,WAAO,QAAQ,WAAW,YAAY;AAAA,EAC1C;AAEA,MAAI,QAAQ,aAAa,UAAU;AAC/B,WAAO;AAAA,EACX;AAEA,SAAQ,mBAAmB,cACvB,QAAQ,eAAe,QACvB,QAAQ,uBACR,cAAc,QAAQ,KACtB,QAAQ,eAAe,oBAAoB,SAAS,QAAQ,mBAAmB,CAAC,KAChF,QAAQ,KAAK,GAAG,QAAQ;AAChC;AAOA,SAAS,YAAY,MAAM;AACvB,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,SAAS;AAC5D;AAEA,IAAI,uBAAuB;AAC3B,IAAI,wBAAwB;AAC5B,SAAS,qBAAqB;AAC1B,SAAO,iBAAiB,IAAI,EAAE,SAAS;AAC3C;AACA,IAAI,uBAAuB;AAAA,EACvB,MAAM;AAAA,EACN,YAAY,CAAC;AAAA,EACb,cAAc;AAClB;AACA,SAAS,mBAAmB,eAAe;AAEvC,SAAO,OAAO,KAAK,aAAa,EAAE,OAAO,SAAU,OAAO,KAAK;AAC3D,QAAI,IAAI,IAAI;AAEZ,QAAI,OAAO;AACP,YAAM,OAAO,WAAW,MAAM,2EAA2E;AAE7G,QAAI,aAAa,OAAO,yBAAyB,OAAO,GAAG;AAC3D,QAAI,SAAS,YAAY;AACrB,YAAM,OAAO,mEAAmE;AAAA,IACpF;AAEA,QAAI,QAAQ,WAAW;AACvB,QAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,YAAM,OAAO,qIAAqI;AAAA,IAEtJ,WACS,YAAY,KAAK,GAAG;AACzB,aAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,KAAK,CAAC,GACnC,GAAG,GAAG,IAAI,SAAS,6BAA6B,KAAK,GAAG,KAAK,GAC7D,GAAG;AAAA,IAEX,WACS,iBAAiB,SAAS;AAC/B,aAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,KAAK,CAAC,GACnC,GAAG,GAAG,IAAI,SAAS,OAAO,CAAC,CAAC,GAC5B,GAAG;AAAA,IACX,WACS,iBAAiB,WAAW;AACjC,aAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,SAAS,OAAO,CAAC,CAAC,GAAG,GAAG;AAAA,IAEhF,WACS,OAAO,KAAK,GAAG;AACpB,aAAO;AAAA,IAEX,WACS,QAAQ,KAAK,OAAO,UAAU,YAAY;AAC/C,YAAM,OAAO,2CAA2C,MAAM,oHAAoH;AAAA,IAEtL,WACS,QAAQ,KAAK,OAAO,UAAU,UAAU;AAC7C,YAAM,OAAO,2CAA2C,MAAM,0FAA0F;AAAA,IAE5J,OACK;AACD,YAAM,OAAO,2CAA2C,MAAM,+CAA+C,QAAQ,QAAQ,OAAO,QAAQ,GAAG;AAAA,IACnJ;AAAA,EACJ,GAAG,aAAa;AACpB;AAKA,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUE,YAAW,MAAM;AAC3B,aAASA,WAAU,MAAM;AACrB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,QAAQ,qBAAqB,IAAI,KAAK;AACzE,YAAM,QAAQ,UAAU;AACxB,YAAM,QAAQ,SAAU,MAAM;AAC1B,eAAO,MAAM,gBAAgB,EAAE,KAAW,CAAC;AAAA,MAC/C;AACA,YAAM,QAAQ,SAAU,YAAY;AAChC,eAAO,MAAM,gBAAgB,EAAE,WAAuB,CAAC;AAAA,MAC3D;AACA,YAAM,qBAAqB,SAAU,cAAc;AAC/C,YAAI,sBAAsB,MAAM;AAChC,YAAI,CAAC;AACD,iBAAO,MAAM,gBAAgB,EAAE,aAA2B,CAAC;AAAA;AAE3D,iBAAO,MAAM,gBAAgB;AAAA,YACzB,cAAc,SAAU,UAAU;AAAE,qBAAO,oBAAoB,aAAa,QAAQ,CAAC;AAAA,YAAG;AAAA,UAC5F,CAAC;AAAA,MACT;AACA,YAAM,sBAAsB,SAAU,eAAe;AACjD,YAAI,uBAAuB,MAAM;AACjC,YAAI,CAAC;AACD,iBAAO,MAAM,gBAAgB,EAAE,cAA6B,CAAC;AAAA;AAE7D,iBAAO,MAAM,gBAAgB;AAAA,YACzB,eAAe,SAAU,UAAU;AAAE,qBAAO,cAAc,qBAAqB,QAAQ,CAAC;AAAA,YAAG;AAAA,UAC/F,CAAC;AAAA,MACT;AACA,aAAO,OAAO,OAAO,sBAAsB,IAAI;AAE/C,YAAM,aAAa,mBAAmB,MAAM,UAAU;AACtD,aAAO,MAAM,UAAU;AACvB,YAAM,gBAAgB,OAAO,KAAK,MAAM,UAAU;AAClD,YAAM,sBAAsB,MAAM,wBAAwB;AAC1D,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,0BAA0B,WAAY;AACtD,UAAI,sBAAsB;AAC1B,WAAK,YAAY,SAAU,UAAU,UAAU;AAC3C,YAAI,SAAS,QAAQ,UAAU,YAAY;AACvC,cAAI;AACA,kBAAM,OAAO,6BAA6B,WAAW,uCAAuC,sBAAsB,6CAA6C;AACnK,gCAAsB;AAAA,QAC1B;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,kBAAkB,SAAU,MAAM;AAClD,aAAO,IAAIA,WAAU;AAAA,QACjB,MAAM,KAAK,QAAQ,KAAK;AAAA,QACxB,YAAY,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,KAAK,UAAU;AAAA,QAC9D,cAAc,KAAK,aAAa,OAAO,KAAK,gBAAgB,CAAC,CAAC;AAAA,QAC9D,cAAc,KAAK,gBAAgB,KAAK;AAAA,QACxC,eAAe,KAAK,iBAAiB,KAAK;AAAA,MAC9C,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,UAAU,SAAU,IAAI;AACxC,UAAI,QAAQ;AACZ,UAAI,oBAAoB,SAAU,MAAM;AACpC,cAAM,mBAAmB,MAAM,GAAG,IAAI,CAAC;AACvC,eAAO;AAAA,MACX;AACA,aAAO,KAAK,gBAAgB,EAAE,cAAc,CAAC,iBAAiB,EAAE,CAAC;AAAA,IACrE;AACA,IAAAA,WAAU,UAAU,qBAAqB,SAAU,MAAM,SAAS;AAE9D,UAAI,CAAC,cAAc,OAAO;AACtB,cAAM,OAAO,qEAAqE;AAEtF,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,MAAM;AAEzC,YAAI,SAAS;AACT,gBAAM,OAAO,2BAA2B,uBAAuB,qEAAqE;AAExI,YAAI,SAAS;AACT,gBAAM,OAAO,2BAA2B,wBAAwB,sEAAsE;AAC1I,YAAI,UAAU,QAAQ,IAAI;AAE1B,YAAI,aAAa,KAAK,IAAI;AAC1B,YAAI,QAAQ,QAAQ,YAAY;AAC5B,cAAI,sBAAsB;AAC1B,oBAAU,WAAY;AAClB,uBAAW,MAAM,MAAM,SAAS;AAChC,gCAAoB,MAAM,MAAM,SAAS;AAAA,UAC7C;AAAA,QACJ;AAGA,YAAI,cAAc,QAAQ;AAC1B,YAAI,cAAc,QAAQ,KAAK,OAAO;AACtC,oBAAY,kBAAkB;AAC9B,YAAI,gBAAgB,oBAAoB,MAAM,MAAM,WAAW;AAC/D,gBAAQ,IAAI,IAAI;AAChB,SAAC,CAAC,QAAQ,IAAI,qBAAqB,uBAAuB,MAAM,MAAM,aAAa;AAAA,MACvF,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,WAAW,SAAU,IAAI;AACzC,UAAI,QAAQ;AACZ,UAAI,OAAO,OAAO,YAAY;AAC1B,cAAM,OAAO,mBAAmB,OAAO,KAAK,8DAA8D;AAAA,MAC9G;AACA,UAAI,mBAAmB,SAAU,MAAM;AACnC,cAAM,yBAAyB,MAAM,GAAG,IAAI,CAAC;AAC7C,eAAO;AAAA,MACX;AACA,aAAO,KAAK,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,EAAE,CAAC;AAAA,IACpE;AACA,IAAAA,WAAU,UAAU,2BAA2B,SAAU,MAAM,OAAO;AAElE,UAAI,CAAC,cAAc,KAAK;AACpB,cAAM,OAAO,0EAA0E;AAC3F,UAAI,MAAM,KAAK;AAAA,IACnB;AACA,IAAAA,WAAU,UAAU,SAAS,SAAU,IAAI;AACvC,UAAI,QAAQ;AACZ,UAAI,cAAc,SAAU,MAAM;AAC9B,YAAI,KAAK,GAAG,IAAI,GAAG,UAAU,GAAG,SAAS,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,OAAO,OAAO,IAAI,CAAC,WAAW,SAAS,OAAO,CAAC;AAC5H,iBAAS,OAAO;AACZ,gBAAM,OAAO,kIAAkI,MAAM,GAAG;AAC5J,YAAI;AACA,gBAAM,yBAAyB,MAAM,KAAK;AAC9C,YAAI;AACA,gBAAM,iBAAiB,MAAM,KAAK;AACtC,YAAI;AACA,gBAAM,mBAAmB,MAAM,OAAO;AAC1C,eAAO;AAAA,MACX;AACA,aAAO,KAAK,gBAAgB,EAAE,cAAc,CAAC,WAAW,EAAE,CAAC;AAAA,IAC/D;AACA,IAAAA,WAAU,UAAU,QAAQ,SAAU,IAAI;AACtC,UAAI,QAAQ;AACZ,UAAI,kBAAkB,SAAU,MAAM;AAClC,cAAM,iBAAiB,MAAM,GAAG,IAAI,CAAC;AACrC,eAAO;AAAA,MACX;AACA,aAAO,KAAK,gBAAgB,EAAE,cAAc,CAAC,eAAe,EAAE,CAAC;AAAA,IACnE;AACA,IAAAA,WAAU,UAAU,mBAAmB,SAAU,MAAM,OAAO;AAE1D,UAAI,CAAC,cAAc,KAAK;AACpB,cAAM,OAAO,iEAAiE;AAClF,aAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AAEtC,YAAI,aAAa,OAAO,yBAAyB,OAAO,GAAG;AAC3D,YAAI,SAAS,YAAY;AACrB,cAAI,eAAe,MAAM,GAAG,GAAG;AAC3B,gBAAI,gBAAgB,kBAAmB,MAAM,GAAG;AAGhD,0BAAc,aAAa,WAAW;AACtC,0BAAc,QAAQ;AACtB,gBAAI,WAAW;AACX,4BAAc,SAAS,OAAO,cAAc,OAAO,WAAW,WAAW,GAAG;AAAA,UACpF,OACK;AACD,qBAAS,MAAM,KAAK,YAAY,IAAI;AAAA,UACxC;AAAA,QACJ,WACS,OAAO,WAAW,UAAU,YAAY;AAC7C,WAAC,CAAC,QAAQ,IAAI,qBAAqB,uBAAuB,MAAM,KAAK,WAAW,KAAK;AAAA,QACzF,OACK;AACD,gBAAM,OAAO,oEAAoE;AAAA,QACrF;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AACpF,UAAI,QAAQ,gBAAgB,YAAY,IAClC,eACA,KAAK,0BAA0B,YAAY;AACjD,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,KAAK;AAAA,IAGrE;AACA,IAAAA,WAAU,UAAU,uBAAuB,SAAU,SAAS,iBAAiB;AAC3E,UAAI,oBAAoB,QAAQ;AAAE,0BAAkB,CAAC;AAAA,MAAG;AACxD,UAAI,OAAO,QAAQ;AACnB,UAAI,SAAS,CAAC;AACd,WAAK,YAAY,SAAU,MAAM,WAAW;AACxC,eAAO,IAAI,IAAI,UAAU,YAAY,SAAS,MAAM,QAAW,gBAAgB,IAAI,CAAC;AAAA,MACxF,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,oBAAoB,SAAU,YAAY;AAC1D,aAAO,WAAW,OAAO,YAAY,cAAc,WAAW;AAAA,IAClE;AACA,IAAAA,WAAU,UAAU,sBAAsB,SAAU,MAAM,UAAU;AAChE,yBAAmB,UAAU,YAAY,kBAAkB;AAC3D,WAAK,YAAY,SAAU,MAAM;AAC7B,uBAAgB,UAAU,MAAM,KAAK,KAAK;AAAA,MAC9C,CAAC;AACD,WAAK,aAAa,OAAO,SAAU,MAAM,IAAI;AAAE,eAAO,GAAG,IAAI;AAAA,MAAG,GAAG,QAAQ;AAC3E,gBAAU,UAAU,KAAK,UAAU;AACnC,cAAQ,UAAU,KAAK,SAAS;AAAA,IACpC;AACA,IAAAA,WAAU,UAAU,aAAa,SAAU,KAAK;AAE5C,UAAI,SAAS;AACb,UAAI,OAAO,iBAAiB,OAAO,MAAM;AACzC,UAAI,UAAU,OAAO;AACrB,WAAK,eAAe,EAAE,QAAiB,CAAC;AACxC,UAAI,YAAY,KAAK,KAAK,WAAW,OAAO;AAE5C,UAAI,WAAW;AACX,0BAAkB,WAAW,OAAO,QAAQ;AAC5C,eAAO,WAAW,UAAU,UAAU,KAAK,aAAa,OAAO,GAAG,OAAO,UAAU,MAAM,OAAO;AAAA,MACpG;AACA,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,YAAY,SAAU,KAAK;AAE3C,UAAI,SAAS;AACb,UAAI,YAAY,iBAAiB,OAAO,MAAM;AAC9C,UAAI,YAAY,UAAU,KAAK,WAAW,OAAO,IAAI;AACrD,UAAI,CAAC,WAAW;AAEZ;AAAA,MACJ;AACA,UAAI,gBAAgB,OAAO,WAAW,OAAO,SAAS,WAAW;AACjE,gBAAU,UAAU;AAAA,QAChB,IAAI;AAAA,QACJ,MAAM,eAAe,OAAO,IAAI;AAAA,QAChC,OAAO,OAAO,SAAS;AAAA,QACvB,UAAU;AAAA,MACd,GAAG,SAAS;AAAA,IAChB;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,MAAM;AAC9C,UAAI,QAAQ;AACZ,UAAI,MAAM,CAAC;AACX,WAAK,YAAY,SAAU,MAAM;AAC7B,YAAI,KAAK,MAAM,aAAa,MAAM,IAAI,CAAC;AAAA,MAC3C,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,eAAe,SAAU,MAAM,KAAK;AACpD,UAAI,EAAE,OAAO,KAAK;AACd,cAAM,OAAO,2BAA2B,GAAG;AAC/C,UAAI,YAAY,kBAAmB,KAAK,aAAa,GAAG,EAAE;AAC1D,UAAI,CAAC;AACD,cAAM,OAAO,qCAAqC,GAAG;AACzD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,MAAM,kBAAkB;AAChE,UAAI,QAAQ;AACZ,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB;AAAA,MAAM;AAC5D,UAAI,MAAM,CAAC;AACX,WAAK,YAAY,SAAU,MAAM,MAAM;AACnC,gBAAQ,KAAK,aAAa,IAAI,EAAE,eAAe;AAC/C,YAAI,IAAI,IAAI,MAAM,aAAa,MAAM,IAAI,EAAE;AAAA,MAC/C,CAAC;AACD,UAAI,kBAAkB;AAClB,eAAO,KAAK,2BAA2B,GAAG;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,yBAAyB,SAAU,YAAY;AAC/D,UAAI,YAAY,CAAC;AACjB,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC3C,kBAAU,GAAG,IAAI,WAAW,GAAG,EAAE,YAAY;AAAA,MACjD,CAAC;AACD,aAAO,KAAK,2BAA2B,SAAS;AAAA,IACpD;AACA,IAAAA,WAAU,UAAU,oBAAoB,SAAU,MAAM,SAAS,OAAO;AACpE,UAAI,EAAE,MAAM,OAAO,aAAa,MAAM,OAAO,QAAQ;AACjD,cAAM,OAAO,uCAAuC,MAAM,EAAE;AAAA,MAChE;AACA,WAAK,YAAY,OAAO,IAAI,MAAM;AAAA,IACtC;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,MAAM,UAAU;AAC1D,UAAI,uBAAuB,KAAK,0BAA0B,QAAQ;AAClE,wBAAkB,MAAM,oBAAoB;AAC5C,WAAK,YAAY,SAAU,MAAM;AAC7B,aAAK,YAAY,IAAI,IAAI,qBAAqB,IAAI;AAAA,MACtD,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,4BAA4B,SAAU,UAAU;AAChE,UAAI,YAAY,KAAK;AACrB,aAAO,YAAY,UAAU,KAAK,MAAM,QAAQ,IAAI;AAAA,IACxD;AACA,IAAAA,WAAU,UAAU,6BAA6B,SAAU,UAAU;AACjE,UAAI,gBAAgB,KAAK;AACzB,UAAI;AACA,eAAO,cAAc,KAAK,MAAM,QAAQ;AAC5C,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,eAAe,SAAU,cAAc;AACvD,qBAAe,cAAc,CAAC;AAC9B,aAAO,KAAK,WAAW,YAAY;AAAA,IACvC;AACA,IAAAA,WAAU,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC5D,UAAI,QAAQ;AACZ,UAAI,WAAW,KAAK,0BAA0B,KAAK;AACnD,UAAI,CAAC,cAAc,QAAQ,GAAG;AAC1B,eAAO,iBAAiB,SAAS,UAAU,6BAA6B;AAAA,MAC5E;AACA,aAAO,kBAAkB,KAAK,cAAc,IAAI,SAAU,KAAK;AAC3D,eAAO,MAAM,WAAW,GAAG,EAAE,SAAS,SAAS,GAAG,GAAG,kBAAkB,SAAS,KAAK,MAAM,WAAW,GAAG,CAAC,CAAC;AAAA,MAC/G,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,IAAI;AAC5C,UAAI,QAAQ;AACZ,WAAK,cAAc,QAAQ,SAAU,KAAK;AAAE,eAAO,GAAG,KAAK,MAAM,WAAW,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IACxF;AACA,IAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,UAAI,QAAQ;AAEZ,aAAQ,OACJ,KAAK,cACA,IAAI,SAAU,KAAK;AAAE,eAAO,MAAM,OAAO,MAAM,WAAW,GAAG,EAAE,SAAS;AAAA,MAAG,CAAC,EAC5E,KAAK,IAAI,IACd;AAAA,IACR;AACA,IAAAA,WAAU,UAAU,qBAAqB,WAAY;AACjD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,cAAc,SAAU,MAAM,SAAS;AACvD,WAAK,YAAY,OAAO,IAAI;AAAA,IAChC;AACA,eAAW;AAAA,MACP;AAAA,IACJ,GAAGA,WAAU,WAAW,iBAAiB,IAAI;AAC7C,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAMb,SAAS,QAAQ;AACb,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,MAAI,OAAO,OAAO,KAAK,CAAC,MAAM,WAAW,KAAK,MAAM,IAAI;AACxD,MAAI,aAAa,KAAK,MAAM,KAAK,CAAC;AAClC,SAAO,IAAI,UAAU,EAAE,MAAY,WAAuB,CAAC;AAC/D;AAQA,SAAS,UAAU;AACf,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AAEA,MAAI,cAAc,OAAO,KAAK,CAAC,MAAM;AACrC,MAAI,WAAW,cAAc,KAAK,CAAC,IAAI;AACvC,MAAI,aAAa;AACb,SAAK,MAAM;AAAA,EACf;AAEA,MAAI,QAAQ,GAAG;AACX,SAAK,QAAQ,SAAU,MAAM,GAAG;AAC5B,gBAAU,MAAM,aAAa,8BAA8B,cAAc,IAAI,IAAI,IAAI,CAAC;AAAA,IAC1F,CAAC;AAAA,EACL;AACA,SAAO,KACF,OAAO,SAAU,MAAM,KAAK;AAC7B,WAAO,KAAK,gBAAgB;AAAA,MACxB,MAAM,KAAK,OAAO,MAAM,IAAI;AAAA,MAC5B,YAAY,IAAI;AAAA,MAChB,cAAc,IAAI;AAAA,MAClB,cAAc,SAAU,UAAU;AAC9B,eAAO,IAAI,0BAA0B,KAAK,0BAA0B,QAAQ,CAAC;AAAA,MACjF;AAAA,MACA,eAAe,SAAU,UAAU;AAC/B,eAAO,IAAI,2BAA2B,KAAK,2BAA2B,QAAQ,CAAC;AAAA,MACnF;AAAA,IACJ,CAAC;AAAA,EACL,CAAC,EACI,MAAM,QAAQ;AACvB;AAOA,SAAS,YAAY,MAAM;AACvB,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,UAAU;AAC7D;AAOA,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC5C,cAAUC,WAAU,MAAM;AAC1B,aAASA,UAAS,MAAM,OAAO,SAAS,aAAa;AACjD,UAAI,gBAAgB,QAAQ;AAAE,sBAAc;AAAA,MAAU;AACtD,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,YAAM,cAAc;AACpB,YAAM,QAAQ;AACd,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,WAAW,WAAY;AACtC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,UAAS,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AACnF,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC5E;AACA,IAAAA,UAAS,UAAU,oBAAoB,SAAU,UAAU;AACvD,aAAO,KAAK,YAAY,QAAQ;AAAA,IACpC;AACA,IAAAA,UAAS,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC3D,UAAI,YAAY,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AAC3C,eAAO,iBAAiB;AAAA,MAC5B;AACA,UAAI,WAAW,KAAK,SAAS,SAAS,0CAA0C,KAAK;AACrF,aAAO,iBAAiB,SAAS,OAAO,oBAAoB,QAAQ;AAAA,IACxE;AACA,WAAOA;AAAA,EACX,EAAE,UAAU;AAAA;AAcZ,IAAI,SAAS,IAAI,SAAS,UAAU,UAAU,QAAQ,SAAU,GAAG;AAAE,SAAO,OAAO,MAAM;AAAU,CAAC;AAcpG,IAAI,SAAS,IAAI,SAAS,UAAU,UAAU,QAAQ,SAAU,GAAG;AAAE,SAAO,OAAO,MAAM;AAAU,CAAC;AAcpG,IAAI,UAAU,IAAI,SAAS,WAAW,UAAU,SAAS,SAAU,GAAG;AAAE,SAAO,UAAU,CAAC;AAAG,CAAC;AAc9F,IAAI,UAAU,IAAI,SAAS,WAAW,UAAU,SAAS,SAAU,GAAG;AAAE,SAAO,OAAO,MAAM;AAAW,CAAC;AAIxG,IAAI,WAAW,IAAI,SAAS,QAAQ,UAAU,MAAM,SAAU,GAAG;AAAE,SAAO,MAAM;AAAM,CAAC;AAIvF,IAAI,gBAAgB,IAAI,SAAS,aAAa,UAAU,WAAW,SAAU,GAAG;AAAE,SAAO,MAAM;AAAW,CAAC;AAC3G,IAAI,iBAAiB,IAAI,SAAS,QAAQ,UAAU,MAAM,SAAU,GAAG;AAAE,SAAO,OAAO,MAAM,YAAY,aAAa;AAAM,GAAG,SAAU,GAAG;AAAE,SAAQ,aAAa,OAAO,IAAI,IAAI,KAAK,CAAC;AAAI,CAAC;AAC7L,eAAe,cAAc,SAAU,MAAM;AACzC,SAAO,KAAK,YAAY,QAAQ;AACpC;AAaA,IAAI,gBAAgB;AAKpB,SAAS,6BAA6B,OAAO;AACzC,UAAQ,OAAO,OAAO;AAAA,IAClB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,UAAI,iBAAiB;AACjB,eAAO;AAAA,EACnB;AACA,QAAM,OAAO,gDAAgD,KAAK;AACtE;AAOA,SAAS,gBAAgB,MAAM;AAC3B,SAAQ,OAAO,IAAI,MACd,KAAK,SACD,UAAU,SACP,UAAU,SACV,UAAU,UACV,UAAU,UACV,UAAU,SACd;AACZ;AAMA,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC3C,cAAUC,UAAS,MAAM;AACzB,aAASA,SAAQ,OAAO;AACpB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC,KAAK;AACxD,YAAM,QAAQ,UAAU;AACxB,YAAM,QAAQ;AACd,aAAO;AAAA,IACX;AACA,IAAAA,SAAQ,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAClF,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC5E;AACA,IAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,aAAO,KAAK,UAAU,KAAK,KAAK;AAAA,IACpC;AACA,IAAAA,SAAQ,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC1D,UAAI,YAAY,KAAK,KAAK,UAAU,KAAK,OAAO;AAC5C,eAAO,iBAAiB;AAAA,MAC5B;AACA,aAAO,iBAAiB,SAAS,OAAO,4BAA4B,KAAK,UAAU,KAAK,KAAK,CAAC;AAAA,IAClG;AACA,WAAOA;AAAA,EACX,EAAE,UAAU;AAAA;AAiBZ,SAAS,QAAQ,OAAO;AAEpB,YAAU,OAAO,aAAa,aAAa,CAAC;AAC5C,SAAO,IAAI,QAAQ,KAAK;AAC5B;AAOA,SAAS,cAAc,MAAM;AACzB,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,WAAW;AAC9D;AAEA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,YAAW,MAAM,UAAU,YAAY,UAAU;AACtD,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,WAAW;AACjB,YAAM,aAAa;AACnB,YAAM,WAAW;AACjB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,YAAW,WAAW,SAAS;AAAA,MACjD,KAAK,WAAY;AACb,eAAO,KAAK,SAAS,QAAQ,UAAU;AAAA,MAC3C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAErF,aAAO,KAAK,SAAS,YAAY,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC/E;AACA,IAAAA,YAAW,UAAU,mBAAmB,SAAU,MAAM;AACpD,aAAO,KAAK,SAAS,iBAAiB,IAAI;AAAA,IAC9C;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC7D,UAAI,gBAAgB,KAAK,SAAS,SAAS,OAAO,OAAO;AACzD,UAAI,cAAc,SAAS;AACvB,eAAO;AACX,UAAI,WAAW,gBAAgB,KAAK,IAAI,iBAAiB,KAAK,EAAE,WAAW;AAC3E,UAAI,CAAC,KAAK,WAAW,QAAQ,GAAG;AAC5B,eAAO,iBAAiB,SAAS,OAAO,KAAK,SAAS,KAAK,CAAC;AAAA,MAChE;AACA,aAAO,iBAAiB;AAAA,IAC5B;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AAC3E,aAAO,KAAK,SAAS,UAAU,SAAS,UAAU,QAAQ,OAAO;AAAA,IACrE;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AASV,SAAS,aAAa;AAClB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,MAAI,OAAO,OAAO,KAAK,CAAC,MAAM,WAAW,KAAK,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,OAAO;AACzF,MAAI,OAAO,KAAK,CAAC;AACjB,MAAI,YAAY,KAAK,CAAC;AACtB,MAAI,UAAU,KAAK,CAAC,IACd,KAAK,CAAC,IACN,SAAU,GAAG;AAAE,WAAO;AAAA,EAAmD;AAE/E,eAAa,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB,iBAAe,MAAM,CAAC;AACtB,mBAAiB,WAAW,CAAC,GAAG,CAAC,CAAC;AAClC,mBAAiB,SAAS,CAAC,GAAG,CAAC,CAAC;AAChC,SAAO,IAAI,WAAW,MAAM,MAAM,WAAW,OAAO;AACxD;AAOA,SAAS,iBAAiB,MAAM;AAC5B,UAAQ,KAAK,QAAQ,UAAU,cAAc;AACjD;AAiBA,SAAS,YAAY,MAAM,SAAS;AAChC,MAAI,cAAc,OAAO,SAAS,WAAW,UAAU;AAEvD,MAAI,QAAQ,GAAG;AACX,gBAAY,QAAQ,SAAU,QAAQ,GAAG;AACrC,qBAAe,QAAQ,IAAI,CAAC;AAAA,IAChC,CAAC;AAAA,EACL;AACA,MAAI,OAAO,MAAM,MAAM,QAAQ,SAAS,YAAY,IAAI,SAAU,QAAQ;AAAE,WAAO,QAAQ,KAAK,MAAM;AAAA,EAAG,CAAC,CAAC,CAAC;AAC5G,MAAI,OAAO,SAAS;AAChB,SAAK,OAAO;AAChB,SAAO;AACX;AAMA,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AACzC,cAAUC,QAAO,MAAM;AACvB,aAASA,OAAM,MAAM,QAAQ,SAAS;AAClC,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,SAAS;AACf,YAAM,SAAS;AACf,gBAAU,SAAS,EAAE,OAAO,MAAM,YAAY,OAAU,GAAG,OAAO;AAClE,YAAM,cAAc,QAAQ;AAC5B,UAAI,CAAC,QAAQ;AACT,cAAM,SAAS;AACnB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,OAAM,WAAW,SAAS;AAAA,MAC5C,KAAK,WAAY;AACb,YAAI,SAAS,UAAU;AACvB,aAAK,OAAO,QAAQ,SAAU,MAAM;AAChC,oBAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,OAAM,UAAU,mBAAmB,SAAU,MAAM;AAC/C,aAAO,KAAK,OAAO,KAAK,SAAU,SAAS;AAAE,eAAO,QAAQ,iBAAiB,IAAI;AAAA,MAAG,CAAC;AAAA,IACzF;AACA,IAAAA,OAAM,UAAU,WAAW,WAAY;AACnC,aAAO,MAAM,KAAK,OAAO,IAAI,SAAU,SAAS;AAAE,eAAO,QAAQ,SAAS;AAAA,MAAG,CAAC,EAAE,KAAK,KAAK,IAAI;AAAA,IAClG;AACA,IAAAA,OAAM,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAChF,UAAI,OAAO,KAAK,cAAc,cAAc,MAAS;AACrD,UAAI,CAAC;AACD,cAAM,OAAO,gCAAgC,KAAK,SAAS,CAAC;AAChE,aAAO,KAAK,YAAY,QAAQ,SAAS,aAAa,YAAY;AAAA,IACtE;AACA,IAAAA,OAAM,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AACtE,UAAI,OAAO,KAAK,cAAc,UAAU,QAAQ,IAAI;AACpD,UAAI,CAAC;AACD,cAAM,OAAO,gCAAgC,KAAK,SAAS,CAAC;AAChE,aAAO,KAAK,UAAU,SAAS,UAAU,QAAQ,OAAO;AAAA,IAC5D;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,OAAO,sBAAsB;AAEnE,UAAI,KAAK,aAAa;AAClB,eAAO,KAAK,YAAY,KAAK;AAAA,MACjC;AAGA,UAAI,sBAAsB;AACtB,YAAI,qBAAqB,GAAG,KAAK,GAAG;AAChC,iBAAO;AAAA,QACX;AACA,eAAO,KAAK,OACP,OAAO,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAsB,CAAC,EAC1D,KAAK,SAAU,MAAM;AAAE,iBAAO,KAAK,GAAG,KAAK;AAAA,QAAG,CAAC;AAAA,MACxD,OACK;AACD,eAAO,KAAK,OAAO,KAAK,SAAU,MAAM;AAAE,iBAAO,KAAK,GAAG,KAAK;AAAA,QAAG,CAAC;AAAA,MACtE;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACxD,UAAI,KAAK,aAAa;AAClB,eAAO,KAAK,YAAY,KAAK,EAAE,SAAS,OAAO,OAAO;AAAA,MAC1D;AACA,UAAI,YAAY,CAAC;AACjB,UAAI,kBAAkB;AACtB,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AACzC,YAAI,OAAO,KAAK,OAAO,CAAC;AACxB,YAAI,SAAS,KAAK,SAAS,OAAO,OAAO;AACzC,YAAI,OAAO,WAAW,GAAG;AACrB,cAAI,KAAK;AACL,mBAAO,iBAAiB;AAAA;AAExB;AAAA,QACR,OACK;AACD,oBAAU,KAAK,MAAM;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,oBAAoB;AACpB,eAAO,iBAAiB;AAC5B,aAAO,iBAAiB,SAAS,OAAO,qCAAqC,EAAE,OAAO,kBAAkB,SAAS,CAAC;AAAA,IACtH;AACA,IAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,aAAO,KAAK;AAAA,IAChB;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAQV,SAAS,MAAM,eAAe;AAC1B,MAAI,aAAa,CAAC;AAClB,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,eAAW,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EACrC;AACA,MAAI,UAAU,OAAO,aAAa,IAAI,SAAY;AAClD,MAAIC,SAAQ,OAAO,aAAa,IAAI,SAAS,CAAC,aAAa,GAAG,UAAU,IAAI;AAC5E,MAAI,OAAO,MAAMA,OAAM,IAAI,SAAU,MAAM;AAAE,WAAO,KAAK;AAAA,EAAM,CAAC,EAAE,KAAK,KAAK,IAAI;AAEhF,MAAI,QAAQ,GAAG;AACX,QAAI,SAAS;AACT,gBAAU,SAAS,SAAU,GAAG;AAAE,eAAO,cAAc,CAAC;AAAA,MAAG,GAAG,qDAAqD,CAAC;AAAA,IACxH;AACA,IAAAA,OAAM,QAAQ,SAAU,MAAM,GAAG;AAC7B,mBAAa,MAAM,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,IAC9C,CAAC;AAAA,EACL;AACA,SAAO,IAAI,MAAM,MAAMA,QAAO,OAAO;AACzC;AAOA,SAAS,YAAY,MAAM;AACvB,UAAQ,KAAK,QAAQ,UAAU,SAAS;AAC5C;AAMA,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,eAAc,UAAU,eAAe,gBAAgB;AAC5D,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,IAAI,KAAK;AAChD,YAAM,WAAW;AACjB,YAAM,gBAAgB;AACtB,YAAM,iBAAiB;AACvB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,eAAc,WAAW,SAAS;AAAA,MACpD,KAAK,WAAY;AACb,eAAO,KAAK,SAAS,QAAQ,UAAU;AAAA,MAC3C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,eAAc,UAAU,WAAW,WAAY;AAC3C,aAAO,KAAK,SAAS,SAAS,IAAI;AAAA,IACtC;AACA,IAAAA,eAAc,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AACxF,UAAI,KAAK,eAAe,QAAQ,YAAY,KAAK,GAAG;AAChD,YAAI,4BAA4B,KAAK,6BAA6B;AAClE,eAAO,KAAK,SAAS,YAAY,QAAQ,SAAS,aAAa,yBAAyB;AAAA,MAC5F;AACA,aAAO,KAAK,SAAS,YAAY,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC/E;AACA,IAAAA,eAAc,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AAC9E,aAAO,KAAK,SAAS,UAAU,SAAS,KAAK,eAAe,QAAQ,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG,QAAQ,IACxG,WACA,KAAK,6BAA6B,GAAG,QAAQ,OAAO;AAAA,IAC9D;AACA,IAAAA,eAAc,UAAU,+BAA+B,WAAY;AAC/D,UAAI,4BAA4B,OAAO,KAAK,kBAAkB,aACxD,KAAK,cAAc,IACnB,KAAK;AAGX,UAAI,OAAO,KAAK,kBAAkB,YAAY;AAC1C,0BAAkB,MAAM,yBAAyB;AAAA,MACrD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAEhE,UAAI,KAAK,eAAe,QAAQ,KAAK,KAAK,GAAG;AACzC,eAAO,iBAAiB;AAAA,MAC5B;AAEA,aAAO,KAAK,SAAS,SAAS,OAAO,OAAO;AAAA,IAChD;AACA,IAAAA,eAAc,UAAU,mBAAmB,SAAU,MAAM;AACvD,aAAO,KAAK,SAAS,iBAAiB,IAAI;AAAA,IAC9C;AACA,IAAAA,eAAc,UAAU,cAAc,WAAY;AAC9C,aAAO,KAAK;AAAA,IAChB;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AACV,SAAS,2BAA2B,MAAM,wBAAwB;AAE9D,MAAI,OAAO,2BAA2B,cAAc,gBAAgB,sBAAsB,GAAG;AACzF,UAAM,OAAO,8GAA8G;AAAA,EAC/H;AACA,eAAa,MAAM,CAAC;AACpB,MAAI,QAAQ,GAAG;AAKX,QAAI,OAAO,2BAA2B,YAAY;AAC9C,wBAAkB,MAAM,sBAAsB;AAAA,IAClD;AAAA,EACJ;AACJ;AA2CA,SAAS,SAAS,MAAM,wBAAwB,gBAAgB;AAC5D,6BAA2B,MAAM,sBAAsB;AACvD,SAAO,IAAI,cAAc,MAAM,wBAAwB,iBAAiB,iBAAiB,yBAAyB;AACtH;AACA,IAAI,4BAA4B,CAAC,MAAS;AAQ1C,SAAS,eAAe,MAAM;AAC1B,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,YAAY;AAC/D;AAEA,IAAI,wBAAwB,SAAS,eAAe,MAAS;AAC7D,IAAI,mBAAmB,SAAS,UAAU,IAAI;AAQ9C,SAAS,MAAM,MAAM;AACjB,eAAa,MAAM,CAAC;AACpB,SAAO,MAAM,MAAM,qBAAqB;AAC5C;AAQA,SAAS,UAAU,MAAM;AACrB,eAAa,MAAM,CAAC;AACpB,SAAO,MAAM,MAAM,gBAAgB;AACvC;AAEA,IAAI;AAAA;AAAA,EAAsB,SAAU,QAAQ;AACxC,cAAUC,OAAM,MAAM;AACtB,aAASA,MAAK,MAAM,aAAa;AAC7B,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,cAAc;AACpB,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,MAAK,WAAW,SAAS;AAAA,MAC3C,KAAK,WAAY;AACb,gBAAQ,KAAK,WAAW,KAAK,SAAS,QAAQ,KAAK,UAAU;AAAA,MACjE;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,MAAK,UAAU,aAAa,SAAU,aAAa;AAC/C,UAAI,CAAC,KAAK,UAAU;AAChB,YAAI,IAAI;AACR,YAAI;AACA,cAAI,KAAK,YAAY;AAAA,QACzB,SACO,GAAG;AACN,cAAI,aAAa;AAEb,gBAAI;AAAA;AAEJ,kBAAM;AAAA,QACd;AACA,YAAI,eAAe,MAAM;AACrB,gBAAM,OAAO,gFAAgF;AACjG,YAAI,GAAG;AACH,cAAI,QAAQ,KAAK,CAAC,OAAO,CAAC;AACtB,kBAAM,OAAO,8EAA8E;AAC/F,eAAK,WAAW;AAAA,QACpB;AAAA,MACJ;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,MAAK,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAC/E,aAAO,KAAK,WAAW,IAAI,EAAE,YAAY,QAAQ,SAAS,aAAa,YAAY;AAAA,IACvF;AACA,IAAAA,MAAK,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AACrE,aAAO,KAAK,WAAW,IAAI,EAAE,UAAU,SAAS,UAAU,QAAQ,OAAO;AAAA,IAC7E;AACA,IAAAA,MAAK,UAAU,WAAW,WAAY;AAClC,UAAI,IAAI,KAAK,WAAW,KAAK;AAC7B,aAAO,IAAI,EAAE,OAAO;AAAA,IACxB;AACA,IAAAA,MAAK,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACvD,UAAI,IAAI,KAAK,WAAW,KAAK;AAC7B,UAAI,CAAC,GAAG;AAEJ,eAAO,iBAAiB;AAAA,MAC5B;AACA,aAAO,EAAE,SAAS,OAAO,OAAO;AAAA,IACpC;AACA,IAAAA,MAAK,UAAU,mBAAmB,SAAU,MAAM;AAC9C,UAAI,IAAI,KAAK,WAAW,KAAK;AAC7B,aAAO,IAAI,EAAE,iBAAiB,IAAI,IAAI;AAAA,IAC1C;AACA,IAAAA,MAAK,UAAU,cAAc,WAAY;AACrC,UAAI,UAAU,KAAK,WAAW,KAAK;AACnC,aAAO,UAAU,UAAU;AAAA,IAC/B;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAiBV,SAAS,KAAK,YAAY,WAAW;AACjC,MAAI,OAAO,OAAO,eAAe,WAAW,aAAa,UAAU,WAAW,SAAS,IAAI;AAC3F,MAAI,OAAO,OAAO,eAAe,WAAW,YAAY;AAExD,MAAI,QAAQ,GAAG;AACX,QAAI,EAAE,OAAO,SAAS,cAAc,KAAK,WAAW;AAChD,YAAM,OAAO,0FACT,IAAI;AAAA,EAChB;AACA,SAAO,IAAI,KAAK,MAAM,IAAI;AAC9B;AAOA,SAAS,WAAW,MAAM;AACtB,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,QAAQ;AAC3D;AAMA,IAAI;AAAA;AAAA,EAAwB,SAAU,QAAQ;AAC1C,cAAUC,SAAQ,MAAM;AACxB,aAASA,QAAO,SAAS;AACrB,UAAI,QAAQ,OAAO,KAAK,MAAM,UAAU,YAAY,QAAQ,OAAO,MAAM,QAAQ,KAAK;AACtF,YAAM,UAAU;AAChB,YAAM,QAAQ,UAAU;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,QAAO,UAAU,WAAW,WAAY;AACpC,aAAO;AAAA,IACX;AACA,IAAAA,QAAO,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,OAAO;AAE1E,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,WAAW,KAAK,CAAC;AAAA,IACjF;AACA,IAAAA,QAAO,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACzD,UAAI,CAAC,eAAe,KAAK,GAAG;AACxB,eAAO,iBAAiB,SAAS,OAAO,gDAAgD;AAAA,MAC5F;AACA,UAAI,KAAK;AACL,eAAO,KAAK,QAAQ,SAAS,OAAO,OAAO;AAC/C,aAAO,iBAAiB;AAAA,IAC5B;AACA,WAAOA;AAAA,EACX,EAAE,UAAU;AAAA;AACZ,IAAI,wBAAwB,IAAI,OAAO;AAyCvC,SAAS,OAAO,KAAK;AACjB,MAAI,UAAU,WAAW;AACrB,WAAO;AAAA,WACF,OAAO,GAAG;AACf,WAAO,IAAI,OAAO,GAAG;AAAA;AAErB,WAAO,SAAS,uBAAuB,GAAG;AAClD;AAOA,SAAS,aAAa,MAAM;AACxB,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,UAAU;AAC7D;AAEA,SAAS,qBAAqB,MAAM;AAChC,UAAQ,MAAM;AAAA,IACV,KAAK,KAAK;AACN,aAAO;AAAA,IACX,KAAK,KAAK;AACN,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,IAAI;AAAA;AAAA,EAAiC,WAAY;AAC7C,aAASC,iBAAgB,OAAO,YAAY;AACxC,WAAK,aAAa;AAClB,UAAI,kBAAkB,KAAK,GAAG;AAC1B,aAAK,aAAa;AAAA,MACtB,WACS,gBAAgB,KAAK,GAAG;AAC7B,YAAI,aAAa,iBAAiB,KAAK;AACvC,YAAI,CAAC,WAAW;AACZ,gBAAM,OAAO,gEAAgE;AACjF,YAAI,KAAK,WAAW;AACpB,YAAI,OAAO,QAAQ,OAAO,QAAW;AACjC,gBAAM,OAAO,oEAAoE;AAAA,QACrF;AACA,aAAK,aAAa;AAAA,MACtB,OACK;AACD,cAAM,OAAO,mEAAmE,QAAQ,GAAG;AAAA,MAC/F;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,0BAA0B,SAAU,MAAM;AAChE,UAAI,eAAe,oBAAoB,KAAK,UAAU;AACtD,UAAI,OAAO,KAAK;AAChB,UAAI,wBAAwB,KAAK,gBAAgB,8BAA8B,YAAY;AAC3F,UAAI,CAAC,KAAK,qBACN,KAAK,kBAAkB,0BAA0B,uBAAuB;AACxE,YAAI,aAAa,KAAK;AAEtB,YAAI,SAAS,KAAK,gBAAgB,QAAQ,YAAY,YAAY;AAClE,YAAI,CAAC,QAAQ;AACT,gBAAM,IAAI,sBAAsB,oDAAoD,KAAK,aAAa,gBAAgB,KAAK,WAAW,OAAO,mBAAmB,KAAK,OAAO,GAAG;AAAA,QACnL;AACA,aAAK,oBAAoB;AAAA,UACrB,MAAM;AAAA,UACN;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,eAAeA,iBAAgB,WAAW,iBAAiB;AAAA,MAC9D,KAAK,WAAY;AACb,aAAK,wBAAwB,KAAK,IAAI;AACtC,eAAO,KAAK,kBAAkB,KAAK;AAAA,MACvC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAOA;AAAA,EACX,EAAE;AAAA;AAKF,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUC,wBAAuB,MAAM;AACvC,aAASA,uBAAsB,GAAG;AAC9B,UAAI,QAAQ,OAAO,KAAK,MAAM,CAAC,KAAK;AACpC,aAAO,eAAe,OAAOA,uBAAsB,SAAS;AAC5D,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAKP,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,eAAe;AAClD,UAAI,QAAQ,OAAO,KAAK,MAAM,eAAe,WAAW,OAAO,GAAG,KAAK;AACvE,YAAM,aAAa;AACnB,YAAM,gBAAgB;AACtB,YAAM,QAAQ,UAAU;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,WAAW,WAAY;AAC/C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,SAAU,MAAM;AAC3D,aAAO,KAAK,WAAW,iBAAiB,IAAI;AAAA,IAChD;AACA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACpE,aAAO,kBAAkB,KAAK,IACxB,iBAAiB,IACjB,iBAAiB,SAAS,OAAO,gEAAgE;AAAA,IAC3G;AACA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,OAAO,eAAe,aAAa,eAAe;AAItG,UAAI,sBAAsB,cAAc;AACxC,UAAI,CAAC,uBAAuB,CAAC,oBAAoB,SAAS;AACtD;AAAA,MACJ;AACA,UAAI,uBAAuB,oBAAoB;AAC/C,UAAI,CAAC,sBAAsB;AACvB;AAAA,MACJ;AACA,WAAK,cAAc;AAAA,QACf;AAAA,QACA,QAAQ;AAAA,QACR,eAAe,gBAAgB,cAAc,cAAc;AAAA,QAC3D,WAAW;AAAA,QACX,YAAY,SAAU,QAAQ;AAC1B,qBAAW,cAAc,KAAK,aAAa;AAAA,YACvC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,MAAM,cAAc;AAAA,UACxB,CAAC;AAAA,QACL;AAAA,QACA,WAAW,WAAY;AACnB,cAAI,YAAY,oBAAoB,IAAI,GAAG;AACvC,iBAAK,WAAW,MAAS;AAAA,UAC7B,OACK;AACD,uBAAW,cAAc,KAAK,aAAa;AAAA,cACvC,IAAI;AAAA,cACJ,MAAM,cAAc;AAAA,YACxB,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,mBAAkB,UAAU,uBAAuB,SAAU,eAAe,aAAa;AACrF,UAAI,QAAQ;AAEZ,UAAI,iBAAiB,KAAK,SAAS,aAAa;AAChD,UAAI,CAAC,gBAAgB;AACjB,eAAO;AAAA,MACX;AACA,UAAI,gBAAgB,iBAAiB,cAAc;AACnD,UAAI,cAAc,SAAU,GAAG,mBAAmB;AAC9C,YAAI,QAAQ,qBAAqB,iBAAiB;AAClD,YAAI,CAAC,OAAO;AACR;AAAA,QACJ;AACA,cAAM,gBAAgB,OAAO,eAAe,aAAa,aAAa;AAAA,MAC1E;AACA,UAAI,8BAA8B,cAAc,aAAa,KAAK,cAAc,WAAW;AAC3F,UAAI,+BAA+B,cAAc,aAAa,KAAK,eAAe,WAAW;AAC7F,aAAO,WAAY;AACf,oCAA4B;AAC5B,qCAA6B;AAAA,MACjC;AAAA,IACJ;AACA,IAAAA,mBAAkB,UAAU,kCAAkC,SAAU,eAAetC,aAAY,cAAc;AAC7G,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,eAAe;AACrB;AAAA,MACJ;AACA,UAAI;AAGJ,oBAAc,aAAa,KAAK,eAAe,WAAY;AACvD,YAAI,kCAAkC;AAClC,2CAAiC;AAAA,QACrC;AAAA,MACJ,CAAC;AACD,UAAI,gBAAgB,SAAU,MAAM;AAEhC,YAAI,kCAAkC;AAClC,2CAAiC;AAAA,QACrC;AAEA,YAAI,sBAAsB,cAAc;AACxC,YAAI,uBAAuB,uBAAuB,oBAAoB;AACtE,YAAI,uBAAuB,oBAAoB,WAAW,sBAAsB;AAC5E,cAAI,sBAAsB;AAC1B,cAAI,cAAc;AACd,kCAAsB,CAAC,CAAC,aAAa,IAAIA,aAAY,oBAAoB;AAAA,UAC7E,OACK;AACD,kCAAsB,cAAc,KAAK,gBAAgB,IAAI,MAAM,YAAY,oBAAoBA,WAAU,CAAC;AAAA,UAClH;AACA,cAAI,CAAC,qBAAqB;AAMtB,gBAAI,CAAC,MAAM;AACP,oBAAM,gBAAgB,4BAA4B,eAAeA,aAAY,IAAI;AAAA,YACrF;AAAA,UACJ,OACK;AACD,+CAAmC,MAAM,qBAAqB,eAAeA,WAAU;AAAA,UAC3F;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,cAAc,UAAU,cAAc,WAAW;AAEjD,sBAAc,IAAI;AAAA,MACtB,OACK;AACD,YAAI,CAAC,cAAc,QAAQ;AAEvB,wBAAc,KAAK,aAAa,KAAK,2BAA2B,WAAY;AAExE,gBAAI,cAAc,QAAQ;AACtB,4BAAc,OAAO,iCAAiC;AAAA,YAC1D;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,sBAAc,aAAa,KAAK,aAAa,WAAY;AACrD,wBAAc,KAAK;AAAA,QACvB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAOsC;AAAA,EACX,EAAE,UAAU;AAAA;AAKZ,IAAI;AAAA;AAAA,EAAyC,SAAU,QAAQ;AAC3D,cAAUC,0BAAyB,MAAM;AACzC,aAASA,yBAAwB,YAAY,eAAe;AACxD,aAAO,OAAO,KAAK,MAAM,YAAY,aAAa,KAAK;AAAA,IAC3D;AACA,IAAAA,yBAAwB,UAAU,WAAW,SAAU,eAAe;AAClE,UAAI,CAAC,cAAc;AACf,eAAO;AACX,UAAI,YAAY,cAAc;AAC9B,aAAO,UAAU;AAAA,IACrB;AACA,IAAAA,yBAAwB,UAAU,cAAc,SAAU,eAAe;AACrE,UAAI,MAAM,cAAc;AACxB,aAAO,IAAI;AAAA,IACf;AACA,IAAAA,yBAAwB,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAClG,UAAIvC,cAAa,gBAAgB,YAAY,IACvC,cAAc,YAAY,IAC1B;AACN,UAAI,YAAY,IAAI,gBAAgB,cAAc,KAAK,UAAU;AACjE,UAAI,gBAAgB,iBAAiB,MAAM,QAAQ,SAAS,aAAa,SAAS;AAClF,gBAAU,OAAO;AACjB,WAAK,gCAAgC,eAAeA,aAAY,MAAS;AACzE,aAAO;AAAA,IACX;AACA,IAAAuC,yBAAwB,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AACxF,UAAI,CAAC,QAAQ,eAAe,QAAQ,SAAS,MAAM;AAC/C,YAAI,iBAAiB,gBAAgB,QAAQ;AAC7C,YAAI,MAAM,QAAQ;AAClB,YAAK,CAAC,kBAAkB,IAAI,eAAe,YACtC,kBAAkB,IAAI,kBAAkB,UAAW;AACpD,kBAAQ,UAAU,QAAQ,OAAO;AACjC,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,UAAU,KAAK,YAAY,QAAQ,SAAS,QAAW,QAAQ;AACnE,cAAQ,IAAI;AACZ,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,iBAAiB;AAAA;AAKnB,IAAI;AAAA;AAAA,EAAqC,SAAU,QAAQ;AACvD,cAAUC,sBAAqB,MAAM;AACrC,aAASA,qBAAoB,YAAY,SAAS,eAAe;AAC7D,UAAI,QAAQ,OAAO,KAAK,MAAM,YAAY,aAAa,KAAK;AAC5D,YAAM,UAAU;AAChB,aAAO;AAAA,IACX;AACA,IAAAA,qBAAoB,UAAU,WAAW,SAAU,eAAe;AAC9D,UAAI,CAAC,cAAc;AACf,eAAO;AACX,UAAI,iBAAiB,KAAK,QAAQ,IAAI,cAAc,aAAa,cAAc,SAAS,cAAc,OAAO,cAAc,IAAI;AAC/H,aAAO;AAAA,IACX;AACA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,eAAe;AACjE,aAAO,cAAc;AAAA,IACzB;AACA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,UAAU;AAC1F,UAAIxC,cAAa,gBAAgB,QAAQ,IACnC,KAAK,QAAQ,IAAI,UAAU,SAAS,OAAO,cAAc,IAAI,IAC7D;AACN,UAAI,gBAAgB,iBAAiB,MAAM,QAAQ,SAAS,aAAaA,WAAU;AACnF,WAAK,gCAAgC,eAAeA,aAAY,KAAK,OAAO;AAC5E,aAAO;AAAA,IACX;AACA,IAAAwC,qBAAoB,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AACpF,UAAI,gBAAgB,gBAAgB,QAAQ,IACtC,KAAK,QAAQ,IAAI,UAAU,UAAU,QAAQ,cAAc,IAAI,IAC/D;AACN,UAAI,CAAC,QAAQ,eACT,QAAQ,SAAS,QACjB,QAAQ,gBAAgB,eAAe;AACvC,gBAAQ,UAAU,QAAQ,OAAO;AACjC,eAAO;AAAA,MACX;AACA,UAAI,UAAU,KAAK,YAAY,QAAQ,SAAS,QAAW,aAAa;AACxE,cAAQ,IAAI;AACZ,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,iBAAiB;AAAA;AAKnB,SAAS,UAAU,SAAS,SAAS;AACjC,eAAa,SAAS,CAAC;AACvB,MAAI,QAAQ,GAAG;AACX,QAAI,UAAU,WAAW,KAAK,OAAO,UAAU,CAAC,MAAM,UAAU;AAE5D,YAAM,OAAO,iFAAiF;AAAA,IAClG;AAAA,EACJ;AACA,MAAI,gBAAgB,UAAU,UAAU;AACxC,MAAI,gBAAgB,UACd,QAAQ,gBACR;AACN,MAAI,kBAAkB,cAAc,OAAO,cAAc,MAAM;AAC3D,QAAI,QAAQ,GAAG;AACX,UAAI,CAAC,cAAc,OAAO,CAAC,cAAc,KAAK;AAC1C,cAAM,OAAO,uFAAuF;AAAA,MACxG;AAAA,IACJ;AACA,WAAO,IAAI,oBAAoB,SAAS;AAAA,MACpC,KAAK,cAAc;AAAA,MACnB,KAAK,cAAc;AAAA,IACvB,GAAG,aAAa;AAAA,EACpB,OACK;AACD,WAAO,IAAI,wBAAwB,SAAS,aAAa;AAAA,EAC7D;AACJ;AAOA,SAAS,gBAAgB,MAAM;AAC3B,UAAQ,KAAK,QAAQ,UAAU,aAAa;AAChD;AAkBA,SAAS,cAAc,SAAS,SAAS;AACrC,MAAI,UAAU,UAAU,SAAS,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG,EAAE,eAAe,SAAU,IAAI;AACxF,QAAI,WAAW,QAAQ,eAAe;AAClC,cAAQ,cAAc,EAAE;AAAA,IAC5B;AACA,OAAG,UAAU;AAAA,EACjB,EAAE,CAAC,CAAC;AACR,MAAI,WAAW,QAAQ,qBAAqB,OAAO;AAC/C,WAAO;AAAA,EACX,OACK;AACD,WAAO,MAAM,OAAO;AAAA,EACxB;AACJ;AAEA,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,MAAM,WAAW;AACzC,UAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,YAAM,YAAY;AAClB,YAAM,QAAQ,UAAU;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AAC7F,UAAI,CAAC,UAAU,EAAE,OAAO,gBAAgB;AACpC,cAAM,OAAO,2EAA2E;AAC5F,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC5E;AACA,IAAAA,oBAAmB,UAAU,YAAY,SAAU,SAAS,UAAU,QAAQ,SAAS;AAEnF,UAAI,QAAQ,gBAAgB;AACxB,cAAM,OAAO,sCAAsC,QAAQ,cAAc,WAAW,WAAW,yCAAyC;AAC5I,cAAQ,UAAU,QAAQ,OAAO;AACjC,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACrE,UAAI,OAAO,UAAU,KAAK,WAAW;AACjC,eAAO,iBAAiB,SAAS,OAAO,0BAA0B,KAAK,SAAS,IAAI,kBAAkB,KAAK,SAAS;AAAA,MACxH;AACA,aAAO,iBAAiB;AAAA,IAC5B;AACA,WAAOA;AAAA,EACX,EAAE,UAAU;AAAA;AAKZ,IAAI;AAAA;AAAA,EAAgC,SAAU,QAAQ;AAClD,cAAUC,iBAAgB,MAAM;AAChC,aAASA,kBAAiB;AACtB,UAAI,QAAQ,OAAO,KAAK,MAAM,cAAc,QAAQ,KAAK;AACzD,YAAM,QAAQ,UAAU;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,gBAAe,UAAU,WAAW,WAAY;AAC5C,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,kBAAkB;AAAA;AAKpB,IAAI;AAAA;AAAA,EAAsC,SAAU,QAAQ;AACxD,cAAUC,uBAAsB,MAAM;AACtC,aAASA,wBAAuB;AAC5B,aAAO,OAAO,KAAK,MAAM,oBAAoB,QAAQ,KAAK;AAAA,IAC9D;AACA,IAAAA,sBAAqB,UAAU,cAAc,SAAU,MAAM;AACzD,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,sBAAqB,UAAU,WAAW,WAAY;AAClD,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,kBAAkB;AAAA;AAkBpB,IAAI,aAAa,IAAI,eAAe;AAcpC,IAAI,mBAAmB,IAAI,qBAAqB;AAOhD,SAAS,iBAAiB,MAAM;AAC5B,SAAO,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU,cAAc;AACjE;AAKA,SAAS,oBAAoB,IAAI;AAC7B,SAAO,KAAK;AAChB;AAKA,SAAS,kBAAkB,IAAI;AAC3B,SAAO,OAAO,OAAO,YAAY,OAAO,OAAO;AACnD;AAKA,SAAS,wBAAwB,IAAI,WAAW;AAC5C,YAAU,IAAI,mBAAmB,iCAAiC,SAAS;AAC/E;AAgDA,SAAS,OAAO,SAAS;AACrB,SAAO,IAAI,WAAW,OAAO;AACjC;AAKA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,YAAW,SAAS;AACzB,UAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ,IAAI,KAAK;AAC/C,YAAM,UAAU;AAChB,YAAM,QAAQ,UAAU;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,OAAO,SAAS;AAC7D,UAAI,KAAK,QAAQ,aAAa,KAAK;AAC/B,eAAO,iBAAiB;AAC5B,UAAI,YAAY,KAAK,QAAQ,qBAAqB,KAAK;AACvD,UAAI,WAAW;AACX,eAAO,iBAAiB,SAAS,OAAO,6BAA6B,KAAK,OAAO,QAAQ,SAAS;AAAA,MACtG;AACA,aAAO,iBAAiB;AAAA,IAC5B;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,MAAM;AAC/C,aAAO,KAAK,QAAQ,WAAW,KAAK,WAAW;AAAA,IACnD;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,QAAQ,SAAS,aAAa,cAAc;AACrF,UAAI,eAAe,KAAK,QAAQ,aAAa,YAAY,IACnD,eACA,KAAK,QAAQ,aAAa,cAAc,UAAU,OAAO,KAAK,WAAW;AAC/E,aAAO,iBAAiB,MAAM,QAAQ,SAAS,aAAa,YAAY;AAAA,IAC5E;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,SAAS,OAAO,QAAQ,SAAS;AACxE,UAAI,aAAa,CAAC,KAAK,QAAQ,aAAa,KAAK;AAEjD,UAAI,CAAC,QAAQ,aAAa;AACtB,YAAI,YAAY,QAAQ,SAAS,SAC5B,aAAa,UAAU,QAAQ,WAAW,UAAU,QAAQ;AACjE,YAAI,WAAW;AACX,kBAAQ,UAAU,QAAQ,OAAO;AACjC,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,eAAe,aACb,KAAK,QAAQ,aAAa,OAAO,OAAO,KAAK,WAAW,IACxD;AACN,UAAI,UAAU,KAAK,YAAY,QAAQ,SAAS,QAAW,YAAY;AACvE,cAAQ,IAAI;AACZ,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,UAAU;AAAA;AAGZ,IAAI,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,MAAM;AAAA,EACN;AACJ;", "names": ["Hook", "d", "b", "__assign", "identifier", "BaseNode", "isAlive", "ScalarNode", "ObjectNode", "livelinessChecking", "TypeFlags", "BaseType", "ComplexType", "SimpleType", "RunningAction", "runningActions", "action", "CollectedMiddlewares", "array", "IdentifierCache", "set", "NodeLifeCycle", "EventHandler", "EventHandlers", "SnapshotProcessor", "MapIdentifierMode", "MSTMap", "MapType", "key", "ArrayType", "i", "ModelType", "CoreType", "Literal", "Refinement", "Union", "types", "OptionalValue", "Late", "Frozen", "StoredReference", "InvalidReferenceError", "BaseReferenceType", "IdentifierReferenceType", "CustomReferenceType", "BaseIdentifierType", "IdentifierType", "IdentifierNumberType", "CustomType"]}