import {
  require_baseFlatten
} from "./chunk-OLJOAOLE.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/flatten.js
var require_flatten = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/flatten.js"(exports, module) {
    var baseFlatten = require_baseFlatten();
    function flatten(array) {
      var length = array == null ? 0 : array.length;
      return length ? baseFlatten(array, 1) : [];
    }
    module.exports = flatten;
  }
});

export {
  require_flatten
};
//# sourceMappingURL=chunk-3LRRGZUN.js.map
