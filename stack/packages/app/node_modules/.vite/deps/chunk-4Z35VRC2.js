import {
  require_upperFirst
} from "./chunk-VNKFJAFK.js";
import {
  require_toString
} from "./chunk-TWO7A3AO.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/capitalize.js
var require_capitalize = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/capitalize.js"(exports, module) {
    var toString = require_toString();
    var upperFirst = require_upperFirst();
    function capitalize(string) {
      return upperFirst(toString(string).toLowerCase());
    }
    module.exports = capitalize;
  }
});

export {
  require_capitalize
};
//# sourceMappingURL=chunk-4Z35VRC2.js.map
