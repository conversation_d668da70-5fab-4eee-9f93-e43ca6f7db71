import {
  require_baseClamp
} from "./chunk-J73GKEBW.js";
import {
  require_toInteger
} from "./chunk-3M5FHVBU.js";
import "./chunk-7V26ZGEI.js";
import {
  require_baseToString,
  require_toString
} from "./chunk-TWO7A3AO.js";
import "./chunk-2A43EO37.js";
import "./chunk-2PGYE62Q.js";
import "./chunk-XDZT3BAO.js";
import "./chunk-MBKML2QC.js";
import "./chunk-WLFKI2V4.js";
import "./chunk-PRKNHKZJ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/endsWith.js
var require_endsWith = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/endsWith.js"(exports, module) {
    var baseClamp = require_baseClamp();
    var baseToString = require_baseToString();
    var toInteger = require_toInteger();
    var toString = require_toString();
    function endsWith(string, target, position) {
      string = toString(string);
      target = baseToString(target);
      var length = string.length;
      position = position === void 0 ? length : baseClamp(toInteger(position), 0, length);
      var end = position;
      position -= target.length;
      return position >= 0 && string.slice(position, end) == target;
    }
    module.exports = endsWith;
  }
});
export default require_endsWith();
//# sourceMappingURL=lodash_endsWith.js.map
