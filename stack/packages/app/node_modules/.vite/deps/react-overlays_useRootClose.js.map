{"version": 3, "sources": ["../../../../../node_modules/.pnpm/warning@4.0.3/node_modules/warning/warning.js", "../../../../../node_modules/.pnpm/dom-helpers@5.2.1/node_modules/dom-helpers/esm/contains.js", "../../../../../node_modules/.pnpm/dom-helpers@5.2.1/node_modules/dom-helpers/esm/canUseDOM.js", "../../../../../node_modules/.pnpm/dom-helpers@5.2.1/node_modules/dom-helpers/esm/addEventListener.js", "../../../../../node_modules/.pnpm/dom-helpers@5.2.1/node_modules/dom-helpers/esm/removeEventListener.js", "../../../../../node_modules/.pnpm/dom-helpers@5.2.1/node_modules/dom-helpers/esm/listen.js", "../../../../../node_modules/.pnpm/react-overlays@5.1.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-overlays/esm/useRootClose.js", "../../../../../node_modules/.pnpm/@restart+hooks@0.3.27_react@18.3.1/node_modules/@restart/hooks/esm/useEventCallback.js", "../../../../../node_modules/.pnpm/@restart+hooks@0.3.27_react@18.3.1/node_modules/@restart/hooks/esm/useCommittedRef.js", "../../../../../node_modules/.pnpm/react-overlays@5.1.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-overlays/esm/safeFindDOMNode.js", "../../../../../node_modules/.pnpm/react-overlays@5.1.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-overlays/esm/ownerDocument.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n", "/* eslint-disable no-bitwise, no-cond-assign */\n\n/**\n * Checks if an element contains another given element.\n * \n * @param context the context element\n * @param node the element to check\n */\nexport default function contains(context, node) {\n  // HTML DOM and SVG DOM may have different support levels,\n  // so we need to check on context instead of a document root element.\n  if (context.contains) return context.contains(node);\n  if (context.compareDocumentPosition) return context === node || !!(context.compareDocumentPosition(node) & 16);\n}", "export default !!(typeof window !== 'undefined' && window.document && window.document.createElement);", "/* eslint-disable no-return-assign */\nimport canUseDOM from './canUseDOM';\nexport var optionsSupported = false;\nexport var onceSupported = false;\n\ntry {\n  var options = {\n    get passive() {\n      return optionsSupported = true;\n    },\n\n    get once() {\n      // eslint-disable-next-line no-multi-assign\n      return onceSupported = optionsSupported = true;\n    }\n\n  };\n\n  if (canUseDOM) {\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, true);\n  }\n} catch (e) {\n  /* */\n}\n\n/**\n * An `addEventListener` ponyfill, supports the `once` option\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction addEventListener(node, eventName, handler, options) {\n  if (options && typeof options !== 'boolean' && !onceSupported) {\n    var once = options.once,\n        capture = options.capture;\n    var wrappedHandler = handler;\n\n    if (!onceSupported && once) {\n      wrappedHandler = handler.__once || function onceHandler(event) {\n        this.removeEventListener(eventName, onceHandler, capture);\n        handler.call(this, event);\n      };\n\n      handler.__once = wrappedHandler;\n    }\n\n    node.addEventListener(eventName, wrappedHandler, optionsSupported ? options : capture);\n  }\n\n  node.addEventListener(eventName, handler, options);\n}\n\nexport default addEventListener;", "/**\n * A `removeEventListener` ponyfill\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction removeEventListener(node, eventName, handler, options) {\n  var capture = options && typeof options !== 'boolean' ? options.capture : options;\n  node.removeEventListener(eventName, handler, capture);\n\n  if (handler.__once) {\n    node.removeEventListener(eventName, handler.__once, capture);\n  }\n}\n\nexport default removeEventListener;", "import addEventListener from './addEventListener';\nimport removeEventListener from './removeEventListener';\n\nfunction listen(node, eventName, handler, options) {\n  addEventListener(node, eventName, handler, options);\n  return function () {\n    removeEventListener(node, eventName, handler, options);\n  };\n}\n\nexport default listen;", "import contains from 'dom-helpers/contains';\nimport listen from 'dom-helpers/listen';\nimport { useCallback, useEffect, useRef } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport warning from 'warning';\nimport ownerDocument from './ownerDocument';\nvar escapeKeyCode = 27;\n\nvar noop = function noop() {};\n\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\n\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nvar getRefTarget = function getRefTarget(ref) {\n  return ref && ('current' in ref ? ref.current : ref);\n};\n\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, _temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      disabled = _ref.disabled,\n      _ref$clickTrigger = _ref.clickTrigger,\n      clickTrigger = _ref$clickTrigger === void 0 ? 'click' : _ref$clickTrigger;\n\n  var preventMouseRootCloseRef = useRef(false);\n  var onClose = onRootClose || noop;\n  var handleMouseCapture = useCallback(function (e) {\n    var currentTarget = getRefTarget(ref);\n    warning(!!currentTarget, 'RootClose captured a close event but does not have a ref to compare it to. ' + 'useRootClose(), should be passed a ref that resolves to a DOM node');\n    preventMouseRootCloseRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!contains(currentTarget, e.target);\n  }, [ref]);\n  var handleMouse = useEventCallback(function (e) {\n    if (!preventMouseRootCloseRef.current) {\n      onClose(e);\n    }\n  });\n  var handleKeyUp = useEventCallback(function (e) {\n    if (e.keyCode === escapeKeyCode) {\n      onClose(e);\n    }\n  });\n  useEffect(function () {\n    if (disabled || ref == null) return undefined; // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n\n    var currentEvent = window.event;\n    var doc = ownerDocument(getRefTarget(ref)); // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n\n    var removeMouseCaptureListener = listen(doc, clickTrigger, handleMouseCapture, true);\n    var removeMouseListener = listen(doc, clickTrigger, function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleMouse(e);\n    });\n    var removeKeyupListener = listen(doc, 'keyup', function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleKeyUp(e);\n    });\n    var mobileSafariHackListeners = [];\n\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(function (el) {\n        return listen(el, 'mousemove', noop);\n      });\n    }\n\n    return function () {\n      removeMouseCaptureListener();\n      removeMouseListener();\n      removeKeyupListener();\n      mobileSafariHackListeners.forEach(function (remove) {\n        return remove();\n      });\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleMouse, handleKeyUp]);\n}\n\nexport default useRootClose;", "import { useCallback } from 'react';\nimport useCommittedRef from './useCommittedRef';\nexport default function useEventCallback(fn) {\n  var ref = useCommittedRef(fn);\n  return useCallback(function () {\n    return ref.current && ref.current.apply(ref, arguments);\n  }, [ref]);\n}", "import { useEffect, useRef } from 'react';\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded befor being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */\n\nfunction useCommittedRef(value) {\n  var ref = useRef(value);\n  useEffect(function () {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\n\nexport default useCommittedRef;", "import ReactDOM from 'react-dom';\nexport default function safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    return ReactDOM.findDOMNode(componentOrElement);\n  }\n\n  return componentOrElement != null ? componentOrElement : null;\n}", "import ownerDocument from 'dom-helpers/ownerDocument';\nimport safeFindDOMNode from './safeFindDOMNode';\nexport default (function (componentOrElement) {\n  return ownerDocument(safeFindDOMNode(componentOrElement));\n});"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAgBA,QAAI,UAAU;AAEd,QAAIA,WAAU,WAAW;AAAA,IAAC;AAE1B,QAAI,SAAS;AACP,qBAAe,SAASC,cAAa,QAAQ,MAAM;AACrD,YAAI,MAAM,UAAU;AACpB,eAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,iBAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAClC,eAAK,MAAM,CAAC,IAAI,UAAU,GAAG;AAAA,QAC/B;AACA,YAAI,WAAW;AACf,YAAI,UAAU,cACZ,OAAO,QAAQ,OAAO,WAAW;AAC/B,iBAAO,KAAK,UAAU;AAAA,QACxB,CAAC;AACH,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAEA,MAAAD,WAAU,SAAS,WAAW,QAAQ,MAAM;AAC1C,YAAI,MAAM,UAAU;AACpB,eAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,iBAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAClC,eAAK,MAAM,CAAC,IAAI,UAAU,GAAG;AAAA,QAC/B;AACA,YAAI,WAAW,QAAW;AACxB,gBAAM,IAAI;AAAA,YACN;AAAA,UAEJ;AAAA,QACF;AACA,YAAI,CAAC,WAAW;AACd,uBAAa,MAAM,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAtCM;AAwCN,WAAO,UAAUA;AAAA;AAAA;;;ACrDF,SAAR,SAA0B,SAAS,MAAM;AAG9C,MAAI,QAAQ,SAAU,QAAO,QAAQ,SAAS,IAAI;AAClD,MAAI,QAAQ,wBAAyB,QAAO,YAAY,QAAQ,CAAC,EAAE,QAAQ,wBAAwB,IAAI,IAAI;AAC7G;;;ACbA,IAAO,oBAAQ,CAAC,EAAE,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,SAAS;;;ACE/E,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AAE3B,IAAI;AACE,YAAU;AAAA,IACZ,IAAI,UAAU;AACZ,aAAO,mBAAmB;AAAA,IAC5B;AAAA,IAEA,IAAI,OAAO;AAET,aAAO,gBAAgB,mBAAmB;AAAA,IAC5C;AAAA,EAEF;AAEA,MAAI,mBAAW;AACb,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAChD,WAAO,oBAAoB,QAAQ,SAAS,IAAI;AAAA,EAClD;AACF,SAAS,GAAG;AAEZ;AAlBM;AA4BN,SAAS,iBAAiB,MAAM,WAAW,SAAS,SAAS;AAC3D,MAAI,WAAW,OAAO,YAAY,aAAa,CAAC,eAAe;AAC7D,QAAI,OAAO,QAAQ,MACf,UAAU,QAAQ;AACtB,QAAI,iBAAiB;AAErB,QAAI,CAAC,iBAAiB,MAAM;AAC1B,uBAAiB,QAAQ,UAAU,SAAS,YAAY,OAAO;AAC7D,aAAK,oBAAoB,WAAW,aAAa,OAAO;AACxD,gBAAQ,KAAK,MAAM,KAAK;AAAA,MAC1B;AAEA,cAAQ,SAAS;AAAA,IACnB;AAEA,SAAK,iBAAiB,WAAW,gBAAgB,mBAAmB,UAAU,OAAO;AAAA,EACvF;AAEA,OAAK,iBAAiB,WAAW,SAAS,OAAO;AACnD;AAEA,IAAO,2BAAQ;;;AC/Cf,SAAS,oBAAoB,MAAM,WAAW,SAAS,SAAS;AAC9D,MAAI,UAAU,WAAW,OAAO,YAAY,YAAY,QAAQ,UAAU;AAC1E,OAAK,oBAAoB,WAAW,SAAS,OAAO;AAEpD,MAAI,QAAQ,QAAQ;AAClB,SAAK,oBAAoB,WAAW,QAAQ,QAAQ,OAAO;AAAA,EAC7D;AACF;AAEA,IAAO,8BAAQ;;;ACdf,SAAS,OAAO,MAAM,WAAW,SAAS,SAAS;AACjD,2BAAiB,MAAM,WAAW,SAAS,OAAO;AAClD,SAAO,WAAY;AACjB,gCAAoB,MAAM,WAAW,SAAS,OAAO;AAAA,EACvD;AACF;AAEA,IAAO,iBAAQ;;;ACRf,IAAAE,gBAA+C;;;ACF/C,IAAAC,gBAA4B;;;ACA5B,mBAAkC;AAWlC,SAAS,gBAAgB,OAAO;AAC9B,MAAI,UAAM,qBAAO,KAAK;AACtB,8BAAU,WAAY;AACpB,QAAI,UAAU;AAAA,EAChB,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;AAEA,IAAO,0BAAQ;;;ADjBA,SAAR,iBAAkC,IAAI;AAC3C,MAAI,MAAM,wBAAgB,EAAE;AAC5B,aAAO,2BAAY,WAAY;AAC7B,WAAO,IAAI,WAAW,IAAI,QAAQ,MAAM,KAAK,SAAS;AAAA,EACxD,GAAG,CAAC,GAAG,CAAC;AACV;;;ADHA,qBAAoB;;;AGJpB,uBAAqB;AACN,SAAR,gBAAiC,oBAAoB;AAC1D,MAAI,sBAAsB,cAAc,oBAAoB;AAC1D,WAAO,iBAAAC,QAAS,YAAY,kBAAkB;AAAA,EAChD;AAEA,SAAO,sBAAsB,OAAO,qBAAqB;AAC3D;;;ACLA,IAAO,wBAAS,SAAU,oBAAoB;AAC5C,SAAO,cAAc,gBAAgB,kBAAkB,CAAC;AAC1D;;;AJEA,IAAI,gBAAgB;AAEpB,IAAI,OAAO,SAASC,QAAO;AAAC;AAE5B,SAAS,iBAAiB,OAAO;AAC/B,SAAO,MAAM,WAAW;AAC1B;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,CAAC,EAAE,MAAM,WAAW,MAAM,UAAU,MAAM,WAAW,MAAM;AACpE;AAEA,IAAI,eAAe,SAASC,cAAa,KAAK;AAC5C,SAAO,QAAQ,aAAa,MAAM,IAAI,UAAU;AAClD;AAcA,SAAS,aAAa,KAAK,aAAa,OAAO;AAC7C,MAAI,OAAO,UAAU,SAAS,CAAC,IAAI,OAC/B,WAAW,KAAK,UAChB,oBAAoB,KAAK,cACzB,eAAe,sBAAsB,SAAS,UAAU;AAE5D,MAAI,+BAA2B,sBAAO,KAAK;AAC3C,MAAI,UAAU,eAAe;AAC7B,MAAI,yBAAqB,2BAAY,SAAU,GAAG;AAChD,QAAI,gBAAgB,aAAa,GAAG;AACpC,uBAAAC,SAAQ,CAAC,CAAC,eAAe,+IAAoJ;AAC7K,6BAAyB,UAAU,CAAC,iBAAiB,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM;AAAA,EACvI,GAAG,CAAC,GAAG,CAAC;AACR,MAAI,cAAc,iBAAiB,SAAU,GAAG;AAC9C,QAAI,CAAC,yBAAyB,SAAS;AACrC,cAAQ,CAAC;AAAA,IACX;AAAA,EACF,CAAC;AACD,MAAI,cAAc,iBAAiB,SAAU,GAAG;AAC9C,QAAI,EAAE,YAAY,eAAe;AAC/B,cAAQ,CAAC;AAAA,IACX;AAAA,EACF,CAAC;AACD,+BAAU,WAAY;AACpB,QAAI,YAAY,OAAO,KAAM,QAAO;AAGpC,QAAI,eAAe,OAAO;AAC1B,QAAI,MAAM,sBAAc,aAAa,GAAG,CAAC;AAIzC,QAAI,6BAA6B,eAAO,KAAK,cAAc,oBAAoB,IAAI;AACnF,QAAI,sBAAsB,eAAO,KAAK,cAAc,SAAU,GAAG;AAE/D,UAAI,MAAM,cAAc;AACtB,uBAAe;AACf;AAAA,MACF;AAEA,kBAAY,CAAC;AAAA,IACf,CAAC;AACD,QAAI,sBAAsB,eAAO,KAAK,SAAS,SAAU,GAAG;AAE1D,UAAI,MAAM,cAAc;AACtB,uBAAe;AACf;AAAA,MACF;AAEA,kBAAY,CAAC;AAAA,IACf,CAAC;AACD,QAAI,4BAA4B,CAAC;AAEjC,QAAI,kBAAkB,IAAI,iBAAiB;AACzC,kCAA4B,CAAC,EAAE,MAAM,KAAK,IAAI,KAAK,QAAQ,EAAE,IAAI,SAAU,IAAI;AAC7E,eAAO,eAAO,IAAI,aAAa,IAAI;AAAA,MACrC,CAAC;AAAA,IACH;AAEA,WAAO,WAAY;AACjB,iCAA2B;AAC3B,0BAAoB;AACpB,0BAAoB;AACpB,gCAA0B,QAAQ,SAAU,QAAQ;AAClD,eAAO,OAAO;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,KAAK,UAAU,cAAc,oBAAoB,aAAa,WAAW,CAAC;AAChF;AAEA,IAAO,uBAAQ;", "names": ["warning", "printWarning", "import_react", "import_react", "ReactDOM", "noop", "getRefTarget", "warning"]}