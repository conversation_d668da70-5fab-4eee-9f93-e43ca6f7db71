{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/flatten.js"], "sourcesContent": ["var baseFlatten = require('./_baseFlatten');\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\n\nmodule.exports = flatten;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAgBlB,aAAS,QAAQ,OAAO;AACtB,UAAI,SAAS,SAAS,OAAO,IAAI,MAAM;AACvC,aAAO,SAAS,YAAY,OAAO,CAAC,IAAI,CAAC;AAAA,IAC3C;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}