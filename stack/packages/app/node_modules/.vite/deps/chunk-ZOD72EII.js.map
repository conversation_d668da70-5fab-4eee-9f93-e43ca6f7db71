{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_escapeHtmlChar.js", "../../../../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/escape.js"], "sourcesContent": ["var basePropertyOf = require('./_basePropertyOf');\n\n/** Used to map characters to HTML entities. */\nvar htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\n\n/**\n * Used by `_.escape` to convert characters to HTML entities.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nvar escapeHtmlChar = basePropertyOf(htmlEscapes);\n\nmodule.exports = escapeHtmlChar;\n", "var escapeHtmlChar = require('./_escapeHtmlChar'),\n    toString = require('./toString');\n\n/** Used to match HTML entities and HTML characters. */\nvar reUnescapedHtml = /[&<>\"']/g,\n    reHasUnescapedHtml = RegExp(reUnescapedHtml.source);\n\n/**\n * Converts the characters \"&\", \"<\", \">\", '\"', and \"'\" in `string` to their\n * corresponding HTML entities.\n *\n * **Note:** No other characters are escaped. To escape additional\n * characters use a third-party library like [_he_](https://mths.be/he).\n *\n * Though the \">\" character is escaped for symmetry, characters like\n * \">\" and \"/\" don't need escaping in HTML and have no special meaning\n * unless they're part of a tag or unquoted attribute value. See\n * [<PERSON>'s article](https://mathiasbynens.be/notes/ambiguous-ampersands)\n * (under \"semi-related fun fact\") for more details.\n *\n * When working with HTML you should always\n * [quote attribute values](http://wonko.com/post/html-escaping) to reduce\n * XSS vectors.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category String\n * @param {string} [string=''] The string to escape.\n * @returns {string} Returns the escaped string.\n * @example\n *\n * _.escape('fred, barney, & pebbles');\n * // => 'fred, barney, &amp; pebbles'\n */\nfunction escape(string) {\n  string = toString(string);\n  return (string && reHasUnescapedHtml.test(string))\n    ? string.replace(reUnescapedHtml, escapeHtmlChar)\n    : string;\n}\n\nmodule.exports = escape;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,iBAAiB;AAGrB,QAAI,cAAc;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AASA,QAAI,iBAAiB,eAAe,WAAW;AAE/C,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,WAAW;AAGf,QAAI,kBAAkB;AAAtB,QACI,qBAAqB,OAAO,gBAAgB,MAAM;AA8BtD,aAAS,OAAO,QAAQ;AACtB,eAAS,SAAS,MAAM;AACxB,aAAQ,UAAU,mBAAmB,KAAK,MAAM,IAC5C,OAAO,QAAQ,iBAAiB,cAAc,IAC9C;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}