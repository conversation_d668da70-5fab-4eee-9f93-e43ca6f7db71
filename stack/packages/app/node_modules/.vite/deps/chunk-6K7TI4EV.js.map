{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@babel+runtime@7.28.3/node_modules/@babel/runtime/helpers/esm/classCallCheck.js", "../../../../../node_modules/.pnpm/@babel+runtime@7.28.3/node_modules/@babel/runtime/helpers/esm/createClass.js", "../../../../../node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/supportUtil.js", "../../../../../node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/numberUtil.js", "../../../../../node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/BigIntDecimal.js", "../../../../../node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/NumberDecimal.js", "../../../../../node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js", "../../../../../node_modules/.pnpm/@rc-component+mini-decimal@1.1.0/node_modules/@rc-component/mini-decimal/es/index.js"], "sourcesContent": ["function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "export function supportBigInt() {\n  return typeof BigInt === 'function';\n}", "import { supportBigInt } from \"./supportUtil\";\nexport function isEmpty(value) {\n  return !value && value !== 0 && !Number.isNaN(value) || !String(value).trim();\n}\n\n/**\n * Format string number to readable number\n */\nexport function trimNumber(numStr) {\n  var str = numStr.trim();\n  var negative = str.startsWith('-');\n  if (negative) {\n    str = str.slice(1);\n  }\n  str = str\n  // Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`\n  .replace(/(\\.\\d*[^0])0*$/, '$1')\n  // Remove useless decimal. `1.` => `1`\n  .replace(/\\.0*$/, '')\n  // Remove integer 0. `0001` => `1`, 000.1' => `.1`\n  .replace(/^0+/, '');\n  if (str.startsWith('.')) {\n    str = \"0\".concat(str);\n  }\n  var trimStr = str || '0';\n  var splitNumber = trimStr.split('.');\n  var integerStr = splitNumber[0] || '0';\n  var decimalStr = splitNumber[1] || '0';\n  if (integerStr === '0' && decimalStr === '0') {\n    negative = false;\n  }\n  var negativeStr = negative ? '-' : '';\n  return {\n    negative: negative,\n    negativeStr: negativeStr,\n    trimStr: trimStr,\n    integerStr: integerStr,\n    decimalStr: decimalStr,\n    fullStr: \"\".concat(negativeStr).concat(trimStr)\n  };\n}\nexport function isE(number) {\n  var str = String(number);\n  return !Number.isNaN(Number(str)) && str.includes('e');\n}\n\n/**\n * [Legacy] Convert 1e-9 to 0.000000001.\n * This may lose some precision if user really want 1e-9.\n */\nexport function getNumberPrecision(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));\n    var decimalMatch = numStr.match(/\\.(\\d+)/);\n    if (decimalMatch !== null && decimalMatch !== void 0 && decimalMatch[1]) {\n      precision += decimalMatch[1].length;\n    }\n    return precision;\n  }\n  return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;\n}\n\n/**\n * Convert number (includes scientific notation) to -xxx.yyy format\n */\nexport function num2str(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    if (number > Number.MAX_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);\n    }\n    if (number < Number.MIN_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);\n    }\n    numStr = number.toFixed(getNumberPrecision(numStr));\n  }\n  return trimNumber(numStr).fullStr;\n}\nexport function validateNumber(num) {\n  if (typeof num === 'number') {\n    return !Number.isNaN(num);\n  }\n\n  // Empty\n  if (!num) {\n    return false;\n  }\n  return (\n    // Normal type: 11.28\n    /^\\s*-?\\d+(\\.\\d+)?\\s*$/.test(num) ||\n    // Pre-number: 1.\n    /^\\s*-?\\d+\\.\\s*$/.test(num) ||\n    // Post-number: .1\n    /^\\s*-?\\.\\d+\\s*$/.test(num)\n  );\n}", "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { isE, isEmpty, num2str, trimNumber, validateNumber } from \"./numberUtil\";\nvar BigIntDecimal = /*#__PURE__*/function () {\n  /** BigInt will convert `0009` to `9`. We need record the len of decimal */\n\n  function BigIntDecimal(value) {\n    _classCallCheck(this, BigIntDecimal);\n    _defineProperty(this, \"origin\", '');\n    _defineProperty(this, \"negative\", void 0);\n    _defineProperty(this, \"integer\", void 0);\n    _defineProperty(this, \"decimal\", void 0);\n    _defineProperty(this, \"decimalLen\", void 0);\n    _defineProperty(this, \"empty\", void 0);\n    _defineProperty(this, \"nan\", void 0);\n    if (isEmpty(value)) {\n      this.empty = true;\n      return;\n    }\n    this.origin = String(value);\n\n    // Act like Number convert\n    if (value === '-' || Number.isNaN(value)) {\n      this.nan = true;\n      return;\n    }\n    var mergedValue = value;\n\n    // We need convert back to Number since it require `toFixed` to handle this\n    if (isE(mergedValue)) {\n      mergedValue = Number(mergedValue);\n    }\n    mergedValue = typeof mergedValue === 'string' ? mergedValue : num2str(mergedValue);\n    if (validateNumber(mergedValue)) {\n      var trimRet = trimNumber(mergedValue);\n      this.negative = trimRet.negative;\n      var numbers = trimRet.trimStr.split('.');\n      this.integer = BigInt(numbers[0]);\n      var decimalStr = numbers[1] || '0';\n      this.decimal = BigInt(decimalStr);\n      this.decimalLen = decimalStr.length;\n    } else {\n      this.nan = true;\n    }\n  }\n  _createClass(BigIntDecimal, [{\n    key: \"getMark\",\n    value: function getMark() {\n      return this.negative ? '-' : '';\n    }\n  }, {\n    key: \"getIntegerStr\",\n    value: function getIntegerStr() {\n      return this.integer.toString();\n    }\n\n    /**\n     * @private get decimal string\n     */\n  }, {\n    key: \"getDecimalStr\",\n    value: function getDecimalStr() {\n      return this.decimal.toString().padStart(this.decimalLen, '0');\n    }\n\n    /**\n     * @private Align BigIntDecimal with same decimal length. e.g. 12.3 + 5 = 1230000\n     * This is used for add function only.\n     */\n  }, {\n    key: \"alignDecimal\",\n    value: function alignDecimal(decimalLength) {\n      var str = \"\".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(decimalLength, '0'));\n      return BigInt(str);\n    }\n  }, {\n    key: \"negate\",\n    value: function negate() {\n      var clone = new BigIntDecimal(this.toString());\n      clone.negative = !clone.negative;\n      return clone;\n    }\n  }, {\n    key: \"cal\",\n    value: function cal(offset, calculator, calDecimalLen) {\n      var maxDecimalLength = Math.max(this.getDecimalStr().length, offset.getDecimalStr().length);\n      var myAlignedDecimal = this.alignDecimal(maxDecimalLength);\n      var offsetAlignedDecimal = offset.alignDecimal(maxDecimalLength);\n      var valueStr = calculator(myAlignedDecimal, offsetAlignedDecimal).toString();\n      var nextDecimalLength = calDecimalLen(maxDecimalLength);\n\n      // We need fill string length back to `maxDecimalLength` to avoid parser failed\n      var _trimNumber = trimNumber(valueStr),\n        negativeStr = _trimNumber.negativeStr,\n        trimStr = _trimNumber.trimStr;\n      var hydrateValueStr = \"\".concat(negativeStr).concat(trimStr.padStart(nextDecimalLength + 1, '0'));\n      return new BigIntDecimal(\"\".concat(hydrateValueStr.slice(0, -nextDecimalLength), \".\").concat(hydrateValueStr.slice(-nextDecimalLength)));\n    }\n  }, {\n    key: \"add\",\n    value: function add(value) {\n      if (this.isInvalidate()) {\n        return new BigIntDecimal(value);\n      }\n      var offset = new BigIntDecimal(value);\n      if (offset.isInvalidate()) {\n        return this;\n      }\n      return this.cal(offset, function (num1, num2) {\n        return num1 + num2;\n      }, function (len) {\n        return len;\n      });\n    }\n  }, {\n    key: \"multi\",\n    value: function multi(value) {\n      var target = new BigIntDecimal(value);\n      if (this.isInvalidate() || target.isInvalidate()) {\n        return new BigIntDecimal(NaN);\n      }\n      return this.cal(target, function (num1, num2) {\n        return num1 * num2;\n      }, function (len) {\n        return len * 2;\n      });\n    }\n  }, {\n    key: \"isEmpty\",\n    value: function isEmpty() {\n      return this.empty;\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return this.nan;\n    }\n  }, {\n    key: \"isInvalidate\",\n    value: function isInvalidate() {\n      return this.isEmpty() || this.isNaN();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(target) {\n      return this.toString() === (target === null || target === void 0 ? void 0 : target.toString());\n    }\n  }, {\n    key: \"lessEquals\",\n    value: function lessEquals(target) {\n      return this.add(target.negate().toString()).toNumber() <= 0;\n    }\n  }, {\n    key: \"toNumber\",\n    value: function toNumber() {\n      if (this.isNaN()) {\n        return NaN;\n      }\n      return Number(this.toString());\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      if (!safe) {\n        return this.origin;\n      }\n      if (this.isInvalidate()) {\n        return '';\n      }\n      return trimNumber(\"\".concat(this.getMark()).concat(this.getIntegerStr(), \".\").concat(this.getDecimalStr())).fullStr;\n    }\n  }]);\n  return BigIntDecimal;\n}();\nexport { BigIntDecimal as default };", "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getNumberPrecision, isEmpty, num2str } from \"./numberUtil\";\n\n/**\n * We can remove this when IE not support anymore\n */\nvar NumberDecimal = /*#__PURE__*/function () {\n  function NumberDecimal(value) {\n    _classCallCheck(this, NumberDecimal);\n    _defineProperty(this, \"origin\", '');\n    _defineProperty(this, \"number\", void 0);\n    _defineProperty(this, \"empty\", void 0);\n    if (isEmpty(value)) {\n      this.empty = true;\n      return;\n    }\n    this.origin = String(value);\n    this.number = Number(value);\n  }\n  _createClass(NumberDecimal, [{\n    key: \"negate\",\n    value: function negate() {\n      return new NumberDecimal(-this.toNumber());\n    }\n  }, {\n    key: \"add\",\n    value: function add(value) {\n      if (this.isInvalidate()) {\n        return new NumberDecimal(value);\n      }\n      var target = Number(value);\n      if (Number.isNaN(target)) {\n        return this;\n      }\n      var number = this.number + target;\n\n      // [Legacy] Back to safe integer\n      if (number > Number.MAX_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MAX_SAFE_INTEGER);\n      }\n      if (number < Number.MIN_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MIN_SAFE_INTEGER);\n      }\n      var maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));\n      return new NumberDecimal(number.toFixed(maxPrecision));\n    }\n  }, {\n    key: \"multi\",\n    value: function multi(value) {\n      var target = Number(value);\n      if (this.isInvalidate() || Number.isNaN(target)) {\n        return new NumberDecimal(NaN);\n      }\n      var number = this.number * target;\n\n      // [Legacy] Back to safe integer\n      if (number > Number.MAX_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MAX_SAFE_INTEGER);\n      }\n      if (number < Number.MIN_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MIN_SAFE_INTEGER);\n      }\n      var maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));\n      return new NumberDecimal(number.toFixed(maxPrecision));\n    }\n  }, {\n    key: \"isEmpty\",\n    value: function isEmpty() {\n      return this.empty;\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return Number.isNaN(this.number);\n    }\n  }, {\n    key: \"isInvalidate\",\n    value: function isInvalidate() {\n      return this.isEmpty() || this.isNaN();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(target) {\n      return this.toNumber() === (target === null || target === void 0 ? void 0 : target.toNumber());\n    }\n  }, {\n    key: \"lessEquals\",\n    value: function lessEquals(target) {\n      return this.add(target.negate().toString()).toNumber() <= 0;\n    }\n  }, {\n    key: \"toNumber\",\n    value: function toNumber() {\n      return this.number;\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      if (!safe) {\n        return this.origin;\n      }\n      if (this.isInvalidate()) {\n        return '';\n      }\n      return num2str(this.number);\n    }\n  }]);\n  return NumberDecimal;\n}();\nexport { NumberDecimal as default };", "/* eslint-disable max-classes-per-file */\n\nimport BigIntDecimal from \"./BigIntDecimal\";\nimport NumberDecimal from \"./NumberDecimal\";\nimport { trimNumber } from \"./numberUtil\";\nimport { supportBigInt } from \"./supportUtil\";\n\n// Still support origin export\nexport { NumberDecimal, BigIntDecimal };\nexport default function getMiniDecimal(value) {\n  // We use BigInt here.\n  // Will fallback to Number if not support.\n  if (supportBigInt()) {\n    return new BigIntDecimal(value);\n  }\n  return new NumberDecimal(value);\n}\n\n/**\n * Align the logic of toFixed to around like 1.5 => 2.\n * If set `cutOnly`, will just remove the over decimal part.\n */\nexport function toFixed(numStr, separatorStr, precision) {\n  var cutOnly = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (numStr === '') {\n    return '';\n  }\n  var _trimNumber = trimNumber(numStr),\n    negativeStr = _trimNumber.negativeStr,\n    integerStr = _trimNumber.integerStr,\n    decimalStr = _trimNumber.decimalStr;\n  var precisionDecimalStr = \"\".concat(separatorStr).concat(decimalStr);\n  var numberWithoutDecimal = \"\".concat(negativeStr).concat(integerStr);\n  if (precision >= 0) {\n    // We will get last + 1 number to check if need advanced number\n    var advancedNum = Number(decimalStr[precision]);\n    if (advancedNum >= 5 && !cutOnly) {\n      var advancedDecimal = getMiniDecimal(numStr).add(\"\".concat(negativeStr, \"0.\").concat('0'.repeat(precision)).concat(10 - advancedNum));\n      return toFixed(advancedDecimal.toString(), separatorStr, precision, cutOnly);\n    }\n    if (precision === 0) {\n      return numberWithoutDecimal;\n    }\n    return \"\".concat(numberWithoutDecimal).concat(separatorStr).concat(decimalStr.padEnd(precision, '0').slice(0, precision));\n  }\n  if (precisionDecimalStr === '.0') {\n    return numberWithoutDecimal;\n  }\n  return \"\".concat(numberWithoutDecimal).concat(precisionDecimalStr);\n}", "import getMiniDecimal from \"./MiniDecimal\";\nexport * from \"./MiniDecimal\";\nimport { trimNumber, getNumberPrecision, num2str, validateNumber } from \"./numberUtil\";\nexport { trimNumber, getNumberPrecision, num2str, validateNumber };\nexport default getMiniDecimal;"], "mappings": ";;;;;;AAAA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAChF;;;ACDA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,cAAc,EAAE,GAAG,GAAG,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAG;AACN;;;ACXO,SAAS,gBAAgB;AAC9B,SAAO,OAAO,WAAW;AAC3B;;;ACDO,SAAS,QAAQ,OAAO;AAC7B,SAAO,CAAC,SAAS,UAAU,KAAK,CAAC,OAAO,MAAM,KAAK,KAAK,CAAC,OAAO,KAAK,EAAE,KAAK;AAC9E;AAKO,SAAS,WAAW,QAAQ;AACjC,MAAI,MAAM,OAAO,KAAK;AACtB,MAAI,WAAW,IAAI,WAAW,GAAG;AACjC,MAAI,UAAU;AACZ,UAAM,IAAI,MAAM,CAAC;AAAA,EACnB;AACA,QAAM,IAEL,QAAQ,kBAAkB,IAAI,EAE9B,QAAQ,SAAS,EAAE,EAEnB,QAAQ,OAAO,EAAE;AAClB,MAAI,IAAI,WAAW,GAAG,GAAG;AACvB,UAAM,IAAI,OAAO,GAAG;AAAA,EACtB;AACA,MAAI,UAAU,OAAO;AACrB,MAAI,cAAc,QAAQ,MAAM,GAAG;AACnC,MAAI,aAAa,YAAY,CAAC,KAAK;AACnC,MAAI,aAAa,YAAY,CAAC,KAAK;AACnC,MAAI,eAAe,OAAO,eAAe,KAAK;AAC5C,eAAW;AAAA,EACb;AACA,MAAI,cAAc,WAAW,MAAM;AACnC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,GAAG,OAAO,WAAW,EAAE,OAAO,OAAO;AAAA,EAChD;AACF;AACO,SAAS,IAAI,QAAQ;AAC1B,MAAI,MAAM,OAAO,MAAM;AACvB,SAAO,CAAC,OAAO,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,SAAS,GAAG;AACvD;AAMO,SAAS,mBAAmB,QAAQ;AACzC,MAAI,SAAS,OAAO,MAAM;AAC1B,MAAI,IAAI,MAAM,GAAG;AACf,QAAI,YAAY,OAAO,OAAO,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC7D,QAAI,eAAe,OAAO,MAAM,SAAS;AACzC,QAAI,iBAAiB,QAAQ,iBAAiB,UAAU,aAAa,CAAC,GAAG;AACvE,mBAAa,aAAa,CAAC,EAAE;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AACA,SAAO,OAAO,SAAS,GAAG,KAAK,eAAe,MAAM,IAAI,OAAO,SAAS,OAAO,QAAQ,GAAG,IAAI,IAAI;AACpG;AAKO,SAAS,QAAQ,QAAQ;AAC9B,MAAI,SAAS,OAAO,MAAM;AAC1B,MAAI,IAAI,MAAM,GAAG;AACf,QAAI,SAAS,OAAO,kBAAkB;AACpC,aAAO,OAAO,cAAc,IAAI,OAAO,MAAM,EAAE,SAAS,IAAI,OAAO,gBAAgB;AAAA,IACrF;AACA,QAAI,SAAS,OAAO,kBAAkB;AACpC,aAAO,OAAO,cAAc,IAAI,OAAO,MAAM,EAAE,SAAS,IAAI,OAAO,gBAAgB;AAAA,IACrF;AACA,aAAS,OAAO,QAAQ,mBAAmB,MAAM,CAAC;AAAA,EACpD;AACA,SAAO,WAAW,MAAM,EAAE;AAC5B;AACO,SAAS,eAAe,KAAK;AAClC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,CAAC,OAAO,MAAM,GAAG;AAAA,EAC1B;AAGA,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA;AAAA;AAAA,IAEE,wBAAwB,KAAK,GAAG;AAAA,IAEhC,kBAAkB,KAAK,GAAG;AAAA,IAE1B,kBAAkB,KAAK,GAAG;AAAA;AAE9B;;;AC5FA,IAAI,gBAA6B,WAAY;AAG3C,WAASA,eAAc,OAAO;AAC5B,oBAAgB,MAAMA,cAAa;AACnC,oBAAgB,MAAM,UAAU,EAAE;AAClC,oBAAgB,MAAM,YAAY,MAAM;AACxC,oBAAgB,MAAM,WAAW,MAAM;AACvC,oBAAgB,MAAM,WAAW,MAAM;AACvC,oBAAgB,MAAM,cAAc,MAAM;AAC1C,oBAAgB,MAAM,SAAS,MAAM;AACrC,oBAAgB,MAAM,OAAO,MAAM;AACnC,QAAI,QAAQ,KAAK,GAAG;AAClB,WAAK,QAAQ;AACb;AAAA,IACF;AACA,SAAK,SAAS,OAAO,KAAK;AAG1B,QAAI,UAAU,OAAO,OAAO,MAAM,KAAK,GAAG;AACxC,WAAK,MAAM;AACX;AAAA,IACF;AACA,QAAI,cAAc;AAGlB,QAAI,IAAI,WAAW,GAAG;AACpB,oBAAc,OAAO,WAAW;AAAA,IAClC;AACA,kBAAc,OAAO,gBAAgB,WAAW,cAAc,QAAQ,WAAW;AACjF,QAAI,eAAe,WAAW,GAAG;AAC/B,UAAI,UAAU,WAAW,WAAW;AACpC,WAAK,WAAW,QAAQ;AACxB,UAAI,UAAU,QAAQ,QAAQ,MAAM,GAAG;AACvC,WAAK,UAAU,OAAO,QAAQ,CAAC,CAAC;AAChC,UAAI,aAAa,QAAQ,CAAC,KAAK;AAC/B,WAAK,UAAU,OAAO,UAAU;AAChC,WAAK,aAAa,WAAW;AAAA,IAC/B,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACA,eAAaA,gBAAe,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,aAAO,KAAK,WAAW,MAAM;AAAA,IAC/B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,KAAK,QAAQ,SAAS;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,EAKF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,gBAAgB;AAC9B,aAAO,KAAK,QAAQ,SAAS,EAAE,SAAS,KAAK,YAAY,GAAG;AAAA,IAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,eAAe;AAC1C,UAAI,MAAM,GAAG,OAAO,KAAK,QAAQ,CAAC,EAAE,OAAO,KAAK,cAAc,CAAC,EAAE,OAAO,KAAK,cAAc,EAAE,OAAO,eAAe,GAAG,CAAC;AACvH,aAAO,OAAO,GAAG;AAAA,IACnB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,QAAQ,IAAIA,eAAc,KAAK,SAAS,CAAC;AAC7C,YAAM,WAAW,CAAC,MAAM;AACxB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,QAAQ,YAAY,eAAe;AACrD,UAAI,mBAAmB,KAAK,IAAI,KAAK,cAAc,EAAE,QAAQ,OAAO,cAAc,EAAE,MAAM;AAC1F,UAAI,mBAAmB,KAAK,aAAa,gBAAgB;AACzD,UAAI,uBAAuB,OAAO,aAAa,gBAAgB;AAC/D,UAAI,WAAW,WAAW,kBAAkB,oBAAoB,EAAE,SAAS;AAC3E,UAAI,oBAAoB,cAAc,gBAAgB;AAGtD,UAAI,cAAc,WAAW,QAAQ,GACnC,cAAc,YAAY,aAC1B,UAAU,YAAY;AACxB,UAAI,kBAAkB,GAAG,OAAO,WAAW,EAAE,OAAO,QAAQ,SAAS,oBAAoB,GAAG,GAAG,CAAC;AAChG,aAAO,IAAIA,eAAc,GAAG,OAAO,gBAAgB,MAAM,GAAG,CAAC,iBAAiB,GAAG,GAAG,EAAE,OAAO,gBAAgB,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAAA,IACzI;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,OAAO;AACzB,UAAI,KAAK,aAAa,GAAG;AACvB,eAAO,IAAIA,eAAc,KAAK;AAAA,MAChC;AACA,UAAI,SAAS,IAAIA,eAAc,KAAK;AACpC,UAAI,OAAO,aAAa,GAAG;AACzB,eAAO;AAAA,MACT;AACA,aAAO,KAAK,IAAI,QAAQ,SAAU,MAAM,MAAM;AAC5C,eAAO,OAAO;AAAA,MAChB,GAAG,SAAU,KAAK;AAChB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,MAAM,OAAO;AAC3B,UAAI,SAAS,IAAIA,eAAc,KAAK;AACpC,UAAI,KAAK,aAAa,KAAK,OAAO,aAAa,GAAG;AAChD,eAAO,IAAIA,eAAc,GAAG;AAAA,MAC9B;AACA,aAAO,KAAK,IAAI,QAAQ,SAAU,MAAM,MAAM;AAC5C,eAAO,OAAO;AAAA,MAChB,GAAG,SAAU,KAAK;AAChB,eAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAASC,WAAU;AACxB,aAAO,KAAK;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,aAAO,KAAK;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,KAAK,QAAQ,KAAK,KAAK,MAAM;AAAA,IACtC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,QAAQ;AAC7B,aAAO,KAAK,SAAS,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAAA,IAC9F;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,QAAQ;AACjC,aAAO,KAAK,IAAI,OAAO,OAAO,EAAE,SAAS,CAAC,EAAE,SAAS,KAAK;AAAA,IAC5D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,UAAI,KAAK,MAAM,GAAG;AAChB,eAAO;AAAA,MACT;AACA,aAAO,OAAO,KAAK,SAAS,CAAC;AAAA,IAC/B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,UAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC/E,UAAI,CAAC,MAAM;AACT,eAAO,KAAK;AAAA,MACd;AACA,UAAI,KAAK,aAAa,GAAG;AACvB,eAAO;AAAA,MACT;AACA,aAAO,WAAW,GAAG,OAAO,KAAK,QAAQ,CAAC,EAAE,OAAO,KAAK,cAAc,GAAG,GAAG,EAAE,OAAO,KAAK,cAAc,CAAC,CAAC,EAAE;AAAA,IAC9G;AAAA,EACF,CAAC,CAAC;AACF,SAAOD;AACT,EAAE;;;ACvKF,IAAI,gBAA6B,WAAY;AAC3C,WAASE,eAAc,OAAO;AAC5B,oBAAgB,MAAMA,cAAa;AACnC,oBAAgB,MAAM,UAAU,EAAE;AAClC,oBAAgB,MAAM,UAAU,MAAM;AACtC,oBAAgB,MAAM,SAAS,MAAM;AACrC,QAAI,QAAQ,KAAK,GAAG;AAClB,WAAK,QAAQ;AACb;AAAA,IACF;AACA,SAAK,SAAS,OAAO,KAAK;AAC1B,SAAK,SAAS,OAAO,KAAK;AAAA,EAC5B;AACA,eAAaA,gBAAe,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,aAAO,IAAIA,eAAc,CAAC,KAAK,SAAS,CAAC;AAAA,IAC3C;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,OAAO;AACzB,UAAI,KAAK,aAAa,GAAG;AACvB,eAAO,IAAIA,eAAc,KAAK;AAAA,MAChC;AACA,UAAI,SAAS,OAAO,KAAK;AACzB,UAAI,OAAO,MAAM,MAAM,GAAG;AACxB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,SAAS;AAG3B,UAAI,SAAS,OAAO,kBAAkB;AACpC,eAAO,IAAIA,eAAc,OAAO,gBAAgB;AAAA,MAClD;AACA,UAAI,SAAS,OAAO,kBAAkB;AACpC,eAAO,IAAIA,eAAc,OAAO,gBAAgB;AAAA,MAClD;AACA,UAAI,eAAe,KAAK,IAAI,mBAAmB,KAAK,MAAM,GAAG,mBAAmB,MAAM,CAAC;AACvF,aAAO,IAAIA,eAAc,OAAO,QAAQ,YAAY,CAAC;AAAA,IACvD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,MAAM,OAAO;AAC3B,UAAI,SAAS,OAAO,KAAK;AACzB,UAAI,KAAK,aAAa,KAAK,OAAO,MAAM,MAAM,GAAG;AAC/C,eAAO,IAAIA,eAAc,GAAG;AAAA,MAC9B;AACA,UAAI,SAAS,KAAK,SAAS;AAG3B,UAAI,SAAS,OAAO,kBAAkB;AACpC,eAAO,IAAIA,eAAc,OAAO,gBAAgB;AAAA,MAClD;AACA,UAAI,SAAS,OAAO,kBAAkB;AACpC,eAAO,IAAIA,eAAc,OAAO,gBAAgB;AAAA,MAClD;AACA,UAAI,eAAe,KAAK,IAAI,mBAAmB,KAAK,MAAM,GAAG,mBAAmB,MAAM,CAAC;AACvF,aAAO,IAAIA,eAAc,OAAO,QAAQ,YAAY,CAAC;AAAA,IACvD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAASC,WAAU;AACxB,aAAO,KAAK;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,aAAO,OAAO,MAAM,KAAK,MAAM;AAAA,IACjC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC7B,aAAO,KAAK,QAAQ,KAAK,KAAK,MAAM;AAAA,IACtC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,QAAQ;AAC7B,aAAO,KAAK,SAAS,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAAA,IAC9F;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,QAAQ;AACjC,aAAO,KAAK,IAAI,OAAO,OAAO,EAAE,SAAS,CAAC,EAAE,SAAS,KAAK;AAAA,IAC5D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,aAAO,KAAK;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,UAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC/E,UAAI,CAAC,MAAM;AACT,eAAO,KAAK;AAAA,MACd;AACA,UAAI,KAAK,aAAa,GAAG;AACvB,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,KAAK,MAAM;AAAA,IAC5B;AAAA,EACF,CAAC,CAAC;AACF,SAAOD;AACT,EAAE;;;ACtGa,SAAR,eAAgC,OAAO;AAG5C,MAAI,cAAc,GAAG;AACnB,WAAO,IAAI,cAAc,KAAK;AAAA,EAChC;AACA,SAAO,IAAI,cAAc,KAAK;AAChC;AAMO,SAAS,QAAQ,QAAQ,cAAc,WAAW;AACvD,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,MAAI,WAAW,IAAI;AACjB,WAAO;AAAA,EACT;AACA,MAAI,cAAc,WAAW,MAAM,GACjC,cAAc,YAAY,aAC1B,aAAa,YAAY,YACzB,aAAa,YAAY;AAC3B,MAAI,sBAAsB,GAAG,OAAO,YAAY,EAAE,OAAO,UAAU;AACnE,MAAI,uBAAuB,GAAG,OAAO,WAAW,EAAE,OAAO,UAAU;AACnE,MAAI,aAAa,GAAG;AAElB,QAAI,cAAc,OAAO,WAAW,SAAS,CAAC;AAC9C,QAAI,eAAe,KAAK,CAAC,SAAS;AAChC,UAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,GAAG,OAAO,aAAa,IAAI,EAAE,OAAO,IAAI,OAAO,SAAS,CAAC,EAAE,OAAO,KAAK,WAAW,CAAC;AACpI,aAAO,QAAQ,gBAAgB,SAAS,GAAG,cAAc,WAAW,OAAO;AAAA,IAC7E;AACA,QAAI,cAAc,GAAG;AACnB,aAAO;AAAA,IACT;AACA,WAAO,GAAG,OAAO,oBAAoB,EAAE,OAAO,YAAY,EAAE,OAAO,WAAW,OAAO,WAAW,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC;AAAA,EAC1H;AACA,MAAI,wBAAwB,MAAM;AAChC,WAAO;AAAA,EACT;AACA,SAAO,GAAG,OAAO,oBAAoB,EAAE,OAAO,mBAAmB;AACnE;;;AC7CA,IAAO,aAAQ;", "names": ["BigIntDecimal", "isEmpty", "NumberDecimal", "isEmpty"]}