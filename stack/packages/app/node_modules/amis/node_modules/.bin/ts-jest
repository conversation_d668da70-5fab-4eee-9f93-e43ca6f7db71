#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.3_@jest+transform@29.7.0_@jest+types@30.0.5_babel-jest@29.7.0_ce52oznlmqjqrs4k424fndj6c4/node_modules/ts-jest/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.3_@jest+transform@29.7.0_@jest+types@30.0.5_babel-jest@29.7.0_ce52oznlmqjqrs4k424fndj6c4/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.3_@jest+transform@29.7.0_@jest+types@30.0.5_babel-jest@29.7.0_ce52oznlmqjqrs4k424fndj6c4/node_modules/ts-jest/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.3_@jest+transform@29.7.0_@jest+types@30.0.5_babel-jest@29.7.0_ce52oznlmqjqrs4k424fndj6c4/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
