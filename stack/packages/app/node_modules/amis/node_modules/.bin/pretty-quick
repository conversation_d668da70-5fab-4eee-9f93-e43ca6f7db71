#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/pretty-quick@3.3.1_prettier@2.8.8/node_modules/pretty-quick/dist/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/pretty-quick@3.3.1_prettier@2.8.8/node_modules/pretty-quick/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/pretty-quick@3.3.1_prettier@2.8.8/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/pretty-quick@3.3.1_prettier@2.8.8/node_modules/pretty-quick/dist/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/pretty-quick@3.3.1_prettier@2.8.8/node_modules/pretty-quick/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/pretty-quick@3.3.1_prettier@2.8.8/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../pretty-quick/dist/cli.js" "$@"
else
  exec node  "$basedir/../pretty-quick/dist/cli.js" "$@"
fi
