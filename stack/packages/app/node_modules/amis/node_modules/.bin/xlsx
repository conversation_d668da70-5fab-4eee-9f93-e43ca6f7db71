#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/xlsx@0.18.5/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/xlsx@0.18.5/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../xlsx/bin/xlsx.njs" "$@"
else
  exec node  "$basedir/../xlsx/bin/xlsx.njs" "$@"
fi
