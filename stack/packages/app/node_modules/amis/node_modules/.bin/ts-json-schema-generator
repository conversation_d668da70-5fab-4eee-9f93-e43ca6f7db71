#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-json-schema-generator@2.4.0/node_modules/ts-json-schema-generator/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-json-schema-generator@2.4.0/node_modules/ts-json-schema-generator/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-json-schema-generator@2.4.0/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-json-schema-generator@2.4.0/node_modules/ts-json-schema-generator/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-json-schema-generator@2.4.0/node_modules/ts-json-schema-generator/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/ts-json-schema-generator@2.4.0/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-json-schema-generator/bin/ts-json-schema-generator.js" "$@"
else
  exec node  "$basedir/../ts-json-schema-generator/bin/ts-json-schema-generator.js" "$@"
fi
