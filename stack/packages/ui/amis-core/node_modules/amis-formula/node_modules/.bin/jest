#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/jest@29.7.0_@types+node@14.18.63_ts-node@10.9.2_@swc+core@1.13.5_@types+node@14.18.63_typescript@4.9.5_/node_modules/jest/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/jest@29.7.0_@types+node@14.18.63_ts-node@10.9.2_@swc+core@1.13.5_@types+node@14.18.63_typescript@4.9.5_/node_modules/jest/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/jest@29.7.0_@types+node@14.18.63_ts-node@10.9.2_@swc+core@1.13.5_@types+node@14.18.63_typescript@4.9.5_/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/jest@29.7.0_@types+node@14.18.63_ts-node@10.9.2_@swc+core@1.13.5_@types+node@14.18.63_typescript@4.9.5_/node_modules/jest/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/jest@29.7.0_@types+node@14.18.63_ts-node@10.9.2_@swc+core@1.13.5_@types+node@14.18.63_typescript@4.9.5_/node_modules/jest/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/jest@29.7.0_@types+node@14.18.63_ts-node@10.9.2_@swc+core@1.13.5_@types+node@14.18.63_typescript@4.9.5_/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jest/bin/jest.js" "$@"
else
  exec node  "$basedir/../jest/bin/jest.js" "$@"
fi
