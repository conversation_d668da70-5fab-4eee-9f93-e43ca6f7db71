/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _Switch = require('amis-ui/lib/components/Switch');
var _Icon = require('amis-ui/lib/components/Icon');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _Switch__default = /*#__PURE__*/_interopDefaultLegacy(_Switch);
var _Icon__default = /*#__PURE__*/_interopDefaultLegacy(_Icon);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);

var SwitchControl = /** @class */function (_super) {
  tslib.__extends(SwitchControl, _super);
  function SwitchControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  SwitchControl.prototype.handleChange = function (checked) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, dispatchEvent, onChange, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, dispatchEvent = _a.dispatchEvent, onChange = _a.onChange;
            return [4 /*yield*/, dispatchEvent('change', amisCore.resolveEventData(this.props, {
              value: checked
            }))];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            onChange && onChange(checked);
            return [2 /*return*/];
        }
      });
    });
  };
  SwitchControl.prototype.getResult = function () {
    var _a = this.props,
      cx = _a.classnames,
      render = _a.render,
      onText = _a.onText,
      offText = _a.offText;
    var onComp = onText;
    var offComp = offText;
    /** 兼容单独使用Icon的场景 */
    if (amisCore.isObject(onText) && onText.icon && !onText.type) {
      onComp = jsxRuntime.jsx(_Icon__default["default"], {
        cx: cx,
        icon: onText.icon,
        className: "Switch-icon"
      });
    } else if (onText != null && typeof onText !== 'string') {
      /** 兼容原来的DOM接口，string类型直接渲染 */
      onComp = render('switch-on-text', onText);
    }
    if (amisCore.isObject(offText) && offText.icon && !offText.type) {
      offComp = jsxRuntime.jsx(_Icon__default["default"], {
        cx: cx,
        icon: offText.icon,
        className: "Switch-icon"
      });
    } else if (offText != null && typeof offText !== 'string') {
      offComp = render('switch-off-text', offText);
    }
    return {
      on: onComp,
      off: offComp
    };
  };
  SwitchControl.prototype.renderBody = function (children) {
    var _a = this.props,
      cx = _a.classnames,
      option = _a.option,
      optionAtLeft = _a.optionAtLeft;
    var Option = jsxRuntime.jsx("span", tslib.__assign({
      className: cx('Switch-option')
    }, {
      children: option
    }));
    return jsxRuntime.jsxs(jsxRuntime.Fragment, {
      children: [optionAtLeft ? Option : null, children, optionAtLeft ? null : Option]
    });
  };
  SwitchControl.prototype.renderStatic = function () {
    var _a = this.props,
      value = _a.value,
      trueValue = _a.trueValue;
    var _b = this.getResult(),
      _c = _b.on,
      on = _c === void 0 ? '开' : _c,
      _d = _b.off,
      off = _d === void 0 ? '关' : _d;
    var body = jsxRuntime.jsx("span", {
      children: value === trueValue ? on : off
    });
    return this.renderBody(body);
  };
  SwitchControl.prototype.doAction = function (action, data, throwErrors, args) {
    var _a, _b;
    if (throwErrors === void 0) {
      throwErrors = false;
    }
    var actionType = action === null || action === void 0 ? void 0 : action.actionType;
    var _c = this.props,
      onChange = _c.onChange,
      formStore = _c.formStore,
      store = _c.store,
      name = _c.name,
      resetValue = _c.resetValue;
    if (actionType === 'clear') {
      onChange === null || onChange === void 0 ? void 0 : onChange(''); // switch的value可能是任何类型，空值可能是'' or null，但因为form的clear的清空现在是''，则这里为了保持一致也用''
    } else if (actionType === 'reset') {
      var pristineVal = (_b = amisCore.getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
      onChange === null || onChange === void 0 ? void 0 : onChange(pristineVal);
    }
  };
  SwitchControl.prototype.render = function () {
    var _a = this.props,
      size = _a.size,
      className = _a.className,
      style = _a.style,
      ns = _a.classPrefix,
      cx = _a.classnames,
      value = _a.value,
      trueValue = _a.trueValue,
      falseValue = _a.falseValue,
      onChange = _a.onChange,
      disabled = _a.disabled,
      loading = _a.loading,
      loadingConfig = _a.loadingConfig,
      testIdBuilder = _a.testIdBuilder;
    var _b = this.getResult(),
      on = _b.on,
      off = _b.off;
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx("SwitchControl", className)
    }, {
      children: this.renderBody(jsxRuntime.jsx(_Switch__default["default"], {
        classPrefix: ns,
        value: value,
        trueValue: trueValue,
        falseValue: falseValue,
        onText: on,
        offText: off,
        disabled: disabled,
        onChange: this.handleChange,
        size: size,
        loading: loading,
        loadingConfig: loadingConfig,
        testIdBuilder: testIdBuilder
      }))
    }));
  };
  SwitchControl.defaultProps = {
    trueValue: true,
    falseValue: false,
    optionAtLeft: false
  };
  tslib.__decorate([amisCore.autobind], SwitchControl.prototype, "handleChange", null);
  tslib.__decorate([StaticHoc.supportStatic()], SwitchControl.prototype, "render", null);
  return SwitchControl;
}(React__default["default"].Component);
var SwitchControlRenderer = /** @class */function (_super) {
  tslib.__extends(SwitchControlRenderer, _super);
  function SwitchControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  SwitchControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'switch',
    sizeMutable: false,
    thin: true
  })], SwitchControlRenderer);
  return SwitchControlRenderer;
}(SwitchControl);

exports.SwitchControlRenderer = SwitchControlRenderer;
exports["default"] = SwitchControl;
