/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _Spinner = require('amis-ui/lib/components/Spinner');
var _TransferPicker = require('amis-ui/lib/components/TransferPicker');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var amisCore = require('amis-core');
var Transfer = require('./Transfer.js');
var StaticHoc = require('./StaticHoc.js');
var pick = require('lodash/pick');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _Spinner__default = /*#__PURE__*/_interopDefaultLegacy(_Spinner);
var _TransferPicker__default = /*#__PURE__*/_interopDefaultLegacy(_TransferPicker);
var pick__default = /*#__PURE__*/_interopDefaultLegacy(pick);

var TransferPickerRenderer = /** @class */function (_super) {
  tslib.__extends(TransferPickerRenderer, _super);
  function TransferPickerRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  TransferPickerRenderer.prototype.dispatchEvent = function (name) {
    var _a = this.props,
      dispatchEvent = _a.dispatchEvent,
      value = _a.value;
    dispatchEvent(name, amisCore.resolveEventData(this.props, {
      value: value
    }));
  };
  TransferPickerRenderer.prototype.onItemClick = function (item) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var dispatchEvent, rendererEvent;
      return tslib.__generator(this, function (_a) {
        switch (_a.label) {
          case 0:
            dispatchEvent = this.props.dispatchEvent;
            return [4 /*yield*/, dispatchEvent('itemClick', amisCore.resolveEventData(this.props, {
              item: item
            }))];
          case 1:
            rendererEvent = _a.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            return [2 /*return*/];
        }
      });
    });
  };
  // 动作
  TransferPickerRenderer.prototype.doAction = function (action) {
    var _a, _b, _c;
    var _d = this.props,
      resetValue = _d.resetValue,
      onChange = _d.onChange,
      formStore = _d.formStore,
      store = _d.store,
      name = _d.name;
    switch (action.actionType) {
      case 'clear':
        onChange === null || onChange === void 0 ? void 0 : onChange('');
        break;
      case 'reset':
        onChange === null || onChange === void 0 ? void 0 : onChange((_c = (_b = amisCore.getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue) !== null && _c !== void 0 ? _c : '');
        break;
    }
  };
  TransferPickerRenderer.prototype.render = function () {
    var _this = this;
    var _a;
    var _b = this.props,
      className = _b.className,
      style = _b.style,
      cx = _b.classnames,
      selectedOptions = _b.selectedOptions,
      sortable = _b.sortable,
      loading = _b.loading,
      searchable = _b.searchable,
      searchResultMode = _b.searchResultMode,
      showArrow = _b.showArrow,
      deferLoad = _b.deferLoad,
      disabled = _b.disabled,
      selectTitle = _b.selectTitle,
      resultTitle = _b.resultTitle,
      pickerSize = _b.pickerSize,
      columns = _b.columns,
      leftMode = _b.leftMode,
      selectMode = _b.selectMode,
      borderMode = _b.borderMode,
      itemHeight = _b.itemHeight,
      virtualThreshold = _b.virtualThreshold,
      loadingConfig = _b.loadingConfig,
      _c = _b.labelField,
      labelField = _c === void 0 ? 'label' : _c,
      _d = _b.valueField,
      valueField = _d === void 0 ? 'value' : _d,
      _e = _b.deferField,
      deferField = _e === void 0 ? 'defer' : _e,
      menuTpl = _b.menuTpl,
      valueTpl = _b.valueTpl,
      mobileUI = _b.mobileUI,
      env = _b.env,
      maxTagCount = _b.maxTagCount,
      overflowTagPopover = _b.overflowTagPopover,
      pagination = _b.pagination,
      formItem = _b.formItem,
      data = _b.data,
      popOverContainer = _b.popOverContainer,
      placeholder = _b.placeholder,
      onlyChildren = _b.onlyChildren,
      _f = _b.autoCheckChildren,
      autoCheckChildren = _f === void 0 ? true : _f,
      _g = _b.initiallyOpen,
      initiallyOpen = _g === void 0 ? true : _g,
      searchPlaceholder = _b.searchPlaceholder;
    // 目前 LeftOptions 没有接口可以动态加载
    // 为了方便可以快速实现动态化，让选项的第一个成员携带
    // LeftOptions 信息
    var _h = this.props,
      options = _h.options,
      leftOptions = _h.leftOptions,
      leftDefaultValue = _h.leftDefaultValue;
    if (selectMode === 'associated' && options && options.length && options[0].leftOptions && Array.isArray(options[0].children)) {
      leftOptions = options[0].leftOptions;
      leftDefaultValue = (_a = options[0].leftDefaultValue) !== null && _a !== void 0 ? _a : leftDefaultValue;
      options = options[0].children;
    }
    return jsxRuntime.jsxs("div", tslib.__assign({
      className: cx('TransferControl', className)
    }, {
      children: [jsxRuntime.jsx(_TransferPicker__default["default"], {
        placeholder: placeholder,
        borderMode: borderMode,
        selectMode: selectMode,
        onlyChildren: onlyChildren,
        value: selectedOptions,
        disabled: disabled,
        options: options,
        onItemClick: this.onItemClick,
        onChange: this.handleChange,
        option2value: this.option2value,
        sortable: sortable,
        searchResultMode: searchResultMode,
        onSearch: searchable ? this.handleSearch : undefined,
        searchPlaceholder: searchPlaceholder,
        showArrow: showArrow,
        onDeferLoad: deferLoad,
        selectTitle: selectTitle,
        resultTitle: resultTitle,
        size: pickerSize,
        columns: columns,
        leftMode: leftMode,
        leftOptions: leftOptions,
        optionItemRender: menuTpl ? this.optionItemRender : undefined,
        resultItemRender: valueTpl ? this.resultItemRender : undefined,
        onFocus: function () {
          return _this.dispatchEvent('focus');
        },
        onBlur: function () {
          return _this.dispatchEvent('blur');
        },
        labelField: labelField,
        valueField: valueField,
        deferField: deferField,
        itemHeight: amisCore.toNumber(itemHeight) > 0 ? amisCore.toNumber(itemHeight) : undefined,
        virtualThreshold: virtualThreshold,
        mobileUI: mobileUI,
        popOverContainer: env === null || env === void 0 ? void 0 : env.getModalContainer,
        maxTagCount: maxTagCount,
        overflowTagPopover: overflowTagPopover,
        pagination: tslib.__assign(tslib.__assign({}, pick__default["default"](pagination, ['layout', 'perPageAvailable', 'popOverContainerSelector'])), {
          className: pagination === null || pagination === void 0 ? void 0 : pagination.className,
          enable: (pagination && pagination.enable !== undefined ? !!(typeof pagination.enable === 'string' ? amisCore.evalExpression(pagination.enable, data) : pagination.enable) : !!(formItem === null || formItem === void 0 ? void 0 : formItem.enableSourcePagination)) && (!selectMode || selectMode === 'list' || selectMode === 'table') && options.length > 0,
          maxButtons: Number.isInteger(pagination === null || pagination === void 0 ? void 0 : pagination.maxButtons) ? pagination === null || pagination === void 0 ? void 0 : pagination.maxButtons : 5,
          page: formItem === null || formItem === void 0 ? void 0 : formItem.sourcePageNum,
          perPage: formItem === null || formItem === void 0 ? void 0 : formItem.sourcePerPageNum,
          total: formItem === null || formItem === void 0 ? void 0 : formItem.sourceTotalNum,
          popOverContainer: popOverContainer !== null && popOverContainer !== void 0 ? popOverContainer : env === null || env === void 0 ? void 0 : env.getModalContainer
        }),
        onPageChange: this.handlePageChange,
        autoCheckChildren: autoCheckChildren,
        initiallyOpen: initiallyOpen
      }), jsxRuntime.jsx(_Spinner__default["default"], {
        loadingConfig: loadingConfig,
        overlay: true,
        show: loading
      }, "info")]
    }));
  };
  tslib.__decorate([amisCore.autobind], TransferPickerRenderer.prototype, "dispatchEvent", null);
  tslib.__decorate([amisCore.autobind
  // 增加点击选项事件函数
  ], TransferPickerRenderer.prototype, "onItemClick", null);
  tslib.__decorate([StaticHoc.supportStatic()], TransferPickerRenderer.prototype, "render", null);
  TransferPickerRenderer = tslib.__decorate([amisCore.OptionsControl({
    type: 'transfer-picker'
  })], TransferPickerRenderer);
  return TransferPickerRenderer;
}(Transfer.BaseTransferRenderer);

exports.TransferPickerRenderer = TransferPickerRenderer;
