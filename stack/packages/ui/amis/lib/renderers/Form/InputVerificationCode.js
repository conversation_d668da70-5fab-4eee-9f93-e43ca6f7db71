/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _VerificationCode = require('amis-ui/lib/components/VerificationCode');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _VerificationCode__default = /*#__PURE__*/_interopDefaultLegacy(_VerificationCode);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);

var VerificationCodeControl = /** @class */function (_super) {
  tslib.__extends(VerificationCodeControl, _super);
  function VerificationCodeControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  /**
   * actions finish
   * @date 2024-06-04 星期二
   * @function
   * @param {}
   * @return {}
   */
  VerificationCodeControl.prototype.onFinish = function (value) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, dispatchEvent, data, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
            return [4 /*yield*/, dispatchEvent('finish', tslib.__assign(tslib.__assign({}, data), {
              value: value
            }), this)];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * actions change
   * @date 2024-06-04 星期二
   * @function
   * @param {}
   * @return {}
   */
  VerificationCodeControl.prototype.onChange = function (value) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, onChange, data, dispatchEvent, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, onChange = _a.onChange, data = _a.data, dispatchEvent = _a.dispatchEvent;
            return [4 /*yield*/, dispatchEvent('change', tslib.__assign(tslib.__assign({}, data), {
              value: value
            }))];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            onChange === null || onChange === void 0 ? void 0 : onChange(value);
            return [2 /*return*/];
        }
      });
    });
  };
  VerificationCodeControl.prototype.render = function () {
    var separator = this.props.separator;
    return jsxRuntime.jsx(_VerificationCode__default["default"], tslib.__assign({}, this.props, {
      separator: typeof separator === 'string' ? function (data) {
        return amisCore.resolveVariableAndFilter(separator, data);
      } : function () {},
      onFinish: this.onFinish,
      onChange: this.onChange
    }));
  };
  tslib.__decorate([amisCore.autobind], VerificationCodeControl.prototype, "onFinish", null);
  tslib.__decorate([amisCore.autobind], VerificationCodeControl.prototype, "onChange", null);
  return VerificationCodeControl;
}(React__default["default"].Component);
var VerificationCodeControlRenderer = /** @class */function (_super) {
  tslib.__extends(VerificationCodeControlRenderer, _super);
  function VerificationCodeControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  VerificationCodeControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-verification-code'
  })], VerificationCodeControlRenderer);
  return VerificationCodeControlRenderer;
}(VerificationCodeControl);

exports.VerificationCodeControlRenderer = VerificationCodeControlRenderer;
exports["default"] = VerificationCodeControl;
