/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _Button = require('amis-ui/lib/components/Button');
var _Modal = require('amis-ui/lib/components/Modal');
var _Spinner = require('amis-ui/lib/components/Spinner');
var _SearchBox = require('amis-ui/lib/components/SearchBox');
var _Icon = require('amis-ui/lib/components/Icon');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var cx = require('classnames');
var matchSorter = require('match-sorter');
var amisCore = require('amis-core');
var debounce = require('lodash/debounce');
var IconSelectStore = require('./IconSelectStore.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _Button__default = /*#__PURE__*/_interopDefaultLegacy(_Button);
var _Modal__default = /*#__PURE__*/_interopDefaultLegacy(_Modal);
var _Spinner__default = /*#__PURE__*/_interopDefaultLegacy(_Spinner);
var _SearchBox__default = /*#__PURE__*/_interopDefaultLegacy(_SearchBox);
var _Icon__default = /*#__PURE__*/_interopDefaultLegacy(_Icon);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);
var debounce__default = /*#__PURE__*/_interopDefaultLegacy(debounce);

/**
 * 新图标选择器
 */
var IconSelectControl = /** @class */function (_super) {
  tslib.__extends(IconSelectControl, _super);
  function IconSelectControl(props) {
    var _this = _super.call(this, props) || this;
    _this.state = {
      activeTypeIndex: 0,
      showModal: false,
      tmpCheckIconId: null,
      searchValue: '',
      isRefreshLoading: false
    };
    _this.handleSearchValueChange = debounce__default["default"](_this.handleSearchValueChange.bind(_this), 300);
    return _this;
  }
  IconSelectControl.prototype.getSvgName = function (value) {
    var _a;
    if (typeof value === 'string') {
      return ((_a = /data-name="(.*?)"/.exec(value)) === null || _a === void 0 ? void 0 : _a[1]) || '';
    } else {
      return (value === null || value === void 0 ? void 0 : value.name) || (value === null || value === void 0 ? void 0 : value.id) || '';
    }
  };
  IconSelectControl.prototype.getSvgId = function (value) {
    var _a;
    if (typeof value === 'string') {
      return ((_a = /data-id="(.*?)"/.exec(value)) === null || _a === void 0 ? void 0 : _a[1]) || '';
    } else {
      return (value === null || value === void 0 ? void 0 : value.id) || '';
    }
  };
  IconSelectControl.prototype.getValueBySvg = function (svg) {
    var _a;
    if (!svg) {
      return null;
    }
    if (typeof svg !== 'string') {
      return tslib.__assign(tslib.__assign({}, svg), {
        svg: (_a = svg.svg) === null || _a === void 0 ? void 0 : _a.replace(/'/g, '')
      });
    }
    var svgName = this.getSvgName(svg);
    var svgId = this.getSvgId(svg);
    return {
      name: svgName,
      id: svgId,
      svg: svg.replace(/'/g, '')
    };
  };
  IconSelectControl.prototype.handleClick = function () {
    if (this.props.disabled) {
      return;
    }
    this.toggleModel(true);
  };
  IconSelectControl.prototype.handleClear = function (e) {
    e.preventDefault();
    e.stopPropagation();
    this.props.onChange && this.props.onChange('');
  };
  IconSelectControl.prototype.renderInputArea = function () {
    var _a = this.props,
      ns = _a.classPrefix,
      disabled = _a.disabled,
      value = _a.value,
      placeholder = _a.placeholder,
      clearable = _a.clearable;
    var svg = this.getValueBySvg(value);
    return jsxRuntime.jsxs("div", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "IconSelectControl-input-area"))
    }, {
      children: [jsxRuntime.jsx("div", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-input-icon-show"))
      }, {
        children: jsxRuntime.jsx(_Icon__default["default"], {
          icon: svg === null || svg === void 0 ? void 0 : svg.svg,
          className: "icon"
        })
      })), jsxRuntime.jsx("span", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-input-icon-id"))
      }, {
        children: svg === null || svg === void 0 ? void 0 : svg.name
      })), clearable && !disabled && svg ? jsxRuntime.jsx("a", tslib.__assign({
        onClick: this.handleClear,
        className: cx__default["default"]("".concat(ns, "IconSelectControl-clear"))
      }, {
        children: jsxRuntime.jsx(_Icon__default["default"], {
          icon: "input-clear",
          className: "icon"
        })
      })) : null, !svg && placeholder && jsxRuntime.jsx("span", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-input-icon-placeholder"))
      }, {
        children: placeholder
      })) || null]
    }));
  };
  IconSelectControl.prototype.handleIconTypeClick = function (item, index) {
    this.setState({
      activeTypeIndex: index
    });
  };
  IconSelectControl.prototype.renderIconTypes = function () {
    var _this = this;
    var ns = this.props.classPrefix;
    var types = IconSelectStore.svgIcons.map(function (item) {
      return {
        id: item.groupId,
        label: item.name
      };
    });
    return jsxRuntime.jsx("ul", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "IconSelectControl-type-list"))
    }, {
      children: types.map(function (item, index) {
        return jsxRuntime.jsx("li", tslib.__assign({
          onClick: function () {
            return _this.handleIconTypeClick(item, index);
          },
          className: cx__default["default"]({
            active: index === _this.state.activeTypeIndex
          })
        }, {
          children: item.label
        }), item.id);
      })
    }));
  };
  IconSelectControl.prototype.handleConfirm = function () {
    var checkedIcon = this.state.tmpCheckIconId;
    if (this.props.returnSvg) {
      var svg = checkedIcon && checkedIcon.svg || '';
      svg = svg.replace(/<svg/, "<svg data-name=\"".concat(checkedIcon === null || checkedIcon === void 0 ? void 0 : checkedIcon.name, "\" data-id=\"").concat(checkedIcon === null || checkedIcon === void 0 ? void 0 : checkedIcon.id, "\""));
      if (this.props.noSize) {
        svg = svg.replace(/width=".*?"/, '').replace(/height=".*?"/, '');
      }
      this.props.onChange && this.props.onChange(svg);
    } else {
      this.props.onChange && this.props.onChange(checkedIcon && checkedIcon.id ? tslib.__assign(tslib.__assign({}, checkedIcon), {
        id: 'svg-' + checkedIcon.id
      }) : '');
    }
    this.toggleModel(false);
  };
  IconSelectControl.prototype.handleLocalUpload = function (icon) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      return tslib.__generator(this, function (_a) {
        this.props.onChange && this.props.onChange(icon);
        this.toggleModel(false);
        return [2 /*return*/];
      });
    });
  };
  IconSelectControl.prototype.handleClickIconInModal = function (icon) {
    var _a;
    this.setState({
      tmpCheckIconId: (icon === null || icon === void 0 ? void 0 : icon.id) === ((_a = this.state.tmpCheckIconId) === null || _a === void 0 ? void 0 : _a.id) ? null : icon
    });
  };
  IconSelectControl.prototype.renderIconList = function (icons) {
    var _this = this;
    var _a = this.props,
      ns = _a.classPrefix,
      noDataTip = _a.noDataTip,
      __ = _a.translate;
    if (!icons || !icons.length) {
      return jsxRuntime.jsx("p", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-icon-list-empty"))
      }, {
        children: __(noDataTip)
      }));
    }
    return jsxRuntime.jsx("ul", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "IconSelectControl-icon-list"))
    }, {
      children: icons.map(function (item, index) {
        var _a;
        return jsxRuntime.jsx("li", {
          children: jsxRuntime.jsxs("div", tslib.__assign({
            className: cx__default["default"]("".concat(ns, "IconSelectControl-icon-list-item"), {
              active: ((_a = _this.state.tmpCheckIconId) === null || _a === void 0 ? void 0 : _a.id) === item.id
            }),
            onClick: function () {
              return _this.handleClickIconInModal(item);
            }
          }, {
            children: [jsxRuntime.jsx("svg", {
              children: jsxRuntime.jsx("use", {
                xlinkHref: "#".concat(item.id)
              })
            }), jsxRuntime.jsx("div", tslib.__assign({
              className: cx__default["default"]("".concat(ns, "IconSelectControl-icon-list-item-info"))
            }, {
              children: jsxRuntime.jsx("p", tslib.__assign({
                className: cx__default["default"]("".concat(ns, "IconSelectControl-icon-list-item-info-name"))
              }, {
                children: item.name
              }))
            }))]
          }))
        }, item.id);
      })
    }));
  };
  IconSelectControl.prototype.handleSearchValueChange = function (e) {
    this.setState({
      searchValue: e
    });
  };
  IconSelectControl.prototype.handleRefreshIconList = function () {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var refreshIconList, e_1;
      return tslib.__generator(this, function (_a) {
        switch (_a.label) {
          case 0:
            refreshIconList = IconSelectStore.refreshIconList;
            if (!(refreshIconList && typeof refreshIconList === 'function')) return [3 /*break*/, 5];
            _a.label = 1;
          case 1:
            _a.trys.push([1, 3, 4, 5]);
            this.setState({
              isRefreshLoading: true
            });
            return [4 /*yield*/, Promise.resolve(refreshIconList())];
          case 2:
            _a.sent();
            return [3 /*break*/, 5];
          case 3:
            e_1 = _a.sent();
            console.error(e_1);
            return [3 /*break*/, 5];
          case 4:
            this.setState({
              isRefreshLoading: false
            });
            return [7 /*endfinally*/];
          case 5:
            return [2 /*return*/];
        }
      });
    });
  };
  IconSelectControl.prototype.renderModalContent = function () {
    var _a = this.props,
      render = _a.render,
      ns = _a.classPrefix,
      loadingConfig = _a.loadingConfig,
      funcSchema = _a.funcSchema,
      FuncCom = _a.funcCom;
    var icons = this.getIconsByType();
    var inputValue = this.state.searchValue;
    var filteredIcons = inputValue ? matchSorter.matchSorter(icons, inputValue, {
      keys: ['name'],
      threshold: matchSorter.matchSorter.rankings.CONTAINS
    }) : icons;
    return jsxRuntime.jsxs(jsxRuntime.Fragment, {
      children: [jsxRuntime.jsx(_SearchBox__default["default"], {
        className: cx__default["default"]("".concat(ns, "IconSelectControl-Modal-search")),
        mini: false,
        clearable: true,
        onChange: this.handleSearchValueChange
      }), IconSelectStore.refreshIconList && render('refresh-btn', {
        type: 'button',
        icon: 'fa fa-refresh'
      }, {
        className: cx__default["default"]("".concat(ns, "IconSelectControl-Modal-refresh")),
        onClick: this.handleRefreshIconList
      }) || null, FuncCom ? jsxRuntime.jsx("div", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-Modal-func"))
      }, {
        children: jsxRuntime.jsx(FuncCom, {
          onUpload: this.handleLocalUpload
        })
      })) : null, jsxRuntime.jsxs("div", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-Modal-content"))
      }, {
        children: [jsxRuntime.jsx(_Spinner__default["default"], {
          size: "lg",
          loadingConfig: loadingConfig,
          overlay: true,
          show: this.state.isRefreshLoading
        }, "info"), jsxRuntime.jsx("div", tslib.__assign({
          className: cx__default["default"]("".concat(ns, "IconSelectControl-Modal-content-aside"))
        }, {
          children: this.renderIconTypes()
        })), jsxRuntime.jsx("div", tslib.__assign({
          className: cx__default["default"]("".concat(ns, "IconSelectControl-Modal-content-main"))
        }, {
          children: this.renderIconList(filteredIcons)
        }))]
      }))]
    });
  };
  IconSelectControl.prototype.getIconsByType = function () {
    return (IconSelectStore === null || IconSelectStore === void 0 ? void 0 : IconSelectStore.svgIcons.length) && IconSelectStore.svgIcons[this.state.activeTypeIndex].children || [];
  };
  IconSelectControl.prototype.toggleModel = function (isShow) {
    var valueTemp = this.props.value;
    var value = typeof valueTemp === 'string' ? this.getValueBySvg(valueTemp) : valueTemp;
    if (isShow === undefined) {
      this.setState({
        showModal: !this.state.showModal,
        searchValue: ''
      });
      return;
    }
    this.setState({
      showModal: isShow,
      // tmpCheckIconId: isShow ? String(value).replace('svg-', '') : '',
      tmpCheckIconId: isShow && (value === null || value === void 0 ? void 0 : value.id) ? tslib.__assign(tslib.__assign({}, value), {
        id: String(value.id).replace(/^svg-/, '')
      }) : null,
      searchValue: ''
    });
  };
  IconSelectControl.prototype.render = function () {
    var _this = this;
    var _a = this.props,
      className = _a.className,
      style = _a.style,
      ns = _a.classPrefix,
      disabled = _a.disabled,
      __ = _a.translate;
    return jsxRuntime.jsxs("div", tslib.__assign({
      className: cx__default["default"](className, "".concat(ns, "IconSelectControl"), {
        'is-focused': this.state.showModal,
        'is-disabled': disabled
      })
    }, {
      children: [jsxRuntime.jsx("div", tslib.__assign({
        className: cx__default["default"]("".concat(ns, "IconSelectControl-input")),
        onClick: this.handleClick
      }, {
        children: this.renderInputArea()
      })), jsxRuntime.jsxs(_Modal__default["default"], tslib.__assign({
        show: this.state.showModal,
        closeOnOutside: true,
        closeOnEsc: true,
        size: "lg",
        overlay: true,
        onHide: function () {
          return _this.toggleModel(false);
        }
      }, {
        children: [jsxRuntime.jsx(_Modal__default["default"].Header, tslib.__assign({
          onClose: function () {
            return _this.toggleModel(false);
          }
        }, {
          children: __('IconSelect.choice')
        })), jsxRuntime.jsx(_Modal__default["default"].Body, {
          children: this.renderModalContent()
        }), jsxRuntime.jsxs(_Modal__default["default"].Footer, {
          children: [jsxRuntime.jsx(_Button__default["default"], tslib.__assign({
            type: "button",
            className: "m-l",
            onClick: function () {
              return _this.toggleModel(false);
            }
          }, {
            children: __('cancel')
          })), jsxRuntime.jsx(_Button__default["default"], tslib.__assign({
            type: "button",
            level: "primary",
            onClick: this.handleConfirm
          }, {
            children: __('confirm')
          }))]
        })]
      }))]
    }));
  };
  IconSelectControl.defaultProps = {
    noDataTip: 'placeholder.noData',
    clearable: true
  };
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "handleClick", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "handleClear", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "renderInputArea", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "handleIconTypeClick", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "renderIconTypes", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "handleConfirm", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "handleLocalUpload", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "renderIconList", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "handleRefreshIconList", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "renderModalContent", null);
  tslib.__decorate([amisCore.autobind], IconSelectControl.prototype, "toggleModel", null);
  return IconSelectControl;
}(React__default["default"].PureComponent);
var IconSelectControlRenderer = /** @class */function (_super) {
  tslib.__extends(IconSelectControlRenderer, _super);
  function IconSelectControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  IconSelectControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'icon-select'
  })], IconSelectControlRenderer);
  return IconSelectControlRenderer;
}(IconSelectControl);

exports.IconSelectControlRenderer = IconSelectControlRenderer;
exports["default"] = IconSelectControl;
