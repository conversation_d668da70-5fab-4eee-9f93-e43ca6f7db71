/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _JSONSchemaEditor = require('amis-ui/lib/components/JSONSchemaEditor');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var pick = require('lodash/pick');
var amisCore = require('amis-core');
var Common = require('amis-ui/lib/components/schema-editor/Common');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _JSONSchemaEditor__default = /*#__PURE__*/_interopDefaultLegacy(_JSONSchemaEditor);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var pick__default = /*#__PURE__*/_interopDefaultLegacy(pick);

var JSONSchemaEditorControl = /** @class */function (_super) {
  tslib.__extends(JSONSchemaEditorControl, _super);
  function JSONSchemaEditorControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  JSONSchemaEditorControl.prototype.normalizePlaceholder = function () {
    var placeholder = this.props.placeholder;
    if (amisCore.isObject(placeholder)) {
      return tslib.__assign(tslib.__assign({}, Common.schemaEditorItemPlaceholder), pick__default["default"](placeholder, ['key', 'title', 'description', 'default', 'empty']));
    }
    return Common.schemaEditorItemPlaceholder;
  };
  JSONSchemaEditorControl.prototype.renderModalProps = function (value, onChange) {
    var _a = this.props,
      render = _a.render,
      advancedSettings = _a.advancedSettings;
    var fields = (advancedSettings === null || advancedSettings === void 0 ? void 0 : advancedSettings[value === null || value === void 0 ? void 0 : value.type]) || [];
    return render("modal", {
      type: 'form',
      wrapWithPanel: false,
      body: fields,
      submitOnChange: true
    }, {
      data: value,
      onSubmit: function (value) {
        return onChange(value);
      }
    });
  };
  JSONSchemaEditorControl.prototype.render = function () {
    var _a = this.props,
      enableAdvancedSetting = _a.enableAdvancedSetting,
      mobileUI = _a.mobileUI,
      env = _a.env,
      rest = tslib.__rest(_a, ["enableAdvancedSetting", "mobileUI", "env"]);
    return jsxRuntime.jsx(_JSONSchemaEditor__default["default"], tslib.__assign({}, rest, {
      mobileUI: mobileUI,
      placeholder: this.normalizePlaceholder(),
      enableAdvancedSetting: enableAdvancedSetting,
      renderModalProps: this.renderModalProps,
      popOverContainer: mobileUI ? env === null || env === void 0 ? void 0 : env.getModalContainer : rest.popOverContainer || env.getModalContainer
    }));
  };
  JSONSchemaEditorControl.defaultProps = {
    enableAdvancedSetting: false,
    placeholder: Common.schemaEditorItemPlaceholder
  };
  tslib.__decorate([amisCore.autobind], JSONSchemaEditorControl.prototype, "renderModalProps", null);
  return JSONSchemaEditorControl;
}(React__default["default"].PureComponent);
var JSONSchemaEditorRenderer = /** @class */function (_super) {
  tslib.__extends(JSONSchemaEditorRenderer, _super);
  function JSONSchemaEditorRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  JSONSchemaEditorRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'json-schema-editor'
  })], JSONSchemaEditorRenderer);
  return JSONSchemaEditorRenderer;
}(JSONSchemaEditorControl);

exports.JSONSchemaEditorRenderer = JSONSchemaEditorRenderer;
exports["default"] = JSONSchemaEditorControl;
