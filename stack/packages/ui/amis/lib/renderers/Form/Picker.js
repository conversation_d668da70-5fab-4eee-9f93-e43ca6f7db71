/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _Icon = require('amis-ui/lib/components/Icon');
var _AutoFoldedList = require('amis-ui/lib/components/AutoFoldedList');
var _Html = require('amis-ui/lib/components/Html');
var _OverflowTpl = require('amis-ui/lib/components/OverflowTpl');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var cx = require('classnames');
var omit = require('lodash/omit');
var find = require('lodash/find');
var isEqual = require('lodash/isEqual');
var findIndex = require('lodash/findIndex');
var merge = require('lodash/merge');
var amisCore = require('amis-core');
var intersectionWith = require('lodash/intersectionWith');
var StaticHoc = require('./StaticHoc.js');
var mobx = require('mobx');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _Icon__default = /*#__PURE__*/_interopDefaultLegacy(_Icon);
var _AutoFoldedList__default = /*#__PURE__*/_interopDefaultLegacy(_AutoFoldedList);
var _Html__default = /*#__PURE__*/_interopDefaultLegacy(_Html);
var _OverflowTpl__default = /*#__PURE__*/_interopDefaultLegacy(_OverflowTpl);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);
var omit__default = /*#__PURE__*/_interopDefaultLegacy(omit);
var find__default = /*#__PURE__*/_interopDefaultLegacy(find);
var isEqual__default = /*#__PURE__*/_interopDefaultLegacy(isEqual);
var findIndex__default = /*#__PURE__*/_interopDefaultLegacy(findIndex);
var merge__default = /*#__PURE__*/_interopDefaultLegacy(merge);
var intersectionWith__default = /*#__PURE__*/_interopDefaultLegacy(intersectionWith);

var PickerControl = /** @class */function (_super) {
  tslib.__extends(PickerControl, _super);
  function PickerControl(props) {
    var _this = _super.call(this, props) || this;
    _this.state = {
      isOpened: false,
      schema: _this.buildSchema(_this.props),
      isFocused: false
    };
    _this.input = React__default["default"].createRef();
    _this.toDispose = [];
    _this.mounted = false;
    var formInited = props.formInited,
      addHook = props.addHook,
      formItem = props.formItem;
    var onIninted = function () {
      return tslib.__awaiter(_this, void 0, void 0, function () {
        var _this = this;
        return tslib.__generator(this, function (_a) {
          switch (_a.label) {
            case 0:
              return [4 /*yield*/, this.fetchOptions()];
            case 1:
              _a.sent();
              this.mounted && this.toDispose.push(mobx.reaction(function () {
                return JSON.stringify(formItem === null || formItem === void 0 ? void 0 : formItem.tmpValue);
              }, function () {
                return _this.fetchOptions();
              }));
              return [2 /*return*/];
          }
        });
      });
    };
    formItem && _this.toDispose.push(formInited || !addHook ? formItem.addInitHook(onIninted) : addHook(onIninted, 'init'));
    return _this;
  }
  PickerControl.prototype.componentDidMount = function () {
    this.mounted = true;
  };
  PickerControl.prototype.componentDidUpdate = function (prevProps) {
    var _a;
    var props = this.props;
    var detectedProps = ['multiple', 'source', 'pickerSchema'];
    if (detectedProps.some(function (key) {
      return !isEqual__default["default"](prevProps[key], props[key]);
    })) {
      this.setState({
        schema: this.buildSchema(props)
      });
    } else if (amisCore.isApiOutdated(prevProps.source, props.source, prevProps.data, props.data)) {
      ((_a = props.formItem) === null || _a === void 0 ? void 0 : _a.inited) && this.fetchOptions();
    }
  };
  PickerControl.prototype.componentWillUnmount = function () {
    this.toDispose.forEach(function (fn) {
      return fn();
    });
    this.toDispose = [];
    this.mounted = false;
  };
  PickerControl.prototype.fetchOptions = function () {
    var _a;
    var _b = this.props,
      value = _b.value,
      formItem = _b.formItem,
      valueField = _b.valueField,
      labelField = _b.labelField,
      source = _b.source,
      data = _b.data;
    var selectedOptions;
    if (!source || !formItem || (valueField || 'value') === (labelField || 'label') || (selectedOptions = formItem.getSelectedOptions(value)) && (!selectedOptions.length || selectedOptions[0][valueField || 'value'] !== selectedOptions[0][labelField || 'label'])) {
      return;
    }
    var ctx = amisCore.createObject(data, (_a = {
      value: value
    }, _a[valueField || 'value'] = value, _a.op = 'loadOptions', _a));
    if (amisCore.isPureVariable(source)) {
      formItem.setOptions(amisCore.resolveVariableAndFilter(source, data, '| raw'));
    } else if (amisCore.isEffectiveApi(source, ctx)) {
      return formItem.loadOptions(source, ctx, {
        autoAppend: true
      });
    }
  };
  PickerControl.prototype.buildSchema = function (props) {
    var _a, _b;
    var isScopeData = amisCore.isPureVariable(props.source);
    return tslib.__assign(tslib.__assign({
      checkOnItemClick: true,
      listItem: {
        title: "${".concat(props.labelField || 'label', "|raw}")
      }
    }, props.pickerSchema), {
      labelTpl: (_b = (_a = props.pickerSchema) === null || _a === void 0 ? void 0 : _a.labelTpl) !== null && _b !== void 0 ? _b : props.labelTpl,
      type: 'crud',
      pickerMode: true,
      syncLocation: false,
      filterCanAccessSuperData: false,
      api: isScopeData ? null : props.source,
      source: isScopeData ? props.source : null,
      keepItemSelectionOnPageChange: true,
      valueField: props.valueField,
      labelField: props.labelField,
      // 不支持批量操作，会乱套
      bulkActions: props.multiple ? props.pickerSchema.bulkActions : []
    });
  };
  PickerControl.prototype.crudRef = function (ref) {
    while (ref && ref.getWrappedInstance) {
      ref = ref.getWrappedInstance();
    }
    this.crud = ref;
  };
  PickerControl.prototype.reload = function (subpath, query) {
    if (this.crud) {
      this.crud.reload(subpath, query);
    } else {
      var reload = this.props.reloadOptions;
      reload && reload(subpath, query);
    }
  };
  PickerControl.prototype.open = function () {
    this.setState({
      isOpened: true
    });
  };
  PickerControl.prototype.close = function () {
    this.setState({
      isOpened: false
    });
  };
  PickerControl.prototype.handleModalConfirm = function (values, action, ctx, components) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var idx;
      return tslib.__generator(this, function (_a) {
        switch (_a.label) {
          case 0:
            idx = findIndex__default["default"](components, function (item) {
              return item.props.type === 'crud';
            });
            return [4 /*yield*/, this.handleChange(values[idx].items)];
          case 1:
            _a.sent();
            this.close();
            return [2 /*return*/];
        }
      });
    });
  };
  PickerControl.prototype.handleChange = function (items) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, joinValues, valueField, delimiter, extractValue, multiple, options, data, dispatchEvent, selectedOptions, setOptions, onChange, value, additionalOptions, option, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, joinValues = _a.joinValues, valueField = _a.valueField, delimiter = _a.delimiter, extractValue = _a.extractValue, multiple = _a.multiple, options = _a.options, data = _a.data, dispatchEvent = _a.dispatchEvent, selectedOptions = _a.selectedOptions, setOptions = _a.setOptions, onChange = _a.onChange;
            value = items;
            if (joinValues) {
              value = items.map(function (item) {
                return item[valueField || 'value'];
              }).join(delimiter || ',');
            } else if (extractValue) {
              value = multiple ? items.map(function (item) {
                return item[valueField || 'value'];
              }) : items[0] && items[0][valueField || 'value'] || '';
            } else {
              value = multiple ? items : items[0];
            }
            additionalOptions = [];
            items.forEach(function (item) {
              if (!find__default["default"](options, function (option) {
                return item[valueField || 'value'] == option[valueField || 'value'];
              })) {
                additionalOptions.push(item);
              }
            });
            additionalOptions.length && setOptions(options.concat(additionalOptions));
            option = multiple ? items : items[0];
            return [4 /*yield*/, dispatchEvent('change', amisCore.resolveEventData(this.props, {
              value: value,
              option: option,
              selectedItems: option
            }))];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            onChange(value);
            return [2 /*return*/];
        }
      });
    });
  };
  PickerControl.prototype.handleItemClick = function (item) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, data, dispatchEvent, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, data = _a.data, dispatchEvent = _a.dispatchEvent;
            return [4 /*yield*/, dispatchEvent('itemClick', amisCore.createObject(data, {
              item: item
            }))];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            return [2 /*return*/];
        }
      });
    });
  };
  PickerControl.prototype.removeItem = function (index) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, selectedOptions, joinValues, extractValue, delimiter, valueField, onChange, multiple, dispatchEvent, items, option, value, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, selectedOptions = _a.selectedOptions, joinValues = _a.joinValues, extractValue = _a.extractValue, delimiter = _a.delimiter, valueField = _a.valueField, onChange = _a.onChange, multiple = _a.multiple, dispatchEvent = _a.dispatchEvent;
            items = selectedOptions.concat();
            option = items.splice(index, 1)[0];
            value = items;
            if (joinValues) {
              value = items.map(function (item) {
                return item[valueField || 'value'];
              }).join(delimiter || ',');
            } else if (extractValue) {
              value = multiple ? items.map(function (item) {
                return item[valueField || 'value'];
              }) : items[0] && items[0][valueField || 'value'] || '';
            } else {
              value = multiple ? items : items[0];
            }
            return [4 /*yield*/, dispatchEvent('change', amisCore.resolveEventData(this.props, {
              value: value,
              option: option,
              selectedItems: option
            }))];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            onChange(value);
            return [2 /*return*/];
        }
      });
    });
  };
  PickerControl.prototype.handleKeyDown = function (e) {
    var selectedOptions = this.props.selectedOptions;
    if (e.key === ' ') {
      this.open();
      e.preventDefault();
    } else if (selectedOptions.length && e.key == 'Backspace') {
      this.removeItem(selectedOptions.length - 1);
    }
  };
  PickerControl.prototype.handleFocus = function (e) {
    this.input.current && this.input.current.focus();
    e.stopPropagation();
    this.setState({
      isFocused: true
    });
  };
  PickerControl.prototype.handleBlur = function () {
    this.setState({
      isFocused: false
    });
  };
  PickerControl.prototype.handleClick = function () {
    this.open();
  };
  PickerControl.prototype.clearValue = function (e) {
    e.stopPropagation();
    var _a = this.props,
      onChange = _a.onChange,
      resetValue = _a.resetValue;
    onChange(resetValue !== void 0 ? resetValue : '');
  };
  PickerControl.prototype.getOverflowConfig = function () {
    var overflowConfig = this.props.overflowConfig;
    return merge__default["default"](PickerControl.defaultProps.overflowConfig, overflowConfig);
  };
  PickerControl.prototype.handleSelect = function (selectedItems, unSelectedItems) {
    var _a = this.props,
      selectedOptions = _a.selectedOptions,
      valueField = _a.valueField;
    // 选择行后，crud 会给出连续多次事件，且selectedItems会变化，会导致初始化和点击无效
    // 过滤掉一些无用事件，否则会导致 value 错误
    if (!Array.isArray(selectedItems) || !Array.isArray(unSelectedItems) || !selectedItems.length && !unSelectedItems.length) {
      return;
    }
    // 取交集，判断是否是无效事件，需要考虑顺序问题
    var intersections = intersectionWith__default["default"](selectedItems, selectedOptions, function (a, b) {
      // 需要考虑没有配置 valueField，而且值里面又没有 value 字段的情况
      var aValue = a[valueField || 'value'];
      var bValue = b[valueField || 'value'];
      return aValue || bValue ? aValue === bValue :
      // selectedOptions 中有 Options 自动添加的 value 字段，所以去掉后才能比较
      isEqual__default["default"](omit__default["default"](a, 'value'), omit__default["default"](b, 'value'));
    });
    if (
    // 前后数量都一样说明是重复事件
    intersections.length === selectedItems.length && intersections.length === selectedOptions.length) {
      return;
    }
    this.handleChange(selectedItems);
  };
  PickerControl.prototype.renderTag = function (item, index) {
    var _this = this;
    var _a = this.props,
      _b = _a.itemClearable,
      itemClearable = _b === void 0 ? true : _b,
      ns = _a.classPrefix,
      cx = _a.classnames,
      labelField = _a.labelField,
      labelTpl = _a.labelTpl,
      __ = _a.translate,
      disabled = _a.disabled,
      env = _a.env,
      id = _a.id,
      themeCss = _a.themeCss,
      css = _a.css;
    return jsxRuntime.jsxs("div", tslib.__assign({
      className: cx("".concat(ns, "Picker-value"), amisCore.setThemeClassName(tslib.__assign(tslib.__assign({}, this.props), {
        name: 'pickValueWrapClassName',
        id: id,
        themeCss: themeCss || css
      })), {
        'is-disabled': disabled
      })
    }, {
      children: [itemClearable && jsxRuntime.jsx("span", tslib.__assign({
        className: cx("".concat(ns, "Picker-valueIcon"), amisCore.setThemeClassName(tslib.__assign(tslib.__assign({}, this.props), {
          name: 'pickValueIconClassName',
          id: id,
          themeCss: themeCss || css
        }))),
        onClick: function (e) {
          e.stopPropagation();
          _this.removeItem(index);
        }
      }, {
        children: "\u00D7"
      })), jsxRuntime.jsx(_OverflowTpl__default["default"], tslib.__assign({
        inline: false,
        tooltip: amisCore.getVariable(item, labelField || 'label')
      }, {
        children: jsxRuntime.jsx("span", tslib.__assign({
          className: cx("".concat(ns, "Picker-valueLabel"), amisCore.setThemeClassName(tslib.__assign(tslib.__assign({}, this.props), {
            name: 'pickFontClassName',
            id: id,
            themeCss: themeCss || css
          }))),
          onClick: function (e) {
            e.stopPropagation();
            _this.handleItemClick(item);
          }
        }, {
          children: labelTpl ? jsxRuntime.jsx(_Html__default["default"], {
            html: amisCore.filter(labelTpl, item)
          }) : "".concat(amisCore.getVariable(item, labelField || 'label') || amisCore.getVariable(item, 'id'))
        }))
      }))]
    }), index);
  };
  PickerControl.prototype.renderValues = function () {
    var _this = this;
    var _a = this.props,
      ns = _a.classPrefix,
      selectedOptions = _a.selectedOptions,
      __ = _a.translate,
      disabled = _a.disabled,
      multiple = _a.multiple,
      popOverContainer = _a.popOverContainer,
      id = _a.id,
      themeCss = _a.themeCss,
      css = _a.css;
    var _b = this.getOverflowConfig(),
      maxTagCount = _b.maxTagCount,
      overflowTagPopover = _b.overflowTagPopover;
    var tags = selectedOptions;
    var enableOverflow = multiple !== false && typeof maxTagCount === 'number' && maxTagCount > 0;
    var tooltipProps = tslib.__assign({
      tooltipClassName: cx__default["default"]('Picker-overflow', overflowTagPopover === null || overflowTagPopover === void 0 ? void 0 : overflowTagPopover.tooltipClassName),
      title: __('已选项')
    }, omit__default["default"](overflowTagPopover, ['children', 'content', 'tooltipClassName']));
    return jsxRuntime.jsx(_AutoFoldedList__default["default"], {
      enabled: !!enableOverflow,
      tooltipClassName: cx__default["default"]('Picker-overflow-wrapper'),
      items: tags,
      popOverContainer: popOverContainer,
      tooltipOptions: tooltipProps,
      maxVisibleCount: maxTagCount,
      renderItem: function (item, index, folded) {
        return _this.renderTag(item, index);
      }
    });
  };
  PickerControl.prototype.overrideCRUDProps = function () {
    // 自定义参数，用于外部透传给 crud 组件外围需要的属性值。
    // 需要保留这个函数和函数名存在即可，返回值是一个对象
    return {};
  };
  PickerControl.prototype.renderBody = function (_a) {
    var _b = _a === void 0 ? {} : _a,
      popOverContainer = _b.popOverContainer;
    var _c = this.props,
      render = _c.render,
      selectedOptions = _c.selectedOptions,
      options = _c.options,
      multiple = _c.multiple,
      valueField = _c.valueField,
      embed = _c.embed,
      source = _c.source,
      strictMode = _c.strictMode,
      testIdBuilder = _c.testIdBuilder;
    var _d = this.getOverflowConfig(),
      maxTagCount = _d.maxTagCount,
      overflowTagPopoverInCRUD = _d.overflowTagPopoverInCRUD,
      displayPosition = _d.displayPosition;
    return render('modal-body', this.state.schema, tslib.__assign(tslib.__assign({
      value: selectedOptions,
      valueField: valueField,
      primaryField: valueField,
      options: source ? [] : options,
      multiple: multiple,
      strictMode: strictMode,
      onSelect: embed ? this.handleSelect : undefined,
      testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('body-schema'),
      ref: this.crudRef,
      popOverContainer: popOverContainer
    }, embed || Array.isArray(displayPosition) && displayPosition.includes('crud') ? {
      maxTagCount: maxTagCount,
      overflowTagPopover: overflowTagPopoverInCRUD
    } : {}), this.overrideCRUDProps()));
  };
  PickerControl.prototype.render = function () {
    var _a = this.props,
      className = _a.className,
      style = _a.style,
      modalClassName = _a.modalClassName,
      cx = _a.classnames,
      disabled = _a.disabled,
      render = _a.render,
      modalMode = _a.modalMode,
      source = _a.source,
      modalSize = _a.modalSize,
      clearable = _a.clearable,
      multiple = _a.multiple,
      placeholder = _a.placeholder,
      embed = _a.embed,
      selectedOptions = _a.selectedOptions,
      __ = _a.translate,
      popOverContainer = _a.popOverContainer,
      modalTitle = _a.modalTitle,
      data = _a.data,
      mobileUI = _a.mobileUI,
      env = _a.env,
      themeCss = _a.themeCss,
      css = _a.css,
      id = _a.id,
      ns = _a.classPrefix,
      testIdBuilder = _a.testIdBuilder;
    return jsxRuntime.jsxs("div", tslib.__assign({
      className: cx("PickerControl", {
        'is-mobile': mobileUI
      }, className)
    }, {
      children: [embed ? jsxRuntime.jsx("div", tslib.__assign({
        className: cx('Picker')
      }, {
        children: this.renderBody({
          popOverContainer: popOverContainer
        })
      })) : jsxRuntime.jsxs("div", tslib.__assign({
        className: cx("Picker", {
          'Picker--single': !multiple,
          'Picker--multi': multiple,
          'is-focused': this.state.isFocused,
          'is-disabled': disabled
        })
      }, {
        children: [jsxRuntime.jsxs("div", tslib.__assign({
          onClick: this.handleClick,
          className: cx('Picker-input', disabled && 'is-disabled', this.state.isFocused && 'is-focused', amisCore.setThemeClassName(tslib.__assign(tslib.__assign({}, this.props), {
            name: 'pickControlClassName',
            id: id,
            themeCss: themeCss || css
          })))
        }, {
          children: [!selectedOptions.length && placeholder ? jsxRuntime.jsx("div", tslib.__assign({
            className: cx('Picker-placeholder')
          }, {
            children: __(placeholder)
          })) : jsxRuntime.jsxs("div", tslib.__assign({
            className: cx('Picker-valueWrap')
          }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId(), {
            children: [this.renderValues(), jsxRuntime.jsx("input", {
              onChange: amisCore.noop,
              value: '',
              ref: this.input,
              onKeyDown: this.handleKeyDown,
              onClick: this.handleFocus,
              onBlur: this.handleBlur,
              readOnly: mobileUI
            })]
          })), clearable && !disabled && selectedOptions.length ? jsxRuntime.jsx("a", tslib.__assign({
            onClick: this.clearValue,
            className: cx('Picker-clear')
          }, {
            children: jsxRuntime.jsx(_Icon__default["default"], {
              icon: "input-clear",
              className: "icon"
            })
          })) : null, jsxRuntime.jsx("span", tslib.__assign({
            onClick: this.open,
            className: cx('Picker-btn')
          }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('picker-open-btn').getTestId(), {
            children: jsxRuntime.jsx(_Icon__default["default"], {
              icon: "window-restore",
              className: cx('icon', amisCore.setThemeClassName(tslib.__assign(tslib.__assign({}, this.props), {
                name: 'pickIconClassName',
                id: id,
                themeCss: themeCss || css
              }))),
              iconContent: "Picker-icon"
            })
          }))]
        })), render('modal', {
          title: modalTitle && typeof modalTitle === 'string' ? amisCore.filter(modalTitle, data) : __('Select.placeholder'),
          size: modalSize,
          type: modalMode,
          className: modalClassName,
          body: {
            children: this.renderBody
          },
          testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('modal')
        }, {
          key: 'modal',
          lazyRender: !!source,
          onConfirm: this.handleModalConfirm,
          onClose: this.close,
          show: this.state.isOpened
        })]
      })), jsxRuntime.jsx(amisCore.CustomStyle, tslib.__assign({}, this.props, {
        config: {
          themeCss: themeCss || css,
          classNames: [{
            key: 'pickControlClassName',
            weights: {
              default: {
                important: true
              },
              hover: {
                important: true
              },
              focused: {
                important: true,
                parent: ".".concat(ns, "Picker.is-focused >")
              },
              disabled: {
                important: true,
                parent: ".".concat(ns, "Picker.is-disabled >")
              }
            }
          }, {
            key: 'pickFontClassName'
          }, {
            key: 'pickValueWrapClassName',
            weights: {
              default: {
                important: true
              }
            }
          }, {
            key: 'pickValueIconClassName',
            weights: {
              default: {
                important: true
              },
              hover: {
                important: true
              }
            }
          }, {
            key: 'pickIconClassName',
            weights: {
              default: {
                suf: ' svg'
              }
            }
          }],
          id: id
        },
        env: env
      }))]
    }));
  };
  PickerControl.propsList = ['modalTitle', 'modalMode', 'modalSize', 'pickerSchema', 'labelField', 'onChange', 'options', 'value', 'inline', 'multiple', 'embed', 'resetValue', 'placeholder', 'onQuery' // 防止 Form 的 onQuery 事件透传下去，不然会导致 table 先后触发 Form 和 Crud 的 onQuery
  ];
  PickerControl.defaultProps = {
    modalMode: 'dialog',
    multiple: false,
    placeholder: 'Picker.placeholder',
    labelField: 'label',
    valueField: 'value',
    pickerSchema: {
      mode: 'list'
    },
    embed: false,
    overflowConfig: {
      /** 默认值为-1，不开启 */
      maxTagCount: -1,
      displayPosition: ['select', 'crud'],
      overflowTagPopover: {
        placement: 'top',
        trigger: 'hover',
        showArrow: false,
        offset: [0, -5]
      },
      overflowTagPopoverInCRUD: {
        placement: 'bottom',
        trigger: 'hover',
        showArrow: false,
        offset: [0, 0]
      }
    }
  };
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "fetchOptions", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "crudRef", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "open", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "close", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleModalConfirm", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleChange", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleItemClick", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleKeyDown", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleFocus", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleBlur", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleClick", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "clearValue", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "handleSelect", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "overrideCRUDProps", null);
  tslib.__decorate([amisCore.autobind], PickerControl.prototype, "renderBody", null);
  tslib.__decorate([StaticHoc.supportStatic()], PickerControl.prototype, "render", null);
  return PickerControl;
}(React__default["default"].PureComponent);
var PickerControlRenderer = /** @class */function (_super) {
  tslib.__extends(PickerControlRenderer, _super);
  function PickerControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  PickerControlRenderer = tslib.__decorate([amisCore.OptionsControl({
    type: 'picker',
    autoLoadOptionsFromSource: false,
    sizeMutable: false
  })], PickerControlRenderer);
  return PickerControlRenderer;
}(PickerControl);

exports.PickerControlRenderer = PickerControlRenderer;
exports["default"] = PickerControl;
