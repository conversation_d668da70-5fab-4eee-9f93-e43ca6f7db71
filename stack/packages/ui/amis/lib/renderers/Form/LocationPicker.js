/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _LocationPicker = require('amis-ui/lib/components/LocationPicker');
var _BaiduMapPicker = require('amis-ui/lib/components/BaiduMapPicker');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _LocationPicker__default = /*#__PURE__*/_interopDefaultLegacy(_LocationPicker);
var _BaiduMapPicker__default = /*#__PURE__*/_interopDefaultLegacy(_BaiduMapPicker);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);

var LocationControl = /** @class */function (_super) {
  tslib.__extends(LocationControl, _super);
  function LocationControl() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.domRef = React__default["default"].createRef();
    _this.state = {
      isOpened: false
    };
    return _this;
  }
  LocationControl.prototype.close = function () {
    this.setState({
      isOpened: false
    });
  };
  LocationControl.prototype.open = function () {
    this.setState({
      isOpened: true
    });
  };
  LocationControl.prototype.handleClick = function () {
    this.state.isOpened ? this.close() : this.open();
  };
  LocationControl.prototype.handleChange = function (value) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, dispatchEvent, onChange, dispatcher;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, dispatchEvent = _a.dispatchEvent, onChange = _a.onChange;
            return [4 /*yield*/, dispatchEvent('change', amisCore.resolveEventData(this.props, {
              value: value
            }))];
          case 1:
            dispatcher = _b.sent();
            if (dispatcher === null || dispatcher === void 0 ? void 0 : dispatcher.prevented) {
              return [2 /*return*/];
            }
            onChange(value);
            return [2 /*return*/];
        }
      });
    });
  };
  LocationControl.prototype.getParent = function () {
    var _a;
    return (_a = this.domRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;
  };
  LocationControl.prototype.getTarget = function () {
    return this.domRef.current;
  };
  LocationControl.prototype.doAction = function (action, data, throwErrors) {
    var _a, _b, _c;
    var _d = this.props,
      resetValue = _d.resetValue,
      onChange = _d.onChange,
      formStore = _d.formStore,
      store = _d.store,
      name = _d.name;
    var actionType = action === null || action === void 0 ? void 0 : action.actionType;
    switch (actionType) {
      case 'clear':
        onChange('');
        break;
      case 'reset':
        onChange === null || onChange === void 0 ? void 0 : onChange((_c = (_b = amisCore.getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue) !== null && _c !== void 0 ? _c : '');
        break;
    }
  };
  LocationControl.prototype.renderStatic = function (displayValue) {
    if (displayValue === void 0) {
      displayValue = '-';
    }
    var _a = this.props,
      cx = _a.classnames,
      value = _a.value,
      staticSchema = _a.staticSchema,
      ak = _a.ak,
      coordinatesType = _a.coordinatesType,
      _b = _a.hideViewControl,
      hideViewControl = _b === void 0 ? false : _b,
      mobileUI = _a.mobileUI;
    var __ = this.props.translate;
    if (!value) {
      return jsxRuntime.jsx(jsxRuntime.Fragment, {
        children: displayValue
      });
    }
    return jsxRuntime.jsx("div", tslib.__assign({
      className: this.props.classnames('LocationControl', {
        'is-mobile': mobileUI
      }),
      ref: this.domRef
    }, {
      children: (staticSchema === null || staticSchema === void 0 ? void 0 : staticSchema.embed) ? jsxRuntime.jsxs(jsxRuntime.Fragment, {
        children: [staticSchema.showAddress === false ? null : jsxRuntime.jsx("div", tslib.__assign({
          className: "mb-2"
        }, {
          children: value.address
        })), jsxRuntime.jsx(_BaiduMapPicker__default["default"], {
          ak: ak,
          value: value,
          coordinatesType: coordinatesType,
          autoSelectCurrentLoc: false,
          onlySelectCurrentLoc: true,
          showSug: false,
          showGeoLoc: staticSchema.showGeoLoc,
          mapStyle: staticSchema.mapStyle,
          hideViewControl: hideViewControl
        })]
      }) : jsxRuntime.jsx("span", {
        children: value.address
      })
    }));
  };
  LocationControl.prototype.render = function () {
    var _a = this.props,
      style = _a.style,
      env = _a.env,
      mobileUI = _a.mobileUI;
    var ak = amisCore.filter(this.props.ak, this.props.data) || env.locationPickerAK;
    return jsxRuntime.jsx("div", tslib.__assign({
      className: this.props.classnames('LocationControl', {
        'is-mobile': mobileUI
      })
    }, {
      children: jsxRuntime.jsx(_LocationPicker__default["default"], tslib.__assign({}, this.props, {
        placeholder: this.props.placeholder,
        ak: amisCore.filter(this.props.ak, this.props.data),
        onChange: this.handleChange
      }))
    }));
  };
  LocationControl.defaultProps = {
    vendor: 'baidu',
    coordinatesType: 'bd09'
  };
  tslib.__decorate([amisCore.autobind], LocationControl.prototype, "close", null);
  tslib.__decorate([amisCore.autobind], LocationControl.prototype, "open", null);
  tslib.__decorate([amisCore.autobind], LocationControl.prototype, "handleClick", null);
  tslib.__decorate([amisCore.autobind], LocationControl.prototype, "handleChange", null);
  tslib.__decorate([amisCore.autobind], LocationControl.prototype, "getParent", null);
  tslib.__decorate([amisCore.autobind], LocationControl.prototype, "getTarget", null);
  tslib.__decorate([StaticHoc.supportStatic()], LocationControl.prototype, "render", null);
  return LocationControl;
}(React__default["default"].Component);
var LocationRenderer = /** @class */function (_super) {
  tslib.__extends(LocationRenderer, _super);
  function LocationRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  LocationRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'location-picker'
  })], LocationRenderer);
  return LocationRenderer;
}(LocationControl);

exports.LocationControl = LocationControl;
exports.LocationRenderer = LocationRenderer;
