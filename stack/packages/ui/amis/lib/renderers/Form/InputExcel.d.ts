import React from 'react';
import { FormControlProps, TestIdBuilder } from 'amis-core';
import { FormBaseControlSchema, SchemaTokenizeableString } from '../../Schema';
import type { CellRichTextValue } from 'exceljs';
/**
 * Excel 文件状态
 */
export type ExcelFileState = 'init' | 'error' | 'pending' | 'parsed' | 'invalid';
/**
 * Excel 文件数据结构
 */
export interface ExcelFileData {
    /** 单个 sheet 的数据 */
    data: any[];
    /** sheet 名称 */
    sheetName?: string;
    /** 图片数据 */
    images?: string[];
}
/**
 * Excel 文件输出数据结构
 */
export interface ExcelOutputData {
    /** 文件名称 */
    fileName: string;
    /** 文件数据 */
    data: any[] | ExcelFileData | ExcelFileData[];
}
/**
 * Excel 文件对象
 */
export interface ExcelFile {
    /** 文件唯一标识 */
    id: string;
    /** 文件名称 */
    name: string;
    /** 文件状态 */
    state: ExcelFileState;
    /** 错误信息 */
    error?: string;
    /** 解析后的数据 */
    data?: any[] | ExcelFileData | ExcelFileData[];
}
/**
 * Excel 解析
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/input-excel
 */
export interface InputExcelControlSchema extends FormBaseControlSchema {
    /**
     * 指定为 Excel 解析
     */
    type: 'input-excel';
    /**
     * 是否解析所有 sheet，默认情况下只解析第一个
     */
    allSheets?: boolean;
    /**
     * 解析模式，array 是解析成二维数组，object 是将第一列作为字段名，解析为对象数组
     */
    parseMode?: 'array' | 'object';
    /**
     * 是否包含空内容，主要用于二维数组模式
     */
    includeEmpty?: boolean;
    /**
     * 纯文本模式
     */
    plainText?: boolean;
    /**
     * 解析图片
     */
    parseImage?: boolean;
    /** 图片解析结果使用 data URI 格式 */
    imageDataURI?: boolean;
    /**
     * 占位文本提示
     */
    placeholder?: string;
    /**
     * 最大上传文件数
     */
    maxLength?: number;
    /**
     * 是否允许上传多个文件
     */
    multiple?: boolean;
    /**
     * 文件解析完成后将字段同步到表单内部
     */
    autoFill?: {
        [propName: string]: SchemaTokenizeableString;
    };
    testIdBuilder?: TestIdBuilder;
}
export interface ExcelProps extends FormControlProps, Omit<InputExcelControlSchema, 'type' | 'className' | 'descriptionClassName' | 'inputClassName'> {
}
export interface ExcelControlState {
    files: Array<ExcelFile>;
    error?: string | null;
}
export type InputExcelRendererEvent = 'change';
export type InputExcelRendererAction = 'clear';
export default class ExcelControl extends React.PureComponent<ExcelProps, ExcelControlState> {
    static defaultProps: Partial<ExcelProps>;
    state: ExcelControlState;
    dropzone: React.RefObject<any>;
    ExcelJS: any;
    componentDidUpdate(prevProps: ExcelProps): void;
    /**
     * 同步自动填充数据
     */
    syncAutoFill(filename: string, fileData?: any): void;
    /**
     * 处理文件上传
     * 支持单文件和多文件上传，自动处理文件数量限制
     */
    handleDrop(files: File[]): Promise<void>;
    /**
     * 并行处理多个文件
     * 使用 Promise.all 同时处理所有文件，提高效率
     */
    processFiles(files: Array<{
        file: File;
        id: string;
    }>): Promise<void>;
    handleSelect(): void;
    /**
     * 移除文件并更新表单数据
     *
     * @param file 要移除的文件对象
     */
    removeFile(file: ExcelFile): void;
    /**
     * 更新文件状态
     * 当状态变为 parsed 时检查是否所有文件都已处理完成
     */
    updateFileState(fileId: string, updates: Partial<ExcelFile>): void;
    /**
     * 更新表单值
     * 根据当前已解析的文件更新表单数据
     * 支持多文件模式和单文件模式
     */
    updateFormValue(): Promise<void>;
    /**
     * 在适当的时机触发自动填充
     */
    triggerAutoFill(): void;
    /**
     * 处理Excel文件
     * 支持 .xls 和 .xlsx 格式
     * 使用 xlsx 库转换 .xls 为 .xlsx，使用 exceljs 解析内容
     */
    processExcelFile(excelFile: File, fileState: ExcelFile): Promise<void>;
    /**
     * 解析 Excel 数据
     * 支持解析所有 sheet 或单个 sheet
     * 支持解析图片和富文本
     */
    parseExcelData(excelData: ArrayBuffer | string, fileState: ExcelFile): Promise<void>;
    /**
     * 解析所有可见的 sheet
     */
    parseAllSheets(workbook: any, parseImage?: boolean, plainText?: boolean): {
        sheetName: string;
        data: any[];
        images?: string[] | undefined;
    }[];
    /** 读取工作表里的图片 */
    readImages(worksheet: any, workbook: any): string[];
    /** 将 buffer 转成 base64 */
    encodeBase64Bytes(bytes: Uint8Array): string;
    /**
     * 触发表单事件
     */
    private dispatchEvent;
    /**
     * 检查当前单元格数据是否为富文本
     *
     * @reference https://github.com/exceljs/exceljs#rich-text
     */
    isRichTextValue(value: any): boolean;
    /**
     * 将富文本类型的单元格内容转化为Plain Text
     *
     * @param {CellRichTextValue} cellValue 单元格值
     * @param {Boolean} html 是否输出为html格式
     */
    richText2PlainString(cellValue: CellRichTextValue, html?: boolean): string;
    /**
     * 读取工作表内容
     */
    readWorksheet(worksheet: any, plainText?: boolean): any[];
    doAction(action: any, _data: object, _throwErrors: boolean): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class ExcelControlRenderer extends ExcelControl {
}
//# sourceMappingURL=InputExcel.d.ts.map