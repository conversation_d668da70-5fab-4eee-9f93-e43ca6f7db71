import React from 'react';
import { FormControlProps } from 'amis-core';
import { FormBaseControlSchema, SchemaTokenizeableString } from '../../Schema';
import type { ListenerAction } from 'amis-core';
/**
 * Diff 编辑器
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/diff
 */
export interface DiffControlSchema extends FormBaseControlSchema {
    /**
     * 指定为 Diff 编辑器
     */
    type: 'diff-editor';
    /**
     * 左侧面板的值， 支持取变量。
     */
    diffValue?: SchemaTokenizeableString;
    /**
     * 语言，参考 monaco-editor
     */
    language?: string;
    /**
     * 编辑器配置
     */
    options?: any;
}
export type DiffEditorRendererEvent = 'blur' | 'focus';
export interface DiffEditorProps extends FormControlProps, Omit<DiffControlSchema, 'type' | 'className' | 'descriptionClassName' | 'inputClassName'> {
}
export interface DiffEditorState {
    focused: boolean;
}
export declare class DiffEditorRenderer extends React.Component<DiffEditorProps, DiffEditorState> {
    static defaultProps: Partial<DiffEditorProps>;
    state: {
        focused: boolean;
    };
    editor: any;
    constructor(props: DiffEditorProps);
    doAction(action: ListenerAction, data: any, throwErrors?: boolean, args?: any): void;
    focus(): void;
    handleFocus(e: any): Promise<void>;
    handleBlur(e: any): Promise<void>;
    handleChange(value: any): Promise<void>;
    handleEditorMounted(editor: any): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class DiffEditorControlRenderer extends DiffEditorRenderer {
    static defaultProps: {
        options?: any;
        remark?: import("../Remark").SchemaRemark | undefined;
        labelRemark?: import("../Remark").SchemaRemark | undefined;
        language?: string | undefined;
        diffValue?: string | undefined;
    };
}
//# sourceMappingURL=DiffEditor.d.ts.map