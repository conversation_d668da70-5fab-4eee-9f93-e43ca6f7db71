/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _DateRangePicker = require('amis-ui/lib/components/DateRangePicker');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var amisCore = require('amis-core');
var cx = require('classnames');
var InputDateRange = require('./InputDateRange.js');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _DateRangePicker__default = /*#__PURE__*/_interopDefaultLegacy(_DateRangePicker);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);

var QuarterRangeControl = /** @class */function (_super) {
  tslib.__extends(QuarterRangeControl, _super);
  function QuarterRangeControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  QuarterRangeControl.prototype.render = function () {
    var _a = this.props,
      className = _a.className,
      style = _a.style,
      ns = _a.classPrefix,
      minDate = _a.minDate,
      maxDate = _a.maxDate,
      minDuration = _a.minDuration,
      maxDuration = _a.maxDuration,
      data = _a.data,
      format = _a.format,
      valueFormat = _a.valueFormat,
      inputFormat = _a.inputFormat,
      displayFormat = _a.displayFormat,
      env = _a.env,
      mobileUI = _a.mobileUI,
      rest = tslib.__rest(_a, ["className", "style", "classPrefix", "minDate", "maxDate", "minDuration", "maxDuration", "data", "format", "valueFormat", "inputFormat", "displayFormat", "env", "mobileUI"]);
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "DateRangeControl"), className)
    }, {
      children: jsxRuntime.jsx(_DateRangePicker__default["default"], tslib.__assign({
        viewMode: "quarters",
        mobileUI: mobileUI,
        valueFormat: valueFormat || format,
        displayFormat: displayFormat || inputFormat,
        classPrefix: ns,
        popOverContainer: mobileUI ? env === null || env === void 0 ? void 0 : env.getModalContainer : rest.popOverContainer || env.getModalContainer,
        popOverContainerSelector: rest.popOverContainerSelector,
        onRef: this.getRef,
        data: data
      }, rest, {
        minDate: minDate ? amisCore.filterDate(minDate, data, valueFormat || format) : undefined,
        maxDate: maxDate ? amisCore.filterDate(maxDate, data, valueFormat || format) : undefined,
        minDuration: minDuration ? amisCore.parseDuration(minDuration) : undefined,
        maxDuration: maxDuration ? amisCore.parseDuration(maxDuration) : undefined,
        onChange: this.handleChange,
        onFocus: this.dispatchEvent,
        onBlur: this.dispatchEvent
      }))
    }));
  };
  tslib.__decorate([StaticHoc.supportStatic()], QuarterRangeControl.prototype, "render", null);
  return QuarterRangeControl;
}(InputDateRange["default"]);
var QuarterRangeControlRenderer = /** @class */function (_super) {
  tslib.__extends(QuarterRangeControlRenderer, _super);
  function QuarterRangeControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  QuarterRangeControlRenderer.defaultProps = {
    format: 'X',
    inputFormat: 'YYYY-[Q]Q',
    joinValues: true,
    delimiter: ',',
    /** shortcuts的兼容配置 */
    ranges: 'thisquarter,prevquarter',
    shortcuts: 'thisquarter,prevquarter',
    animation: true
  };
  QuarterRangeControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-quarter-range'
  })], QuarterRangeControlRenderer);
  return QuarterRangeControlRenderer;
}(QuarterRangeControl);

exports.QuarterRangeControlRenderer = QuarterRangeControlRenderer;
exports["default"] = QuarterRangeControl;
