/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _DateRangePicker = require('amis-ui/lib/components/DateRangePicker');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');
var cx = require('classnames');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _DateRangePicker__default = /*#__PURE__*/_interopDefaultLegacy(_DateRangePicker);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);

var DateRangeControl = /** @class */function (_super) {
  tslib.__extends(DateRangeControl, _super);
  function DateRangeControl(props) {
    var _this = _super.call(this, props) || this;
    var defaultValue = props.defaultValue,
      setPrinstineValue = props.setPrinstineValue,
      delimiter = props.delimiter,
      format = props.format,
      valueFormat = props.valueFormat,
      data = props.data,
      value = props.value,
      joinValues = props.joinValues,
      utc = props.utc;
    if (defaultValue && value === defaultValue) {
      var arr = typeof defaultValue === 'string' ? defaultValue.split(delimiter) : defaultValue;
      setPrinstineValue(_DateRangePicker__default["default"].formatValue({
        startDate: amisCore.filterDate(arr[0], data, valueFormat || format),
        endDate: amisCore.filterDate(arr[1], data, valueFormat || format)
      }, valueFormat || format, joinValues, delimiter, utc));
    }
    return _this;
    // todo 支持值格式的自动纠正
  }
  DateRangeControl.prototype.componentDidUpdate = function (prevProps) {
    var _a = this.props,
      defaultValue = _a.defaultValue,
      delimiter = _a.delimiter,
      joinValues = _a.joinValues,
      setPrinstineValue = _a.setPrinstineValue,
      data = _a.data,
      utc = _a.utc,
      format = _a.format,
      valueFormat = _a.valueFormat;
    if (prevProps.defaultValue !== defaultValue) {
      var arr = typeof defaultValue === 'string' ? defaultValue.split(delimiter) : defaultValue;
      setPrinstineValue(arr ? _DateRangePicker__default["default"].formatValue({
        startDate: amisCore.filterDate(arr[0], data, valueFormat || format),
        endDate: amisCore.filterDate(arr[1], data, valueFormat || format)
      }, valueFormat || format, joinValues, delimiter, utc) : undefined);
    }
  };
  DateRangeControl.prototype.getRef = function (ref) {
    while (ref && ref.getWrappedInstance) {
      ref = ref.getWrappedInstance();
    }
    this.dateRef = ref;
  };
  // 派发有event的事件
  DateRangeControl.prototype.dispatchEvent = function (eventName) {
    var _a = this.props,
      dispatchEvent = _a.dispatchEvent,
      data = _a.data,
      value = _a.value;
    dispatchEvent(eventName, amisCore.resolveEventData(this.props, {
      value: value
    }));
  };
  // 动作
  DateRangeControl.prototype.doAction = function (action, data, throwErrors) {
    var _a, _b, _c, _d;
    var _e = this.props,
      resetValue = _e.resetValue,
      formStore = _e.formStore,
      store = _e.store,
      name = _e.name;
    if (action.actionType === 'clear') {
      (_a = this.dateRef) === null || _a === void 0 ? void 0 : _a.clear();
      return;
    }
    if (action.actionType === 'reset') {
      var pristineVal = (_c = amisCore.getVariable((_b = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _b !== void 0 ? _b : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _c !== void 0 ? _c : resetValue;
      (_d = this.dateRef) === null || _d === void 0 ? void 0 : _d.reset(pristineVal);
    }
  };
  DateRangeControl.prototype.setData = function (value) {
    var _a = this.props,
      data = _a.data,
      delimiter = _a.delimiter,
      valueFormat = _a.valueFormat,
      format = _a.format,
      joinValues = _a.joinValues,
      utc = _a.utc,
      onChange = _a.onChange;
    if (typeof value === 'string') {
      var arr = typeof value === 'string' ? value.split(delimiter) : value;
      value = _DateRangePicker__default["default"].formatValue({
        startDate: amisCore.filterDate(arr[0], data, valueFormat || format),
        endDate: amisCore.filterDate(arr[1], data, valueFormat || format)
      }, valueFormat || format, joinValues, delimiter, utc);
    }
    onChange(value);
  };
  // 值的变化
  DateRangeControl.prototype.handleChange = function (nextValue) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, dispatchEvent, data, dispatcher;
      return tslib.__generator(this, function (_b) {
        _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
        dispatcher = dispatchEvent('change', amisCore.resolveEventData(this.props, {
          value: nextValue
        }));
        // 因为前面没有 await，所以这里的 dispatcher.prevented 是不准确的。
        // 为什么没写 onChange，我估计是不能让 onChange 太慢执行
        // if (dispatcher?.prevented) {
        //   return;
        // }
        this.props.onChange(nextValue);
        return [2 /*return*/];
      });
    });
  };
  DateRangeControl.prototype.render = function () {
    var _this = this;
    var _a;
    var _b = this.props,
      className = _b.className,
      style = _b.style,
      ns = _b.classPrefix,
      defaultValue = _b.defaultValue,
      defaultData = _b.defaultData,
      minDate = _b.minDate,
      maxDate = _b.maxDate,
      minDuration = _b.minDuration,
      maxDuration = _b.maxDuration,
      data = _b.data,
      format = _b.format,
      valueFormat = _b.valueFormat,
      inputFormat = _b.inputFormat,
      displayFormat = _b.displayFormat,
      env = _b.env,
      mobileUI = _b.mobileUI,
      rest = tslib.__rest(_b, ["className", "style", "classPrefix", "defaultValue", "defaultData", "minDate", "maxDate", "minDuration", "maxDuration", "data", "format", "valueFormat", "inputFormat", "displayFormat", "env", "mobileUI"]);
    var comptType = (_a = this.props) === null || _a === void 0 ? void 0 : _a.type;
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "DateRangeControl"), {
        'is-date': /date-/.test(comptType),
        'is-datetime': /datetime-/.test(comptType)
      }, className)
    }, {
      children: jsxRuntime.jsx(_DateRangePicker__default["default"], tslib.__assign({}, rest, {
        mobileUI: mobileUI,
        classPrefix: ns,
        popOverContainer: mobileUI ? env === null || env === void 0 ? void 0 : env.getModalContainer : rest.popOverContainer || env.getModalContainer,
        popOverContainerSelector: rest.popOverContainerSelector,
        onRef: this.getRef,
        data: data,
        valueFormat: valueFormat || format,
        displayFormat: displayFormat || inputFormat,
        minDate: minDate ? amisCore.filterDate(minDate, data, valueFormat || format) : undefined,
        maxDate: maxDate ? amisCore.filterDate(maxDate, data, valueFormat || format) : undefined,
        minDateRaw: minDate,
        maxDateRaw: maxDate,
        minDuration: minDuration ? amisCore.parseDuration(minDuration) : undefined,
        maxDuration: maxDuration ? amisCore.parseDuration(maxDuration) : undefined,
        onChange: this.handleChange,
        onFocus: function () {
          return _this.dispatchEvent('focus');
        },
        onBlur: function () {
          return _this.dispatchEvent('blur');
        }
      }))
    }));
  };
  DateRangeControl.defaultProps = {
    format: 'X',
    joinValues: true,
    delimiter: ',',
    animation: true
  };
  tslib.__decorate([amisCore.autobind], DateRangeControl.prototype, "getRef", null);
  tslib.__decorate([amisCore.autobind], DateRangeControl.prototype, "dispatchEvent", null);
  tslib.__decorate([amisCore.autobind], DateRangeControl.prototype, "handleChange", null);
  tslib.__decorate([StaticHoc.supportStatic()], DateRangeControl.prototype, "render", null);
  return DateRangeControl;
}(React__default["default"].Component);
var DateRangeControlRenderer = /** @class */function (_super) {
  tslib.__extends(DateRangeControlRenderer, _super);
  function DateRangeControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  DateRangeControlRenderer.defaultProps = tslib.__assign({}, DateRangeControl.defaultProps);
  DateRangeControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-date-range'
  })], DateRangeControlRenderer);
  return DateRangeControlRenderer;
}(DateRangeControl);
var DateTimeRangeControlRenderer = /** @class */function (_super) {
  tslib.__extends(DateTimeRangeControlRenderer, _super);
  function DateTimeRangeControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  DateTimeRangeControlRenderer.defaultProps = tslib.__assign(tslib.__assign({}, DateRangeControl.defaultProps), {
    inputFormat: 'YYYY-MM-DD HH:mm'
  });
  DateTimeRangeControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-datetime-range',
    sizeMutable: false
  })], DateTimeRangeControlRenderer);
  return DateTimeRangeControlRenderer;
}(DateRangeControl);
var TimeRangeControlRenderer = /** @class */function (_super) {
  tslib.__extends(TimeRangeControlRenderer, _super);
  function TimeRangeControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  TimeRangeControlRenderer.defaultProps = tslib.__assign(tslib.__assign({}, DateRangeControl.defaultProps), {
    format: 'HH:mm',
    inputFormat: 'HH:mm',
    viewMode: 'time',
    /** shortcuts的兼容配置 */
    ranges: '',
    shortcuts: ''
  });
  TimeRangeControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-time-range',
    sizeMutable: false
  })], TimeRangeControlRenderer);
  return TimeRangeControlRenderer;
}(DateRangeControl);

exports.DateRangeControlRenderer = DateRangeControlRenderer;
exports.DateTimeRangeControlRenderer = DateTimeRangeControlRenderer;
exports.TimeRangeControlRenderer = TimeRangeControlRenderer;
exports["default"] = DateRangeControl;
