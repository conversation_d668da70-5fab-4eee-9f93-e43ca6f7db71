/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var cx = require('classnames');
var amisCore = require('amis-core');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);

// todo amis-ui 里面组件直接改成按需加载
var ColorPicker = React__default["default"].lazy(function () {
  return Promise.resolve().then(function() {return new Promise(function(fullfill) {require(['amis-ui/lib/components/ColorPicker', "tslib"], function(mod, tslib) {fullfill(tslib.__importStar(mod))})})});
});
var ColorControl = /** @class */function (_super) {
  tslib.__extends(ColorControl, _super);
  function ColorControl() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.state = {
      open: false
    };
    return _this;
  }
  ColorControl.prototype.render = function () {
    var _a = this.props,
      className = _a.className,
      style = _a.style,
      ns = _a.classPrefix,
      value = _a.value,
      env = _a.env,
      isStatic = _a.static,
      mobileUI = _a.mobileUI,
      rest = tslib.__rest(_a, ["className", "style", "classPrefix", "value", "env", "static", "mobileUI"]);
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "ColorControl"), className)
    }, {
      children: jsxRuntime.jsx(React.Suspense, tslib.__assign({
        fallback: jsxRuntime.jsx("div", {
          children: "..."
        })
      }, {
        children: jsxRuntime.jsx(ColorPicker, tslib.__assign({
          classPrefix: ns
        }, rest, {
          placeholder: rest.placeholder,
          mobileUI: mobileUI,
          popOverContainer: mobileUI ? env === null || env === void 0 ? void 0 : env.getModalContainer : rest.popOverContainer || env.getModalContainer,
          value: value || ''
        }))
      }))
    }));
  };
  ColorControl.defaultProps = {
    format: 'hex',
    clearable: true
  };
  tslib.__decorate([StaticHoc.supportStatic()], ColorControl.prototype, "render", null);
  return ColorControl;
}(React__default["default"].PureComponent);
var ColorControlRenderer = /** @class */function (_super) {
  tslib.__extends(ColorControlRenderer, _super);
  function ColorControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  ColorControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-color'
  })], ColorControlRenderer);
  return ColorControlRenderer;
}(ColorControl);

exports.ColorControlRenderer = ColorControlRenderer;
exports.ColorPicker = ColorPicker;
exports["default"] = ColorControl;
