/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var React__default = /*#__PURE__*/_interopDefaultLegacy(React);

var ListControl = /** @class */function (_super) {
  tslib.__extends(ListControl, _super);
  function ListControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  ListControl.prototype.doAction = function (action, data, throwErrors) {
    var _a, _b;
    var _c = this.props,
      resetValue = _c.resetValue,
      onChange = _c.onChange,
      formStore = _c.formStore,
      store = _c.store,
      name = _c.name;
    var actionType = action === null || action === void 0 ? void 0 : action.actionType;
    if (actionType === 'clear') {
      onChange === null || onChange === void 0 ? void 0 : onChange('');
    } else if (actionType === 'reset') {
      var pristineVal = (_b = amisCore.getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
      onChange === null || onChange === void 0 ? void 0 : onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
    }
  };
  ListControl.prototype.handleDBClick = function (option, e) {
    this.props.onToggle(option, false, true);
    this.props.onAction(null, {
      type: 'submit'
    });
  };
  ListControl.prototype.handleClick = function (option, e) {
    if (e.target && e.target.closest('a,button')) {
      return;
    }
    var onToggle = this.props.onToggle;
    onToggle(option);
  };
  ListControl.prototype.reload = function (subpath, query) {
    var reload = this.props.reloadOptions;
    reload && reload(subpath, query);
  };
  ListControl.prototype.renderStatic = function (displayValue) {
    if (displayValue === void 0) {
      displayValue = '-';
    }
    var _a = this.props,
      itemSchema = _a.itemSchema,
      labelField = _a.labelField,
      valueField = _a.valueField,
      imageClassName = _a.imageClassName,
      itemClassName = _a.itemClassName,
      selectedOptions = _a.selectedOptions,
      cx = _a.classnames,
      render = _a.render,
      data = _a.data;
    if (!selectedOptions.length) {
      return displayValue;
    }
    var itemRender = function (option, key) {
      var label = option[labelField || 'label'];
      label = label || "\u9009\u9879".concat(key + 1);
      if (itemSchema || option.body || option.image) {
        return jsxRuntime.jsx("div", tslib.__assign({
          className: cx('ListControl-static-item', itemClassName)
        }, {
          children: itemSchema ? render("".concat(key, "/body"), itemSchema, {
            data: amisCore.createObject(data, option)
          }) : option.body ? render("".concat(key, "/body"), option.body) : [option.image ? jsxRuntime.jsx("div", tslib.__assign({
            className: cx('ListControl-itemImage', imageClassName)
          }, {
            children: jsxRuntime.jsx("img", {
              src: option.image,
              alt: label
            })
          }), "image") : null, jsxRuntime.jsx("div", tslib.__assign({
            className: cx('ListControl-itemLabel')
          }, {
            children: label
          }), "label")]
        }), key);
      }
      return jsxRuntime.jsx("div", tslib.__assign({
        className: cx("ListControl-static-item")
      }, {
        children: label
      }), key);
    };
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx('StaticList')
    }, {
      children: selectedOptions.map(itemRender)
    }));
  };
  ListControl.prototype.render = function () {
    var _this = this;
    var _a = this.props,
      render = _a.render,
      itemClassName = _a.itemClassName,
      cx = _a.classnames,
      className = _a.className,
      style = _a.style,
      disabled = _a.disabled,
      options = _a.options,
      placeholder = _a.placeholder,
      selectedOptions = _a.selectedOptions,
      imageClassName = _a.imageClassName,
      submitOnDBClick = _a.submitOnDBClick,
      itemSchema = _a.itemSchema,
      activeItemSchema = _a.activeItemSchema,
      data = _a.data,
      labelField = _a.labelField,
      listClassName = _a.listClassName,
      __ = _a.translate,
      testIdBuilder = _a.testIdBuilder;
    var body = null;
    if (options && options.length) {
      body = jsxRuntime.jsx("div", tslib.__assign({
        className: cx('ListControl-items', listClassName)
      }, {
        children: options.map(function (option, key) {
          return jsxRuntime.jsx("div", tslib.__assign({
            className: cx("ListControl-item", itemClassName, {
              'is-active': ~selectedOptions.indexOf(option),
              'is-disabled': option.disabled || disabled,
              'is-custom': !!itemSchema
            }),
            onClick: _this.handleClick.bind(_this, option),
            onDoubleClick: submitOnDBClick ? _this.handleDBClick.bind(_this, option) : undefined
          }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("options-".concat(option.value || key)).getTestId(), {
            children: itemSchema ? render("".concat(key, "/body"), ~selectedOptions.indexOf(option) ? activeItemSchema !== null && activeItemSchema !== void 0 ? activeItemSchema : itemSchema : itemSchema, {
              data: amisCore.createObject(data, option)
            }) : option.body ? render("".concat(key, "/body"), option.body) : [option.image ? jsxRuntime.jsx("div", tslib.__assign({
              className: cx('ListControl-itemImage', imageClassName)
            }, {
              children: jsxRuntime.jsx("img", {
                src: option.image,
                alt: option[labelField || 'label']
              })
            }), "image") : null, option[labelField || 'label'] ? jsxRuntime.jsx("div", tslib.__assign({
              className: cx('ListControl-itemLabel')
            }, {
              children: amisCore.filter(String(option[labelField || 'label']), data)
            }), "label") : null
            // {/* {option.tip ? (<div className={`${ns}ListControl-tip`}>{option.tip}</div>) : null} */}
            ]
          }), key);
        })
      }));
    }
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx('ListControl', className)
    }, {
      children: body ? body : jsxRuntime.jsx("span", tslib.__assign({
        className: cx('ListControl-placeholder')
      }, {
        children: __(placeholder)
      }))
    }));
  };
  ListControl.propsList = ['itemSchema', 'value', 'renderFormItems'];
  ListControl.defaultProps = {
    clearable: false,
    imageClassName: '',
    submitOnDBClick: false
  };
  tslib.__decorate([StaticHoc.supportStatic()], ListControl.prototype, "render", null);
  return ListControl;
}(React__default["default"].Component);
var ListControlRenderer = /** @class */function (_super) {
  tslib.__extends(ListControlRenderer, _super);
  function ListControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  ListControlRenderer = tslib.__decorate([amisCore.OptionsControl({
    type: 'list-select',
    sizeMutable: false
  })], ListControlRenderer);
  return ListControlRenderer;
}(ListControl);

exports.ListControlRenderer = ListControlRenderer;
exports["default"] = ListControl;
