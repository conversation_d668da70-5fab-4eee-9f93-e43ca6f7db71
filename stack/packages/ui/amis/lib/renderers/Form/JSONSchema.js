/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _withRemoteConfig = require('amis-ui/lib/withRemoteConfig');
var _InputJSONSchema = require('amis-ui/lib/components/InputJSONSchema');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _withRemoteConfig__default = /*#__PURE__*/_interopDefaultLegacy(_withRemoteConfig);
var _InputJSONSchema__default = /*#__PURE__*/_interopDefaultLegacy(_InputJSONSchema);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);

var EnhancedInputJSONSchema = _withRemoteConfig__default["default"]({
  sourceField: 'schema',
  injectedPropsFilter: function (injectedProps, props) {
    return {
      schema: injectedProps.config,
      loading: injectedProps.loading
    };
  }
})(_InputJSONSchema__default["default"]);
var JSONSchemaControl = /** @class */function (_super) {
  tslib.__extends(JSONSchemaControl, _super);
  function JSONSchemaControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  JSONSchemaControl.prototype.controlRef = function (ref) {
    while (ref === null || ref === void 0 ? void 0 : ref.getWrappedInstance) {
      ref = ref.getWrappedInstance();
    }
    this.control = ref;
  };
  JSONSchemaControl.prototype.validate = function () {
    var _a;
    return (_a = this.control) === null || _a === void 0 ? void 0 : _a.validate();
  };
  JSONSchemaControl.prototype.render = function () {
    var rest = tslib.__rest(this.props, []);
    return jsxRuntime.jsx(EnhancedInputJSONSchema, tslib.__assign({}, rest, {
      ref: this.controlRef
    }));
  };
  tslib.__decorate([amisCore.autobind], JSONSchemaControl.prototype, "controlRef", null);
  return JSONSchemaControl;
}(React__default["default"].PureComponent);
var JSONSchemaRenderer = /** @class */function (_super) {
  tslib.__extends(JSONSchemaRenderer, _super);
  function JSONSchemaRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  JSONSchemaRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'json-schema',
    strictMode: false
  })], JSONSchemaRenderer);
  return JSONSchemaRenderer;
}(JSONSchemaControl);

exports.JSONSchemaRenderer = JSONSchemaRenderer;
exports["default"] = JSONSchemaControl;
