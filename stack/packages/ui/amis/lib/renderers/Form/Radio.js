/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _withBadge = require('amis-ui/lib/components/withBadge');
var _Checkbox = require('amis-ui/lib/components/Checkbox');
var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');
var cx = require('classnames');
var StaticHoc = require('./StaticHoc.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _withBadge__default = /*#__PURE__*/_interopDefaultLegacy(_withBadge);
var _Checkbox__default = /*#__PURE__*/_interopDefaultLegacy(_Checkbox);
var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);

var RadioControl = /** @class */function (_super) {
  tslib.__extends(RadioControl, _super);
  function RadioControl() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  RadioControl.prototype.doAction = function (action, data, throwErrors) {
    var _a, _b;
    var _c = this.props,
      resetValue = _c.resetValue,
      onChange = _c.onChange,
      formStore = _c.formStore,
      store = _c.store,
      name = _c.name;
    var actionType = action === null || action === void 0 ? void 0 : action.actionType;
    if (actionType === 'clear') {
      onChange('');
    } else if (actionType === 'reset') {
      var pristineVal = (_b = amisCore.getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
      onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
    }
  };
  RadioControl.prototype.dispatchChangeEvent = function (eventData) {
    if (eventData === void 0) {
      eventData = {};
    }
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, dispatchEvent, onChange, submitOnChange, onRadioChange, ctx, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, dispatchEvent = _a.dispatchEvent, onChange = _a.onChange, submitOnChange = _a.submitOnChange, onRadioChange = _a.onRadioChange;
            ctx = amisCore.resolveEventData(this.props, {
              value: eventData
            });
            if ((onRadioChange === null || onRadioChange === void 0 ? void 0 : onRadioChange(ctx, this.props)) === false) {
              return [2 /*return*/];
            }
            return [4 /*yield*/, dispatchEvent('change', ctx)];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            onChange && onChange(eventData, submitOnChange, true);
            return [2 /*return*/];
        }
      });
    });
  };
  RadioControl.prototype.renderStatic = function () {
    var _a = this.props,
      value = _a.value,
      trueValue = _a.trueValue,
      falseValue = _a.falseValue,
      option = _a.option,
      render = _a.render,
      partial = _a.partial,
      optionType = _a.optionType,
      checked = _a.checked,
      labelClassName = _a.labelClassName;
    return jsxRuntime.jsx(_Checkbox__default["default"], tslib.__assign({
      type: "radio",
      inline: true,
      value: value || '',
      trueValue: trueValue,
      falseValue: falseValue,
      disabled: true,
      partial: partial,
      optionType: optionType,
      checked: checked,
      labelClassName: labelClassName
    }, {
      children: option ? render('option', option) : null
    }));
  };
  RadioControl.prototype.render = function () {
    var _this = this;
    var _a = this.props,
      className = _a.className,
      style = _a.style,
      value = _a.value,
      trueValue = _a.trueValue,
      falseValue = _a.falseValue,
      option = _a.option,
      onChange = _a.onChange,
      disabled = _a.disabled,
      render = _a.render,
      partial = _a.partial,
      optionType = _a.optionType,
      checked = _a.checked,
      labelClassName = _a.labelClassName,
      ns = _a.classPrefix;
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "CheckboxControl"), className)
    }, {
      children: jsxRuntime.jsx(_Checkbox__default["default"], tslib.__assign({
        type: "radio",
        inline: true,
        value: value || '',
        trueValue: trueValue,
        falseValue: falseValue,
        disabled: disabled,
        onChange: function (value) {
          return _this.dispatchChangeEvent(value);
        },
        partial: partial,
        optionType: optionType,
        checked: checked,
        labelClassName: labelClassName
      }, {
        children: option ? render('option', option) : null
      }))
    }));
  };
  RadioControl.defaultProps = {
    trueValue: true,
    falseValue: false
  };
  tslib.__decorate([amisCore.autobind], RadioControl.prototype, "dispatchChangeEvent", null);
  tslib.__decorate([StaticHoc.supportStatic()], RadioControl.prototype, "render", null);
  return RadioControl;
}(React__default["default"].Component);
// @ts-ignore
var RadioControlRenderer = /** @class */function (_super) {
  tslib.__extends(RadioControlRenderer, _super);
  function RadioControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  RadioControlRenderer = tslib.__decorate([_withBadge__default["default"], amisCore.FormItem({
    type: 'radio',
    sizeMutable: false,
    thin: true
  })], RadioControlRenderer);
  return RadioControlRenderer;
}(RadioControl);

exports.RadioControlRenderer = RadioControlRenderer;
exports["default"] = RadioControl;
