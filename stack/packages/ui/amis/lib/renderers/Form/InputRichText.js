/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');
var amisCore = require('amis-core');
var isEqual = require('lodash/isEqual');
var cx = require('classnames');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var React__default = /*#__PURE__*/_interopDefaultLegacy(React);
var isEqual__default = /*#__PURE__*/_interopDefaultLegacy(isEqual);
var cx__default = /*#__PURE__*/_interopDefaultLegacy(cx);

function loadRichText(type) {
  if (type === void 0) {
    type = 'froala';
  }
  return function () {
    return type === 'tinymce' ? Promise.resolve().then(function() {return new Promise(function(fullfill) {require(['amis-ui/lib/components/Tinymce', "tslib"], function(mod, tslib) {fullfill(tslib.__importStar(mod))})})}).then(function (item) {
      return item.default;
    }) : Promise.resolve().then(function() {return new Promise(function(fullfill) {require(['amis-ui/lib/components/RichText', "tslib"], function(mod, tslib) {fullfill(tslib.__importStar(mod))})})}).then(function (item) {
      return item.default;
    });
  };
}
var RichTextControl = /** @class */function (_super) {
  tslib.__extends(RichTextControl, _super);
  function RichTextControl(props) {
    var _this = _super.call(this, props) || this;
    _this.state = {
      config: null,
      focused: false
    };
    _this.handleFocus = _this.handleFocus.bind(_this);
    _this.handleBlur = _this.handleBlur.bind(_this);
    _this.handleChange = _this.handleChange.bind(_this);
    _this.state.config = _this.getConfig(props);
    return _this;
  }
  RichTextControl.prototype.componentDidUpdate = function (prevProps) {
    var props = this.props;
    var finnalVendor = props.vendor || (props.env.richTextToken ? 'froala' : 'tinymce');
    if (finnalVendor === 'froala') {
      if (!isEqual__default["default"](prevProps.options, props.options) || !isEqual__default["default"](prevProps.editorClass, props.editorClass) || !isEqual__default["default"](prevProps.placeholder, props.placeholder) || !isEqual__default["default"](prevProps.buttons, props.buttons)) {
        this.setState({
          config: this.getConfig(props)
        });
      }
    } else if (finnalVendor === 'tinymce') {
      if (!isEqual__default["default"](prevProps.options, props.options) || !isEqual__default["default"](prevProps.fileField, props.fileField)) {
        this.setState({
          config: this.getConfig(props)
        });
      }
    }
  };
  RichTextControl.prototype.uploadFile = function (blob, filename, mediaType) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    return tslib.__awaiter(this, void 0, void 0, function () {
      var formData, _l, imageReceiver, videoReceiver, fileReceiver, env, fileField, data, api, apiObject, fetcher, receiver, response, location_1;
      return tslib.__generator(this, function (_m) {
        switch (_m.label) {
          case 0:
            formData = new FormData();
            _l = this.props, imageReceiver = _l.receiver, videoReceiver = _l.videoReceiver, fileReceiver = _l.fileReceiver, env = _l.env, fileField = _l.fileField, data = _l.data;
            api = (mediaType === 'file' ? fileReceiver : mediaType === 'media' ? videoReceiver : imageReceiver) || imageReceiver;
            apiObject = amisCore.buildApi(api, data, {
              method: api.method || 'post'
            });
            if (!apiObject.url) {
              return [2 /*return*/, new Promise(function (resolve) {
                var reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = function () {
                  var base64data = reader.result;
                  resolve({
                    link: base64data,
                    meta: {
                      alt: filename,
                      text: filename,
                      title: filename,
                      type: 'image',
                      size: blob.size
                    }
                  });
                };
              })];
            } else if (apiObject.data) {
              amisCore.qsstringify(apiObject.data).split('&').filter(function (item) {
                return item !== '';
              }).forEach(function (item) {
                var parts = item.split('=');
                formData.append(parts[0], decodeURIComponent(parts[1]));
              });
            }
            formData.append(fileField || 'file', blob, filename);
            fetcher = env.fetcher;
            receiver = tslib.__assign({
              adaptor: function (payload) {
                return tslib.__assign(tslib.__assign({}, payload), {
                  data: payload
                });
              }
            }, apiObject);
            return [4 /*yield*/, fetcher(receiver, formData, {
              method: 'post'
            })];
          case 1:
            response = _m.sent();
            if (response.ok) {
              location_1 = ((_a = response.data) === null || _a === void 0 ? void 0 : _a.link) || ((_b = response.data) === null || _b === void 0 ? void 0 : _b.url) || ((_c = response.data) === null || _c === void 0 ? void 0 : _c.value) || ((_e = (_d = response.data) === null || _d === void 0 ? void 0 : _d.data) === null || _e === void 0 ? void 0 : _e.link) || ((_g = (_f = response.data) === null || _f === void 0 ? void 0 : _f.data) === null || _g === void 0 ? void 0 : _g.url) || ((_j = (_h = response.data) === null || _h === void 0 ? void 0 : _h.data) === null || _j === void 0 ? void 0 : _j.value);
              if (location_1) {
                return [2 /*return*/, {
                  link: location_1,
                  meta: tslib.__assign(tslib.__assign({
                    alt: filename,
                    text: filename,
                    title: filename
                  }, ((_k = response.data) === null || _k === void 0 ? void 0 : _k.data) || response.data), {
                    type: 'image',
                    size: blob.size
                  })
                }];
              } else {
                console.warn('must have return value');
                throw new Error('must have return value');
              }
            } else {
              throw new Error(response.msg || '上传失败');
            }
            return [2 /*return*/];
        }
      });
    });
  };
  RichTextControl.prototype.getConfig = function (props) {
    var _this = this;
    var _a;
    var finnalVendor = props.vendor || (props.env.richTextToken ? 'froala' : 'tinymce');
    var imageReceiver = amisCore.normalizeApi(props.receiver, ((_a = props.receiver) === null || _a === void 0 ? void 0 : _a.method) || 'post');
    imageReceiver.data = imageReceiver.data || {};
    var imageApi = amisCore.buildApi(imageReceiver, props.data, {
      method: props.receiver.method || 'post'
    });
    if (finnalVendor === 'froala') {
      var videoReceiver = amisCore.normalizeApi(props.videoReceiver, props.videoReceiver.method || 'post');
      videoReceiver.data = videoReceiver.data || {};
      var videoApi = amisCore.buildApi(videoReceiver, props.data, {
        method: props.videoReceiver.method || 'post'
      });
      return tslib.__assign(tslib.__assign(tslib.__assign({
        imageAllowedTypes: ['jpeg', 'jpg', 'png', 'gif'],
        imageDefaultAlign: 'left',
        imageEditButtons: props.imageEditable ? ['imageReplace', 'imageAlign', 'imageRemove', '|', 'imageLink', 'linkOpen', 'linkEdit', 'linkRemove', '-', 'imageDisplay', 'imageStyle', 'imageAlt', 'imageSize'] : [],
        key: props.env.richTextToken,
        attribution: false
      }, props.options), {
        editorClass: props.editorClass,
        placeholderText: props.translate(props.placeholder),
        imageUploadURL: imageApi.url,
        imageUploadParams: tslib.__assign({
          from: 'rich-text'
        }, imageApi.data),
        videoUploadURL: videoApi.url,
        videoUploadParams: tslib.__assign({
          from: 'rich-text'
        }, videoApi.data),
        events: tslib.__assign(tslib.__assign({}, props.options && props.options.events), {
          focus: this.handleFocus,
          blur: this.handleBlur
        }),
        language: !this.props.locale || this.props.locale === 'zh-CN' ? 'zh_cn' : ''
      }), props.buttons ? {
        toolbarButtons: props.buttons
      } : {});
    } else {
      return tslib.__assign(tslib.__assign({
        images_file_types: 'gif,jpg,png,svg,webp',
        file_picker_types: 'file media image'
      }, props.options), {
        onLoaded: this.handleTinyMceLoaded,
        images_upload_handler: function (blobInfo, progress) {
          return _this.uploadFile(blobInfo.blob(), blobInfo.filename(), 'image').then(function (item) {
            return item.link;
          });
        },
        file_picker_callback: function (callback, value, meta) {
          var input = document.createElement('input');
          input.setAttribute('type', 'file');
          if (meta.filetype === 'media') {
            input.setAttribute('accept', 'video/*');
          } else if (meta.filetype === 'image') {
            input.setAttribute('accept', 'image/*');
          } else {
            // 其他类型，先不限制了
          }
          input.onchange = function (e) {
            var file = e.target.files[0];
            if (file) {
              _this.uploadFile(file, file.name, meta.filetype).then(function (item) {
                callback(item.link, item.meta);
              });
            }
          };
          input.click();
        }
      });
    }
  };
  RichTextControl.prototype.handleFocus = function () {
    this.setState({
      focused: true
    });
  };
  RichTextControl.prototype.handleBlur = function () {
    this.setState({
      focused: false
    });
  };
  RichTextControl.prototype.handleChange = function (value, submitOnChange, changeImmediately) {
    return tslib.__awaiter(this, void 0, void 0, function () {
      var _a, onChange, disabled, dispatchEvent, rendererEvent;
      return tslib.__generator(this, function (_b) {
        switch (_b.label) {
          case 0:
            _a = this.props, onChange = _a.onChange, disabled = _a.disabled, dispatchEvent = _a.dispatchEvent;
            if (disabled) {
              return [2 /*return*/];
            }
            return [4 /*yield*/, dispatchEvent('change', amisCore.resolveEventData(this.props, {
              value: value
            }))];
          case 1:
            rendererEvent = _b.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [2 /*return*/];
            }
            onChange === null || onChange === void 0 ? void 0 : onChange(value, submitOnChange, changeImmediately);
            return [2 /*return*/];
        }
      });
    });
  };
  RichTextControl.prototype.handleTinyMceLoaded = function (tinymce) {
    var _a;
    var env = this.props.env;
    return (_a = env === null || env === void 0 ? void 0 : env.loadTinymcePlugin) === null || _a === void 0 ? void 0 : _a.call(env, tinymce);
  };
  RichTextControl.prototype.render = function () {
    var _a, _b;
    var _c = this.props,
      className = _c.className,
      style = _c.style,
      ns = _c.classPrefix,
      value = _c.value,
      onChange = _c.onChange,
      disabled = _c.disabled,
      isStatic = _c.static,
      size = _c.size,
      vendor = _c.vendor,
      env = _c.env,
      locale = _c.locale,
      translate = _c.translate,
      borderMode = _c.borderMode;
    var finnalVendor = vendor || (env.richTextToken ? 'froala' : 'tinymce');
    if (isStatic) {
      return jsxRuntime.jsx("div", {
        className: cx__default["default"]("".concat(ns, "RichTextControl"), className, (_a = {
          'is-focused': this.state.focused,
          'is-disabled': disabled
        }, _a["".concat(ns, "RichTextControl--border").concat(amisCore.ucFirst(borderMode))] = borderMode, _a)),
        dangerouslySetInnerHTML: {
          __html: env.filterHtml(value)
        }
      });
    }
    return jsxRuntime.jsx("div", tslib.__assign({
      className: cx__default["default"]("".concat(ns, "RichTextControl"), className, (_b = {
        'is-focused': this.state.focused,
        'is-disabled': disabled
      }, _b["".concat(ns, "RichTextControl--border").concat(amisCore.ucFirst(borderMode))] = borderMode, _b))
    }, {
      children: jsxRuntime.jsx(amisCore.LazyComponent, {
        getComponent: loadRichText(finnalVendor),
        model: value,
        onModelChange: this.handleChange,
        onFocus: this.handleFocus,
        onBlur: this.handleBlur,
        config: this.state.config,
        disabled: disabled,
        locale: locale,
        translate: translate
      })
    }));
  };
  RichTextControl.defaultProps = {
    imageEditable: true,
    receiver: '/api/upload/image',
    videoReceiver: '/api/upload/video',
    fileField: 'file',
    placeholder: 'placeholder.enter',
    options: {
      toolbarButtons: ['undo', 'redo', 'paragraphFormat', 'textColor', 'backgroundColor', 'bold', 'underline', 'strikeThrough', 'formatOL', 'formatUL', 'align', 'quote', 'insertLink', 'insertImage', 'insertEmotion', 'insertTable', 'html']
    }
  };
  tslib.__decorate([amisCore.autobind], RichTextControl.prototype, "handleTinyMceLoaded", null);
  return RichTextControl;
}(React__default["default"].Component);
var RichTextControlRenderer = /** @class */function (_super) {
  tslib.__extends(RichTextControlRenderer, _super);
  function RichTextControlRenderer() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  RichTextControlRenderer = tslib.__decorate([amisCore.FormItem({
    type: 'input-rich-text',
    sizeMutable: false,
    detectProps: ['options', 'buttons']
  })], RichTextControlRenderer);
  return RichTextControlRenderer;
}(RichTextControl);

exports.RichTextControlRenderer = RichTextControlRenderer;
exports["default"] = RichTextControl;
