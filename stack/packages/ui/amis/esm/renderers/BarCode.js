/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React, { Suspense } from 'react';
import { getPropValue, Renderer } from 'amis-core';

var BarCode = React.lazy(function () { return import('amis-ui/lib/components/BarCode'); });
var BarCodeField = /** @class */ (function (_super) {
    __extends(BarCodeField, _super);
    function BarCodeField() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    BarCodeField.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, width = _a.width, height = _a.height, cx = _a.classnames, options = _a.options;
        var value = getPropValue(this.props);
        return (jsx(Suspense, __assign({ fallback: jsx("div", { children: "..." }) }, { children: jsx("div", __assign({ "data-testid": "barcode", className: cx('BarCode', className), style: style }, { children: jsx(BarCode, { value: value, options: options }) })) })));
    };
    return BarCodeField;
}(React.Component));
var BarCodeFieldRenderer = /** @class */ (function (_super) {
    __extends(BarCodeFieldRenderer, _super);
    function BarCodeFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    BarCodeFieldRenderer = __decorate([
        Renderer({
            type: 'barcode'
        })
    ], BarCodeFieldRenderer);
    return BarCodeFieldRenderer;
}(BarCodeField));

export { BarCodeField, BarCodeFieldRenderer };
