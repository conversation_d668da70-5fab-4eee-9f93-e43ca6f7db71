/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign, __extends, __rest, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { filter, createObject, isObject, isPureVariable, resolveVariableAndFilter, isEffectiveApi, autobind, Renderer } from 'amis-core';
import { Timeline, withRemoteConfig } from 'amis-ui';

var DirectionMode;
(function (DirectionMode) {
    DirectionMode["left"] = "left";
    DirectionMode["right"] = "right";
    DirectionMode["top"] = "top";
    DirectionMode["bottom"] = "bottom";
    DirectionMode["alternate"] = "alternate";
})(DirectionMode || (DirectionMode = {}));
function TimelineCmpt(props) {
    var items = props.items, mode = props.mode, style = props.style, direction = props.direction, reverse = props.reverse, data = props.data, itemTitleSchema = props.itemTitleSchema, className = props.className, timeClassName = props.timeClassName, titleClassName = props.titleClassName, detailClassName = props.detailClassName, commonCardSchema = props.cardSchema, name = props.name, itemKeyName = props.itemKeyName, indexKeyName = props.indexKeyName, render = props.render;
    // 渲染内容
    var resolveRender = function (region, val) {
        return typeof val === 'string' ? filter(val, data) : val && render(region, val);
    };
    // 处理源数据
    var resolveTimelineItems = (items || []).map(function (timelineItem, index) {
        var _a, _b;
        var icon = timelineItem.icon, iconClassName = timelineItem.iconClassName, title = timelineItem.title, time = timelineItem.time, detail = timelineItem.detail, timeClassName = timelineItem.timeClassName, titleClassName = timelineItem.titleClassName, detailClassName = timelineItem.detailClassName, cardSchema = timelineItem.cardSchema;
        var cardRenderer = cardSchema || commonCardSchema;
        var ctx = createObject(data, __assign(__assign({}, (isObject(timelineItem)
            ? __assign({ index: index }, timelineItem) : (_a = {}, _a[name] = timelineItem, _a))), (_b = {}, _b[itemKeyName || 'item'] = timelineItem, _b[indexKeyName || 'index'] = index, _b)));
        return __assign(__assign({}, timelineItem), { iconClassName: iconClassName, timeClassName: timeClassName, titleClassName: titleClassName, detailClassName: detailClassName, icon: isPureVariable(icon)
                ? resolveVariableAndFilter(icon, ctx, '| raw')
                : icon, title: itemTitleSchema
                ? render("".concat(index, "/body"), itemTitleSchema, {
                    data: ctx
                })
                : resolveRender('title', title), time: resolveRender('time', time), detail: resolveRender('detail', detail), cardNode: cardRenderer
                ? render('card', cardRenderer, {
                    data: ctx // 当前继承的data和本身节点的数据作为当前卡片schema的渲染数据
                })
                : undefined });
    });
    return (jsx(Timeline, { items: resolveTimelineItems, direction: direction, reverse: reverse, mode: mode, style: style, className: className, timeClassName: timeClassName, titleClassName: titleClassName, detailClassName: detailClassName }));
}
var TimelineWithRemoteConfig = withRemoteConfig({
    adaptor: function (data) { return data.items || data; }
})(/** @class */ (function (_super) {
    __extends(class_1, _super);
    function class_1() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    class_1.prototype.render = function () {
        var _a = this.props, config = _a.config, items = _a.items, deferLoad = _a.deferLoad, loading = _a.loading, updateConfig = _a.updateConfig, rest = __rest(_a, ["config", "items", "deferLoad", "loading", "updateConfig"]);
        var sourceItems = config
            ? Array.isArray(config)
                ? config
                : Object.keys(config).map(function (key) { return ({
                    time: key,
                    title: config[key]
                }); })
            : items || [];
        return jsx(TimelineCmpt, __assign({ items: sourceItems }, rest));
    };
    return class_1;
}(React.Component)));
var TimelineRenderer = /** @class */ (function (_super) {
    __extends(TimelineRenderer, _super);
    function TimelineRenderer() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.remoteRef = undefined;
        return _this;
    }
    TimelineRenderer.prototype.remoteConfigRef = function (ref) {
        this.remoteRef = ref;
    };
    TimelineRenderer.prototype.componentDidUpdate = function (prevProps) {
        var _a = this.props, source = _a.source, data = _a.data;
        if (this.remoteRef && source !== prevProps.source) {
            // 如果是变量，则同步配置。如果为api，则重新加载配置
            (isPureVariable(source) && this.remoteRef.syncConfig()) ||
                (isEffectiveApi(source, data)
                    ? source.autoRefresh !== false &&
                        this.remoteRef.loadConfig()
                    : this.remoteRef.setConfig(undefined));
        }
    };
    TimelineRenderer.prototype.render = function () {
        return (jsx(TimelineWithRemoteConfig, __assign({}, this.props, { remoteConfigRef: this.remoteConfigRef })));
    };
    __decorate([
        autobind
    ], TimelineRenderer.prototype, "remoteConfigRef", null);
    TimelineRenderer = __decorate([
        Renderer({
            type: 'timeline'
        })
    ], TimelineRenderer);
    return TimelineRenderer;
}(React.Component));

export { TimelineCmpt, TimelineRenderer };
