/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign } from 'tslib';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import React from 'react';
import { findDOMNode } from 'react-dom';
import hoistNonReactStatic from 'hoist-non-react-statics';
import { RootClose, Overlay, PopOver } from 'amis-core';
import { Icon } from 'amis-ui';

var HocPopOver = function (config) {
    if (config === void 0) { config = {}; }
    return function (Component) {
        var lastOpenedInstance = null;
        var PopOverComponent = /** @class */ (function (_super) {
            __extends(PopOverComponent, _super);
            function PopOverComponent(props) {
                var _this = _super.call(this, props) || this;
                _this.openPopOver = _this.openPopOver.bind(_this);
                _this.closePopOver = _this.closePopOver.bind(_this);
                _this.closePopOverLater = _this.closePopOverLater.bind(_this);
                _this.clearCloseTimer = _this.clearCloseTimer.bind(_this);
                _this.targetRef = _this.targetRef.bind(_this);
                // this.handleClickOutside = this.handleClickOutside.bind(this);
                _this.state = {
                    isOpened: false
                };
                return _this;
            }
            PopOverComponent.prototype.targetRef = function (ref) {
                this.target = ref;
            };
            PopOverComponent.prototype.openPopOver = function (event) {
                var _this = this;
                var onPopOverOpened = this.props.onPopOverOpened;
                lastOpenedInstance === null || lastOpenedInstance === void 0 ? void 0 : lastOpenedInstance.closePopOver();
                lastOpenedInstance = this;
                var e = event.currentTarget;
                // 如果内容不超出，不需要弹出
                if ((this.getClassName() === 'ellipsis' &&
                    e &&
                    e.clientHeight >= e.scrollHeight) ||
                    this.getClassName() === 'noWrap') {
                    return;
                }
                this.setState({
                    isOpened: true
                }, function () { return onPopOverOpened && onPopOverOpened(_this.props.popOver); });
            };
            PopOverComponent.prototype.closePopOver = function () {
                var _this = this;
                clearTimeout(this.timer);
                if (!this.state.isOpened) {
                    return;
                }
                lastOpenedInstance = null;
                var onPopOverClosed = this.props.onPopOverClosed;
                this.setState({
                    isOpened: false
                }, function () { return onPopOverClosed && onPopOverClosed(_this.props.popOver); });
            };
            PopOverComponent.prototype.closePopOverLater = function () {
                // 5s 后自动关闭。
                this.timer = setTimeout(this.closePopOver, 500);
            };
            PopOverComponent.prototype.clearCloseTimer = function () {
                clearTimeout(this.timer);
            };
            PopOverComponent.prototype.buildSchema = function () {
                var _a = this.props, popOver = _a.popOver, name = _a.name, label = _a.label, __ = _a.translate, column = _a.column;
                var schema;
                if (popOver === true) {
                    schema = {
                        type: 'panel',
                        body: "${".concat(name, "}")
                    };
                }
                else if (popOver &&
                    (popOver.mode === 'dialog' || popOver.mode === 'drawer')) {
                    schema = __assign(__assign({ actions: [
                            {
                                label: __('Dialog.close'),
                                type: 'button',
                                actionType: 'cancel'
                            }
                        ] }, popOver), { type: popOver.mode });
                }
                else if (typeof popOver === 'string') {
                    schema = {
                        type: 'panel',
                        body: popOver
                    };
                }
                else if (popOver) {
                    schema = __assign({ type: 'panel' }, popOver);
                }
                else if (this.getClassName() === 'ellipsis') {
                    schema = {
                        type: 'panel',
                        body: column && column.type === 'mapping' ? column : "${".concat(name, "}")
                    };
                }
                return schema || 'error';
            };
            PopOverComponent.prototype.getOffset = function () {
                var popOver = this.props.popOver;
                if (!popOver || typeof popOver === 'boolean' || !popOver.offset) {
                    return undefined;
                }
                // PopOver 组件接收的 offset 格式为 { x: number, y: number }
                return {
                    x: popOver.offset.left || 0,
                    y: popOver.offset.top || 0
                };
            };
            PopOverComponent.prototype.renderPopOver = function () {
                var _this = this;
                var _a = this.props, popOver = _a.popOver, render = _a.render, popOverContainer = _a.popOverContainer, cx = _a.classnames, ns = _a.classPrefix;
                if (popOver &&
                    (popOver.mode === 'dialog' ||
                        popOver.mode === 'drawer')) {
                    return render('popover-detail', this.buildSchema(), {
                        show: true,
                        onClose: this.closePopOver,
                        onConfirm: this.closePopOver
                    });
                }
                var content = render('popover-detail', this.buildSchema(), {
                    className: cx(popOver && popOver.className)
                });
                if (!popOverContainer) {
                    popOverContainer = function () { return findDOMNode(_this); };
                }
                var selectClassName = this.getClassName();
                var defaultPlacement = selectClassName === 'ellipsis' && !popOver ? 'auto' : 'center';
                var position = (popOver && popOver.position) || '';
                var isFixed = /^fixed\-/.test(position);
                return isFixed ? (jsx(RootClose, __assign({ disabled: !this.state.isOpened, onRootClose: this.closePopOver }, { children: function (ref) {
                        return (jsx("div", __assign({ className: cx("PopOverAble--fixed PopOverAble--".concat(position)), onMouseLeave: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover'
                                ? _this.closePopOver
                                : undefined, onMouseEnter: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover'
                                ? _this.clearCloseTimer
                                : undefined, ref: ref }, { children: content })));
                    } }))) : (jsx(Overlay, __assign({ container: popOverContainer, placement: position || config.position || defaultPlacement, target: function () { return _this.target; }, onHide: this.closePopOver, rootClose: true, show: true }, { children: jsx(PopOver, __assign({ classPrefix: ns, className: cx('PopOverAble-popover', popOver && popOver.popOverClassName), offset: this.getOffset(), onMouseLeave: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover' ||
                            selectClassName
                            ? this.closePopOver
                            : undefined, onMouseEnter: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover' ||
                            selectClassName
                            ? this.clearCloseTimer
                            : undefined }, { children: content })) })));
            };
            PopOverComponent.prototype.getClassName = function () {
                var textOverflow = this.props.textOverflow;
                return textOverflow === 'default' ? '' : textOverflow;
            };
            PopOverComponent.prototype.render = function () {
                var _a = this.props, popOver = _a.popOver, popOverEnabled = _a.popOverEnabled, popOverEnable = _a.popOverEnable, className = _a.className, noHoc = _a.noHoc, width = _a.width, cx = _a.classnames, showIcon = _a.showIcon;
                var selectClassName = this.getClassName();
                if ((!popOver && !selectClassName) ||
                    popOverEnabled === false ||
                    noHoc ||
                    popOverEnable === false) {
                    return jsx(Component, __assign({}, this.props));
                }
                var triggerProps = {};
                var trigger = popOver === null || popOver === void 0 ? void 0 : popOver.trigger;
                if (trigger === 'hover' ||
                    (selectClassName === 'ellipsis' && !popOver)) {
                    triggerProps.onMouseEnter = this.openPopOver;
                    triggerProps.onMouseLeave = this.closePopOverLater;
                }
                else {
                    triggerProps.onClick = this.openPopOver;
                }
                return (jsx(Component, __assign({}, this.props, { className: cx("Field--popOverAble", className, {
                        in: this.state.isOpened
                    }), ref: config.targetOutter ? this.targetRef : undefined }, { children: (popOver === null || popOver === void 0 ? void 0 : popOver.showIcon) !== false && popOver ? (jsxs(Fragment, { children: [jsx(Component, __assign({}, this.props, { contentsOnly: true, noHoc: true })), jsx("span", __assign({ className: cx('Field-popOverBtn') }, triggerProps, { ref: config.targetOutter ? undefined : this.targetRef }, { children: jsx(Icon, { icon: "zoom-in", className: "icon" }) }), "popover-btn"), this.state.isOpened ? this.renderPopOver() : null] })) : (jsxs(Fragment, { children: [jsx("div", __assign({ className: cx('Field-popOverWrap', selectClassName
                                    ? 'Field-popOverWrap-' + selectClassName
                                    : '') }, triggerProps, { ref: config.targetOutter ? undefined : this.targetRef }, { children: jsx(Component, __assign({}, this.props, { contentsOnly: true, noHoc: true })) })), this.state.isOpened ? this.renderPopOver() : null] })) })));
            };
            PopOverComponent.ComposedComponent = Component;
            return PopOverComponent;
        }(React.Component));
        hoistNonReactStatic(PopOverComponent, Component);
        return PopOverComponent;
    };
};

export { HocPopOver, HocPopOver as default };
