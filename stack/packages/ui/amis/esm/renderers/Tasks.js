/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import { isApiOutdated, isEffectiveApi, createObject, ScopedContext, Renderer } from 'amis-core';
import update from 'immutability-helper';
import { Spinner } from 'amis-ui';

var Task = /** @class */ (function (_super) {
    __extends(Task, _super);
    function Task(props) {
        var _this = _super.call(this, props) || this;
        _this.state = {
            items: props.items ? props.items.concat() : []
        };
        _this.handleLoaded = _this.handleLoaded.bind(_this);
        _this.tick = _this.tick.bind(_this);
        return _this;
    }
    Task.prototype.componentDidMount = function () {
        this.tick(!!this.props.checkApi);
    };
    Task.prototype.componentDidUpdate = function (prevProps) {
        var props = this.props;
        if (prevProps.items !== props.items) {
            this.setState({
                items: props.items ? props.items.concat() : []
            });
        }
        else if (isApiOutdated(prevProps.checkApi, props.checkApi, prevProps.data, props.data)) {
            this.tick(true);
        }
    };
    Task.prototype.componentWillUnmount = function () {
        clearTimeout(this.timer);
    };
    Task.prototype.reload = function () {
        this.tick(true);
    };
    Task.prototype.tick = function (force) {
        var _this = this;
        if (force === void 0) { force = false; }
        var _a = this.props, loadingStatusCode = _a.loadingStatusCode, data = _a.data, interval = _a.interval, checkApi = _a.checkApi, env = _a.env;
        var items = this.state.items;
        clearTimeout(this.timer);
        // 如果每个 task 都完成了, 则不需要取查看状态.
        if (!force && !items.some(function (item) { return item.status === loadingStatusCode; })) {
            return;
        }
        if (interval && !isEffectiveApi(checkApi)) {
            return env.alert('checkApi 没有设置, 不能及时获取任务状态');
        }
        isEffectiveApi(checkApi, data) &&
            env &&
            env
                .fetcher(checkApi, data)
                .then(this.handleLoaded)
                .catch(function (e) { return _this.setState({ error: e }); });
    };
    Task.prototype.handleLoaded = function (ret) {
        if (!Array.isArray(ret.data)) {
            return this.props.env.alert('返回格式不正确, 期望 response.data 为数组, 包含每个 task 的状态信息');
        }
        this.setState({
            items: ret.data
        });
        var interval = this.props.interval;
        clearTimeout(this.timer);
        this.timer = setTimeout(this.tick, interval);
    };
    Task.prototype.submitTask = function (item, index, retry) {
        var _this = this;
        if (retry === void 0) { retry = false; }
        var _a = this.props, submitApi = _a.submitApi, reSubmitApi = _a.reSubmitApi, loadingStatusCode = _a.loadingStatusCode, errorStatusCode = _a.errorStatusCode, data = _a.data, env = _a.env;
        if (!retry && !isEffectiveApi(submitApi)) {
            return env.alert('submitApi 没有配置');
        }
        else if (retry && !isEffectiveApi(reSubmitApi)) {
            return env.alert('reSubmitApi 没有配置');
        }
        this.setState(update(this.state, {
            items: {
                $splice: [
                    [
                        index,
                        1,
                        __assign(__assign({}, item), { status: loadingStatusCode })
                    ]
                ]
            }
        }));
        var api = retry ? reSubmitApi : submitApi;
        isEffectiveApi(api, data) &&
            env &&
            env
                .fetcher(api, createObject(data, item))
                .then(function (ret) {
                if (ret && ret.data) {
                    if (Array.isArray(ret.data)) {
                        _this.handleLoaded(ret);
                    }
                    else {
                        var replace = api && api.replaceData;
                        var items = _this.state.items.map(function (item) {
                            return item.key === ret.data.key
                                ? __assign(__assign({}, (api.replaceData ? {} : item)), ret.data) : item;
                        });
                        _this.handleLoaded(__assign(__assign({}, ret), { data: items }));
                    }
                    return;
                }
                clearTimeout(_this.timer);
                _this.timer = setTimeout(_this.tick, 4);
            })
                .catch(function (e) {
                return _this.setState(update(_this.state, {
                    items: {
                        $splice: [
                            [
                                index,
                                1,
                                __assign(__assign({}, item), { status: errorStatusCode, remark: e.message || e })
                            ]
                        ]
                    }
                }));
            });
    };
    Task.prototype.render = function () {
        var _this = this;
        var _a = this.props, cx = _a.classnames, className = _a.className, style = _a.style, tableClassName = _a.tableClassName, taskNameLabel = _a.taskNameLabel, operationLabel = _a.operationLabel, statusLabel = _a.statusLabel, remarkLabel = _a.remarkLabel, btnText = _a.btnText, retryBtnText = _a.retryBtnText, btnClassName = _a.btnClassName, retryBtnClassName = _a.retryBtnClassName, statusLabelMap = _a.statusLabelMap, statusTextMap = _a.statusTextMap, readyStatusCode = _a.readyStatusCode, loadingStatusCode = _a.loadingStatusCode, canRetryStatusCode = _a.canRetryStatusCode, __ = _a.translate, render = _a.render, loadingConfig = _a.loadingConfig;
        var items = this.state.items;
        var error = this.state.error;
        return (jsx("div", __assign({ className: cx('Table-content', className), style: style }, { children: jsxs("table", __assign({ className: cx('Table-table', tableClassName) }, { children: [jsx("thead", { children: jsxs("tr", { children: [jsx("th", { children: taskNameLabel }), jsx("th", { children: __(operationLabel) }), jsx("th", { children: statusLabel }), jsx("th", { children: remarkLabel })] }) }), jsx("tbody", { children: error ? (jsx("tr", { children: jsx("td", __assign({ colSpan: 4 }, { children: jsx("div", __assign({ className: "text-danger" }, { children: error })) })) })) : (items.map(function (item, key) {
                            var _a;
                            return (jsxs("tr", { children: [jsx("td", { children: jsx("span", __assign({ className: cx('word-break') }, { children: item.label })) }), jsx("td", { children: item.status == loadingStatusCode ? (jsx(Spinner, { loadingConfig: loadingConfig, show: true, icon: "reload", spinnerClassName: cx('Task-spinner') })) : item.status == canRetryStatusCode ? (jsx("a", __assign({ onClick: function () { return _this.submitTask(item, key, true); }, className: cx('Button', 'Button--danger', 'Button--size-md', retryBtnClassName || btnClassName) }, { children: retryBtnText || btnText }))) : (jsx("a", __assign({ onClick: function () { return _this.submitTask(item, key); }, className: cx('Button', 'Button--default', 'Button--size-md', btnClassName, (_a = {},
                                                _a["is-disabled"] = item.status !== readyStatusCode,
                                                _a)) }, { children: btnText }))) }), jsx("td", { children: jsx("span", __assign({ className: cx('label', statusLabelMap && statusLabelMap[item.status || 0]) }, { children: statusTextMap && statusTextMap[item.status || 0] })) }), jsx("td", { children: item.remark ? render("".concat(key, "/remark"), item.remark) : null })] }, key));
                        })) })] })) })));
    };
    Task.defaultProps = {
        className: '',
        tableClassName: '',
        taskNameLabel: '任务名称',
        operationLabel: 'Table.operation',
        statusLabel: '状态',
        remarkLabel: '备注说明',
        btnText: '上线',
        retryBtnText: '重试',
        btnClassName: '',
        retryBtnClassName: '',
        statusLabelMap: [
            'label-warning',
            'label-info',
            'label-info',
            'label-danger',
            'label-success',
            'label-danger'
        ],
        statusTextMap: ['未开始', '就绪', '进行中', '出错', '已完成', '出错'],
        initialStatusCode: 0,
        readyStatusCode: 1,
        loadingStatusCode: 2,
        errorStatusCode: 3,
        finishStatusCode: 4,
        canRetryStatusCode: 5,
        interval: 3000
    };
    return Task;
}(React.Component));
var TaskRenderer = /** @class */ (function (_super) {
    __extends(TaskRenderer, _super);
    function TaskRenderer(props, context) {
        var _this = _super.call(this, props) || this;
        var scoped = context;
        scoped.registerComponent(_this);
        return _this;
    }
    TaskRenderer.prototype.componentWillUnmount = function () {
        _super.prototype.componentWillUnmount.call(this);
        var scoped = this.context;
        scoped.unRegisterComponent(this);
    };
    TaskRenderer.contextType = ScopedContext;
    TaskRenderer = __decorate([
        Renderer({
            type: 'tasks'
        })
    ], TaskRenderer);
    return TaskRenderer;
}(Task));

export { TaskRenderer, Task as default };
