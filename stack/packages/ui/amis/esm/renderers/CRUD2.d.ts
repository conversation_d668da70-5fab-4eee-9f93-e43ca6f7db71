import React from 'react';
import { RendererProps, ActionObject, IScopedContext, ICRUDStore } from 'amis-core';
import { SpinnerExtraProps } from 'amis-ui';
import { BaseSchema, SchemaApi, SchemaExpression, SchemaName, SchemaObject, SchemaTokenizeableString } from '../Schema';
import { BaseCardsSchema } from './Cards';
import { BaseListSchema } from './List';
import { BaseTableSchema2 } from './Table2';
import { SchemaCollection } from '../Schema';
import type { Table2RendererEvent } from './Table2';
import type { CardsRendererEvent } from './Cards';
export type CRUDRendererEvent = Table2RendererEvent | CardsRendererEvent;
export interface CRUD2CommonSchema extends BaseSchema, SpinnerExtraProps {
    /**
     *  指定为 CRUD2 渲染器。
     */
    type: 'crud2';
    /**
     * 指定内容区的展示模式。
     */
    mode?: 'table' | 'grid' | 'cards' | /* grid 的别名*/ 'list' | 'table2';
    /**
     * 初始化数据 API
     */
    api?: SchemaApi;
    /**
     * 也可以直接从环境变量中读取，但是不太推荐。
     */
    source?: SchemaTokenizeableString;
    /**
     * 静默拉取
     */
    silentPolling?: boolean;
    /**
     * 设置自动刷新时间
     */
    interval?: number;
    stopAutoRefreshWhen?: SchemaExpression;
    /**
     * 数据展示模式 无限加载 or 分页
     */
    loadType?: 'more' | 'pagination';
    /**
     * 无限加载时，根据此项设置其每页加载数量，可以不限制
     */
    perPage?: number;
    /**
     * 是否为前端单次加载模式，可以用来实现前端分页。
     */
    loadDataOnce?: boolean;
    /**
     * 是否可以选择数据，外部事件动作
     */
    selectable?: boolean;
    /**
     * 是否可以多选数据，仅当selectable为 true 时生效
     */
    multiple?: boolean;
    /**
     * 是否展示已选数据区域，仅当selectable为 true 时生效
     */
    showSelection?: boolean;
    /**
     * 快速编辑后用来批量保存的 API
     */
    quickSaveApi?: SchemaApi;
    /**
     * 快速编辑配置成及时保存时使用的 API
     */
    quickSaveItemApi?: SchemaApi;
    /**
     * 保存排序的 api
     */
    saveOrderApi?: SchemaApi;
    /**
     * 是否将过滤条件的参数同步到地址栏,默认为true
     * @default true
     */
    syncLocation?: boolean;
    /**
     * 设置分页页码字段名。
     * @default page
     */
    pageField?: string;
    /**
     * 设置分页一页显示的多少条数据的字段名。
     * @default perPage
     */
    perPageField?: string;
    name?: SchemaName;
    /**
     * 是否隐藏快速编辑的按钮。
     */
    hideQuickSaveBtn?: boolean;
    /**
     * 是否自动跳顶部，当切分页的时候。
     */
    autoJumpToTopOnPagerChange?: boolean;
    /**
     * 顶部区域
     */
    headerToolbar?: SchemaCollection;
    /**
     * 顶部区域CSS类名
     */
    headerToolbarClassName?: string;
    /**
     * 底部区域
     */
    footerToolbar?: SchemaCollection;
    /**
     * 底部区域CSS类名
     */
    footerToolbarClassName?: string;
    /**
     * 是否将接口返回的内容自动同步到地址栏，前提是开启了同步地址栏。
     */
    syncResponse2Query?: boolean;
    /**
     * 翻页时是否保留用户已选的数据
     */
    keepItemSelectionOnPageChange?: boolean;
    /**
     * 内容区域占满屏幕剩余空间
     */
    autoFillHeight?: boolean;
    /** 行标识符，默认为id */
    primaryField?: string;
    /**
     * 是否开启Query信息转换，开启后将会对url中的Query进行转换，默认开启，默认仅转化布尔值
     */
    parsePrimitiveQuery?: {
        enable: boolean;
        types?: ('boolean' | 'number')[];
    } | boolean;
    /**
     * 下拉刷新配置
     */
    pullRefresh?: {
        /**
         * 是否禁用下拉刷新
         */
        disabled?: boolean;
        /**
         * 是否显示loading图标
         * @default true
         */
        showIcon?: boolean;
        /**
         * 是否显示文本
         * @default true
         */
        showText?: boolean;
        /**
         * 指定图标样式
         * @default 'loading-outline'
         */
        iconType?: string;
        /**
         * 图标和文字颜色
         * @default '#777777'
         */
        color?: string;
        /**
         * 各状态文字说明
         */
        contentText?: {
            /** 下拉刷新的默认文字 */
            normalText?: string;
            /** 下拉过程中的文字 */
            pullingText?: string;
            /** 释放立即刷新的文字 */
            loosingText?: string;
            /** 加载中的文字 */
            loadingText?: string;
            /** 加载成功的文字 */
            successText?: string;
            /** 全部加载完成的文字 */
            completedText?: string;
        };
        /**
         * 新数据追加的位置
         * @default 'bottom'
         */
        dataAppendTo?: 'top' | 'bottom';
        /**
         * 加载状态的最短显示时间(毫秒)
         * @default 0
         */
        minLoadingTime?: number;
        /**
         * 手势方向
         * @default 'up'
         */
        gestureDirection?: 'up' | 'down';
    };
}
export type CRUD2CardsSchema = CRUD2CommonSchema & {
    mode: 'cards';
} & BaseCardsSchema;
export type CRUD2ListSchema = CRUD2CommonSchema & {
    mode: 'list';
} & BaseListSchema;
export type CRUD2TableSchema = CRUD2CommonSchema & {
    mode?: 'table2';
} & BaseTableSchema2;
export type CRUD2Schema = CRUD2CardsSchema | CRUD2ListSchema | CRUD2TableSchema;
export interface CRUD2Props extends RendererProps, Omit<CRUD2CommonSchema, 'type' | 'className'>, SpinnerExtraProps {
    store: ICRUDStore;
    pickerMode?: boolean;
}
export default class CRUD2<T extends CRUD2Props> extends React.Component<T, any> {
    static propsList: Array<keyof CRUD2Props>;
    static defaultProps: {
        toolbarInline: boolean;
        syncLocation: boolean;
        hideQuickSaveBtn: boolean;
        autoJumpToTopOnPagerChange: boolean;
        silentPolling: boolean;
        autoFillHeight: boolean;
        showSelection: boolean;
        primaryField: string;
        parsePrimitiveQuery: boolean;
        pullRefresh: {
            disabled: boolean;
            showIcon: boolean;
            showText: boolean;
            iconType: string;
            color: string;
            dataAppendTo: string;
            gestureDirection: string;
            minLoadingTime: number;
            contentText: {
                normalText: string;
                pullingText: string;
                loosingText: string;
                loadingText: string;
                successText: string;
                completedText: string;
            };
        };
    };
    control: any;
    lastQuery: any;
    lastData: any;
    timer: ReturnType<typeof setTimeout>;
    mounted: boolean;
    stopingAutoRefresh: boolean;
    constructor(props: T);
    componentDidMount(): void;
    componentDidUpdate(prevProps: CRUD2Props): void;
    componentWillUnmount(): void;
    getPaginationPerPage(): number;
    getParseQueryOptions(props: CRUD2Props): {
        parsePrimitive: boolean;
        primitiveTypes: ("number" | "boolean")[];
    };
    controlRef(control: any): void;
    initQuery(values: object): void;
    /**
     * 加载更多动作处理器
     */
    handleLoadMore(): void;
    /**
     * 发起一次新的查询，查询条件不同，需要从第一页数据加载
     */
    handleSearch(data: {
        query?: Record<string, any>;
        resetQuery?: boolean;
        replaceQuery?: boolean;
        loadMore?: boolean;
        /** 是否重置当页码到首页 */
        resetPage?: boolean;
    }): void;
    handleStopAutoRefresh(): void;
    handleStartAutoRefresh(): void;
    reloadTarget(target: string, data: any): void;
    closeTarget(target: string): void;
    updateQuery(newQuery?: any): void;
    /**
     * 更新列表数据
     */
    getData(
    /** 静默更新，不显示加载状态 */
    silent?: boolean, 
    /** 清空已选择数据 */
    clearSelection?: boolean, 
    /** 强制重新加载 */
    forceReload?: boolean, 
    /** 加载更多数据，默认模式取props中的配置，只有事件动作需要直接触发 */
    loadMore?: boolean): Promise<any>;
    handleChangePage(page: number, perPage?: number): void;
    handleSave(rows: Array<object> | object, diff: Array<object> | object, indexes: Array<string>, unModifiedItems?: Array<any>, rowsOrigin?: Array<object> | object, options?: {
        resetOnFailed?: boolean;
        reload?: string;
    }): void;
    handleSaveOrder(moved: Array<object>, rows: Array<object>): void;
    handleSelect(items: Array<any>, unSelectedItems: Array<any>): void;
    /**
     * 更新Query筛选触发
     */
    handleQuerySearch(values: object, forceReload?: boolean): Promise<any>;
    reload(subpath?: string, query?: any): Promise<any>;
    receive(values: object): Promise<any>;
    doAction(action: ActionObject, data: object, throwErrors?: boolean): any;
    handleAction(e: React.UIEvent<any> | undefined, action: ActionObject, ctx: object, throwErrors?: boolean, delegate?: IScopedContext): any;
    dispatchEvent(e: React.MouseEvent<any> | string, data: any, renderer?: React.Component<RendererProps>, // for didmount
    scoped?: IScopedContext): any;
    unSelectItem(item: any, index: number): void;
    clearSelection(): void;
    toggleAllColumns(value: boolean): void;
    toggleToggle(index: number): void;
    handlePullRefresh(): Promise<void>;
    renderChild(region: string, schema: any, props?: object): any;
    renderToolbar(region: string, toolbar?: SchemaCollection): any[] | null;
    renderFilter(filterSchema: SchemaObject[] | SchemaObject): any[] | null;
    renderSelection(): React.ReactNode;
    transformTable2cards(): {
        columnsCount: number;
        type: string;
        card: any;
    } | null;
    transMobileHeaderToolbar(toolbar: any, fixedHeader: () => void): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class CRUD2RendererBase<T extends CRUD2Props> extends CRUD2<T> {
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    constructor(props: T, context: IScopedContext);
    componentWillUnmount(): void;
    reload(subpath?: string, query?: any, ctx?: any): Promise<any>;
    receive(values: any, subPath?: string): Promise<any>;
    reloadTarget(target: string, data: any): void;
    closeTarget(target: string): void;
}
export declare class CRUD2Renderer extends CRUD2RendererBase<CRUD2Props> {
}
//# sourceMappingURL=CRUD2.d.ts.map