/// <reference types="node" />
import React, { MouseEvent, TouchEvent } from 'react';
import { RendererProps } from 'amis-core';
import { ActionObject } from 'amis-core';
import { BaseSchema, SchemaCollection, SchemaName } from '../Schema';
import { IScopedContext } from 'amis-core';
/**
 * Carousel 轮播图渲染器。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/carousel
 */
export interface CarouselSchema extends BaseSchema {
    /**
     * 指定为轮播图类型
     */
    type: 'carousel';
    /**
     * 轮播图方向，默认为水平方向
     */
    direction?: 'horizontal' | 'vertical';
    /**
     * 是否循环播放, 默认为 true。
     */
    loop?: boolean;
    /**
     * 是否支持鼠标事件
     * 默认为 true。
     */
    mouseEvent?: boolean;
    /**
     * 是否自动播放
     */
    auto?: boolean;
    /**
     * 轮播间隔时间
     */
    interval?: number | string;
    /**
     * 动画时长
     */
    duration?: number;
    /**
     * 设置宽度
     */
    width?: number | string;
    /**
     * 设置高度
     */
    height?: number | string;
    controlsTheme?: 'light' | 'dark';
    /**
     * 占位
     */
    placeholder?: string;
    /**
     * 配置控件内容
     */
    controls?: Array<'dots' | 'arrows'>;
    /**
     * 动画类型
     */
    animation?: 'fade' | 'slide' | 'marquee';
    /**
     * 配置单条呈现模板
     */
    itemSchema?: SchemaCollection;
    name?: SchemaName;
    /**
     * 预览图模式
     */
    thumbMode?: 'contain' | 'cover';
    /**
     * 配置固定值
     */
    options?: Array<any>;
    /**
     * 是否一直显示箭头
     */
    alwaysShowArrow?: boolean;
    /**
     * 多图模式配置项
     */
    multiple?: {
        count: number;
    };
    /**
     * 自定义箭头图标
     */
    icons?: {
        prev?: SchemaCollection;
        next?: SchemaCollection;
    };
}
export interface CarouselProps extends RendererProps, Omit<CarouselSchema, 'className'> {
    value?: any;
}
export interface CarouselState {
    current: number;
    options: any[];
    nextAnimation: string;
    mouseStartLocation: number | null;
    isPaused: boolean;
}
export declare class Carousel extends React.Component<CarouselProps, CarouselState> {
    wrapperRef: React.RefObject<HTMLDivElement>;
    intervalTimeout: NodeJS.Timer | number;
    durationTimeout: NodeJS.Timer | number;
    static defaultProps: Pick<CarouselProps, 'auto' | 'interval' | 'duration' | 'controlsTheme' | 'animation' | 'controls' | 'placeholder' | 'multiple' | 'alwaysShowArrow'>;
    state: {
        current: number;
        options: any;
        nextAnimation: string;
        mouseStartLocation: null;
        isPaused: boolean;
    };
    loading: boolean;
    marqueeRef: React.RefObject<HTMLDivElement>;
    contentRef: React.RefObject<HTMLDivElement>;
    marqueeRequestId: number;
    componentDidMount(): void;
    componentDidUpdate(prevProps: CarouselProps): void;
    componentWillUnmount(): void;
    marquee(): void;
    doAction(action: ActionObject, ctx: object, throwErrors: boolean, args: object): any;
    prepareAutoSlide(): void;
    autoSlide(rel?: string): void;
    transitFramesTowards(direction: string, nextAnimation: string): Promise<void>;
    getFrameId(pos?: string): number;
    next(): void;
    prev(): void;
    clearAutoTimeout(): void;
    changeSlide(index: number): Promise<void>;
    renderDots(): import("react/jsx-runtime").JSX.Element;
    renderArrows(): import("react/jsx-runtime").JSX.Element;
    handleMouseEnter(): void;
    handleMouseLeave(): void;
    getNewOptions(options: any, count?: number): any[];
    /**
     * 获取事件发生的屏幕坐标，兼容鼠标事件和触摸事件。
     *
     * @param event 事件对象，可以是鼠标事件或触摸事件
     * @returns 返回包含屏幕横纵坐标的对象
     */
    getEventScreenXY(event: MouseEvent<HTMLDivElement> | TouchEvent<HTMLDivElement>): {
        screenX: number | undefined;
        screenY: number | undefined;
    };
    /**
     * 添加鼠标按下事件监听器, 用于判断滑动方向
     * @param event 鼠标事件对象
     */
    addMouseDownListener(event: MouseEvent<HTMLDivElement> | TouchEvent<HTMLDivElement>): void;
    /**
     * 添加鼠标抬起事件监听器, 用于判断滑动方向
     * @param event 鼠标事件对象
     */
    addMouseUpListener(event: MouseEvent<HTMLDivElement> | TouchEvent<HTMLDivElement>): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class CarouselRenderer extends Carousel {
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    constructor(props: CarouselProps, context: IScopedContext);
    componentWillUnmount(): void;
}
//# sourceMappingURL=Carousel.d.ts.map