/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __rest, __assign, __decorate } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import { Spinner } from 'amis-ui';
import { Renderer } from 'amis-core';
import React from 'react';

var SpinnerRenderer = /** @class */ (function (_super) {
    __extends(SpinnerRenderer, _super);
    function SpinnerRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    SpinnerRenderer.prototype.renderBody = function () {
        var _a = this.props, body = _a.body, render = _a.render;
        return body ? render('body', body) : null;
    };
    SpinnerRenderer.prototype.render = function () {
        var _a = this.props, cx = _a.classnames, spinnerWrapClassName = _a.spinnerWrapClassName, body = _a.body, rest = __rest(_a, ["classnames", "spinnerWrapClassName", "body"]);
        return body ? (jsxs("div", __assign({ className: cx("Spinner-wrap", spinnerWrapClassName) }, { children: [jsx(Spinner, __assign({}, rest)), this.renderBody()] }))) : (jsx(Spinner, __assign({}, rest)));
    };
    SpinnerRenderer = __decorate([
        Renderer({
            type: 'spinner'
        })
    ], SpinnerRenderer);
    return SpinnerRenderer;
}(React.Component));

export { SpinnerRenderer };
