/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import { visibilityFilter, buildStyle, Renderer } from 'amis-core';

var Property = /** @class */ (function (_super) {
    __extends(Property, _super);
    function Property(props) {
        return _super.call(this, props) || this;
    }
    /**
     * 算好每行的分布情况，方便后续渲染
     */
    Property.prototype.prepareRows = function () {
        var _a = this.props, _b = _a.column, column = _b === void 0 ? 3 : _b, items = _a.items, source = _a.source, data = _a.data;
        var propertyItems = items ? items : source || [];
        var rows = [];
        var row = [];
        var columnLeft = column;
        var index = 0;
        var filteredItems = visibilityFilter(propertyItems, data);
        for (var _i = 0, filteredItems_1 = filteredItems; _i < filteredItems_1.length; _i++) {
            var item = filteredItems_1[_i];
            index = index + 1;
            var span = Math.min(item.span || 1, column);
            columnLeft = columnLeft - span;
            var rowItem = {
                label: item.label,
                content: item.content,
                span: span
            };
            // 如果还能放得下就放这一行
            if (columnLeft >= 0) {
                row.push(rowItem);
            }
            else {
                rows.push(row);
                columnLeft = column - span;
                row = [rowItem];
            }
            // 最后一行将最后的数据 push
            if (index === filteredItems.length) {
                rows.push(row);
            }
        }
        return rows;
    };
    Property.prototype.renderRow = function (rows) {
        var _a = this.props, render = _a.render, contentStyle = _a.contentStyle, labelStyle = _a.labelStyle, _b = _a.separator, separator = _b === void 0 ? ': ' : _b, _c = _a.mode, mode = _c === void 0 ? 'table' : _c, data = _a.data;
        return rows.map(function (row, key) {
            return (jsx("tr", { children: row.map(function (property, index) {
                    return mode === 'table' ? (jsxs(React.Fragment, { children: [jsx("th", __assign({ style: buildStyle(labelStyle, data) }, { children: render('label', property.label) })), jsx("td", __assign({ colSpan: property.span + property.span - 1, style: buildStyle(contentStyle, data) }, { children: render('content', property.content) }))] }, "item-".concat(index))) : (jsxs("td", __assign({ colSpan: property.span, style: buildStyle(contentStyle, data) }, { children: [jsx("span", __assign({ style: buildStyle(labelStyle, data) }, { children: render('label', property.label) })), separator, render('content', property.content)] }), "item-".concat(index)));
                }) }, key));
        });
    };
    Property.prototype.render = function () {
        var _a = this.props, style = _a.style, title = _a.title, _b = _a.column, column = _b === void 0 ? 3 : _b, cx = _a.classnames, className = _a.className, titleStyle = _a.titleStyle, data = _a.data, _c = _a.mode, mode = _c === void 0 ? 'table' : _c;
        var rows = this.prepareRows();
        return (jsx("div", __assign({ className: cx('Property', "Property--".concat(mode), className), style: buildStyle(style, data) }, { children: jsxs("table", { children: [title ? (jsx("thead", { children: jsx("tr", { children: jsx("th", __assign({ colSpan: mode === 'table' ? column + column : column, style: buildStyle(titleStyle, data) }, { children: title })) }) })) : null, jsx("tbody", { children: this.renderRow(rows) })] }) })));
    };
    return Property;
}(React.Component));
var PropertyRenderer = /** @class */ (function (_super) {
    __extends(PropertyRenderer, _super);
    function PropertyRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PropertyRenderer = __decorate([
        Renderer({
            type: 'property',
            autoVar: true
        })
    ], PropertyRenderer);
    return PropertyRenderer;
}(Property));

export { PropertyRenderer, Property as default };
