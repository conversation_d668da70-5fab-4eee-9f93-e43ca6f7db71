import React from 'react';
import { RendererProps } from 'amis-core';
import { IScopedContext } from 'amis-core';
import { BaseSchema, SchemaUrlPath } from '../Schema';
/**
 * IFrame 渲染器
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/iframe
 */
export interface IFrameSchema extends BaseSchema {
    type: 'iframe';
    /**
     * 页面地址
     */
    src: SchemaUrlPath;
    width?: number | string;
    height?: number | string;
    allow?: string;
    name?: string;
    referrerpolicy?: 'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
    sandbox?: string;
}
export interface IFrameProps extends RendererProps, Omit<IFrameSchema, 'type' | 'className'> {
    inDragging?: boolean;
}
export default class IFrame extends React.Component<IFrameProps, object> {
    IFrameRef: React.RefObject<HTMLIFrameElement>;
    static propsList: Array<string>;
    static defaultProps: Partial<IFrameProps>;
    state: {
        width: string | number;
        height: string | number;
    };
    componentDidMount(): void;
    componentDidUpdate(prevProps: IFrameProps): void;
    componentWillUnmount(): void;
    /** 校验URL是否合法 */
    validateURL(url: any): boolean;
    onMessage(e: MessageEvent): Promise<void>;
    onLoad(): void;
    reload(subpath?: any, query?: any): void;
    receive(values: object): void;
    postMessage(type: string, data: any): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class IFrameRenderer extends IFrame {
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    constructor(props: IFrameProps, context: IScopedContext);
    componentWillUnmount(): void;
}
//# sourceMappingURL=IFrame.d.ts.map