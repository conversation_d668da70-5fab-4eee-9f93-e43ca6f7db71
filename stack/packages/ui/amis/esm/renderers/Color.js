/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import { getPropValue, Renderer } from 'amis-core';

var ColorField = /** @class */ (function (_super) {
    __extends(ColorField, _super);
    function ColorField() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ColorField.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, cx = _a.classnames, defaultColor = _a.defaultColor, showValue = _a.showValue;
        var color = getPropValue(this.props) || defaultColor;
        return (jsxs("div", __assign({ className: cx('ColorField', className), style: style }, { children: [jsx("i", { className: cx('ColorField-previewIcon'), style: { backgroundColor: color } }), showValue && color ? (jsx("span", __assign({ className: cx('ColorField-value') }, { children: color }))) : null] })));
    };
    ColorField.defaultProps = {
        className: '',
        defaultColor: '',
        showValue: true
    };
    return ColorField;
}(React.Component));
var ColorFieldRenderer = /** @class */ (function (_super) {
    __extends(ColorFieldRenderer, _super);
    function ColorFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ColorFieldRenderer = __decorate([
        Renderer({
            type: 'color'
        })
    ], ColorFieldRenderer);
    return ColorFieldRenderer;
}(ColorField));

export { ColorField, ColorFieldRenderer };
