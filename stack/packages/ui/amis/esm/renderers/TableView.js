/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import { isVisible, resolveMappingObject, setThemeClassName, CustomStyle, Renderer } from 'amis-core';

var defaultPadding = 'var(--TableCell-paddingY) var(--TableCell-paddingX)';
var TableView = /** @class */ (function (_super) {
    __extends(TableView, _super);
    function TableView(props) {
        return _super.call(this, props) || this;
    }
    TableView.prototype.renderTd = function (td, colIndex, rowIndex) {
        var _a;
        var _b = this.props, border = _b.border, borderColor = _b.borderColor, render = _b.render, data = _b.data, padding = _b.padding;
        var key = "td-".concat(colIndex);
        var styleBorder;
        if (border) {
            styleBorder = "1px solid ".concat(borderColor);
        }
        return isVisible(td, data) ? (jsx("td", __assign({ style: __assign({ border: styleBorder, color: td.color, fontWeight: td.bold ? 'bold' : 'normal', background: td.background, padding: (_a = td.padding) !== null && _a !== void 0 ? _a : padding, width: td.width || 'auto', textAlign: td.align || 'left', verticalAlign: td.valign || 'center' }, td.style), align: td.align, valign: td.valign, rowSpan: td.rowspan, colSpan: td.colspan }, { children: this.renderTdBody(td.body) }), key)) : null;
    };
    TableView.prototype.renderTdBody = function (body) {
        var render = this.props.render;
        return render('td', body || '');
    };
    TableView.prototype.renderTds = function (tds, rowIndex) {
        var _this = this;
        var data = this.props.data;
        return tds.map(function (td, colIndex) {
            return _this.renderTd(resolveMappingObject(td, data), colIndex, rowIndex);
        });
    };
    TableView.prototype.renderTr = function (tr, rowIndex) {
        var key = "tr-".concat(rowIndex);
        var data = this.props.data;
        return isVisible(tr, data) ? (jsx("tr", __assign({ style: __assign({ height: tr.height, background: tr.background }, tr.style) }, { children: this.renderTds(tr.tds || [], rowIndex) }), key)) : null;
    };
    TableView.prototype.renderTrs = function (trs) {
        var _this = this;
        var data = this.props.data;
        var tr = trs.map(function (tr, rowIndex) {
            return _this.renderTr(resolveMappingObject(tr, data), rowIndex);
        });
        return tr;
    };
    TableView.prototype.renderCols = function () {
        var _a = this.props, cols = _a.cols, data = _a.data;
        if (cols) {
            var colsElement = cols.map(function (col) {
                col = resolveMappingObject(col, data);
                return jsx("col", { span: col.span, style: col.style });
            });
            return jsx("colgroup", { children: colsElement });
        }
        return null;
    };
    TableView.prototype.renderCaption = function () {
        if (this.props.caption) {
            return (jsx("caption", __assign({ style: {
                    captionSide: this.props.captionSide === 'bottom' ? 'bottom' : 'top'
                } }, { children: this.props.caption })));
        }
        return null;
    };
    TableView.prototype.render = function () {
        var _a = this.props, width = _a.width, _b = _a.trs, trs = _b === void 0 ? [] : _b, cx = _a.classnames, className = _a.className, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env, themeCss = _a.themeCss, style = _a.style;
        var renderNode = (jsxs("table", __assign({ className: cx('TableView', className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), style: { width: width, borderCollapse: 'collapse' }, "data-id": id }, { children: [this.renderCaption(), this.renderCols(), jsx("tbody", { children: this.renderTrs(trs) }), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            }
                        ]
                    }, env: env }))] })));
        if (style && Object.keys(style).length) {
            return (jsx("div", __assign({ className: "ae-TableViewEditor", style: style }, { children: renderNode })));
        }
        return renderNode;
    };
    TableView.defaultProps = {
        padding: defaultPadding,
        width: '100%',
        border: true,
        borderColor: 'var(--borderColor)'
    };
    return TableView;
}(React.Component));
var TableViewRenderer = /** @class */ (function (_super) {
    __extends(TableViewRenderer, _super);
    function TableViewRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TableViewRenderer = __decorate([
        Renderer({
            type: 'table-view',
            autoVar: true
        })
    ], TableViewRenderer);
    return TableViewRenderer;
}(TableView));

export { TableViewRenderer, TableView as default };
