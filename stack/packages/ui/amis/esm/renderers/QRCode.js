/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import cx from 'classnames';
import { isObject, isPureVariable, resolveVariableAndFilter, isNumeric, getPropValue, filter, ScopedContext, Renderer } from 'amis-core';
import { QRCode as QRCode$1 } from 'qrcode-react-next';
import mapValues from 'lodash/mapValues';
import { saveAs } from 'file-saver';

function downloadBlob(blob, filename) {
    return saveAs(blob, filename);
}
var QRCode = /** @class */ (function (_super) {
    __extends(QRCode, _super);
    function QRCode(props) {
        var _this = _super.call(this, props) || this;
        _this.ref = React.createRef();
        return _this;
    }
    /**
     * 获取图片配置
     */
    QRCode.prototype.getImageSettings = function () {
        var _a = this.props, imageSettings = _a.imageSettings, data = _a.data;
        if (!imageSettings ||
            !isObject(imageSettings) ||
            !imageSettings.src ||
            typeof imageSettings.src !== 'string') {
            return undefined;
        }
        if (isPureVariable(imageSettings.src)) {
            imageSettings.src = resolveVariableAndFilter(imageSettings.src, data, '| raw');
        }
        return mapValues(imageSettings, function (value, key) {
            if (!!~['width', 'height', 'x', 'y'].indexOf(key)) {
                /** 处理非数字格式的输入，QRCodeSVG内部会对空值进行默认赋值 */
                return isNumeric(value) ? Number(value) : null;
            }
            return value;
        });
    };
    /**
     * 接收动作事件
     */
    QRCode.prototype.doAction = function (action, data, throwErrors, args) {
        var _a;
        var codeSize = this.props.codeSize;
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        if (actionType === 'saveAs') {
            if ((_a = this.ref) === null || _a === void 0 ? void 0 : _a.current) {
                if (this.props.mode === 'svg') {
                    var svgElement = this.ref.current.querySelector('svg');
                    if (svgElement) {
                        var contentWithSvg = "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" version=\"1.1\" height=\"".concat(codeSize, "\" width=\"").concat(codeSize, "\" viewBox=\"").concat(svgElement.getAttribute('viewBox') || '0 0 37 37', "\">\n         ").concat(svgElement.innerHTML, "\n         </svg>");
                        var blob = new Blob([contentWithSvg], { type: 'image/svg+xml' });
                        downloadBlob(blob, (args === null || args === void 0 ? void 0 : args.name) || 'qr-code.svg');
                    }
                }
                else {
                    var canvasElement = this.ref.current.querySelector('canvas');
                    if (canvasElement) {
                        canvasElement.toBlob(function (blob) {
                            blob &&
                                downloadBlob(blob, (args === null || args === void 0 ? void 0 : args.name)
                                    ? args.name.replace(/\.svg$/, '.png')
                                    : 'qr-code.png');
                        }, 'image/png');
                    }
                }
            }
        }
    };
    QRCode.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, qrcodeClassName = _a.qrcodeClassName, codeSize = _a.codeSize, backgroundColor = _a.backgroundColor, foregroundColor = _a.foregroundColor, placeholder = _a.placeholder, level = _a.level, defaultValue = _a.defaultValue, data = _a.data, mode = _a.mode, eyeType = _a.eyeType, eyeBorderColor = _a.eyeBorderColor, eyeBorderSize = _a.eyeBorderSize, eyeInnerColor = _a.eyeInnerColor, pointType = _a.pointType, pointSize = _a.pointSize, pointSizeRandom = _a.pointSizeRandom, __ = _a.translate, ns = _a.classPrefix;
        var finalValue = getPropValue(this.props, function () { return filter(defaultValue, data, '| raw') || undefined; });
        return (jsx("div", __assign({ className: cx("".concat(ns, "QrCode"), className), style: style, ref: this.ref }, { children: !finalValue ? (jsx("span", __assign({ className: "".concat(ns, "QrCode--placeholder") }, { children: placeholder }))) : finalValue.length > 2953 ? (
            // https://github.com/zpao/qrcode.react/issues/69
            jsx("span", __assign({ className: "text-danger" }, { children: __('QRCode.tooLong', { max: 2953 }) }))) : (jsx(QRCode$1, { className: qrcodeClassName, value: finalValue, config: {
                    level: level || 'L',
                    minVersion: 2,
                    boostLevel: true
                }, styleConfig: {
                    size: codeSize,
                    bgColor: backgroundColor,
                    color: foregroundColor,
                    eyeType: eyeType,
                    eyeBorderColor: eyeBorderColor,
                    eyeBorderSize: eyeBorderSize,
                    eyeInnerColor: eyeInnerColor,
                    pointType: pointType,
                    pointSize: pointSize,
                    pointSizeRandom: pointSizeRandom
                }, logoConfig: this.getImageSettings(), mode: mode })) })));
    };
    QRCode.defaultProps = {
        codeSize: 128,
        qrcodeClassName: '',
        backgroundColor: '#fff',
        foregroundColor: '#000',
        level: 'L',
        placeholder: '-',
        mode: 'canvas'
    };
    return QRCode;
}(React.Component));
var QRCodeRenderer = /** @class */ (function (_super) {
    __extends(QRCodeRenderer, _super);
    function QRCodeRenderer(props, context) {
        var _this = _super.call(this, props) || this;
        var scoped = context;
        scoped.registerComponent(_this);
        return _this;
    }
    QRCodeRenderer.prototype.componentWillUnmount = function () {
        var _a;
        (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);
        var scoped = this.context;
        scoped.unRegisterComponent(this);
    };
    QRCodeRenderer.contextType = ScopedContext;
    QRCodeRenderer = __decorate([
        Renderer({
            type: 'qrcode',
            alias: ['qr-code'],
            name: 'qrcode'
        })
    ], QRCodeRenderer);
    return QRCodeRenderer;
}(QRCode));

export { QRCodeRenderer, QRCode as default };
