/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { isVisible, uc<PERSON><PERSON><PERSON>, Renderer } from 'amis-core';

var HBox = /** @class */ (function (_super) {
    __extends(HBox, _super);
    function HBox() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    HBox.prototype.renderChild = function (region, node, props) {
        if (props === void 0) { props = {}; }
        var render = this.props.render;
        return render(region, node, props);
    };
    HBox.prototype.renderColumn = function (column, key, length) {
        var _a;
        var _b = this.props, itemRender = _b.itemRender, data = _b.data, cx = _b.classnames, subFormMode = _b.subFormMode, subFormHorizontal = _b.subFormHorizontal, formMode = _b.formMode, formHorizontal = _b.formHorizontal;
        if (!isVisible(column, data) || !column) {
            return null;
        }
        var style = __assign({ width: column.width, height: column.height }, column.style);
        return (jsx("div", __assign({ className: cx("Hbox-col", style.width === 'auto'
                ? 'Hbox-col--auto'
                : style.width
                    ? 'Hbox-col--customWidth'
                    : '', (_a = {},
                _a["Hbox-col--v".concat(ucFirst(column.valign))] = column.valign,
                _a), column.columnClassName), style: style }, { children: itemRender
                ? itemRender(__assign(__assign({}, column), (column.body ? { type: 'wrapper', wrap: false } : {})), key, length, this.props)
                : this.renderChild("column/".concat(key), column.body, {
                    formMode: column.mode || subFormMode || formMode,
                    formHorizontal: column.horizontal || subFormHorizontal || formHorizontal
                }) }), key));
    };
    HBox.prototype.renderColumns = function () {
        var _this = this;
        var columns = this.props.columns;
        return columns.map(function (column, key) {
            return _this.renderColumn(column, key, columns.length);
        });
    };
    HBox.prototype.render = function () {
        var _a;
        var _b = this.props, className = _b.className, style = _b.style, cx = _b.classnames, gap = _b.gap, vAlign = _b.valign, hAlign = _b.align;
        return (jsx("div", __assign({ className: cx("Hbox", className, (_a = {},
                _a["Hbox--".concat(gap)] = gap,
                _a["Hbox--v".concat(ucFirst(vAlign))] = vAlign,
                _a["Hbox--h".concat(ucFirst(hAlign))] = hAlign,
                _a)), style: style }, { children: this.renderColumns() })));
    };
    HBox.propsList = ['columns'];
    HBox.defaultProps = {
        gap: 'xs'
    };
    return HBox;
}(React.Component));
var HBoxRenderer = /** @class */ (function (_super) {
    __extends(HBoxRenderer, _super);
    function HBoxRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    HBoxRenderer = __decorate([
        Renderer({
            type: 'hbox'
        })
    ], HBoxRenderer);
    return HBoxRenderer;
}(HBox));

export { HBoxRenderer, HBox as default };
