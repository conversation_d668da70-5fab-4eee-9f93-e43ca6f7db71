/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { getPropValue, filter, Renderer } from 'amis-core';
import { MultilineText } from 'amis-ui';

var MultilineTextField = /** @class */ (function (_super) {
    __extends(MultilineTextField, _super);
    function MultilineTextField() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    MultilineTextField.prototype.render = function () {
        var text = getPropValue(this.props, function (props) {
            return props.text ? filter(props.text, props.data, '| raw') : undefined;
        });
        return jsx(MultilineText, __assign({}, this.props, { text: text }));
    };
    return MultilineTextField;
}(React.Component));
var MultilineTextFieldRenderer = /** @class */ (function (_super) {
    __extends(MultilineTextFieldRenderer, _super);
    function MultilineTextFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    MultilineTextFieldRenderer = __decorate([
        Renderer({
            type: 'multiline-text'
        })
    ], MultilineTextFieldRenderer);
    return MultilineTextFieldRenderer;
}(MultilineTextField));

export { MultilineTextField, MultilineTextFieldRenderer };
