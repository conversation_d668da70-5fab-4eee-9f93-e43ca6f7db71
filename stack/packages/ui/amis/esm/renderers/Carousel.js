/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __rest, __decorate } from 'tslib';
import { jsx, Fragment, jsxs } from 'react/jsx-runtime';
import React from 'react';
import Transition, { ENTERING, ENTERED, EXITING, EXITED } from 'react-transition-group/Transition';
import { getPropValue, isArrayChildrenModified, resolveVariableAndFilter, createObject, isObject, setThemeClassName, CustomStyle, autobind, ScopedContext, Renderer } from 'amis-core';
import { Html, Icon } from 'amis-ui';
import ThemedImageThumb from './Image.js';

var _a;
var animationStyles = (_a = {},
    _a[ENTERING] = 'in',
    _a[ENTERED] = 'in',
    _a[EXITING] = 'out',
    _a);
var defaultSchema = {
    component: function (props) {
        var data = props.data || {};
        var thumbMode = props.thumbMode;
        var cx = props.classnames;
        return (jsx(Fragment, { children: data.hasOwnProperty('image') ? (jsx(ThemedImageThumb, { src: data.image, title: data.hasOwnProperty('title') ? data.title : '', href: data.hasOwnProperty('href') ? data.href : '', blank: data.hasOwnProperty('blank') ? data.blank : false, htmlTarget: data.hasOwnProperty('htmlTarget') ? data.htmlTarget : '', caption: data.hasOwnProperty('description') ? data.description : '', thumbMode: data.hasOwnProperty('thumbMode')
                    ? data.thumbMode
                    : thumbMode !== null && thumbMode !== void 0 ? thumbMode : 'contain', imageMode: "original", className: cx('Carousel-image') })) : data.hasOwnProperty('html') ? (jsx(Html, { html: data.html })) : data.hasOwnProperty('item') ? (jsx("span", { children: data.item })) : (jsx("p", {})) }));
    }
};
var SCROLL_THRESHOLD = 20;
var Carousel = /** @class */ (function (_super) {
    __extends(Carousel, _super);
    function Carousel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.wrapperRef = React.createRef();
        _this.state = {
            current: 0,
            options: _this.props.options || getPropValue(_this.props) || [],
            nextAnimation: '',
            mouseStartLocation: null,
            isPaused: false
        };
        _this.loading = false;
        _this.marqueeRef = React.createRef();
        _this.contentRef = React.createRef();
        return _this;
    }
    Carousel.prototype.componentDidMount = function () {
        this.prepareAutoSlide();
        // 跑马灯效果
        if (this.props.animation === 'marquee') {
            this.marquee();
        }
    };
    Carousel.prototype.componentDidUpdate = function (prevProps) {
        var props = this.props;
        var nextOptions = props.options || getPropValue(props) || [];
        var prevOptions = prevProps.options || getPropValue(prevProps) || [];
        if (isArrayChildrenModified(prevOptions, nextOptions)) {
            this.setState({
                options: nextOptions
            });
        }
        if (this.props.animation === 'marquee' &&
            prevProps.animation !== 'marquee') {
            this.marquee();
        }
    };
    Carousel.prototype.componentWillUnmount = function () {
        this.clearAutoTimeout();
        cancelAnimationFrame(this.marqueeRequestId);
    };
    Carousel.prototype.marquee = function () {
        var _this = this;
        if (!this.marqueeRef.current || !this.contentRef.current) {
            return;
        }
        var positionNum = 0;
        var lastTime = performance.now();
        var contentDom = this.contentRef.current;
        var animate = function (time) {
            var _a, _b;
            var diffTime = time - lastTime;
            lastTime = time;
            var wrapWidth = (_b = (_a = _this.marqueeRef.current) === null || _a === void 0 ? void 0 : _a.offsetWidth) !== null && _b !== void 0 ? _b : 0;
            if (!_this.state.isPaused) {
                // 计算每帧移动距离
                var moveDistance = wrapWidth * (diffTime / _this.props.duration);
                positionNum += -moveDistance;
                // 检查是否需要重置位置
                var contentWidth = contentDom.scrollWidth / 2;
                if (Math.abs(positionNum) >= contentWidth) {
                    positionNum = 0;
                }
                contentDom.style.transform = "translateX(".concat(positionNum, "px)");
            }
            _this.marqueeRequestId = requestAnimationFrame(animate);
        };
        this.marqueeRequestId = requestAnimationFrame(animate);
    };
    Carousel.prototype.doAction = function (action, ctx, throwErrors, args) {
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        if (!!~['next', 'prev'].indexOf(actionType)) {
            this.autoSlide(actionType);
        }
        else if (actionType === 'goto-image') {
            this.changeSlide((args === null || args === void 0 ? void 0 : args.activeIndex) - 1);
        }
    };
    Carousel.prototype.prepareAutoSlide = function () {
        if (this.state.options.length < 2) {
            return;
        }
        this.clearAutoTimeout();
        if (this.props.auto) {
            var interval = this.props.interval;
            this.intervalTimeout = setTimeout(this.autoSlide, typeof interval === 'string'
                ? resolveVariableAndFilter(interval, this.props.data) || 5000
                : interval);
        }
    };
    Carousel.prototype.autoSlide = function (rel) {
        this.clearAutoTimeout();
        var animation = this.props.animation;
        var nextAnimation = this.state.nextAnimation;
        switch (rel) {
            case 'prev':
                animation === 'slide'
                    ? (nextAnimation = 'slideRight')
                    : (nextAnimation = '');
                this.transitFramesTowards('right', nextAnimation);
                break;
            case 'next':
            default:
                nextAnimation = '';
                this.transitFramesTowards('left', nextAnimation);
                break;
        }
        this.durationTimeout = setTimeout(this.prepareAutoSlide, this.props.duration);
    };
    Carousel.prototype.transitFramesTowards = function (direction, nextAnimation) {
        return __awaiter(this, void 0, void 0, function () {
            var current, prevIndex, _a, dispatchEvent, data, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        current = this.state.current;
                        prevIndex = current;
                        // 如果这里是不循环状态，需要阻止切换到第一张或者最后一张图片
                        if (this.props.loop === false &&
                            ((current === 0 && direction === 'right') ||
                                (current === this.state.options.length - 1 && direction === 'left'))) {
                            return [2 /*return*/];
                        }
                        switch (direction) {
                            case 'left':
                                current = this.getFrameId('next');
                                break;
                            case 'right':
                                current = this.getFrameId('prev');
                                break;
                        }
                        _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
                        return [4 /*yield*/, dispatchEvent('change', createObject(data, {
                                activeIndex: current + 1,
                                prevIndex: prevIndex
                            }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        this.setState({
                            current: current,
                            nextAnimation: nextAnimation
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    Carousel.prototype.getFrameId = function (pos) {
        var _a = this.state, options = _a.options, current = _a.current;
        var total = options.length;
        switch (pos) {
            case 'prev':
                return (current - 1 + total) % total;
            case 'next':
                return (current + 1) % total;
            default:
                return current;
        }
    };
    Carousel.prototype.next = function () {
        var multiple = this.props.multiple;
        if (this.loading && multiple && multiple.count > 1) {
            return;
        }
        this.autoSlide('next');
    };
    Carousel.prototype.prev = function () {
        var multiple = this.props.multiple;
        if (this.loading && multiple && multiple.count > 1) {
            return;
        }
        this.autoSlide('prev');
    };
    Carousel.prototype.clearAutoTimeout = function () {
        clearTimeout(this.intervalTimeout);
        clearTimeout(this.durationTimeout);
    };
    Carousel.prototype.changeSlide = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var current, _a, dispatchEvent, data, multiple, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        current = this.state.current;
                        _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data, multiple = _a.multiple;
                        if (this.loading && multiple && multiple.count > 1) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, dispatchEvent('change', createObject(data, {
                                activeIndex: index,
                                prevIndex: current
                            }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        this.setState({
                            current: index
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    Carousel.prototype.renderDots = function () {
        var _this = this;
        var cx = this.props.classnames;
        var _a = this.state, current = _a.current, options = _a.options;
        return (jsx("div", __assign({ className: cx('Carousel-dotsControl'), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, { children: Array.from({ length: options.length }).map(function (_, i) { return (jsx("span", { onClick: function () { return _this.changeSlide(i); }, className: cx('Carousel-dot', current === i ? 'is-active' : '') }, i)); }) })));
    };
    Carousel.prototype.renderArrows = function () {
        var cx = this.props.classnames;
        return (jsxs("div", __assign({ className: cx('Carousel-arrowsControl'), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, { children: [jsx("div", __assign({ className: cx('Carousel-leftArrow'), onClick: this.prev }, { children: jsx(Icon, { icon: "left-arrow", className: "icon" }) })), jsx("div", __assign({ className: cx('Carousel-rightArrow'), onClick: this.next }, { children: jsx(Icon, { icon: "right-arrow", className: "icon" }) }))] })));
    };
    Carousel.prototype.handleMouseEnter = function () {
        var multiple = this.props.multiple;
        if (multiple && multiple.count > 1) {
            return;
        }
        this.clearAutoTimeout();
    };
    Carousel.prototype.handleMouseLeave = function () {
        var multiple = this.props.multiple;
        if (multiple && multiple.count > 1) {
            return;
        }
        this.prepareAutoSlide();
    };
    // 处理options
    Carousel.prototype.getNewOptions = function (options, count) {
        if (count === void 0) { count = 1; }
        var newOptions = options;
        if (Array.isArray(options) && options.length) {
            newOptions = new Array(options.length);
            for (var i = 0; i < options.length; i++) {
                newOptions[i] = new Array(count);
                for (var j = 0; j < count; j++) {
                    newOptions[i][j] = options[(i + j) % options.length];
                }
            }
        }
        return newOptions;
    };
    /**
     * 获取事件发生的屏幕坐标，兼容鼠标事件和触摸事件。
     *
     * @param event 事件对象，可以是鼠标事件或触摸事件
     * @returns 返回包含屏幕横纵坐标的对象
     */
    Carousel.prototype.getEventScreenXY = function (event) {
        var _a, _b, _c, _d, _e, _f;
        var screenX, screenY;
        if (event.screenX !== undefined) {
            screenX = event.screenX;
            screenY = event.screenY;
        }
        else if ((_a = event.touches) === null || _a === void 0 ? void 0 : _a.length) {
            // touchStart 事件
            screenX = (_b = event.touches[0]) === null || _b === void 0 ? void 0 : _b.screenX;
            screenY = (_c = event.touches[0]) === null || _c === void 0 ? void 0 : _c.screenY;
        }
        else if ((_d = event.changedTouches) === null || _d === void 0 ? void 0 : _d.length) {
            // touchEnd 事件
            screenX = (_e = event.changedTouches[0]) === null || _e === void 0 ? void 0 : _e.screenX;
            screenY = (_f = event.changedTouches[0]) === null || _f === void 0 ? void 0 : _f.screenY;
        }
        return {
            screenX: screenX,
            screenY: screenY
        };
    };
    /**
     * 添加鼠标按下事件监听器, 用于判断滑动方向
     * @param event 鼠标事件对象
     */
    Carousel.prototype.addMouseDownListener = function (event) {
        var direction = this.props.direction;
        var _a = this.getEventScreenXY(event), screenX = _a.screenX, screenY = _a.screenY;
        // 根据当前滑动方向确定是应该使用x坐标还是y坐标做mark
        var location = direction === 'vertical' ? screenY : screenX;
        location !== undefined &&
            this.setState({
                mouseStartLocation: location
            });
    };
    /**
     * 添加鼠标抬起事件监听器, 用于判断滑动方向
     * @param event 鼠标事件对象
     */
    Carousel.prototype.addMouseUpListener = function (event) {
        var _a = this.getEventScreenXY(event), screenX = _a.screenX, screenY = _a.screenY;
        // 根据当前滑动方向确定是应该使用x坐标还是y坐标做mark
        var direction = this.props.direction;
        var location = direction === 'vertical' ? screenY : screenX;
        if (this.state.mouseStartLocation !== null && location !== undefined) {
            if (location - this.state.mouseStartLocation > SCROLL_THRESHOLD) {
                this.autoSlide('prev');
            }
            else if (this.state.mouseStartLocation - location > SCROLL_THRESHOLD) {
                this.autoSlide();
            }
            this.setState({
                mouseStartLocation: null
            });
        }
    };
    Carousel.prototype.render = function () {
        var _a, _b;
        var _this = this;
        var _c = this.props, render = _c.render, className = _c.className, style = _c.style, cx = _c.classnames, itemSchema = _c.itemSchema, animation = _c.animation, width = _c.width, height = _c.height, controls = _c.controls, controlsTheme = _c.controlsTheme, placeholder = _c.placeholder, data = _c.data, name = _c.name, duration = _c.duration, multiple = _c.multiple, alwaysShowArrow = _c.alwaysShowArrow, icons = _c.icons, id = _c.id, wrapperCustomStyle = _c.wrapperCustomStyle, env = _c.env, themeCss = _c.themeCss;
        var _d = this.state, options = _d.options, current = _d.current, nextAnimation = _d.nextAnimation;
        var body = null;
        var carouselStyles = style ? __assign({}, style) : {};
        // 不允许传0，需要有最小高度
        if (width) {
            // 数字类型认为是px单位，否则传入字符串直接赋给style对象
            !isNaN(Number(width))
                ? (carouselStyles.width = width + 'px')
                : (carouselStyles.width = width);
        }
        if (height) {
            !isNaN(Number(height))
                ? (carouselStyles.height = height + 'px')
                : (carouselStyles.height = height);
        }
        var _e = [
            controls.indexOf('dots') > -1 && animation !== 'marquee',
            controls.indexOf('arrows') > -1 && animation !== 'marquee'
        ], dots = _e[0], arrows = _e[1];
        var animationName = nextAnimation || animation;
        if (Array.isArray(options) && options.length) {
            var multipleCount_1 = 1;
            if (multiple &&
                typeof multiple.count === 'number' &&
                multiple.count >= 2) {
                multipleCount_1 =
                    Math.floor(multiple.count) < options.length
                        ? Math.floor(multiple.count)
                        : options.length;
            }
            var newOptions_1 = this.getNewOptions(options, multipleCount_1);
            var transitionDuration = multipleCount_1 > 1 && typeof duration === 'number'
                ? "".concat(duration, "ms")
                : duration || '500ms';
            var timeout_1 = multipleCount_1 > 1 && typeof duration === 'number' ? duration : 500;
            var transformStyles = (_a = {},
                _a[ENTERING] = 0,
                _a[ENTERED] = 0,
                _a[EXITING] = animationName === 'slideRight'
                    ? 100 / multipleCount_1
                    : -100 / multipleCount_1,
                _a[EXITED] = animationName === 'slideRight'
                    ? -100 / multipleCount_1
                    : 100 / multipleCount_1,
                _a);
            var itemStyle_1 = multipleCount_1 > 1
                ? __assign({ transitionTimingFunction: 'linear', transitionDuration: transitionDuration }, (animation === 'slide'
                    ? {
                        transform: "translateX(".concat(transformStyles[status], "%)")
                    }
                    : {})) : {};
            var itemRender_1 = function (option) {
                var _a;
                var optionItemSchema = option.itemSchema, restOption = __rest(option, ["itemSchema"]);
                return render("".concat(current, "/body"), optionItemSchema || itemSchema
                    ? optionItemSchema || itemSchema
                    : defaultSchema, {
                    thumbMode: _this.props.thumbMode,
                    data: createObject(data, isObject(option) ? restOption : (_a = { item: option }, _a[name] = option, _a))
                });
            };
            body =
                animation === 'marquee' ? (jsx("div", __assign({ ref: this.marqueeRef, className: cx('Marquee-container'), onMouseEnter: function () {
                        return _this.setState({
                            isPaused: true
                        });
                    }, onMouseLeave: function () {
                        return _this.setState({
                            isPaused: false
                        });
                    }, style: {
                        width: '100%',
                        height: height
                    } }, { children: jsx("div", __assign({ className: cx('Marquee-content'), ref: this.contentRef }, { children: options.concat(options).map(function (option, key) { return (jsxs("div", __assign({ className: cx('Marquee-item') }, { children: [multipleCount_1 === 1 ? itemRender_1(option) : null, multipleCount_1 > 1
                                    ? newOptions_1
                                        .concat(newOptions_1)[key].map(function (option, index) { return (jsx("div", __assign({ style: {
                                            width: 100 / multipleCount_1 + '%',
                                            height: '100%',
                                            float: 'left'
                                        } }, { children: itemRender_1(option) }), index)); })
                                    : null] }), key)); }) })) }))) : (jsx("div", __assign({ ref: this.wrapperRef, className: cx('Carousel-container'), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, { children: options.map(function (option, key) { return (jsx(Transition, __assign({ mountOnEnter: true, unmountOnExit: true, in: key === current, timeout: timeout_1 }, { children: function (status) {
                            if (status === ENTERING) {
                                _this.wrapperRef.current &&
                                    _this.wrapperRef.current.childNodes.forEach(function (item) { return item.offsetHeight; });
                            }
                            if (multipleCount_1 > 1) {
                                if ((status === ENTERING || status === EXITING) &&
                                    !_this.loading) {
                                    _this.loading = true;
                                }
                                else if ((status === ENTERED || status === EXITED) &&
                                    _this.loading) {
                                    _this.loading = false;
                                }
                            }
                            return (jsxs("div", __assign({ className: cx('Carousel-item', animationName, animationStyles[status]), style: itemStyle_1 }, { children: [multipleCount_1 === 1 ? itemRender_1(option) : null, multipleCount_1 > 1
                                        ? newOptions_1[key].map(function (option, index) { return (jsx("div", __assign({ style: {
                                                width: 100 / multipleCount_1 + '%',
                                                height: '100%',
                                                float: 'left'
                                            } }, { children: itemRender_1(option) }), index)); })
                                        : null] })));
                        } }), key)); }) })));
        }
        return (jsxs("div", __assign({ className: cx("Carousel Carousel--".concat(controlsTheme), (_b = {}, _b['Carousel-arrow--always'] = !!alwaysShowArrow, _b), className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle })), { 'Carousel-vertical': this.props.direction === 'vertical' }), onMouseDown: this.props.mouseEvent ? this.addMouseDownListener : undefined, onMouseUp: this.props.mouseEvent ? this.addMouseUpListener : undefined, onMouseLeave: this.props.mouseEvent ? this.addMouseUpListener : undefined, onTouchStart: this.props.mouseEvent ? this.addMouseDownListener : undefined, onTouchEnd: this.props.mouseEvent ? this.addMouseUpListener : undefined, style: carouselStyles }, { children: [body ? body : placeholder, dots ? this.renderDots() : null, arrows ? (jsx("div", __assign({ className: cx('Carousel-leftArrow', setThemeClassName(__assign(__assign({}, this.props), { name: 'galleryControlClassName', id: id, themeCss: themeCss }))), onClick: this.prev }, { children: (icons === null || icons === void 0 ? void 0 : icons.prev) ? (jsx("div", __assign({ className: "ImageGallery-prevBtn" }, { children: jsx(Icon, { icon: icons.prev }) }))) : (jsx(Icon, { icon: "left-arrow", className: "icon", iconContent: "ImageGallery-prevBtn" })) }))) : null, arrows ? (jsx("div", __assign({ className: cx('Carousel-rightArrow', setThemeClassName(__assign(__assign({}, this.props), { name: 'galleryControlClassName', id: id, themeCss: themeCss }))), onClick: this.next }, { children: (icons === null || icons === void 0 ? void 0 : icons.next) ? (jsx("div", __assign({ className: "ImageGallery-prevBtn" }, { children: jsx(Icon, { icon: icons.next }) }))) : (jsx(Icon, { icon: "right-arrow", className: "icon", iconContent: "ImageGallery-nextBtn" })) }))) : null, jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            },
                            {
                                key: 'galleryControlClassName',
                                weights: {
                                    default: {
                                        suf: ' svg',
                                        important: true
                                    }
                                }
                            }
                        ]
                    }, env: env }))] })));
    };
    Carousel.defaultProps = {
        auto: true,
        interval: 5000,
        duration: 500,
        controlsTheme: 'light',
        animation: 'fade',
        controls: ['dots', 'arrows'],
        placeholder: '-',
        multiple: { count: 1 },
        alwaysShowArrow: false
    };
    __decorate([
        autobind
    ], Carousel.prototype, "prepareAutoSlide", null);
    __decorate([
        autobind
    ], Carousel.prototype, "autoSlide", null);
    __decorate([
        autobind
    ], Carousel.prototype, "transitFramesTowards", null);
    __decorate([
        autobind
    ], Carousel.prototype, "getFrameId", null);
    __decorate([
        autobind
    ], Carousel.prototype, "next", null);
    __decorate([
        autobind
    ], Carousel.prototype, "prev", null);
    __decorate([
        autobind
    ], Carousel.prototype, "clearAutoTimeout", null);
    __decorate([
        autobind
    ], Carousel.prototype, "changeSlide", null);
    __decorate([
        autobind
    ], Carousel.prototype, "handleMouseEnter", null);
    __decorate([
        autobind
    ], Carousel.prototype, "handleMouseLeave", null);
    __decorate([
        autobind
    ], Carousel.prototype, "addMouseDownListener", null);
    __decorate([
        autobind
    ], Carousel.prototype, "addMouseUpListener", null);
    return Carousel;
}(React.Component));
var CarouselRenderer = /** @class */ (function (_super) {
    __extends(CarouselRenderer, _super);
    function CarouselRenderer(props, context) {
        var _this = _super.call(this, props) || this;
        var scoped = context;
        scoped.registerComponent(_this);
        return _this;
    }
    CarouselRenderer.prototype.componentWillUnmount = function () {
        var _a;
        (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);
        var scoped = this.context;
        scoped.unRegisterComponent(this);
    };
    CarouselRenderer.contextType = ScopedContext;
    CarouselRenderer = __decorate([
        Renderer({
            type: 'carousel'
        })
    ], CarouselRenderer);
    return CarouselRenderer;
}(Carousel));

export { Carousel, CarouselRenderer };
