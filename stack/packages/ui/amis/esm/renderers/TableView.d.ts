/**
 * @file 用于表格类型的展现效果，界面可定制化能力更强
 */
import React from 'react';
import { RendererProps } from 'amis-core';
import { BaseSchema, SchemaObject } from '../Schema';
export type TdObject = {
    /**
     * 单元格背景色
     */
    background?: string;
    /**
     * 单元格文字颜色
     */
    color?: string;
    /**
     * 单元格文字是否加粗
     */
    bold?: boolean;
    /**
     * 单元格的内边距
     */
    padding?: number;
    /**
     * 单元格宽度
     */
    width?: number;
    /**
     * 单元格内的组件
     */
    body?: SchemaObject;
    /**
     * 水平对齐
     */
    align?: 'left' | 'center' | 'right' | 'justify';
    /**
     * 垂直对齐
     */
    valign?: 'top' | 'middle' | 'bottom' | 'baseline';
    /**
     * 跨几行
     */
    colspan?: number;
    /**
     * 跨几列
     */
    rowspan?: number;
    /**
     * 自定义样式
     */
    style?: object;
    visibleOn?: string;
    hiddenOn?: string;
};
/**
 * 行设置
 */
export type TrObject = {
    /**
     * 行背景色
     */
    background?: string;
    /**
     * 行高度
     */
    height?: number;
    /**
     * 单元格配置
     */
    tds: TdObject[];
    style?: object;
    visibleOn?: string;
    hiddenOn?: string;
};
/**
 * 列设置
 */
export type ColObject = {
    span?: number;
    style?: Object;
};
/**
 * 表格展现渲染器
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/table-view
 */
export interface TableViewSchema extends BaseSchema {
    /**
     * 指定为 table-view 展示类型
     */
    type: 'table-view';
    /**
     * table 容器宽度，默认是 auto
     */
    width?: number | string;
    /**
     *  默认单元格内边距
     */
    padding?: number | string;
    /**
     * 是否显示边框
     */
    border?: boolean;
    /**
     * 边框颜色
     */
    borderColor?: string;
    /**
     * 标题设置
     */
    caption?: string;
    /**
     * 标题位置
     */
    captionSide?: 'top' | 'bottom';
    /**
     * 行设置
     */
    trs: TrObject[];
    /**
     * 列设置
     */
    cols: ColObject[];
}
export interface TableViewProps extends RendererProps, Omit<TableViewSchema, 'type' | 'className'> {
}
export default class TableView extends React.Component<TableViewProps, object> {
    static defaultProps: Partial<TableViewProps>;
    constructor(props: TableViewProps);
    renderTd(td: TdObject, colIndex: number, rowIndex: number): import("react/jsx-runtime").JSX.Element | null;
    renderTdBody(body?: SchemaObject): any;
    renderTds(tds: TdObject[], rowIndex: number): (import("react/jsx-runtime").JSX.Element | null)[];
    renderTr(tr: TrObject, rowIndex: number): import("react/jsx-runtime").JSX.Element | null;
    renderTrs(trs: TrObject[]): (import("react/jsx-runtime").JSX.Element | null)[];
    renderCols(): import("react/jsx-runtime").JSX.Element | null;
    renderCaption(): import("react/jsx-runtime").JSX.Element | null;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class TableViewRenderer extends TableView {
}
//# sourceMappingURL=TableView.d.ts.map