/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { validations, handleAction, getPropValue, isPureVariable, resolveVariableAndFilter, autobind, Renderer } from 'amis-core';
import { GridNav, GridNavItem } from 'amis-ui';

var List = /** @class */ (function (_super) {
    __extends(List, _super);
    function List() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    List.prototype.handleClick = function (item) {
        var _this = this;
        return function (e) {
            var action;
            if (item.link) {
                action = validations.isUrl({}, item.link)
                    ? {
                        type: 'button',
                        actionType: 'url',
                        url: item.link,
                        blank: item.blank
                    }
                    : {
                        type: 'button',
                        actionType: 'link',
                        link: item.link
                    };
            }
            else {
                action = item.clickAction;
            }
            handleAction(e, action, _this.props, item);
        };
    };
    List.prototype.render = function () {
        var _this = this;
        var _a = this.props, itemClassName = _a.itemClassName, style = _a.style, contentClassName = _a.contentClassName, source = _a.source, data = _a.data, options = _a.options, classnames = _a.classnames;
        var value = getPropValue(this.props);
        var list = [];
        if (typeof source === 'string' && isPureVariable(source)) {
            list = resolveVariableAndFilter(source, data, '| raw') || undefined;
        }
        else if (Array.isArray(value)) {
            list = value;
        }
        else if (Array.isArray(options)) {
            list = options;
        }
        if (list && !Array.isArray(list)) {
            list = [list];
        }
        if (!(list === null || list === void 0 ? void 0 : list.length)) {
            return null;
        }
        return (jsx(GridNav, __assign({}, this.props, { children: list.map(function (item, index) { return (jsx(GridNavItem, { onClick: item.clickAction || item.link ? _this.handleClick(item) : undefined, className: itemClassName, contentClassName: contentClassName, text: item.text, icon: item.icon, classnames: classnames, badge: item.badge
                    ? {
                        badge: item.badge,
                        data: data,
                        classnames: classnames
                    }
                    : undefined }, index)); }) })));
    };
    __decorate([
        autobind
    ], List.prototype, "handleClick", null);
    List = __decorate([
        Renderer({
            type: 'grid-nav'
        })
    ], List);
    return List;
}(React.Component));

export { List as default };
