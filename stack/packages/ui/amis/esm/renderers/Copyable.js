/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import hoistNonReactStatic from 'hoist-non-react-statics';
import { filter } from 'amis-core';
import { TooltipWrapper, Icon } from 'amis-ui';

var HocCopyable = function () {
    return function (Component) {
        var QuickEditComponent = /** @class */ (function (_super) {
            __extends(QuickEditComponent, _super);
            function QuickEditComponent() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            QuickEditComponent.prototype.handleClick = function (content) {
                var _a = this.props, env = _a.env, copyFormat = _a.copyFormat;
                env.copy && env.copy(content, { format: copyFormat });
            };
            QuickEditComponent.prototype.render = function () {
                var _a = this.props, name = _a.name, className = _a.className, data = _a.data, noHoc = _a.noHoc, cx = _a.classnames, __ = _a.translate, env = _a.env, tooltipContainer = _a.tooltipContainer;
                var copyable = this.props.copyable;
                if (copyable && !noHoc) {
                    var content = filter(copyable.content || '${' + name + ' | raw }', data);
                    var tooltip = (copyable === null || copyable === void 0 ? void 0 : copyable.tooltip) != null
                        ? filter(copyable.tooltip, data)
                        : copyable === null || copyable === void 0 ? void 0 : copyable.tooltip;
                    if (content) {
                        return (jsxs(Component, __assign({}, this.props, { className: cx("Field--copyable", className) }, { children: [jsx(Component, __assign({}, this.props, { contentsOnly: true, noHoc: true })), jsx(TooltipWrapper, __assign({ placement: "right", tooltip: tooltip !== null && tooltip !== void 0 ? tooltip : __('Copyable.tip'), trigger: "hover", container: tooltipContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer) }, { children: jsx("a", __assign({ className: cx('Field-copyBtn'), onClick: this.handleClick.bind(this, content) }, { children: jsx(Icon, { icon: "copy", className: "icon" }) }), "edit-btn") }))] })));
                    }
                }
                return jsx(Component, __assign({}, this.props));
            };
            QuickEditComponent.ComposedComponent = Component;
            return QuickEditComponent;
        }(React.PureComponent));
        hoistNonReactStatic(QuickEditComponent, Component);
        return QuickEditComponent;
    };
};

export { HocCopyable, HocCopyable as default };
