/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { resolveVariableAndFilter, Renderer } from 'amis-core';
import mapValues from 'lodash/mapValues';

var WebComponent = /** @class */ (function (_super) {
    __extends(WebComponent, _super);
    function WebComponent() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    WebComponent.prototype.renderBody = function () {
        var _a = this.props, body = _a.body, render = _a.render;
        return body ? render('body', body) : null;
    };
    WebComponent.prototype.render = function () {
        var _a = this.props, tag = _a.tag, props = _a.props, data = _a.data, style = _a.style;
        var propsValues = mapValues(props, function (s) {
            if (typeof s === 'string') {
                return resolveVariableAndFilter(s, data, '| raw') || s;
            }
            else {
                return s;
            }
        });
        var Component = tag || 'div';
        return (jsx(Component, __assign({}, propsValues, { style: style }, { children: this.renderBody() })));
    };
    return WebComponent;
}(React.Component));
var WebComponentRenderer = /** @class */ (function (_super) {
    __extends(WebComponentRenderer, _super);
    function WebComponentRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    WebComponentRenderer = __decorate([
        Renderer({
            type: 'web-component'
        })
    ], WebComponentRenderer);
    return WebComponentRenderer;
}(WebComponent));

export { WebComponentRenderer, WebComponent as default };
