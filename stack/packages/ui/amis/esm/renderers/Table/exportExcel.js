/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign, __awaiter, __generator } from 'tslib';
import { filter, removeHTMLTag, decodeEntity, isEffectiveApi, isPureVariable, resolveVariableAndFilter, createObject, isObject, getImageDimensions, toDataURL, getVariable, arraySlice, flattenTree, TableStore } from 'amis-core';
import './ColumnToggler.js';
import { saveAs } from 'file-saver';
import memoize from 'lodash/memoize';
import { getSnapshot } from 'mobx-state-tree';
import moment from 'moment';

/**
 * 导出 Excel 功能
 */
var loadDb = function () {
    // @ts-ignore
    return import('amis-ui/lib/components/CityDB');
};
/**
 * 将 url 转成绝对地址
 */
var getAbsoluteUrl = (function () {
    var link;
    return function (url) {
        if (!link)
            link = document.createElement('a');
        link.href = url;
        return link.href;
    };
})();
/**
 * 将 computedStyle 的 rgba 转成 argb hex
 */
var rgba2argb = memoize(function (rgba) {
    var color = "".concat(rgba
        .match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+\.{0,1}\d*))?\)$/)
        .slice(1)
        .map(function (n, i) {
        return (i === 3 ? Math.round(parseFloat(n) * 255) : parseFloat(n))
            .toString(16)
            .padStart(2, '0')
            .replace('NaN', '');
    })
        .join(''));
    if (color.length === 6) {
        return 'FF' + color;
    }
    return color;
});
/**
 * 将 classname 转成对应的 excel 样式，只支持字体颜色、粗细、背景色
 */
var getCellStyleByClassName = memoize(function (className) {
    if (!className)
        return {};
    var classNameElm = document.getElementsByClassName(className).item(0);
    if (classNameElm) {
        var computedStyle = getComputedStyle(classNameElm);
        var font = {};
        var fill = {};
        if (computedStyle.color && computedStyle.color.indexOf('rgb') !== -1) {
            var color = rgba2argb(computedStyle.color);
            // 似乎不支持完全透明的情况，所以就不设置
            if (!color.startsWith('00')) {
                font['color'] = { argb: color };
            }
        }
        if (computedStyle.fontWeight && parseInt(computedStyle.fontWeight) >= 700) {
            font['bold'] = true;
        }
        if (computedStyle.backgroundColor &&
            computedStyle.backgroundColor.indexOf('rgb') !== -1) {
            var color = rgba2argb(computedStyle.backgroundColor);
            if (!color.startsWith('00')) {
                fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: color }
                };
            }
        }
        return { font: font, fill: fill };
    }
    return {};
});
/**
 * 设置单元格样式
 */
var applyCellStyle = function (sheetRow, columIndex, schema, data) {
    var cellStyle = {};
    if (schema.className) {
        for (var _i = 0, _a = schema.className.split(/\s+/); _i < _a.length; _i++) {
            var className = _a[_i];
            var style = getCellStyleByClassName(className);
            if (style) {
                cellStyle = __assign(__assign({}, cellStyle), style);
            }
        }
    }
    if (schema.classNameExpr) {
        var classNames = filter(schema.classNameExpr, data);
        if (classNames) {
            for (var _b = 0, _c = classNames.split(/\s+/); _b < _c.length; _b++) {
                var className = _c[_b];
                var style = getCellStyleByClassName(className);
                if (style) {
                    cellStyle = __assign(__assign({}, cellStyle), style);
                }
            }
        }
    }
    if (cellStyle.font && Object.keys(cellStyle.font).length > 0) {
        sheetRow.getCell(columIndex).font = cellStyle.font;
    }
    if (cellStyle.fill && Object.keys(cellStyle.fill).length > 0) {
        sheetRow.getCell(columIndex).fill = cellStyle.fill;
    }
};
/**
 * 输出总结行
 */
var renderSummary = function (worksheet, data, summarySchema, rowIndex) {
    if (summarySchema && summarySchema.length > 0) {
        var firstSchema = summarySchema[0];
        // 总结行支持二维数组，所以统一转成二维数组来方便操作
        var affixRows = summarySchema;
        if (!Array.isArray(firstSchema)) {
            affixRows = [summarySchema];
        }
        for (var _i = 0, affixRows_1 = affixRows; _i < affixRows_1.length; _i++) {
            var affix = affixRows_1[_i];
            rowIndex += 1;
            var sheetRow = worksheet.getRow(rowIndex);
            var columIndex = 0;
            for (var _a = 0, affix_1 = affix; _a < affix_1.length; _a++) {
                var col = affix_1[_a];
                columIndex += 1;
                // 文档示例中只有这两种，所以主要支持这两种，没法支持太多，因为没法用 react 渲染结果
                if (col.text) {
                    sheetRow.getCell(columIndex).value = col.text;
                }
                if (col.tpl) {
                    sheetRow.getCell(columIndex).value = removeHTMLTag(decodeEntity(filter(col.tpl, data)));
                }
                // 处理合并行
                if (col.colSpan) {
                    worksheet.mergeCells(rowIndex, columIndex, rowIndex, columIndex + col.colSpan - 1);
                    columIndex += col.colSpan - 1;
                }
            }
        }
    }
    return rowIndex;
};
/**
 * 获取 map 的映射数据
 * @param remoteMappingCache 缓存
 * @param env mobx env
 * @param column 列配置
 * @param data 上下文数据
 * @param rowData 当前行数据
 * @returns
 */
function getMap(remoteMappingCache, env, column, data, rowData) {
    return __awaiter(this, void 0, void 0, function () {
        var map, source, sourceValue, mapKey, res;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    map = column.pristine.map;
                    source = column.pristine.source;
                    if (!source) return [3 /*break*/, 4];
                    sourceValue = source;
                    if (!isPureVariable(source)) return [3 /*break*/, 1];
                    map = resolveVariableAndFilter(source, rowData, '| raw');
                    return [3 /*break*/, 4];
                case 1:
                    if (!isEffectiveApi(source, data)) return [3 /*break*/, 4];
                    mapKey = JSON.stringify(source);
                    if (!(mapKey in remoteMappingCache)) return [3 /*break*/, 2];
                    map = remoteMappingCache[mapKey];
                    return [3 /*break*/, 4];
                case 2: return [4 /*yield*/, env.fetcher(sourceValue, rowData)];
                case 3:
                    res = _a.sent();
                    if (res.data) {
                        remoteMappingCache[mapKey] = res.data;
                        map = res.data;
                    }
                    _a.label = 4;
                case 4: return [2 /*return*/, map];
            }
        });
    });
}
/**
 * 导出 Excel
 * @param ExcelJS ExcelJS 对象
 * @param props Table 组件的 props
 * @param toolbar 导出 Excel 的 toolbar 配置
 * @param withoutData 如果为 true 就不导出数据，只导出表头
 */
function exportExcel(ExcelJS, props, toolbar, withoutData) {
    var _a, _b, _c, _d, _e;
    if (withoutData === void 0) { withoutData = false; }
    return __awaiter(this, void 0, void 0, function () {
        var store, env, cx, __, data, prefixRow, affixRow, columns, rows, tmpStore, filename, pageField, perPageField, ctx, res, _i, _f, key, workbook, worksheet, exportColumnNames, hasCustomExportColumns, _g, columns_1, column, filteredColumns, firstRowLabels, firstRow, remoteMappingCache, rowIndex, _h, rows_1, row, rowData, sheetRow, columIndex, _loop_1, _j, filteredColumns_1, column;
        var _k;
        return __generator(this, function (_l) {
            switch (_l.label) {
                case 0:
                    store = props.store, env = props.env, cx = props.classnames, __ = props.translate, data = props.data, prefixRow = props.prefixRow, affixRow = props.affixRow;
                    columns = store.exportColumns || [];
                    rows = [];
                    filename = 'data';
                    if (!(typeof toolbar === 'object' && toolbar.api)) return [3 /*break*/, 2];
                    pageField = toolbar.pageField || 'page';
                    perPageField = toolbar.perPageField || 'perPage';
                    ctx = createObject(data, __assign(__assign({}, props.query), (_k = {}, _k[pageField] = data.page || 1, _k[perPageField] = data.perPage || 10, _k)));
                    return [4 /*yield*/, env.fetcher(toolbar.api, ctx, {
                            autoAppend: true,
                            pageField: pageField,
                            perPageField: perPageField
                        })];
                case 1:
                    res = _l.sent();
                    if (((_a = toolbar.api) === null || _a === void 0 ? void 0 : _a.responseType) === 'blob') {
                        // 如果是返回的文件流就直接下载
                        return [2 /*return*/];
                    }
                    else if (!res.data) {
                        env.notify('warning', __('placeholder.noData'));
                        return [2 /*return*/];
                    }
                    /**
                     * 优先找items和rows，找不到就拿第一个值为数组的字段
                     * 和CRUD中的处理逻辑保持一致，避免能渲染和导出的不一致
                     */
                    if (Array.isArray(res.data)) {
                        rows = res.data;
                    }
                    else if (Array.isArray((_b = res.data) === null || _b === void 0 ? void 0 : _b.rows)) {
                        rows = res.data.rows;
                    }
                    else if (Array.isArray((_c = res.data) === null || _c === void 0 ? void 0 : _c.items)) {
                        rows = res.data.items;
                    }
                    else {
                        for (_i = 0, _f = Object.keys(res.data); _i < _f.length; _i++) {
                            key = _f[_i];
                            if (res.data.hasOwnProperty(key) && Array.isArray(res.data[key])) {
                                rows = res.data[key];
                                break;
                            }
                        }
                    }
                    // 因为很多方法是 store 里的，所以需要构建 store 来处理
                    tmpStore = TableStore.create(getSnapshot(store));
                    tmpStore.initRows(rows);
                    rows = tmpStore.rows;
                    return [3 /*break*/, 3];
                case 2:
                    rows = store.rows;
                    _l.label = 3;
                case 3:
                    if (typeof toolbar === 'object' && toolbar.filename) {
                        filename = filter(toolbar.filename, data, '| raw');
                    }
                    if (rows.length === 0) {
                        env.notify('warning', __('placeholder.noData'));
                        return [2 /*return*/];
                    }
                    workbook = new ExcelJS.Workbook();
                    worksheet = workbook.addWorksheet('sheet', {
                        properties: { defaultColWidth: 15 }
                    });
                    worksheet.views = [{ state: 'frozen', xSplit: 0, ySplit: 1 }];
                    exportColumnNames = toolbar.columns;
                    if (isPureVariable(exportColumnNames)) {
                        exportColumnNames = resolveVariableAndFilter(exportColumnNames, data, '| raw');
                    }
                    hasCustomExportColumns = toolbar.exportColumns && Array.isArray(toolbar.exportColumns);
                    if (hasCustomExportColumns) {
                        columns = toolbar.exportColumns;
                        // 因为后面列 props 都是从 pristine 里获取，所以这里归一一下
                        for (_g = 0, columns_1 = columns; _g < columns_1.length; _g++) {
                            column = columns_1[_g];
                            column.pristine = column;
                        }
                    }
                    filteredColumns = exportColumnNames
                        ? columns.filter(function (column) {
                            var filterColumnsNames = exportColumnNames;
                            if (column.name && filterColumnsNames.indexOf(column.name) !== -1) {
                                return hasCustomExportColumns ? true : (column === null || column === void 0 ? void 0 : column.type) !== 'operation';
                            }
                            return false;
                        })
                        : columns.filter(function (column) { return (column === null || column === void 0 ? void 0 : column.type) !== 'operation'; });
                    firstRowLabels = filteredColumns.map(function (column) {
                        return filter(column.label, data);
                    });
                    firstRow = worksheet.getRow(1);
                    firstRow.values = firstRowLabels;
                    worksheet.autoFilter = {
                        from: {
                            row: 1,
                            column: 1
                        },
                        to: {
                            row: 1,
                            column: firstRowLabels.length
                        }
                    };
                    if (withoutData) {
                        return [2 /*return*/, exportExcelWithoutData(workbook, worksheet, filteredColumns, filename, env, data)];
                    }
                    remoteMappingCache = {};
                    rowIndex = 1;
                    if (toolbar.rowSlice) {
                        rows = arraySlice(rows, toolbar.rowSlice);
                    }
                    // 前置总结行
                    rowIndex = renderSummary(worksheet, data, prefixRow, rowIndex);
                    // children 展开
                    rows = flattenTree(rows, function (item) { return item; });
                    _h = 0, rows_1 = rows;
                    _l.label = 4;
                case 4:
                    if (!(_h < rows_1.length)) return [3 /*break*/, 9];
                    row = rows_1[_h];
                    rowData = createObject(data, row.data);
                    rowIndex += 1;
                    sheetRow = worksheet.getRow(rowIndex);
                    columIndex = 0;
                    _loop_1 = function (column) {
                        var name_1, value, type, imageData, imageDimensions, imageWidth, imageHeight, imageMaxSize, imageMatch, imageExt, imageId, linkURL, e_1, href, linkURL, body, text, absoluteURL, map, valueField_1, labelField, viewValue, label, text, viewValue, _m, fromNow, _o, format, _p, valueFormat, ISODate, NormalDate, db, cellValue;
                        return __generator(this, function (_q) {
                            switch (_q.label) {
                                case 0:
                                    columIndex += 1;
                                    name_1 = column.name;
                                    value = getVariable(rowData, name_1);
                                    if (typeof value === 'undefined' && !column.pristine.tpl) {
                                        return [2 /*return*/, "continue"];
                                    }
                                    // 处理合并单元格
                                    if (name_1 in row.rowSpans) {
                                        if (row.rowSpans[name_1] === 0) {
                                            return [2 /*return*/, "continue"];
                                        }
                                        else {
                                            // start row, start column, end row, end column
                                            worksheet.mergeCells(rowIndex, columIndex, rowIndex + row.rowSpans[name_1] - 1, columIndex);
                                        }
                                    }
                                    applyCellStyle(sheetRow, columIndex, column.pristine, rowData);
                                    type = column.type || 'plain';
                                    if (!((type === 'image' || type === 'static-image') && value)) return [3 /*break*/, 6];
                                    _q.label = 1;
                                case 1:
                                    _q.trys.push([1, 4, , 5]);
                                    return [4 /*yield*/, toDataURL(value)];
                                case 2:
                                    imageData = _q.sent();
                                    return [4 /*yield*/, getImageDimensions(imageData)];
                                case 3:
                                    imageDimensions = _q.sent();
                                    imageWidth = imageDimensions.width;
                                    imageHeight = imageDimensions.height;
                                    imageMaxSize = 100;
                                    if (imageWidth > imageHeight) {
                                        if (imageWidth > imageMaxSize) {
                                            imageHeight = (imageMaxSize * imageHeight) / imageWidth;
                                            imageWidth = imageMaxSize;
                                        }
                                    }
                                    else {
                                        if (imageHeight > imageMaxSize) {
                                            imageWidth = (imageMaxSize * imageWidth) / imageHeight;
                                            imageHeight = imageMaxSize;
                                        }
                                    }
                                    imageMatch = imageData.match(/data:image\/(.*);/);
                                    imageExt = 'png';
                                    if (imageMatch) {
                                        imageExt = imageMatch[1];
                                    }
                                    // 目前 excel 只支持这些格式，所以其它格式直接输出 url
                                    if (imageExt != 'png' && imageExt != 'jpeg' && imageExt != 'gif') {
                                        sheetRow.getCell(columIndex).value = value;
                                        return [2 /*return*/, "continue"];
                                    }
                                    imageId = workbook.addImage({
                                        base64: imageData,
                                        extension: imageExt
                                    });
                                    linkURL = getAbsoluteUrl(value);
                                    worksheet.addImage(imageId, {
                                        // 这里坐标位置是从 0 开始的，所以要减一
                                        tl: { col: columIndex - 1, row: rowIndex - 1 },
                                        ext: {
                                            width: imageWidth,
                                            height: imageHeight
                                        },
                                        hyperlinks: {
                                            tooltip: linkURL
                                        }
                                    });
                                    return [3 /*break*/, 5];
                                case 4:
                                    e_1 = _q.sent();
                                    console.warn(e_1);
                                    return [3 /*break*/, 5];
                                case 5: return [3 /*break*/, 13];
                                case 6:
                                    if (!(type == 'link' || type === 'static-link')) return [3 /*break*/, 7];
                                    href = column.pristine.href;
                                    linkURL = (typeof href === 'string' && href
                                        ? filter(href, rowData, '| raw')
                                        : undefined) || value;
                                    body = column.pristine.body;
                                    text = typeof body === 'string' && body
                                        ? filter(body, rowData, '| raw')
                                        : undefined;
                                    absoluteURL = getAbsoluteUrl(linkURL);
                                    sheetRow.getCell(columIndex).value = {
                                        text: text || absoluteURL,
                                        hyperlink: absoluteURL
                                    };
                                    return [3 /*break*/, 13];
                                case 7:
                                    if (!(type === 'mapping' || type === 'static-mapping')) return [3 /*break*/, 9];
                                    return [4 /*yield*/, getMap(remoteMappingCache, env, column, data, rowData)];
                                case 8:
                                    map = _q.sent();
                                    valueField_1 = column.pristine.valueField || 'value';
                                    labelField = column.pristine.labelField || 'label';
                                    if (Array.isArray(map)) {
                                        map = map.reduce(function (res, now) {
                                            if (now == null) {
                                                return res;
                                            }
                                            else if (isObject(now)) {
                                                var keys = Object.keys(now);
                                                if (keys.length === 1 ||
                                                    (keys.length == 2 && keys.includes('$$id'))) {
                                                    // 针对amis-editor的特殊处理
                                                    keys = keys.filter(function (key) { return key !== '$$id'; });
                                                    // 单key 数组对象
                                                    res[keys[0]] = now[keys[0]];
                                                }
                                                else if (keys.length > 1) {
                                                    // 多key 数组对象
                                                    res[now[valueField_1]] = now;
                                                }
                                            }
                                            return res;
                                        }, {});
                                    }
                                    if (typeof value !== 'undefined' && map && ((_d = map[value]) !== null && _d !== void 0 ? _d : map['*'])) {
                                        viewValue = (_e = map[value]) !== null && _e !== void 0 ? _e : (value === true && map['1']
                                            ? map['1']
                                            : value === false && map['0']
                                                ? map['0']
                                                : map['*']);
                                        label = viewValue;
                                        if (isObject(viewValue)) {
                                            if (labelField === undefined || labelField === '') {
                                                if (!viewValue.hasOwnProperty('type')) {
                                                    // 映射值是object
                                                    // 没配置labelField
                                                    // object 也没有 type，不能作为schema渲染
                                                    // 默认取 label 字段
                                                    label = viewValue['label'];
                                                }
                                            }
                                            else {
                                                label = viewValue[labelField || 'label'];
                                            }
                                        }
                                        text = removeHTMLTag(label);
                                        /** map可能会使用比较复杂的html结构，富文本也无法完全支持，直接把里面的变量解析出来即可 */
                                        if (isPureVariable(text)) {
                                            text = resolveVariableAndFilter(text, rowData, '| raw');
                                        }
                                        else {
                                            text = filter(text, rowData);
                                        }
                                        sheetRow.getCell(columIndex).value = text;
                                    }
                                    else {
                                        sheetRow.getCell(columIndex).value = removeHTMLTag(value);
                                    }
                                    return [3 /*break*/, 13];
                                case 9:
                                    if (!(type === 'date' || type === 'static-date')) return [3 /*break*/, 10];
                                    viewValue = void 0;
                                    _m = column.pristine, fromNow = _m.fromNow, _o = _m.format, format = _o === void 0 ? 'YYYY-MM-DD' : _o, _p = _m.valueFormat, valueFormat = _p === void 0 ? 'X' : _p;
                                    if (value) {
                                        ISODate = moment(value, moment.ISO_8601);
                                        NormalDate = moment(value, valueFormat);
                                        viewValue = ISODate.isValid()
                                            ? ISODate.format(format)
                                            : NormalDate.isValid()
                                                ? NormalDate.format(format)
                                                : false;
                                    }
                                    if (fromNow) {
                                        viewValue = moment(value).fromNow();
                                    }
                                    if (viewValue) {
                                        sheetRow.getCell(columIndex).value = viewValue;
                                    }
                                    return [3 /*break*/, 13];
                                case 10:
                                    if (!(type === 'input-city')) return [3 /*break*/, 12];
                                    return [4 /*yield*/, loadDb()];
                                case 11:
                                    db = _q.sent();
                                    if (db.default && value && value in db.default) {
                                        sheetRow.getCell(columIndex).value = db.default[value];
                                    }
                                    return [3 /*break*/, 13];
                                case 12:
                                    if (column.pristine.tpl) {
                                        sheetRow.getCell(columIndex).value = removeHTMLTag(decodeEntity(filter(column.pristine.tpl, rowData)));
                                    }
                                    else {
                                        sheetRow.getCell(columIndex).value = value;
                                    }
                                    _q.label = 13;
                                case 13:
                                    cellValue = sheetRow.getCell(columIndex).value;
                                    if (Number.isInteger(cellValue)) {
                                        sheetRow.getCell(columIndex).numFmt = '0';
                                    }
                                    return [2 /*return*/];
                            }
                        });
                    };
                    _j = 0, filteredColumns_1 = filteredColumns;
                    _l.label = 5;
                case 5:
                    if (!(_j < filteredColumns_1.length)) return [3 /*break*/, 8];
                    column = filteredColumns_1[_j];
                    return [5 /*yield**/, _loop_1(column)];
                case 6:
                    _l.sent();
                    _l.label = 7;
                case 7:
                    _j++;
                    return [3 /*break*/, 5];
                case 8:
                    _h++;
                    return [3 /*break*/, 4];
                case 9:
                    // 后置总结行
                    renderSummary(worksheet, data, affixRow, rowIndex);
                    downloadFile(workbook, filename);
                    return [2 /*return*/];
            }
        });
    });
}
function downloadFile(workbook, filename) {
    return __awaiter(this, void 0, void 0, function () {
        var buffer, blob;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, workbook.xlsx.writeBuffer()];
                case 1:
                    buffer = _a.sent();
                    if (buffer) {
                        blob = new Blob([buffer], {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        });
                        saveAs(blob, filename + '.xlsx');
                    }
                    return [2 /*return*/];
            }
        });
    });
}
function numberToLetters(num) {
    var letters = '';
    while (num >= 0) {
        letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[num % 26] + letters;
        num = Math.floor(num / 26) - 1;
    }
    return letters;
}
/**
 * 只导出表头
 */
function exportExcelWithoutData(workbook, worksheet, filteredColumns, filename, env, data) {
    var _a;
    return __awaiter(this, void 0, void 0, function () {
        var index, rowNumber, mapCache, _i, filteredColumns_2, column, map, keys, rowIndex;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    index = 0;
                    rowNumber = 100;
                    mapCache = {};
                    _i = 0, filteredColumns_2 = filteredColumns;
                    _b.label = 1;
                case 1:
                    if (!(_i < filteredColumns_2.length)) return [3 /*break*/, 4];
                    column = filteredColumns_2[_i];
                    index += 1;
                    if (!(((_a = column.pristine) === null || _a === void 0 ? void 0 : _a.type) === 'mapping')) return [3 /*break*/, 3];
                    return [4 /*yield*/, getMap(mapCache, env, column, data, {})];
                case 2:
                    map = _b.sent();
                    if (map && isObject(map)) {
                        keys = Object.keys(map);
                        for (rowIndex = 1; rowIndex < rowNumber; rowIndex++) {
                            worksheet.getCell(numberToLetters(index) + rowIndex).dataValidation =
                                {
                                    type: 'list',
                                    allowBlank: true,
                                    formulae: ["\"".concat(keys.join(','), "\"")]
                                };
                        }
                    }
                    _b.label = 3;
                case 3:
                    _i++;
                    return [3 /*break*/, 1];
                case 4:
                    downloadFile(workbook, filename);
                    return [2 /*return*/];
            }
        });
    });
}

export { exportExcel };
