/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign, __extends } from 'tslib';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import React from 'react';
import { TableBody } from './TableBody.js';
import { observer } from 'mobx-react';
import ItemActionsWrapper from './ItemActionsWrapper.js';
import { Icon } from 'amis-ui';
import ColGroup from './ColGroup.js';

function renderItemActions(props) {
    var itemActions = props.itemActions, render = props.render, store = props.store, cx = props.classnames;
    if (!store.hoverRow) {
        return null;
    }
    var finalActions = Array.isArray(itemActions)
        ? itemActions.filter(function (action) { return !action.hiddenOnHover; })
        : [];
    if (!finalActions.length) {
        return null;
    }
    return (jsx(ItemActionsWrapper, __assign({ store: store, classnames: cx }, { children: jsx("div", __assign({ className: cx('Table-itemActions') }, { children: finalActions.map(function (action, index) {
                return render("itemAction/".concat(index), __assign(__assign({}, action), { isMenuItem: true }), {
                    key: index,
                    item: store.hoverRow,
                    data: store.hoverRow.locals,
                    rowIndex: store.hoverRow.index
                });
            }) })) })));
}
var TableContent = /** @class */ (function (_super) {
    __extends(TableContent, _super);
    function TableContent() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TableContent.prototype.render = function () {
        var _a = this.props, placeholder = _a.placeholder, cx = _a.classnames, render = _a.render, className = _a.className, columns = _a.columns, columnsGroup = _a.columnsGroup, onMouseMove = _a.onMouseMove, onScroll = _a.onScroll, tableRef = _a.tableRef, rows = _a.rows, renderHeadCell = _a.renderHeadCell, renderCell = _a.renderCell, onCheck = _a.onCheck, onRowClick = _a.onRowClick, onRowDbClick = _a.onRowDbClick, onRowMouseEnter = _a.onRowMouseEnter, onRowMouseLeave = _a.onRowMouseLeave, rowClassName = _a.rowClassName, onQuickChange = _a.onQuickChange, footable = _a.footable, footableColumns = _a.footableColumns, checkOnItemClick = _a.checkOnItemClick, buildItemProps = _a.buildItemProps, onAction = _a.onAction, rowClassNameExpr = _a.rowClassNameExpr, affixRowClassName = _a.affixRowClassName, prefixRowClassName = _a.prefixRowClassName, data = _a.data, prefixRow = _a.prefixRow, locale = _a.locale, translate = _a.translate, itemAction = _a.itemAction, affixRow = _a.affixRow, store = _a.store, dispatchEvent = _a.dispatchEvent, onEvent = _a.onEvent, loading = _a.loading, testIdBuilder = _a.testIdBuilder, children = _a.children;
        var tableClassName = cx('Table-table', this.props.tableClassName);
        var hideHeader = columns.every(function (column) { return !column.label; });
        return (jsxs("div", __assign({ onMouseMove: onMouseMove, className: cx('Table-content', className), onScroll: onScroll }, { children: [jsxs("table", __assign({ ref: tableRef, className: cx(tableClassName) }, { children: [jsx(ColGroup, { columns: columns, store: store }), jsxs("thead", { children: [columnsGroup.length ? (jsx("tr", { children: columnsGroup.map(function (item, index) {
                                        var _a = store.getStickyStyles(item, columnsGroup), stickyStyle = _a[0], stickyClassName = _a[1];
                                        /**
                                         * 勾选列和展开列的表头单独成列
                                         * 如果分组列只有一个元素且未分组时，也要执行表头合并
                                         */
                                        return !!~['__checkme', '__expandme'].indexOf(item.has[0].type) ||
                                            (item.has.length === 1 &&
                                                !/^__/.test(item.has[0].type) &&
                                                !item.has[0].groupName) ? (renderHeadCell(item.has[0], {
                                            'data-index': item.has[0].index,
                                            'key': index,
                                            'colSpan': item.colSpan,
                                            'rowSpan': item.rowSpan,
                                            'style': stickyStyle,
                                            'className': stickyClassName
                                        })) : (jsx("th", __assign({ "data-index": item.index, colSpan: item.colSpan, rowSpan: item.rowSpan, style: stickyStyle, className: stickyClassName }, { children: item.label ? render('tpl', item.label) : null }), index));
                                    }) })) : null, jsx("tr", __assign({ className: hideHeader ? 'fake-hide' : '' }, { children: columns.map(function (column) {
                                        var _a;
                                        return ((_a = columnsGroup.find(function (group) { return ~group.has.indexOf(column); })) === null || _a === void 0 ? void 0 : _a.rowSpan) === 2
                                            ? null
                                            : renderHeadCell(column, {
                                                'data-index': column.index,
                                                'key': column.index
                                            });
                                    }) }))] }), !rows.length ? (jsx("tbody", { children: jsx("tr", __assign({ className: cx('Table-placeholder') }, { children: !loading ? (jsx("td", __assign({ colSpan: columns.length }, { children: typeof placeholder === 'string' ? (jsxs(Fragment, { children: [jsx(Icon, { icon: "desk-empty", className: cx('Table-placeholder-empty-icon', 'icon') }), translate(placeholder || 'placeholder.noData')] })) : (render('placeholder', translate(placeholder || 'placeholder.noData'))) }))) : null })) })) : (jsx(TableBody, { store: store, itemAction: itemAction, classnames: cx, render: render, renderCell: renderCell, onCheck: onCheck, onRowClick: onRowClick, onRowDbClick: onRowDbClick, onRowMouseEnter: onRowMouseEnter, onRowMouseLeave: onRowMouseLeave, onQuickChange: onQuickChange, footable: footable, footableColumns: footableColumns, checkOnItemClick: checkOnItemClick, buildItemProps: buildItemProps, onAction: onAction, rowClassNameExpr: rowClassNameExpr, rowClassName: rowClassName, prefixRowClassName: prefixRowClassName, affixRowClassName: affixRowClassName, rows: rows, columns: columns, locale: locale, translate: translate, prefixRow: prefixRow, affixRow: affixRow, data: data, testIdBuilder: testIdBuilder, rowsProps: {
                                dispatchEvent: dispatchEvent,
                                onEvent: onEvent
                            } }))] })), children] })));
    };
    return TableContent;
}(React.PureComponent));
var TableContent$1 = observer(function (props) {
    var store = props.store;
    // 分析 table/index.tsx 中的 renderHeadCell 依赖了以下属性
    // store.someChecked;
    // store.allChecked;
    // store.isSelectionThresholdReached;
    // store.allExpanded;
    // store.orderBy
    // store.orderDir
    var className = props.classnames(props.className, store.rows.length > store.lazyRenderAfter ? 'use-virtual-list' : '');
    var tableClassName = props.classnames(props.tableClassName, store.tableLayout === 'fixed' ? 'is-layout-fixed' : undefined);
    return (jsx(TableContent, __assign({}, props, { className: className, tableClassName: tableClassName, columnWidthReady: store.columnWidthReady, someChecked: store.someChecked, allChecked: store.allChecked, isSelectionThresholdReached: store.isSelectionThresholdReached, orderBy: store.orderBy, orderDir: store.orderDir })));
});

export { TableContent, TableContent$1 as default, renderItemActions };
