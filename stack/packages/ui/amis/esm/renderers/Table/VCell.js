/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import Cell from './Cell.js';
import { useInView } from 'react-intersection-observer';
import { observer } from 'mobx-react-lite';

var VCell = observer(function VCell(props) {
    var cx = props.classnames;
    var column = props.column;
    var _a = useInView({
        threshold: 0,
        triggerOnce: true,
        onChange: column.markAppeared,
        skip: column.appeared
    }), ref = _a.ref, inView = _a.inView;
    return inView || column.appeared ? (jsx(Cell, __assign({}, props))) : (jsx("td", __assign({ ref: ref, rowSpan: props.rowSpan > 1 ? props.rowSpan : undefined, style: props.style, className: props.className }, { children: jsx("div", __assign({ className: cx('Table-emptyBlock') }, { children: "\u00A0" })) })));
});

export { VCell as default };
