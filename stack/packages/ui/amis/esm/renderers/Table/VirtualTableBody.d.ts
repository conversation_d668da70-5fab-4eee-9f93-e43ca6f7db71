/// <reference types="hoist-non-react-statics" />
import React from 'react';
import { ITableStore, ThemeProps } from 'amis-core';
export interface VirtualTableBodyProps extends ThemeProps {
    className?: string;
    rows: React.ReactNode[];
    store: ITableStore;
}
declare function VirtualTableBody(props: VirtualTableBodyProps): import("react/jsx-runtime").JSX.Element;
declare const _default: {
    new (props: Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps): {
        ref: any;
        childRef(ref: any): void;
        getWrappedInstance(): any;
        render(): import("react/jsx-runtime").JSX.Element;
        context: unknown;
        setState<K extends never>(state: {} | ((prevState: Readonly<{}>, props: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>) => {} | Pick<{}, K> | null) | Pick<{}, K> | null, callback?: (() => void) | undefined): void;
        forceUpdate(callback?: (() => void) | undefined): void;
        readonly props: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>;
        state: Readonly<{}>;
        refs: {
            [key: string]: React.ReactInstance;
        };
        componentDidMount?(): void;
        shouldComponentUpdate?(nextProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, nextState: Readonly<{}>, nextContext: any): boolean;
        componentWillUnmount?(): void;
        componentDidCatch?(error: Error, errorInfo: React.ErrorInfo): void;
        getSnapshotBeforeUpdate?(prevProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, prevState: Readonly<{}>): any;
        componentDidUpdate?(prevProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, prevState: Readonly<{}>, snapshot?: any): void;
        componentWillMount?(): void;
        UNSAFE_componentWillMount?(): void;
        componentWillReceiveProps?(nextProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, nextContext: any): void;
        UNSAFE_componentWillReceiveProps?(nextProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, nextContext: any): void;
        componentWillUpdate?(nextProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, nextState: Readonly<{}>, nextContext: any): void;
        UNSAFE_componentWillUpdate?(nextProps: Readonly<Omit<VirtualTableBodyProps, keyof import("amis-core/esm").ThemeProps> & import("amis-core/esm/theme").ThemeOuterProps>, nextState: Readonly<{}>, nextContext: any): void;
    };
    displayName: string;
    contextType: React.Context<string>;
    ComposedComponent: React.ComponentType<typeof VirtualTableBody>;
} & import("hoist-non-react-statics").NonReactStatics<typeof VirtualTableBody, {}> & {
    ComposedComponent: typeof VirtualTableBody;
};
export default _default;
//# sourceMappingURL=VirtualTableBody.d.ts.map