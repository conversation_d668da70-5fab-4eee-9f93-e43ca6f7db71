import React from 'react';
import { ITableStore } from 'amis-core';
import { ClassNamesFn } from 'amis-core';
export interface ItemActionsProps {
    classnames: ClassNamesFn;
    children: React.ReactNode | Array<React.ReactNode>;
    store: ITableStore;
}
declare function ItemActionsWrapper(props: ItemActionsProps): import("react/jsx-runtime").JSX.Element;
declare const _default: typeof ItemActionsWrapper;
export default _default;
//# sourceMappingURL=ItemActionsWrapper.d.ts.map