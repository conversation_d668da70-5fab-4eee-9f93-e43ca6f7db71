/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign } from 'tslib';
import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import React from 'react';
import { getScrollParent, resizeSensor, themeable } from 'amis-core';

function VirtualTableBody(props) {
    var className = props.className, rows = props.rows, store = props.store, classPrefix = props.classPrefix;
    var leadingPlaceholderRef = React.useRef(null);
    var trailingPlaceholderRef = React.useRef(null);
    var tBodyRef = React.useRef(null);
    var _a = React.useState(0), scrollTop = _a[0], setScrollTop = _a[1];
    var itemHeight = React.useRef(44);
    var sizeRef = React.useRef(20);
    var buffer = 10;
    var offset = Math.floor(scrollTop / itemHeight.current);
    var start = Math.max(0, offset - buffer);
    var visibleRows = React.useMemo(function () {
        return rows.slice(start, offset + sizeRef.current + buffer);
    }, [rows, offset, start, buffer]);
    React.useEffect(function () {
        var tbody = tBodyRef.current;
        var table = tbody.parentElement;
        var wrap = table.parentElement;
        var rootDom = wrap.closest(".".concat(classPrefix, "Table"));
        var header = (rootDom === null || rootDom === void 0 ? void 0 : rootDom.querySelector(":scope > .".concat(classPrefix, "Table-fixedTop"))) ||
            table.querySelector(':scope > thead');
        var firstRow = leadingPlaceholderRef.current;
        var isAutoFill = rootDom.classList.contains("".concat(classPrefix, "Table--autoFillHeight"));
        var toDispose = [];
        var check = function () {
            var rect = header.getBoundingClientRect();
            var rect2 = firstRow.getBoundingClientRect();
            var scrollTop = rect.bottom - rect2.top;
            setScrollTop(scrollTop);
            if (scrollTop && store.tableLayout !== 'fixed') {
                store.switchToFixedLayout();
            }
        };
        var timer = null;
        var lazyCheck = function () {
            timer && cancelAnimationFrame(timer);
            timer = requestAnimationFrame(check);
        };
        if (isAutoFill) {
            wrap.addEventListener('scroll', lazyCheck);
            toDispose.push(function () { return wrap.removeEventListener('scroll', lazyCheck); });
        }
        else {
            var scrollContainer_1 = getScrollParent(rootDom);
            scrollContainer_1 =
                scrollContainer_1 === document.body ? document : scrollContainer_1;
            scrollContainer_1.addEventListener('scroll', lazyCheck);
            toDispose.push(function () {
                return scrollContainer_1.removeEventListener('scroll', lazyCheck);
            });
        }
        toDispose.push(resizeSensor(wrap, function () {
            itemHeight.current = tbody
                .querySelector(':scope > tr')
                .getBoundingClientRect().height;
            sizeRef.current = Math.min(Math.ceil(Math.min(isAutoFill ? wrap.clientHeight : window.innerHeight) /
                itemHeight.current), 20);
            check();
        }));
        return function () {
            toDispose.forEach(function (fn) { return fn(); });
            toDispose.length = 0;
        };
    }, []);
    var styles = {
        '--Table-scroll-height': "".concat(itemHeight.current * rows.length, "px"),
        '--Table-scroll-offset': "".concat(start * itemHeight.current, "px"),
        '--Table-frame-height': "".concat(itemHeight.current * visibleRows.length, "px")
    };
    return (jsxs(Fragment, { children: [jsx("tbody", __assign({ style: styles, className: "virtual-table-body-placeholder leading" }, { children: jsx("tr", { children: jsx("td", __assign({ colSpan: store.filteredColumns.length }, { children: jsx("div", { ref: leadingPlaceholderRef }) })) }) })), jsx("tbody", __assign({ className: className, ref: tBodyRef }, { children: visibleRows })), jsx("tbody", __assign({ style: styles, className: "virtual-table-body-placeholder trailing" }, { children: jsx("tr", { children: jsx("td", __assign({ colSpan: store.filteredColumns.length }, { children: jsx("div", { ref: trailingPlaceholderRef }) })) }) }))] }));
}
var VirtualTableBody$1 = themeable(VirtualTableBody);

export { VirtualTableBody$1 as default };
