/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import { observer } from 'mobx-react';
import React, { useRef, useEffect } from 'react';

function ItemActionsWrapper(props) {
    var _a;
    var cx = props.classnames;
    var children = props.children;
    var store = props.store;
    var divRef = useRef(null);
    var updatePosition = React.useCallback(function (id) {
        var _a, _b;
        if (id === void 0) { id = ((_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id) || ''; }
        var frame = (_b = divRef.current.parentElement) === null || _b === void 0 ? void 0 : _b.querySelector('table');
        var dom = frame === null || frame === void 0 ? void 0 : frame.querySelector("tr[data-id=\"".concat(id, "\"]"));
        if (!dom) {
            return;
        }
        var rect = dom.getBoundingClientRect();
        var height = rect.height;
        var top = rect.top -
            frame.getBoundingClientRect().top +
            parseInt(getComputedStyle(frame)['marginTop'], 10);
        divRef.current.style.cssText += "top: ".concat(top, "px;height: ").concat(height, "px; left: ").concat(frame.parentElement.scrollLeft, "px;");
    }, []);
    useEffect(function () {
        var row = store.hoverRow;
        if (!row) {
            return;
        }
        updatePosition(row.id);
    }, [(_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id]);
    useEffect(function () {
        var frame = divRef.current.parentElement;
        if (!frame) {
            return;
        }
        var onScroll = function () {
            var _a;
            updatePosition((_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id);
        };
        frame.addEventListener('scroll', onScroll);
        return function () {
            frame.removeEventListener('scroll', onScroll);
        };
    });
    return (jsx("div", __assign({ className: cx('Table-itemActions-wrap'), ref: divRef }, { children: children })));
}
var ItemActionsWrapper$1 = observer(ItemActionsWrapper);

export { ItemActionsWrapper$1 as default };
