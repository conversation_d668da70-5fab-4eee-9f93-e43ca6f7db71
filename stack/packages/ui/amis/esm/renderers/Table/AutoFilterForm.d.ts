import { IColumn, ITableStore, RendererProps } from 'amis-core';
export interface AutoFilterFormProps extends RendererProps {
    searchFormExpanded: boolean;
    autoGenerateFilter: any;
    activedSearchableColumns: Array<IColumn>;
    searchableColumns: Array<IColumn>;
    columnsNum: number;
    onItemToggleExpanded?: (column: IColumn, value: boolean) => void;
    onToggleExpanded?: () => void;
    query?: any;
    canAccessSuperData?: boolean;
    popOverContainer?: any;
    onSearchableFromReset?: any;
    onSearchableFromSubmit?: any;
    onSearchableFromInit?: any;
}
export declare function AutoFilterForm({ autoGenerateFilter, searchFormExpanded, activedSearchableColumns, searchableColumns, onItemToggleExpanded, onToggleExpanded, classnames: cx, translate: __, render, data, onSearchableFromReset, onSearchableFromSubmit, onSearchableFromInit, popOverContainer, testIdBuilder, canAccessSuperData }: AutoFilterFormProps): any;
declare const _default: ({ store, query, data, ...rest }: Omit<AutoFilterFormProps, "searchFormExpanded" | "activedSearchableColumns" | "searchableColumns" | "onItemToggleExpanded" | "onToggleExpanded"> & {
    store: ITableStore;
    query: any;
}) => import("react/jsx-runtime").JSX.Element;
export default _default;
//# sourceMappingURL=AutoFilterForm.d.ts.map