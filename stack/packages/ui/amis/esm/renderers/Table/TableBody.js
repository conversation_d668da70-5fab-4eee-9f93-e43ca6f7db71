/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React, { createElement } from 'react';
import { filter, createObject, autobind } from 'amis-core';
import TableRow from './TableRow.js';
import { observer } from 'mobx-react';
import flatten from 'lodash/flatten';
import VirtualTableBody from './VirtualTableBody.js';

var TableBody = /** @class */ (function (_super) {
    __extends(TableBody, _super);
    function TableBody() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TableBody.prototype.componentDidMount = function () {
        this.props.store.initTableWidth();
    };
    TableBody.prototype.testIdBuilder = function (rowPath) {
        var _a;
        return (_a = this.props.testIdBuilder) === null || _a === void 0 ? void 0 : _a.getChild("row-".concat(rowPath));
    };
    TableBody.prototype.renderRows = function (rows, columns, rowProps, indexPath) {
        var _this = this;
        if (columns === void 0) { columns = this.props.columns; }
        if (rowProps === void 0) { rowProps = {}; }
        var _a = this.props, rowClassName = _a.rowClassName, rowClassNameExpr = _a.rowClassNameExpr, onAction = _a.onAction, buildItemProps = _a.buildItemProps, checkOnItemClick = _a.checkOnItemClick, cx = _a.classnames, render = _a.render, renderCell = _a.renderCell, onCheck = _a.onCheck, onQuickChange = _a.onQuickChange, footable = _a.footable, ignoreFootableContent = _a.ignoreFootableContent, footableColumns = _a.footableColumns, itemAction = _a.itemAction, onRowClick = _a.onRowClick, onRowDbClick = _a.onRowDbClick, onRowMouseEnter = _a.onRowMouseEnter, onRowMouseLeave = _a.onRowMouseLeave, store = _a.store;
        return rows.map(function (item, rowIndex) {
            var itemProps = buildItemProps ? buildItemProps(item, rowIndex) : null;
            var rowPath = "".concat(indexPath ? indexPath + '/' : '').concat(rowIndex);
            var doms = [
                createElement(TableRow, __assign({}, itemProps, { testIdBuilder: _this.testIdBuilder, store: store, itemAction: itemAction, classnames: cx, checkOnItemClick: checkOnItemClick, key: item.id, itemIndex: rowIndex, rowPath: rowPath, item: item, itemClassName: cx(rowClassNameExpr
                        ? filter(rowClassNameExpr, item.locals)
                        : rowClassName, {
                        'is-last': item.depth > 1 &&
                            rowIndex === rows.length - 1 &&
                            !item.children.length
                    }), columns: columns, renderCell: renderCell, render: render, onAction: onAction, onCheck: onCheck, 
                    // todo 先注释 quickEditEnabled={item.depth === 1}
                    onQuickChange: onQuickChange, onRowClick: onRowClick, onRowDbClick: onRowDbClick, onRowMouseEnter: onRowMouseEnter, onRowMouseLeave: onRowMouseLeave }, rowProps))
            ];
            if (footable && footableColumns.length) {
                if (item.depth === 1) {
                    doms.push(createElement(TableRow, __assign({}, itemProps, { store: store, itemAction: itemAction, classnames: cx, checkOnItemClick: checkOnItemClick, key: "foot-".concat(item.id), itemIndex: rowIndex, rowPath: rowPath, item: item, itemClassName: cx(rowClassNameExpr
                            ? filter(rowClassNameExpr, item.locals)
                            : rowClassName), columns: footableColumns, renderCell: renderCell, render: render, onAction: onAction, onCheck: onCheck, onRowClick: onRowClick, onRowDbClick: onRowDbClick, onRowMouseEnter: onRowMouseEnter, onRowMouseLeave: onRowMouseLeave, footableMode: true, footableColSpan: columns.length, onQuickChange: onQuickChange, ignoreFootableContent: ignoreFootableContent }, rowProps, { testIdBuilder: _this.testIdBuilder })));
                }
            }
            else if (item.children.length && item.expanded) {
                // 嵌套表格
                doms.push.apply(doms, _this.renderRows(item.children, columns, __assign(__assign({}, rowProps), { parent: item }), rowPath));
            }
            return doms;
        });
    };
    TableBody.prototype.renderSummaryRow = function (position, items, rowIndex) {
        var _a, _b;
        var _c = this.props, columns = _c.columns, render = _c.render, data = _c.data, cx = _c.classnames, rows = _c.rows, prefixRowClassName = _c.prefixRowClassName, affixRowClassName = _c.affixRowClassName, store = _c.store;
        if (!(Array.isArray(items) && items.length)) {
            return null;
        }
        var offset = 0;
        // 将列的隐藏对应的把总结行也隐藏起来
        var result = items
            .map(function (item, index) {
            var colIdxs = [offset + index];
            if (item.colSpan > 1) {
                for (var i = 1; i < item.colSpan; i++) {
                    colIdxs.push(offset + index + i);
                }
                offset += item.colSpan - 1;
            }
            var matchedColumns = colIdxs
                .map(function (idx) { return columns.find(function (col) { return col.rawIndex === idx; }); })
                .filter(function (item) { return item; });
            return __assign(__assign({}, item), { colSpan: matchedColumns.length, firstColumn: matchedColumns[0], lastColumn: matchedColumns[matchedColumns.length - 1] });
        })
            .filter(function (item) { return item.colSpan; });
        //  如果是勾选栏，或者是展开栏，或者是拖拽栏
        // 临时补一个空格，这样不会跟功能栏冲突
        if (result[0] &&
            typeof ((_a = columns[0]) === null || _a === void 0 ? void 0 : _a.type) === 'string' &&
            ((_b = columns[0]) === null || _b === void 0 ? void 0 : _b.type.substring(0, 2)) === '__') {
            result.unshift({
                firstColumn: columns[0],
                lastColumn: columns[0],
                colSpan: 1,
                text: ' ',
                type: 'text'
            });
            // result[0].firstColumn = columns[0];
            // result[0].colSpan = (result[0].colSpan || 1) + 1;
        }
        // 缺少的单元格补齐
        var resultLen = result.reduce(function (p, c) { return p + (c.colSpan || 1); }, 0);
        var appendLen = columns.length - resultLen;
        // 多了则干掉一些
        while (appendLen < 0) {
            var item = result.pop();
            if (!item) {
                break;
            }
            appendLen += item.colSpan || 1;
        }
        // 少了则补个空的
        // 只补空的时，当存在fixed:right时会导致样式有问题 会把其他列的盖住
        if (appendLen) {
            var item = {
                type: 'html',
                html: '&nbsp;'
            };
            for (var i = resultLen; i < store.filteredColumns.length; i++) {
                var column = store.filteredColumns[i];
                result.push(__assign(__assign({}, item), { colSpan: 1, firstColumn: column, lastColumn: column }));
            }
        }
        var ctx = createObject(data, {
            items: rows.map(function (row) { return row.locals; })
        });
        return (jsx("tr", __assign({ className: cx('Table-table-tr', 'is-summary', position === 'prefix' ? prefixRowClassName : '', position === 'affix' ? affixRowClassName : '') }, { children: result.map(function (item, index) {
                var Com = item.isHead ? 'th' : 'td';
                var firstColumn = item.firstColumn;
                var lastColumn = item.lastColumn;
                var style = __assign({}, item.style);
                if (item.align) {
                    style.textAlign = item.align;
                }
                if (item.vAlign) {
                    style.verticalAlign = item.vAlign;
                }
                var _a = store.getStickyStyles(lastColumn.fixed === 'right' ? lastColumn : firstColumn, store.filteredColumns, item.colSpan), stickyStyle = _a[0], stickyClassName = _a[1];
                Object.assign(style, stickyStyle);
                return (jsx(Com, __assign({ colSpan: item.colSpan == 1 ? undefined : item.colSpan, style: style, className: (item.cellClassName || '') + ' ' + stickyClassName }, { children: render("summary-row/".concat(index), item, {
                        data: ctx
                    }) }), index));
            }) }), "summary-".concat(position, "-").concat(rowIndex || 0)));
    };
    TableBody.prototype.renderSummary = function (position, items) {
        var _this = this;
        return Array.isArray(items)
            ? items.some(function (i) { return Array.isArray(i); })
                ? items.map(function (i, rowIndex) {
                    return _this.renderSummaryRow(position, Array.isArray(i) ? i : [i], rowIndex);
                })
                : this.renderSummaryRow(position, items)
            : null;
    };
    TableBody.prototype.render = function () {
        var _a = this.props, cx = _a.classnames, className = _a.className, render = _a.render, store = _a.store, rows = _a.rows, columns = _a.columns, rowsProps = _a.rowsProps, prefixRow = _a.prefixRow, affixRow = _a.affixRow, __ = _a.translate;
        var doms = flatten([]
            .concat(this.renderSummary('prefix', prefixRow))
            .concat(this.renderRows(rows, columns, rowsProps))
            .concat(this.renderSummary('affix', affixRow))).filter(Boolean);
        return rows.length > store.lazyRenderAfter ? (jsx(VirtualTableBody, { rows: doms, store: this.props.store })) : (jsx("tbody", __assign({ className: className }, { children: doms })));
    };
    __decorate([
        autobind
    ], TableBody.prototype, "testIdBuilder", null);
    TableBody = __decorate([
        observer
    ], TableBody);
    return TableBody;
}(React.Component));

export { TableBody };
