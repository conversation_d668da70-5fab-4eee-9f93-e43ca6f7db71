/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import { buildStyle, setThemeClassName, CustomStyle, Renderer } from 'amis-core';

var Flex = /** @class */ (function (_super) {
    __extends(Flex, _super);
    function Flex(props) {
        return _super.call(this, props) || this;
    }
    Flex.prototype.renderItems = function () {
        var _a = this.props, items = _a.items, render = _a.render, disabled = _a.disabled, cx = _a.classnames;
        var children = Array.isArray(items) ? items : items ? [items] : [];
        return children.map(function (item, key) {
            var _a;
            return render("items/".concat(key), item, {
                key: "items/".concat(key),
                disabled: (_a = item === null || item === void 0 ? void 0 : item.disabled) !== null && _a !== void 0 ? _a : disabled
            });
        });
    };
    Flex.prototype.render = function () {
        var _a = this.props, items = _a.items, direction = _a.direction, justify = _a.justify, alignItems = _a.alignItems, alignContent = _a.alignContent, style = _a.style, className = _a.className, render = _a.render, disabled = _a.disabled, data = _a.data, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env, themeCss = _a.themeCss, cx = _a.classnames;
        var styleVar = buildStyle(style, data);
        var flexStyle = __assign({ display: 'flex', flexDirection: direction, justifyContent: justify, alignItems: alignItems, alignContent: alignContent }, styleVar);
        if (flexStyle.flexBasis !== undefined && flexStyle.flex) {
            // 合并flex和flexBasis，并优先使用flexBasis
            var flexValArr = flexStyle.flex.split(' ');
            flexStyle.flex = "".concat(flexValArr[0], " ").concat(flexValArr[1] || flexValArr[0], " ").concat(flexStyle.flexBasis);
        }
        return (jsxs("div", __assign({ style: flexStyle, className: cx('Flex', className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), "data-id": id, "data-role": "container" }, { children: [this.renderItems(), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            }
                        ]
                    }, env: env }))] })));
    };
    Flex.defaultProps = {
        direction: 'row',
        justify: 'center',
        alignItems: 'stretch',
        alignContent: 'center'
    };
    return Flex;
}(React.Component));
var FlexItem = /** @class */ (function (_super) {
    __extends(FlexItem, _super);
    function FlexItem() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    FlexItem.prototype.renderBody = function () {
        var _a = this.props, children = _a.children, body = _a.body, render = _a.render, disabled = _a.disabled;
        return children
            ? typeof children === 'function'
                ? children(this.props)
                : children
            : body
                ? render('body', body, { disabled: disabled })
                : null;
    };
    FlexItem.prototype.render = function () {
        var _a = this.props, className = _a.className, size = _a.size, cx = _a.classnames, style = _a.style;
        return (jsx("div", __assign({ className: className, style: style }, { children: this.renderBody() })));
    };
    FlexItem.propsList = ['body', 'className', 'children'];
    return FlexItem;
}(React.Component));
var FlexRenderer = /** @class */ (function (_super) {
    __extends(FlexRenderer, _super);
    function FlexRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    FlexRenderer = __decorate([
        Renderer({
            type: 'flex'
        })
    ], FlexRenderer);
    return FlexRenderer;
}(Flex));
var FlexItemRenderer = /** @class */ (function (_super) {
    __extends(FlexItemRenderer, _super);
    function FlexItemRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    FlexItemRenderer = __decorate([
        Renderer({
            type: 'flex-item'
        })
    ], FlexItemRenderer);
    return FlexItemRenderer;
}(FlexItem));

export { FlexItem, FlexItemRenderer, FlexRenderer, Flex as default };
