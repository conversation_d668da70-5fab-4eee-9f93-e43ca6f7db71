/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React, { createRef } from 'react';
import { filter, isPureVariable, resolveVariableAndFilter, getPropValue, setThemeClassName, CustomStyle, autobind, Renderer } from 'amis-core';
import ThemedImageThumb, { imagePlaceholder } from './Image.js';
import Transition, { ENTERING, ENTERED, EXITING, EXITED } from 'react-transition-group/Transition';

var ImagesField = /** @class */ (function (_super) {
    __extends(ImagesField, _super);
    function ImagesField(props) {
        var _this = _super.call(this, props) || this;
        _this.containerRef = createRef();
        _this.resizeObserver = null;
        _this.isSwiping = false;
        _this.startX = 0;
        _this.list = [];
        _this.gap = 5;
        _this.evenReg = /^even-[1-9]\d*-[1-9]\d*$/;
        _this.wrapperRef = React.createRef();
        /**
         * 计算照片子元素高度
         * */
        _this.generateHeight = function (sortType, index) {
            var height = Number(_this.props.height) || _this.state.defaultHeight;
            if (sortType === 'sm-ss-sss-m' ||
                sortType === 'sss-ss-ms-m' ||
                sortType === 'sms-ss-sms-m') {
                if (index === 0) {
                    return height;
                }
                else {
                    return (height - _this.gap) * 0.5;
                }
            }
            else if (sortType === 'sm-ss-sss-ss' ||
                sortType === 'ms-ss-sss-ss' ||
                sortType === 'sss-ss-sm-ss' ||
                sortType === 'mss-ss-ssm-ss' ||
                sortType === 'sss-ss-mm-ss') {
                return (height - _this.gap) * 0.5;
            }
            else if (_this.evenReg.test(sortType || '')) {
                var rows = Number(sortType === null || sortType === void 0 ? void 0 : sortType.split('-')[1]);
                var columns = Number(sortType === null || sortType === void 0 ? void 0 : sortType.split('-')[2]);
                if (index < rows * columns) {
                    return (height - _this.gap * (rows - 1)) / rows;
                }
            }
            return 0;
        };
        /**
         * 计算照片子元素宽度
         * */
        _this.generateWidth = function (sortType, index) {
            var width = Number(_this.props.width) || _this.state.defaultWidth;
            if (sortType === 'sm-ss-sss-m' || sortType === 'sss-ss-ms-m') {
                if (index === 0) {
                    return (width - 2 * _this.gap) / 3;
                }
                else if (index === 1) {
                    return ((width - 2 * _this.gap) / 3) * 2 + _this.gap;
                }
                else {
                    return (width - 2 * _this.gap) / 3;
                }
            }
            else if (sortType === 'sms-ss-sms-m') {
                if ([0, 2, 4].includes(index)) {
                    return (width - 2 * _this.gap) / 4;
                }
                else if (index === 1 || index === 3) {
                    return (width - 2 * _this.gap) / 2 + _this.gap;
                }
            }
            else if (sortType === 'sm-ss-sss-ss') {
                if ([0, 2, 3, 4].includes(index)) {
                    return (width - 2 * _this.gap) / 3;
                }
                else {
                    return ((width - 2 * _this.gap) / 3) * 2 + _this.gap;
                }
            }
            else if (sortType === 'ms-ss-sss-ss') {
                if ([1, 2, 3, 4].includes(index)) {
                    return (width - 2 * _this.gap) / 3;
                }
                else {
                    return ((width - 2 * _this.gap) / 3) * 2 + _this.gap;
                }
            }
            else if (sortType === 'sss-ss-sm-ss') {
                if ([0, 1, 2, 4].includes(index)) {
                    return (width - 2 * _this.gap) / 3;
                }
                else {
                    return ((width - 2 * _this.gap) / 3) * 2 + _this.gap;
                }
            }
            else if (sortType === 'mss-ss-ssm-ss') {
                if ([1, 2, 4, 5].includes(index)) {
                    return (width - 2 * _this.gap) / 4;
                }
                else {
                    return (width - 2 * _this.gap) / 2 + _this.gap;
                }
            }
            else if (sortType === 'sss-ss-mm-ss') {
                if ([0, 1, 2].includes(index)) {
                    return (width - _this.gap * 2) / 3;
                }
                else {
                    return (width - _this.gap) / 2;
                }
            }
            else if (_this.evenReg.test(sortType || '')) {
                var rows = Number(sortType === null || sortType === void 0 ? void 0 : sortType.split('-')[1]);
                var columns = Number(sortType === null || sortType === void 0 ? void 0 : sortType.split('-')[2]);
                if (index < rows * columns) {
                    return (width - _this.gap * (columns - 1)) / columns;
                }
            }
            return 0;
        };
        /**
         * 计算照片子元素平移位置
         * */
        _this.generateTranslate = function (sortType, index) {
            var width = Number(_this.props.width) || _this.state.defaultWidth;
            var height = Number(_this.props.height) || _this.state.defaultHeight;
            var styleObj = {
                position: 'absolute',
                boxSizing: 'content-box',
                height: _this.generateHeight(sortType, index) + 'px',
                width: _this.generateWidth(sortType, index) + 'px'
            };
            if (sortType === 'sm-ss-sss-m') {
                if (index === 1) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 2) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat((height - _this.gap) * 0.5 + _this.gap, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat((height - _this.gap) * 0.5 + _this.gap, "px)");
                }
            }
            else if (sortType === 'sss-ss-ms-m') {
                if (index === 1) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat((height - _this.gap) * 0.5 + _this.gap, "px)");
                }
                else if (index === 2) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat(0, "px)");
                }
            }
            else if (sortType === 'sms-ss-sms-m') {
                if (index === 1) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 4 + _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 2) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 4) * 3 + 3 * _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 4 + _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 4) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 4) * 3 + 3 * _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
            }
            else if (sortType === 'sm-ss-sss-ss') {
                if (index === 1) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 2) {
                    styleObj.transform = "translate(".concat(0, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 4) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
            }
            else if (sortType === 'ms-ss-sss-ss') {
                if (index === 1) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 2) {
                    styleObj.transform = "translate(".concat(0, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 4) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
            }
            else if (sortType === 'sss-ss-sm-ss') {
                if (index === 1) {
                    styleObj.transform = "translate(".concat((width - 2 * _this.gap) / 3 + _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 2) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat(0, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat(0, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 4) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3) * 2 + 2 * _this.gap, "px,").concat((height - _this.gap) / 2 + _this.gap, "px");
                }
            }
            else if (sortType === 'mss-ss-ssm-ss') {
                if (index === 1 || index === 2) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 4 + _this.gap) * (index + 1), "px,").concat(0, "px)");
                }
                else if (index === 3) {
                    styleObj.transform = "translate(".concat(0, "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
                else if (index === 4 || index === 5) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 4 + _this.gap) * (index - 2), "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
            }
            else if (sortType === 'sss-ss-mm-ss') {
                if (index === 1 || index === 2) {
                    styleObj.transform = "translate(".concat(((width - 2 * _this.gap) / 3 + _this.gap) * index, "px,").concat(0, "px)");
                }
                else if (index === 3 || index === 4) {
                    styleObj.transform = "translate(".concat(((width - _this.gap) / 2 + _this.gap) * (index - 3), "px,").concat((height - _this.gap) / 2 + _this.gap, "px)");
                }
            }
            else if (_this.evenReg.test(sortType || '')) {
                styleObj.transform = _this.generateEvenTranslate(sortType, index);
            }
            return styleObj;
        };
        _this.state = {
            defaultWidth: 200,
            defaultHeight: 112.5,
            currentIndex: 0,
            nextAnimation: ''
        };
        return _this;
    }
    // 根据当前索引和方向获取下一帧的索引
    ImagesField.prototype.getFrameId = function (pos) {
        var currentIndex = this.state.currentIndex;
        var total = this.list.length;
        switch (pos) {
            case 'prev':
                return (currentIndex - 1 + total) % total;
            case 'next':
                return (currentIndex + 1) % total;
            default:
                return currentIndex;
        }
    };
    // 根据方向和动画类型切换到下一帧
    ImagesField.prototype.transitFramesTowards = function (direction, nextAnimation) {
        return __awaiter(this, void 0, void 0, function () {
            var currentIndex, prevIndex;
            return __generator(this, function (_a) {
                currentIndex = this.state.currentIndex;
                prevIndex = currentIndex;
                switch (direction) {
                    case 'left':
                        currentIndex = this.getFrameId('next');
                        nextAnimation = 'slideLeft';
                        break;
                    case 'right':
                        currentIndex = this.getFrameId('prev');
                        nextAnimation = 'slideRight';
                        break;
                    default:
                        return [2 /*return*/];
                }
                this.setState({ currentIndex: currentIndex, nextAnimation: nextAnimation });
                return [2 /*return*/];
            });
        });
    };
    ImagesField.prototype.handleSwipe = function (deltaX) {
        var threshold = 50;
        if (Math.abs(deltaX) > threshold) {
            if (deltaX > 0) {
                // 向右滑
                this.transitFramesTowards('right', 'slideRight');
            }
            else {
                // 向左滑
                this.transitFramesTowards('left', 'slideLeft');
            }
        }
    };
    ImagesField.prototype.handleTouchStart = function (e) {
        if (this.props.displayMode !== 'full')
            return;
        this.isSwiping = true;
        this.startX = e.touches[0].clientX;
    };
    ImagesField.prototype.handleTouchEnd = function (e) {
        if (!this.isSwiping)
            return;
        var deltaX = e.changedTouches[0].clientX - this.startX;
        this.handleSwipe(deltaX);
        this.isSwiping = false;
    };
    ImagesField.prototype.handleMouseDown = function (e) {
        if (this.props.displayMode !== 'full')
            return;
        // 阻止图片默认的拖拽行为
        e.preventDefault();
        this.isSwiping = true;
        this.startX = e.clientX;
        document.addEventListener('mouseup', this.handleMouseUp);
    };
    ImagesField.prototype.handleMouseUp = function (e) {
        if (!this.isSwiping)
            return;
        var deltaX = e.clientX - this.startX;
        this.handleSwipe(deltaX);
        this.isSwiping = false;
        document.removeEventListener('mouseup', this.handleMouseUp);
    };
    ImagesField.prototype.handleEnlarge = function (info) {
        var _a = this.props, onImageEnlarge = _a.onImageEnlarge, src = _a.src, originalSrc = _a.originalSrc;
        onImageEnlarge &&
            onImageEnlarge(__assign(__assign({}, info), { originalSrc: info.originalSrc || info.src, list: this.list.map(function (item) { return ({
                    src: src
                        ? filter(src, item, '| raw')
                        : (item && item.image) || item,
                    originalSrc: originalSrc
                        ? filter(originalSrc, item, '| raw')
                        : (item === null || item === void 0 ? void 0 : item.src) || filter(src, item, '| raw') || (item === null || item === void 0 ? void 0 : item.image) || item,
                    title: item && (item.enlargeTitle || item.title),
                    caption: item && (item.enlargeCaption || item.description || item.caption)
                }); }) }), this.props);
    };
    /**
     * 计算网格布局
     * */
    ImagesField.prototype.generateEvenTranslate = function (sortType, index) {
        var result = "";
        var width = Number(this.props.width) || this.state.defaultWidth;
        var height = Number(this.props.height) || this.state.defaultHeight;
        var rows = Number(sortType === null || sortType === void 0 ? void 0 : sortType.split('-')[1]);
        var columns = Number(sortType === null || sortType === void 0 ? void 0 : sortType.split('-')[2]);
        if (index < rows * columns) {
            // 计算每个网格单元的宽度和高度
            var cellWidth = (width - (columns - 1) * this.gap) / columns;
            var cellHeight = (height - (rows - 1) * this.gap) / rows;
            // 计算当前索引的行号和列号
            var row = Math.floor(index / columns);
            var col = index % columns;
            // 计算图片的 x, y 坐标
            var x = col * (cellWidth + this.gap);
            var y = row * (cellHeight + this.gap);
            result = "translate(".concat(x, "px,").concat(y, "px)");
        }
        return result;
    };
    ImagesField.prototype.componentDidMount = function () {
        if (!this.props.width ||
            !this.props.height ||
            (String(this.props.width).includes('%') &&
                String(this.props.height).includes('%'))) {
            //监听父级元素大小变化
            this.observeParentSize();
        }
    };
    ImagesField.prototype.componentWillUnmount = function () {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
    };
    ImagesField.prototype.observeParentSize = function () {
        var _this = this;
        var _a;
        if ((_a = this.containerRef.current) === null || _a === void 0 ? void 0 : _a.parentElement) {
            this.resizeObserver = new ResizeObserver(function (entries) {
                for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {
                    var entry = entries_1[_i];
                    _this.setState({
                        defaultWidth: (entry.contentRect.width *
                            parseFloat(_this.props.width || '100')) /
                            100,
                        defaultHeight: (entry.contentRect.height *
                            parseFloat(_this.props.height || '100')) /
                            100
                    });
                    // 这里可以触发组件状态更新
                    _this.forceUpdate();
                }
            });
            this.resizeObserver.observe(this.containerRef.current.parentElement);
        }
    };
    ImagesField.prototype.render = function () {
        var _this = this;
        var _a = this.props, className = _a.className, style = _a.style, defaultImage = _a.defaultImage, thumbMode = _a.thumbMode, thumbRatio = _a.thumbRatio, data = _a.data, name = _a.name, placeholder = _a.placeholder, cx = _a.classnames, source = _a.source, delimiter = _a.delimiter, enlargeAble = _a.enlargeAble, enlargeWithGallary = _a.enlargeWithGallary, src = _a.src, originalSrc = _a.originalSrc, listClassName = _a.listClassName, options = _a.options, showToolbar = _a.showToolbar, toolbarActions = _a.toolbarActions, imageGallaryClassName = _a.imageGallaryClassName, galleryControlClassName = _a.galleryControlClassName, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env, themeCss = _a.themeCss, sortType = _a.sortType, imagesControlClassName = _a.imagesControlClassName, displayMode = _a.displayMode, fullThumbMode = _a.fullThumbMode, maskColor = _a.maskColor;
        var currentIndex = this.state.currentIndex;
        var value;
        var list;
        if (typeof source === 'string' && isPureVariable(source)) {
            list = resolveVariableAndFilter(source, data, '| raw') || undefined;
        }
        else if (Array.isArray((value = getPropValue(this.props))) ||
            typeof value === 'string') {
            list = value;
        }
        else if (Array.isArray(options)) {
            list = options;
        }
        if (typeof list === 'string') {
            list = list.split(delimiter);
        }
        else if (list && !Array.isArray(list)) {
            list = [list];
        }
        this.list = list;
        /**
         * 截取图集图片数量，多余图片不显示
         * */
        if (this.props.sortType === 'sm-ss-sss-m') {
            this.list = list.slice(0, 4);
        }
        else if (this.props.sortType === 'sss-ss-ms-m') {
            this.list = list.slice(0, 5);
        }
        else if (this.props.sortType === 'sms-ss-sms-m' ||
            this.props.sortType === 'sm-ss-sss-ss' ||
            this.props.sortType === 'ms-ss-sss-ss' ||
            this.props.sortType === 'sss-ss-sm-ss') {
            this.list = list.slice(0, 6);
        }
        else if (this.props.sortType === 'mss-ss-ssm-ss') {
            this.list = list.slice(0, 8);
        }
        else if (this.props.sortType === 'sss-ss-mm-ss') {
            this.list = list.slice(0, 6);
        }
        if (this.props.sortType) {
            return (jsx("div", __assign({ ref: this.containerRef, className: sortType, style: {
                    width: (this.props.width || this.state.defaultWidth) + 'px',
                    height: (this.props.height || this.state.defaultHeight) + 'px'
                } }, { children: this.list.map(function (item, index) { return (jsx(ThemedImageThumb, { maskColor: maskColor, fontStyle: _this.props.fontStyle, style: _this.generateTranslate(sortType, index), width: _this.generateWidth(sortType, index), height: _this.generateHeight(sortType, index), hoverMode: _this.props.hoverMode || 'text-style-4', index: index, className: cx('Images-item'), src: (src ? filter(src, item, '| raw') : item && item.image) || item, originalSrc: (originalSrc
                        ? filter(originalSrc, item, '| raw')
                        : item && item.src) || item, title: item && item.title, sortType: _this.props.sortType, caption: item && (item.description || item.caption), thumbMode: thumbMode, thumbRatio: thumbRatio, enlargeAble: enlargeAble, enlargeWithGallary: enlargeWithGallary, onEnlarge: _this.handleEnlarge, showToolbar: showToolbar, imageGallaryClassName: "".concat(imageGallaryClassName, " ").concat(setThemeClassName(__assign(__assign({}, _this.props), { name: 'imageGallaryClassName', id: id, themeCss: themeCss })), " ").concat(setThemeClassName(__assign(__assign({}, _this.props), { name: 'galleryControlClassName', id: id, themeCss: themeCss }))), toolbarActions: toolbarActions }, index)); }) })));
        }
        else {
            return (jsxs("div", __assign({ ref: this.wrapperRef, className: cx('ImagesField', className, displayMode === 'full' ? 'ImagesField--full' : '', setThemeClassName(__assign(__assign({}, this.props), { name: 'imagesControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), style: __assign(__assign({}, style), (displayMode === 'full'
                    ? {
                        height: 'auto'
                    }
                    : {})), onTouchStart: this.handleTouchStart, onTouchEnd: this.handleTouchEnd, onMouseDown: this.handleMouseDown }, { children: [Array.isArray(list) ? (jsx("div", __assign({ className: cx('Images', listClassName) }, { children: displayMode === 'full' ? (jsx("div", __assign({ className: cx('Images-container') }, { children: list.map(function (item, index) { return (jsx(Transition, __assign({ in: index === currentIndex, timeout: 300, mountOnEnter: true, unmountOnExit: true }, { children: function (status) {
                                    var _a, _b;
                                    var _c;
                                    if (status === ENTERING) {
                                        (_c = _this.wrapperRef.current) === null || _c === void 0 ? void 0 : _c.childNodes.forEach(function (item) { return item.offsetHeight; });
                                    }
                                    var animationStyles = (_a = {},
                                        _a[ENTERING] = {
                                            opacity: 1,
                                            transform: 'translateX(0)'
                                        },
                                        _a[ENTERED] = {
                                            opacity: 1,
                                            transform: 'translateX(0)'
                                        },
                                        _a[EXITING] = {
                                            opacity: 0,
                                            transform: _this.state.nextAnimation === 'slideRight'
                                                ? 'translateX(100%)'
                                                : 'translateX(-100%)'
                                        },
                                        _a[EXITED] = {
                                            opacity: 0,
                                            transform: _this.state.nextAnimation === 'slideRight'
                                                ? 'translateX(-100%)'
                                                : 'translateX(100%)'
                                        },
                                        _a);
                                    return (jsx("div", __assign({ className: cx('Images-item'), style: __assign({ position: 'absolute', width: '100%', height: '100%', transition: 'all 300ms ease-in-out' }, animationStyles[status]) }, { children: jsxs("div", __assign({ className: cx('Images-itemInner') }, { children: [jsx("img", { className: cx('Image-image', (_b = {},
                                                        _b["Image-image--".concat(fullThumbMode)] = displayMode === 'full',
                                                        _b)), src: (src
                                                        ? filter(src, item, '| raw')
                                                        : item && item.image) || item, alt: item && item.title, draggable: false, onDragStart: function (e) { return e.preventDefault(); } }), jsxs("div", __assign({ className: cx('Images-itemIndex') }, { children: [index + 1, "/", list.length] }))] })) })));
                                } }), index)); }) }))) : (list.map(function (item, index) { return (jsx(ThemedImageThumb, { hoverMode: _this.props.hoverMode || 'text-style-4', fontStyle: _this.props.fontStyle, index: index, className: cx('Images-item'), src: (src ? filter(src, item, '| raw') : item && item.image) ||
                                item, originalSrc: (originalSrc
                                ? filter(originalSrc, item, '| raw')
                                : item && item.src) || item, title: item && item.title, caption: item && (item.description || item.caption), thumbMode: thumbMode, thumbRatio: thumbRatio, enlargeAble: enlargeAble, enlargeWithGallary: enlargeWithGallary, onEnlarge: _this.handleEnlarge, showToolbar: showToolbar, imageGallaryClassName: "".concat(imageGallaryClassName, " ").concat(setThemeClassName(__assign(__assign({}, _this.props), { name: 'imageGallaryClassName', id: id, themeCss: themeCss })), " ").concat(setThemeClassName(__assign(__assign({}, _this.props), { name: 'galleryControlClassName', id: id, themeCss: themeCss }))), toolbarActions: toolbarActions }, index)); })) }))) : defaultImage ? (jsx("div", __assign({ className: cx('Images', listClassName) }, { children: jsx(ThemedImageThumb, { hoverMode: this.props.hoverMode || 'text-style-4', className: cx('Images-item'), src: defaultImage, thumbMode: thumbMode, thumbRatio: thumbRatio }) }))) : (placeholder), jsx(CustomStyle, __assign({}, this.props, { config: {
                            wrapperCustomStyle: wrapperCustomStyle,
                            id: id,
                            themeCss: themeCss,
                            classNames: [
                                {
                                    key: 'imagesControlClassName'
                                },
                                {
                                    key: 'galleryControlClassName'
                                }
                            ]
                        }, env: env }))] })));
        }
    };
    ImagesField.defaultProps = {
        className: '',
        delimiter: ',',
        defaultImage: imagePlaceholder,
        placehoder: '-',
        thumbMode: 'cover',
        thumbRatio: '1:1',
        displayMode: 'thumb',
        fullThumbMode: 'cover'
    };
    __decorate([
        autobind
    ], ImagesField.prototype, "getFrameId", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "transitFramesTowards", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "handleSwipe", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "handleTouchStart", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "handleTouchEnd", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "handleMouseDown", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "handleMouseUp", null);
    __decorate([
        autobind
    ], ImagesField.prototype, "handleEnlarge", null);
    return ImagesField;
}(React.Component));
var ImagesFieldRenderer = /** @class */ (function (_super) {
    __extends(ImagesFieldRenderer, _super);
    function ImagesFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ImagesFieldRenderer = __decorate([
        Renderer({
            type: 'images'
        })
    ], ImagesFieldRenderer);
    return ImagesFieldRenderer;
}(ImagesField));

export { ImagesField, ImagesFieldRenderer };
