/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { FormItem } from 'amis-core';

var ButtonToolbar = /** @class */ (function (_super) {
    __extends(ButtonToolbar, _super);
    function ButtonToolbar() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * 这个方法editor里要用作hack，所以不能删掉这个方法
     * @returns
     */
    ButtonToolbar.prototype.renderButtons = function () {
        var _a = this.props, render = _a.render, ns = _a.classPrefix, buttons = _a.buttons;
        return Array.isArray(buttons)
            ? buttons.map(function (button, key) {
                return render("button/".concat(key), button, {
                    key: key
                });
            })
            : null;
    };
    ButtonToolbar.prototype.render = function () {
        var _a = this.props, buttons = _a.buttons, className = _a.className, cx = _a.classnames, render = _a.render, style = _a.style;
        return (jsx("div", __assign({ className: cx('ButtonToolbar', className) }, { children: this.renderButtons() })));
    };
    ButtonToolbar.propsList = ['buttons', 'className'];
    return ButtonToolbar;
}(React.Component));
var ButtonToolbarRenderer = /** @class */ (function (_super) {
    __extends(ButtonToolbarRenderer, _super);
    function ButtonToolbarRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ButtonToolbarRenderer = __decorate([
        FormItem({
            type: 'button-toolbar',
            strictMode: false
        })
    ], ButtonToolbarRenderer);
    return ButtonToolbarRenderer;
}(ButtonToolbar));

export { ButtonToolbarRenderer, ButtonToolbar as default };
