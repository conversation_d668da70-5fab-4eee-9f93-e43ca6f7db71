import React from 'react';
import { OptionsControlProps, Option, TestIdBuilder } from 'amis-core';
import { ActionObject } from 'amis-core';
import { FormOptionsSchema } from '../../Schema';
/**
 * Radio 单选框。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/radios
 */
export interface RadiosControlSchema extends FormOptionsSchema {
    /**
     * 指定为 Radios 渲染器。
     * https://aisuda.bce.baidu.com/amis/zh-CN/components/form/radios
     */
    type: 'radios';
    /**
     * 每行显示多少个
     */
    columnsCount?: number;
}
export interface RadiosProps extends OptionsControlProps {
    placeholder?: any;
    columnsCount?: number;
    labelField?: string;
    /**
     * @deprecated 和checkbox的labelClassName有冲突，请用optionClassName代替
     */
    labelClassName?: string;
    /** 选项CSS类名 */
    optionClassName?: string;
    testIdBuilder?: TestIdBuilder;
}
export default class RadiosControl extends React.Component<RadiosProps, any> {
    static defaultProps: Partial<RadiosProps>;
    doAction(action: ActionObject, data: object, throwErrors: boolean): void;
    handleChange(option: Option): Promise<void>;
    reload(subpath?: string, query?: any): void;
    renderLabel(option: Option, { labelField }: any): import("react/jsx-runtime").JSX.Element;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class RadiosControlRenderer extends RadiosControl {
    static defaultProps: {
        multiple: boolean;
        inline: boolean;
    };
}
//# sourceMappingURL=Radios.d.ts.map