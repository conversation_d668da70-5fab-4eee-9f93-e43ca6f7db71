import React from 'react';
import { FormControlProps } from 'amis-core';
import { IComboStore } from 'amis-core';
import { BaseComboControlSchema } from './Combo';
/**
 * InputArray 数组输入框。 combo 的别名。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/array
 */
export interface ArrayControlSchema extends BaseComboControlSchema {
    /**
     * 指定为数组输入框类型
     */
    type: 'input-array';
}
export interface InputArrayProps extends FormControlProps, Omit<ArrayControlSchema, 'type' | 'className' | 'descriptionClassName' | 'inputClassName'> {
    store: IComboStore;
}
export default class InputArrayControl extends React.Component<InputArrayProps> {
    comboInstance: any;
    constructor(props: InputArrayProps);
    comboRef(ref: any): void;
    validate(args: Array<any>): any;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class ArrayControlRenderer extends InputArrayControl {
}
//# sourceMappingURL=InputArray.d.ts.map