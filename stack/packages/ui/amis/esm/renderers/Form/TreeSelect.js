/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __awaiter, __generator, __decorate } from 'tslib';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import React from 'react';
import { isEffectiveApi, findTree, hasAbility, resolveEventData, getVariable, normalizeOptions, findTreeIndex, createObject, getTreeAncestors, setThemeClassName, Overlay, PopOver, CustomStyle, autobind, OptionsControl } from 'amis-core';
import { Tree, ResultBox, Spinner, PopUp } from 'amis-ui';
import { matchSorter } from 'match-sorter';
import debounce from 'lodash/debounce';
import { findDOMNode } from 'react-dom';
import { supportStatic } from './StaticHoc.js';

var TreeSelectControl = /** @class */ (function (_super) {
    __extends(TreeSelectControl, _super);
    function TreeSelectControl(props) {
        var _this = _super.call(this, props) || this;
        _this.container = React.createRef();
        _this.input = React.createRef();
        _this.cache = {};
        _this.targetRef = function (ref) {
            return (_this.target = ref ? findDOMNode(ref) : null);
        };
        /** source数据源是否已加载 */
        _this.sourceLoaded = false;
        _this.state = {
            inputValue: '',
            tempValue: '',
            isOpened: false
        };
        _this.open = _this.open.bind(_this);
        _this.close = _this.close.bind(_this);
        _this.handleChange = _this.handleChange.bind(_this);
        _this.handleTempChange = _this.handleTempChange.bind(_this);
        _this.handleConfirm = _this.handleConfirm.bind(_this);
        _this.clearValue = _this.clearValue.bind(_this);
        _this.handleFocus = _this.handleFocus.bind(_this);
        _this.handleBlur = _this.handleBlur.bind(_this);
        _this.handleKeyPress = _this.handleKeyPress.bind(_this);
        _this.handleInputChange = debounce(_this.handleInputChange.bind(_this), 150, {
            trailing: true,
            leading: false
        });
        _this.handleInputKeyDown = _this.handleInputKeyDown.bind(_this);
        _this.loadRemote = debounce(_this.loadRemote.bind(_this), 250, {
            trailing: true,
            leading: false
        });
        return _this;
    }
    TreeSelectControl.prototype.componentDidMount = function () {
        this.loadRemote('');
    };
    TreeSelectControl.prototype.componentWillUnmount = function () {
        this.sourceLoaded = false;
    };
    TreeSelectControl.prototype.open = function (fn) {
        if (this.props.disabled) {
            return;
        }
        this.setState({
            isOpened: true
        }, fn);
    };
    TreeSelectControl.prototype.close = function () {
        var _this = this;
        this.setState({
            isOpened: false,
            inputValue: this.props.multiple ? this.state.inputValue : ''
        }, function () { return _this.loadRemote(_this.state.inputValue); });
    };
    TreeSelectControl.prototype.resolveOptions = function () {
        var _a = this.props, options = _a.options, searchable = _a.searchable, autoComplete = _a.autoComplete;
        return !isEffectiveApi(autoComplete) && searchable && this.state.inputValue
            ? this.filterOptions(options, this.state.inputValue)
            : options;
    };
    TreeSelectControl.prototype.resolveOption = function (options, value) {
        var _this = this;
        return findTree(options, function (item) {
            var valueAbility = _this.props.valueField || 'value';
            var itemValue = hasAbility(item, valueAbility)
                ? item[valueAbility]
                : '';
            return itemValue === value;
        });
    };
    TreeSelectControl.prototype.handleFocus = function (e) {
        var _a = this.props, dispatchEvent = _a.dispatchEvent, value = _a.value;
        var items = this.resolveOptions();
        var item = this.resolveOption(items, value);
        dispatchEvent('focus', resolveEventData(this.props, { value: value, item: item, items: items }));
    };
    TreeSelectControl.prototype.handleBlur = function (e) {
        var _a = this.props, dispatchEvent = _a.dispatchEvent, value = _a.value;
        var items = this.resolveOptions();
        var item = this.resolveOption(items, value);
        dispatchEvent('blur', resolveEventData(this.props, { value: value, item: item, items: items }));
    };
    TreeSelectControl.prototype.handleKeyPress = function (e) {
        /**
         * 考虑到label/value中有空格的case
         * 这里使用组合键关闭 win：shift + space，mac：shift + space
         */
        if (e.key === ' ' && e.shiftKey) {
            this.handleOutClick(e);
            e.preventDefault();
        }
    };
    TreeSelectControl.prototype.validate = function () {
        var _a = this.props, value = _a.value, minLength = _a.minLength, maxLength = _a.maxLength, delimiter = _a.delimiter;
        var curValue = Array.isArray(value)
            ? value
            : (value ? String(value) : '').split(delimiter || ',');
        if (minLength && curValue.length < minLength) {
            return "\u5DF2\u9009\u62E9\u6570\u91CF\u4F4E\u4E8E\u8BBE\u5B9A\u7684\u6700\u5C0F\u4E2A\u6570".concat(minLength, "\uFF0C\u8BF7\u9009\u62E9\u66F4\u591A\u7684\u9009\u9879\u3002");
        }
        else if (maxLength && curValue.length > maxLength) {
            return "\u5DF2\u9009\u62E9\u6570\u91CF\u8D85\u51FA\u8BBE\u5B9A\u7684\u6700\u5927\u4E2A\u6570".concat(maxLength, "\uFF0C\u8BF7\u53D6\u6D88\u9009\u62E9\u8D85\u51FA\u7684\u9009\u9879\u3002");
        }
    };
    TreeSelectControl.prototype.removeItem = function (index, e) {
        var _a = this.props, selectedOptions = _a.selectedOptions, joinValues = _a.joinValues, extractValue = _a.extractValue, delimiter = _a.delimiter, valueField = _a.valueField, onChange = _a.onChange, disabled = _a.disabled;
        e && e.stopPropagation();
        if (disabled) {
            return;
        }
        var items = selectedOptions.concat();
        items.splice(index, 1);
        var value = items;
        if (joinValues) {
            value = items
                .map(function (item) { return item[valueField || 'value']; })
                .join(delimiter || ',');
        }
        else if (extractValue) {
            value = items.map(function (item) { return item[valueField || 'value']; });
        }
        onChange(value);
    };
    TreeSelectControl.prototype.handleChange = function (value) {
        var _this = this;
        var multiple = this.props.multiple;
        if (!multiple) {
            this.close();
        }
        this.setState({
            inputValue: ''
        }, function () { return _this.resultChangeEvent(value); });
    };
    TreeSelectControl.prototype.handleTempChange = function (value) {
        this.setState({
            tempValue: value
        });
    };
    TreeSelectControl.prototype.handleConfirm = function () {
        var _this = this;
        this.close();
        this.setState({
            inputValue: ''
        }, function () { return _this.resultChangeEvent(_this.state.tempValue); });
    };
    TreeSelectControl.prototype.handleInputChange = function (value) {
        var _this = this;
        var _a = this.props, autoComplete = _a.autoComplete, data = _a.data;
        this.setState({
            inputValue: value
        }, isEffectiveApi(autoComplete, data)
            ? function () { return _this.loadRemote(_this.state.inputValue); }
            : undefined);
    };
    TreeSelectControl.prototype.handleInputKeyDown = function (event) {
        var inputValue = this.state.inputValue;
        var _a = this.props, multiple = _a.multiple, selectedOptions = _a.selectedOptions;
        if (event.key === 'Backspace' &&
            !inputValue &&
            selectedOptions.length &&
            multiple) {
            this.removeItem(selectedOptions.length - 1);
        }
    };
    TreeSelectControl.prototype.resetValue = function () {
        var _a, _b;
        var _c = this.props, onChange = _c.onChange, resetValue = _c.resetValue, formStore = _c.formStore, store = _c.store, name = _c.name;
        var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
        onChange(pristineVal);
    };
    TreeSelectControl.prototype.clearValue = function () {
        var _a = this.props, onChange = _a.onChange, resetValue = _a.resetValue;
        onChange(typeof resetValue === 'undefined' ? '' : resetValue);
    };
    TreeSelectControl.prototype.filterOptions = function (options, keywords) {
        var _this = this;
        var _a = this.props, labelField = _a.labelField, valueField = _a.valueField;
        return options.map(function (option) {
            option = __assign({}, option);
            option.visible = !!matchSorter([option], keywords, {
                keys: [labelField || 'label', valueField || 'value'],
                threshold: matchSorter.rankings.CONTAINS
            }).length;
            if (!option.visible && option.children) {
                option.children = _this.filterOptions(option.children, keywords);
                var visibleCount = option.children.filter(function (item) { return item.visible; }).length;
                option.visible = !!visibleCount;
            }
            option.visible && (option.collapsed = false);
            return option;
        });
    };
    TreeSelectControl.prototype.loadRemote = function (input) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, autoComplete, env, data, setOptions, setLoading, source, options, combinedOptions, ret, options, combinedOptions;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, autoComplete = _a.autoComplete, env = _a.env, data = _a.data, setOptions = _a.setOptions, setLoading = _a.setLoading, source = _a.source;
                        // 同时配置source和autoComplete时，首次渲染需要加载source数据
                        if (!isEffectiveApi(autoComplete, data) ||
                            (!input && isEffectiveApi(source) && !this.sourceLoaded)) {
                            this.sourceLoaded = true;
                            return [2 /*return*/];
                        }
                        else if (!env || !env.fetcher) {
                            throw new Error('fetcher is required');
                        }
                        if (this.cache[input] || ~input.indexOf("'") /*中文没输完 233*/) {
                            options = this.cache[input] || [];
                            combinedOptions = this.mergeOptions(options);
                            setOptions(combinedOptions);
                            return [2 /*return*/, Promise.resolve({
                                    options: combinedOptions
                                })];
                        }
                        setLoading(true);
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, , 3, 4]);
                        return [4 /*yield*/, env.fetcher(autoComplete, __assign(__assign({}, data), { term: input, value: input }))];
                    case 2:
                        ret = _b.sent();
                        options = (ret.data && ret.data.options) || ret.data || [];
                        this.cache[input] = options;
                        combinedOptions = this.mergeOptions(options);
                        setOptions(combinedOptions);
                        return [2 /*return*/, {
                                options: combinedOptions
                            }];
                    case 3:
                        setLoading(false);
                        return [7 /*endfinally*/];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    TreeSelectControl.prototype.mergeOptions = function (options) {
        var selectedOptions = this.props.selectedOptions;
        var combinedOptions = normalizeOptions(options).concat();
        if (Array.isArray(selectedOptions) && selectedOptions.length) {
            selectedOptions.forEach(function (option) {
                if (!findTree(combinedOptions, function (item) { return item.value == option.value; })) {
                    combinedOptions.push(__assign({}, option));
                }
            });
        }
        return combinedOptions;
    };
    TreeSelectControl.prototype.reload = function (subpath, query) {
        var reload = this.props.reloadOptions;
        reload && reload(subpath, query);
    };
    TreeSelectControl.prototype.handleOutClick = function (e) {
        e.defaultPrevented ||
            this.setState({
                isOpened: true
            });
    };
    TreeSelectControl.prototype.handleResultChange = function (value) {
        var _a = this.props, joinValues = _a.joinValues, extractValue = _a.extractValue, delimiter = _a.delimiter, valueField = _a.valueField, multiple = _a.multiple;
        var newValue = Array.isArray(value) ? value.concat() : [];
        if (!multiple && !newValue.length) {
            this.resultChangeEvent('');
            return;
        }
        if (joinValues || extractValue) {
            newValue = value.map(function (item) { return item[valueField || 'value']; });
        }
        if (joinValues) {
            newValue = newValue.join(delimiter || ',');
        }
        this.resultChangeEvent(newValue);
    };
    TreeSelectControl.prototype.doAction = function (action, data, throwErrors) {
        var _a, _b, _c, _d, _e;
        if (action.actionType === 'clear') {
            this.clearValue();
        }
        else if (action.actionType === 'reset') {
            this.resetValue();
        }
        else if (action.actionType === 'add') {
            this.addItemFromAction((_a = action.args) === null || _a === void 0 ? void 0 : _a.item, (_b = action.args) === null || _b === void 0 ? void 0 : _b.parentValue);
        }
        else if (action.actionType === 'edit') {
            this.editItemFromAction((_c = action.args) === null || _c === void 0 ? void 0 : _c.item, (_d = action.args) === null || _d === void 0 ? void 0 : _d.originValue);
        }
        else if (action.actionType === 'delete') {
            this.deleteItemFromAction((_e = action.args) === null || _e === void 0 ? void 0 : _e.value);
        }
        else if (action.actionType === 'reload') {
            this.reload();
        }
    };
    TreeSelectControl.prototype.addItemFromAction = function (item, parentValue) {
        var _a = this.props, onAdd = _a.onAdd, options = _a.options, valueField = _a.valueField;
        var idxes = findTreeIndex(options, function (item) {
            var valueAbility = valueField || 'value';
            var value = hasAbility(item, valueAbility) ? item[valueAbility] : '';
            return value === parentValue;
        }) || [];
        onAdd && onAdd(idxes.concat(0), item, true);
    };
    TreeSelectControl.prototype.editItemFromAction = function (item, originValue) {
        var _a = this.props, onEdit = _a.onEdit, options = _a.options;
        var editItem = this.resolveOption(options, originValue);
        onEdit && editItem && onEdit(__assign(__assign({}, item), { originValue: originValue }), editItem, true);
    };
    TreeSelectControl.prototype.deleteItemFromAction = function (value) {
        var _a = this.props, onDelete = _a.onDelete, options = _a.options;
        var deleteItem = this.resolveOption(options, value);
        onDelete && deleteItem && onDelete(deleteItem);
    };
    TreeSelectControl.prototype.resultChangeEvent = function (value) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, onChange, dispatchEvent, items, item, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, onChange = _a.onChange, dispatchEvent = _a.dispatchEvent;
                        items = this.resolveOptions();
                        item = this.resolveOption(items, value);
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, {
                                value: value,
                                item: item,
                                items: this.resolveOptions()
                            }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onChange && onChange(value);
                        return [2 /*return*/];
                }
            });
        });
    };
    TreeSelectControl.prototype.handleNodeClick = function (item) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, dispatchEvent, data, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
                        return [4 /*yield*/, dispatchEvent('itemClick', createObject(data, { item: item }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    /** 下拉框选项渲染 */
    TreeSelectControl.prototype.renderOptionItem = function (option, states) {
        var _a = this.props, menuTpl = _a.menuTpl, render = _a.render, data = _a.data;
        return render("option/".concat(states.index), menuTpl, {
            data: createObject(createObject(data, __assign({}, states)), option)
        });
    };
    /** 输入框选项渲染 */
    TreeSelectControl.prototype.renderItem = function (item) {
        var _a = this.props, labelField = _a.labelField, options = _a.options, hideNodePathLabel = _a.hideNodePathLabel;
        if (hideNodePathLabel) {
            return item[labelField || 'label'];
        }
        // 将所有祖先节点也展现出来
        var ancestors = getTreeAncestors(options, item, true);
        return "".concat(ancestors
            ? ancestors.map(function (item) { return "".concat(item[labelField || 'label']); }).join(' / ')
            : item[labelField || 'label']);
    };
    TreeSelectControl.prototype.domRef = function (ref) {
        this.treeRef = ref;
    };
    TreeSelectControl.prototype.renderOuter = function () {
        var _a = this.props, value = _a.value, enableNodePath = _a.enableNodePath, _b = _a.pathSeparator, pathSeparator = _b === void 0 ? '/' : _b, disabled = _a.disabled, joinValues = _a.joinValues, extractValue = _a.extractValue, delimiter = _a.delimiter, placeholder = _a.placeholder, options = _a.options, multiple = _a.multiple, valueField = _a.valueField, initiallyOpen = _a.initiallyOpen, unfoldedLevel = _a.unfoldedLevel, withChildren = _a.withChildren, rootLabel = _a.rootLabel, cascade = _a.cascade, rootValue = _a.rootValue, showIcon = _a.showIcon, showRadio = _a.showRadio, popOverContainer = _a.popOverContainer, onlyChildren = _a.onlyChildren, onlyLeaf = _a.onlyLeaf, ns = _a.classPrefix, optionsPlaceholder = _a.optionsPlaceholder, searchable = _a.searchable, autoComplete = _a.autoComplete, maxLength = _a.maxLength, minLength = _a.minLength, labelField = _a.labelField, deferField = _a.deferField, nodePath = _a.nodePath, onAdd = _a.onAdd, creatable = _a.creatable, createTip = _a.createTip, addControls = _a.addControls, onEdit = _a.onEdit, editable = _a.editable, editTip = _a.editTip, editControls = _a.editControls, removable = _a.removable, removeTip = _a.removeTip, onDelete = _a.onDelete, rootCreatable = _a.rootCreatable, rootCreateTip = _a.rootCreateTip, __ = _a.translate, deferLoad = _a.deferLoad, expandTreeOptions = _a.expandTreeOptions, selfDisabledAffectChildren = _a.selfDisabledAffectChildren, showOutline = _a.showOutline, autoCheckChildren = _a.autoCheckChildren, autoCancelParent = _a.autoCancelParent, hideRoot = _a.hideRoot, virtualThreshold = _a.virtualThreshold, itemHeight = _a.itemHeight, menuTpl = _a.menuTpl, enableDefaultIcon = _a.enableDefaultIcon, mobileUI = _a.mobileUI, testIdBuilder = _a.testIdBuilder, nodeBehavior = _a.nodeBehavior, itemActions = _a.itemActions, cx = _a.classnames, id = _a.id, themeCss = _a.themeCss;
        var filtedOptions = !isEffectiveApi(autoComplete) && searchable && this.state.inputValue
            ? this.filterOptions(options, this.state.inputValue)
            : options;
        return (jsx(Tree, { classPrefix: ns, onRef: this.domRef, onlyChildren: onlyChildren, onHandleNodeClick: this.handleNodeClick, onlyLeaf: onlyLeaf, labelField: labelField, valueField: valueField, deferField: deferField, disabled: disabled, onChange: mobileUI ? this.handleTempChange : this.handleChange, joinValues: joinValues, extractValue: extractValue, delimiter: delimiter, placeholder: __(optionsPlaceholder), options: filtedOptions, highlightTxt: this.state.inputValue, multiple: multiple, initiallyOpen: initiallyOpen, unfoldedLevel: unfoldedLevel, withChildren: withChildren, autoCheckChildren: autoCheckChildren, autoCancelParent: autoCancelParent, rootLabel: __(rootLabel), rootValue: rootValue, showIcon: showIcon, showRadio: showRadio, showOutline: showOutline, cascade: cascade, foldedField: "collapsed", hideRoot: hideRoot, value: value || '', nodePath: nodePath, enableNodePath: enableNodePath, pathSeparator: pathSeparator, maxLength: maxLength, minLength: minLength, onAdd: onAdd, creatable: creatable, createTip: createTip, rootCreatable: rootCreatable, rootCreateTip: rootCreateTip, onEdit: onEdit, editable: editable, editTip: editTip, removable: removable, removeTip: removeTip, onDelete: onDelete, bultinCUD: !addControls && !editControls, onDeferLoad: deferLoad, onExpandTree: expandTreeOptions, selfDisabledAffectChildren: selfDisabledAffectChildren, virtualThreshold: virtualThreshold, 
            // itemHeight={toNumber(itemHeight) > 0 ? toNumber(itemHeight) : undefined}
            itemRender: menuTpl ? this.renderOptionItem : undefined, enableDefaultIcon: enableDefaultIcon, mobileUI: mobileUI, nodeBehavior: nodeBehavior, itemActionsRender: itemActions ? this.renderItemActions : undefined, actionClassName: cx(setThemeClassName(__assign(__assign({}, this.props), { name: 'actionControlClassName', id: id, themeCss: themeCss }))), testIdBuilder: testIdBuilder }));
    };
    TreeSelectControl.prototype.renderItemActions = function (option, states) {
        var _a = this.props, itemActions = _a.itemActions, data = _a.data, render = _a.render;
        return render("action/".concat(states.index), itemActions || '', {
            data: createObject(createObject(data, __assign({}, states)), option)
        });
    };
    TreeSelectControl.prototype.render = function () {
        var _this = this;
        var _a = this.props, className = _a.className, style = _a.style, disabled = _a.disabled, inline = _a.inline, loading = _a.loading, multiple = _a.multiple, value = _a.value, clearable = _a.clearable, ns = _a.classPrefix, cx = _a.classnames, searchable = _a.searchable, autoComplete = _a.autoComplete, selectedOptions = _a.selectedOptions, placeholder = _a.placeholder, popOverContainer = _a.popOverContainer, mobileUI = _a.mobileUI, maxTagCount = _a.maxTagCount, overflowTagPopover = _a.overflowTagPopover, __ = _a.translate, env = _a.env, loadingConfig = _a.loadingConfig, testIdBuilder = _a.testIdBuilder, wrapperCustomStyle = _a.wrapperCustomStyle, id = _a.id, themeCss = _a.themeCss;
        var isOpened = this.state.isOpened;
        var resultValue = multiple
            ? selectedOptions
            : selectedOptions.length
                ? this.renderItem(selectedOptions[0])
                : '';
        return (jsxs(Fragment, { children: [jsxs("div", __assign({ ref: this.container, className: cx("TreeSelectControl", className) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId(), { children: [jsx(ResultBox, __assign({ popOverContainer: popOverContainer || env.getModalContainer, maxTagCount: maxTagCount, overflowTagPopover: overflowTagPopover, disabled: disabled, ref: this.targetRef, placeholder: __(placeholder !== null && placeholder !== void 0 ? placeholder : 'placeholder.empty'), inputPlaceholder: '', className: cx("TreeSelect", {
                                'TreeSelect--inline': inline,
                                'TreeSelect--single': !multiple,
                                'TreeSelect--multi': multiple,
                                'TreeSelect--searchable': searchable || isEffectiveApi(autoComplete),
                                'is-opened': this.state.isOpened,
                                'is-disabled': disabled
                            }), result: resultValue, onResultClick: this.handleOutClick, value: this.state.inputValue, onChange: this.handleInputChange, onResultChange: this.handleResultChange, itemRender: this.renderItem, onKeyPress: this.handleKeyPress, onFocus: this.handleFocus, onBlur: this.handleBlur, onKeyDown: this.handleInputKeyDown, clearable: clearable, allowInput: !mobileUI &&
                                (searchable || isEffectiveApi(autoComplete)) &&
                                (multiple || !resultValue), hasDropDownArrow: true, readOnly: mobileUI, mobileUI: mobileUI, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('result-box') }, { children: loading ? (jsx(Spinner, { loadingConfig: loadingConfig, size: "sm" })) : undefined })), !mobileUI && isOpened ? (jsx(Overlay, __assign({ container: popOverContainer || (function () { return _this.container.current; }), target: function () { return _this.target; }, show: true }, { children: jsx(PopOver, __assign({ classPrefix: ns, className: "".concat(ns, "TreeSelect-popover"), style: {
                                    minWidth: this.target ? this.target.offsetWidth : undefined
                                }, onHide: this.close, overlay: true }, { children: this.renderOuter() })) }))) : null, mobileUI ? (jsx(PopUp, __assign({ container: env.getModalContainer, className: cx("".concat(ns, "TreeSelect-popup")), isShow: isOpened, onHide: this.close, showConfirm: true, onConfirm: this.handleConfirm }, { children: this.renderOuter() }))) : null] })), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'actionControlClassName'
                            }
                        ]
                    }, env: env }))] }));
    };
    TreeSelectControl.defaultProps = {
        hideRoot: true,
        placeholder: 'Select.placeholder',
        optionsPlaceholder: 'placeholder.noData',
        multiple: false,
        clearable: true,
        rootLabel: 'Tree.root',
        rootValue: '',
        showIcon: true,
        joinValues: true,
        extractValue: false,
        delimiter: ',',
        resetValue: '',
        hideNodePathLabel: false,
        enableNodePath: false,
        pathSeparator: '/',
        selfDisabledAffectChildren: true
    };
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "handleOutClick", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "handleResultChange", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "addItemFromAction", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "editItemFromAction", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "deleteItemFromAction", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "resultChangeEvent", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "handleNodeClick", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "renderOptionItem", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "renderItem", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "domRef", null);
    __decorate([
        autobind
    ], TreeSelectControl.prototype, "renderItemActions", null);
    __decorate([
        supportStatic()
    ], TreeSelectControl.prototype, "render", null);
    return TreeSelectControl;
}(React.Component));
var TreeSelectControlRenderer = /** @class */ (function (_super) {
    __extends(TreeSelectControlRenderer, _super);
    function TreeSelectControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TreeSelectControlRenderer = __decorate([
        OptionsControl({
            type: 'tree-select'
        })
    ], TreeSelectControlRenderer);
    return TreeSelectControlRenderer;
}(TreeSelectControl));

export { TreeSelectControlRenderer, TreeSelectControl as default };
