/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __rest, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React, { Suspense } from 'react';
import cx from 'classnames';
import { FormItem } from 'amis-core';
import { supportStatic } from './StaticHoc.js';

// todo amis-ui 里面组件直接改成按需加载
var ColorPicker = React.lazy(function () { return import('amis-ui/lib/components/ColorPicker'); });
var ColorControl = /** @class */ (function (_super) {
    __extends(ColorControl, _super);
    function ColorControl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.state = {
            open: false
        };
        return _this;
    }
    ColorControl.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, ns = _a.classPrefix, value = _a.value, env = _a.env, isStatic = _a.static, mobileUI = _a.mobileUI, rest = __rest(_a, ["className", "style", "classPrefix", "value", "env", "static", "mobileUI"]);
        return (jsx("div", __assign({ className: cx("".concat(ns, "ColorControl"), className) }, { children: jsx(Suspense, __assign({ fallback: jsx("div", { children: "..." }) }, { children: jsx(ColorPicker, __assign({ classPrefix: ns }, rest, { placeholder: rest.placeholder, mobileUI: mobileUI, popOverContainer: mobileUI
                        ? env === null || env === void 0 ? void 0 : env.getModalContainer
                        : rest.popOverContainer || env.getModalContainer, value: value || '' })) })) })));
    };
    ColorControl.defaultProps = {
        format: 'hex',
        clearable: true
    };
    __decorate([
        supportStatic()
    ], ColorControl.prototype, "render", null);
    return ColorControl;
}(React.PureComponent));
var ColorControlRenderer = /** @class */ (function (_super) {
    __extends(ColorControlRenderer, _super);
    function ColorControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ColorControlRenderer = __decorate([
        FormItem({
            type: 'input-color'
        })
    ], ColorControlRenderer);
    return ColorControlRenderer;
}(ColorControl));

export { ColorControlRenderer, ColorPicker, ColorControl as default };
