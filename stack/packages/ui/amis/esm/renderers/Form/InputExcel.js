/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __spreadArray, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import DropZone from 'react-dropzone';
import omit from 'lodash/omit';
import merge from 'lodash/merge';
import isEmpty from 'lodash/isEmpty';
import isPlainObject from 'lodash/isPlainObject';
import { isObject, dataMapping, guid, isExpression, getVariable, resolveEventData, autobind, FormItem } from 'amis-core';
import { TooltipWrapper, Icon } from 'amis-ui';

var ExcelControl = /** @class */ (function (_super) {
    __extends(ExcelControl, _super);
    function ExcelControl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.state = {
            files: []
        };
        _this.dropzone = React.createRef();
        return _this;
    }
    ExcelControl.prototype.componentDidUpdate = function (prevProps) {
        // 当值被外部设置为空时，清空文件列表
        if (prevProps.value !== this.props.value && !this.props.value) {
            this.setState({ files: [] });
        }
        // 如果值从外部改变了，并且不是被清空，则触发自动填充
        if (prevProps.value !== this.props.value && this.props.value) {
            this.triggerAutoFill();
        }
    };
    /**
     * 同步自动填充数据
     */
    ExcelControl.prototype.syncAutoFill = function (filename, fileData) {
        var _a = this.props, autoFill = _a.autoFill, onBulkChange = _a.onBulkChange, data = _a.data, name = _a.name, multiple = _a.multiple;
        if ((autoFill === null || autoFill === void 0 ? void 0 : autoFill.hasOwnProperty('api')) || !isObject(autoFill)) {
            return;
        }
        var excludeSelfAutoFill = name ? omit(autoFill, name) : autoFill;
        if (!isEmpty(excludeSelfAutoFill) && onBulkChange) {
            var context = void 0;
            if (multiple === true) {
                // 多文件模式：提供所有已解析文件的数组作为上下文
                var parsedFiles = this.state.files
                    .filter(function (f) { return f.state === 'parsed'; })
                    .map(function (file) { return ({
                    filename: file.name,
                    data: file.data
                }); });
                context = {
                    items: parsedFiles,
                    currentFile: {
                        filename: filename,
                        data: fileData
                    }
                };
            }
            else {
                // 单文件模式：只提供当前文件信息作为上下文
                context = {
                    filename: filename
                };
            }
            var toSync_1 = dataMapping(excludeSelfAutoFill, context);
            Object.keys(toSync_1).forEach(function (key) {
                if (isPlainObject(toSync_1[key]) && isPlainObject(data[key])) {
                    toSync_1[key] = merge({}, data[key], toSync_1[key]);
                }
            });
            onBulkChange(toSync_1);
        }
    };
    /**
     * 处理文件上传
     * 支持单文件和多文件上传，自动处理文件数量限制
     */
    ExcelControl.prototype.handleDrop = function (files) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, maxLength, multiple, filesToProcess_1, remainingSlots, filesToProcess;
            var _this = this;
            return __generator(this, function (_b) {
                _a = this.props, maxLength = _a.maxLength, multiple = _a.multiple;
                if (!files.length) {
                    return [2 /*return*/];
                }
                // 如果 multiple 未定义或为 false，只取第一个文件并清除现有文件
                if (!multiple) {
                    filesToProcess_1 = [
                        {
                            file: files[0],
                            id: guid()
                        }
                    ];
                    this.setState({
                        files: filesToProcess_1.map(function (_a) {
                            var file = _a.file, id = _a.id;
                            return ({
                                id: id,
                                name: file.name,
                                state: 'pending'
                            });
                        })
                    }, function () {
                        _this.processFiles(filesToProcess_1);
                    });
                    return [2 /*return*/];
                }
                remainingSlots = maxLength
                    ? maxLength - this.state.files.length
                    : files.length;
                if (remainingSlots <= 0) {
                    return [2 /*return*/];
                }
                filesToProcess = files.slice(0, remainingSlots).map(function (file) { return ({
                    file: file,
                    id: guid()
                }); });
                this.setState(function (state) { return ({
                    files: __spreadArray(__spreadArray([], state.files, true), filesToProcess.map(function (_a) {
                        var file = _a.file, id = _a.id;
                        return ({
                            id: id,
                            name: file.name,
                            state: 'pending'
                        });
                    }), true)
                }); }, function () {
                    _this.processFiles(filesToProcess);
                });
                return [2 /*return*/];
            });
        });
    };
    /**
     * 并行处理多个文件
     * 使用 Promise.all 同时处理所有文件，提高效率
     */
    ExcelControl.prototype.processFiles = function (files) {
        return __awaiter(this, void 0, void 0, function () {
            var pendingFiles;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        pendingFiles = files
                            .map(function (_a) {
                            var file = _a.file, id = _a.id;
                            var fileState = _this.state.files.find(function (f) { return f.id === id && f.state === 'pending'; });
                            return fileState ? { file: file, fileState: fileState } : null;
                        })
                            .filter(function (item) { return item !== null; });
                        if (!pendingFiles.length) return [3 /*break*/, 2];
                        return [4 /*yield*/, Promise.all(pendingFiles.map(function (_a) {
                                var file = _a.file, fileState = _a.fileState;
                                return _this.processExcelFile(file, fileState);
                            }))];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        // 如果没有待处理的文件，仍然需要更新表单值
                        // 这处理了清空所有文件的情况
                        this.updateFormValue();
                        _a.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    ExcelControl.prototype.handleSelect = function () {
        var _a = this.props, disabled = _a.disabled, maxLength = _a.maxLength, multiple = _a.multiple;
        var canAddMore = multiple === true || this.state.files.length === 0;
        if (!disabled &&
            !(maxLength && this.state.files.length >= maxLength) &&
            canAddMore &&
            this.dropzone.current) {
            this.dropzone.current.open();
        }
    };
    /**
     * 移除文件并更新表单数据
     *
     * @param file 要移除的文件对象
     */
    ExcelControl.prototype.removeFile = function (file) {
        var _this = this;
        this.setState(function (state) { return ({
            files: state.files.filter(function (f) { return f.id !== file.id; })
        }); }, function () {
            // 检查是否有待处理的文件
            var pendingFiles = _this.state.files.filter(function (f) { return f.state === 'pending'; });
            if (pendingFiles.length === 0) {
                // 没有待处理文件，可以安全地更新表单值
                _this.updateFormValue();
            }
            // 否则等待所有文件处理完成后再更新值
        });
    };
    /**
     * 更新文件状态
     * 当状态变为 parsed 时检查是否所有文件都已处理完成
     */
    ExcelControl.prototype.updateFileState = function (fileId, updates) {
        var _this = this;
        this.setState(function (state) { return ({
            files: state.files.map(function (f) { return (f.id === fileId ? __assign(__assign({}, f), updates) : f); })
        }); }, function () {
            if (updates.state === 'parsed') {
                // 检查是否所有待处理的文件都已经处理完成
                var pendingFiles = _this.state.files.filter(function (f) { return f.state === 'pending'; });
                if (pendingFiles.length === 0) {
                    // 所有文件都已处理完成，一次性更新表单值
                    _this.updateFormValue();
                    // autoFill 会在 updateFormValue 中触发
                }
            }
        });
    };
    /**
     * 更新表单值
     * 根据当前已解析的文件更新表单数据
     * 支持多文件模式和单文件模式
     */
    ExcelControl.prototype.updateFormValue = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, data, multiple, allSheets, parseImage, parsedFiles, value, file, dispatcher;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, data = _a.data, multiple = _a.multiple, allSheets = _a.allSheets, parseImage = _a.parseImage;
                        parsedFiles = this.state.files.filter(function (f) { return f.state === 'parsed'; });
                        if (!(parsedFiles.length === 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.dispatchEvent('change')];
                    case 1:
                        _b.sent();
                        this.props.onChange(undefined);
                        return [2 /*return*/];
                    case 2:
                        if (multiple === true) {
                            // 多文件模式：返回包含文件名和sheet数据的数组
                            value = parsedFiles.map(function (file) {
                                var fileData = Array.isArray(file.data) ? file.data : [file.data];
                                return {
                                    fileName: file.name,
                                    data: fileData.map(function (sheet) {
                                        // 确保每个sheet数据包含所需的属性
                                        var sheetData = {
                                            sheetName: sheet.sheetName || 'Sheet1',
                                            data: sheet.data || sheet
                                        };
                                        // 如果启用了图片解析，确保图片数据也被包含
                                        if (parseImage && sheet.images) {
                                            sheetData.images = sheet.images;
                                        }
                                        return sheetData;
                                    })
                                };
                            });
                        }
                        else {
                            file = parsedFiles[0];
                            if (allSheets) {
                                // 多表格模式
                                value = Array.isArray(file.data) ? file.data : [file.data];
                            }
                            else {
                                // 单表格模式
                                value = file.data;
                            }
                        }
                        // 检查并处理变量表达式
                        if (value && isExpression(value)) {
                            value = getVariable(data, value);
                        }
                        return [4 /*yield*/, this.dispatchEvent('change', value)];
                    case 3:
                        dispatcher = _b.sent();
                        if (dispatcher === null || dispatcher === void 0 ? void 0 : dispatcher.prevented) {
                            return [2 /*return*/];
                        }
                        this.props.onChange(value);
                        // 在值变化时也触发自动填充
                        this.triggerAutoFill();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 在适当的时机触发自动填充
     */
    ExcelControl.prototype.triggerAutoFill = function () {
        var autoFill = this.props.autoFill;
        if (autoFill && !autoFill.hasOwnProperty('api') && isObject(autoFill)) {
            var parsedFiles = this.state.files.filter(function (f) { return f.state === 'parsed'; });
            if (parsedFiles.length > 0) {
                // 取最后一个处理的文件
                var lastFile = parsedFiles[parsedFiles.length - 1];
                this.syncAutoFill(lastFile.name, lastFile.data);
            }
        }
    };
    /**
     * 处理Excel文件
     * 支持 .xls 和 .xlsx 格式
     * 使用 xlsx 库转换 .xls 为 .xlsx，使用 exceljs 解析内容
     */
    ExcelControl.prototype.processExcelFile = function (excelFile, fileState) {
        return __awaiter(this, void 0, void 0, function () {
            var __, arrayBuffer, isXls, buffer, XLSX, workbook, xlsxFile, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        __ = this.props.translate;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 7, , 8]);
                        return [4 /*yield*/, new Promise(function (resolve, reject) {
                                var reader = new FileReader();
                                reader.onload = function () {
                                    if (reader.result) {
                                        resolve(reader.result);
                                    }
                                    else {
                                        reject(new Error('Failed to read file'));
                                    }
                                };
                                reader.onerror = function () { return reject(reader.error); };
                                reader.readAsArrayBuffer(excelFile);
                            })];
                    case 2:
                        arrayBuffer = _a.sent();
                        isXls = excelFile.name.toLowerCase().endsWith('.xls');
                        buffer = void 0;
                        if (!isXls) return [3 /*break*/, 4];
                        return [4 /*yield*/, import('xlsx')];
                    case 3:
                        XLSX = _a.sent();
                        workbook = XLSX.read(new Uint8Array(arrayBuffer), {
                            cellDates: true
                        });
                        xlsxFile = XLSX.writeXLSX(workbook, { type: 'array' });
                        buffer = xlsxFile.buffer;
                        return [3 /*break*/, 5];
                    case 4:
                        buffer = arrayBuffer;
                        _a.label = 5;
                    case 5: return [4 /*yield*/, this.parseExcelData(buffer, fileState)];
                    case 6:
                        _a.sent();
                        return [3 /*break*/, 8];
                    case 7:
                        error_1 = _a.sent();
                        console.error('Excel parsing error:', error_1);
                        this.updateFileState(fileState.id, {
                            state: 'error',
                            error: error_1.message || __('Excel.parseError')
                        });
                        return [3 /*break*/, 8];
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 解析 Excel 数据
     * 支持解析所有 sheet 或单个 sheet
     * 支持解析图片和富文本
     */
    ExcelControl.prototype.parseExcelData = function (excelData, fileState) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, allSheets, parseImage, plainText, __, ExcelJS, workbook, data, sheetsResult, worksheet, error_2;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, allSheets = _a.allSheets, parseImage = _a.parseImage, plainText = _a.plainText, __ = _a.translate;
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 4, , 5]);
                        return [4 /*yield*/, import('exceljs')];
                    case 2:
                        ExcelJS = (_b.sent()).default;
                        this.ExcelJS = ExcelJS;
                        workbook = new ExcelJS.Workbook();
                        data = typeof excelData === 'string'
                            ? new Uint8Array(excelData.split('').map(function (c) { return c.charCodeAt(0); })).buffer
                            : excelData;
                        return [4 /*yield*/, workbook.xlsx.load(data)];
                    case 3:
                        _b.sent();
                        sheetsResult = void 0;
                        if (allSheets) {
                            sheetsResult = this.parseAllSheets(workbook, parseImage, plainText);
                        }
                        else {
                            worksheet = workbook.worksheets.find(function (sheet) { return sheet.state !== 'hidden'; });
                            sheetsResult = parseImage
                                ? {
                                    data: this.readWorksheet(worksheet, plainText),
                                    images: this.readImages(worksheet, workbook)
                                }
                                : this.readWorksheet(worksheet, plainText);
                        }
                        this.updateFileState(fileState.id, {
                            state: 'parsed',
                            data: sheetsResult
                        });
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _b.sent();
                        console.error('Excel parsing error:', error_2);
                        this.updateFileState(fileState.id, {
                            state: 'error',
                            error: error_2.message || __('Excel.parseError')
                        });
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 解析所有可见的 sheet
     */
    ExcelControl.prototype.parseAllSheets = function (workbook, parseImage, plainText) {
        var _this = this;
        if (parseImage === void 0) { parseImage = false; }
        if (plainText === void 0) { plainText = true; }
        var sheetsResult = [];
        workbook.eachSheet(function (worksheet) {
            var sheetState = worksheet.state || 'visible';
            // hidden 的不处理
            if (sheetState === 'hidden') {
                return;
            }
            var sheetData = {
                sheetName: worksheet.name,
                data: _this.readWorksheet(worksheet, plainText)
            };
            if (parseImage) {
                sheetData.images = _this.readImages(worksheet, workbook);
            }
            sheetsResult.push(sheetData);
        });
        return sheetsResult;
    };
    /** 读取工作表里的图片 */
    ExcelControl.prototype.readImages = function (worksheet, workbook) {
        var imageDataURI = this.props.imageDataURI;
        var images = worksheet.getImages();
        var imgResult = [];
        for (var _i = 0, images_1 = images; _i < images_1.length; _i++) {
            var image = images_1[_i];
            var img = workbook.getImage(+image.imageId);
            var imgBase64 = this.encodeBase64Bytes(img.buffer);
            if (imageDataURI) {
                var extension = img.extension || 'png';
                imgResult.push("data:image/".concat(extension, ";base64,") + imgBase64);
            }
            else {
                imgResult.push(imgBase64);
            }
        }
        return imgResult;
    };
    /** 将 buffer 转成 base64 */
    ExcelControl.prototype.encodeBase64Bytes = function (bytes) {
        return btoa(bytes.reduce(function (acc, current) { return acc + String.fromCharCode(current); }, ''));
    };
    /**
     * 触发表单事件
     */
    ExcelControl.prototype.dispatchEvent = function (eventName, eventData) {
        return __awaiter(this, void 0, void 0, function () {
            var dispatchEvent;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        dispatchEvent = this.props.dispatchEvent;
                        return [4 /*yield*/, dispatchEvent(eventName, resolveEventData(this.props, { value: eventData }))];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    /**
     * 检查当前单元格数据是否为富文本
     *
     * @reference https://github.com/exceljs/exceljs#rich-text
     */
    ExcelControl.prototype.isRichTextValue = function (value) {
        return !!(value &&
            isObject(value) &&
            value.hasOwnProperty('richText') &&
            Array.isArray(value === null || value === void 0 ? void 0 : value.richText));
    };
    /**
     * 将富文本类型的单元格内容转化为Plain Text
     *
     * @param {CellRichTextValue} cellValue 单元格值
     * @param {Boolean} html 是否输出为html格式
     */
    ExcelControl.prototype.richText2PlainString = function (cellValue, html) {
        if (html === void 0) { html = false; }
        var result = cellValue.richText.map(function (_a) {
            var text = _a.text, _b = _a.font, font = _b === void 0 ? {} : _b;
            var outputStr = text;
            /* 如果以HTML格式输出，简单处理一下样式 */
            if (html) {
                var styles = '';
                var htmlTag = (font === null || font === void 0 ? void 0 : font.bold)
                    ? 'strong'
                    : (font === null || font === void 0 ? void 0 : font.italic)
                        ? 'em'
                        : (font === null || font === void 0 ? void 0 : font.vertAlign) === 'superscript'
                            ? 'sup'
                            : (font === null || font === void 0 ? void 0 : font.vertAlign) === 'subscript'
                                ? 'sub'
                                : 'span';
                if (font === null || font === void 0 ? void 0 : font.strike) {
                    styles += 'text-decoration: line-through;';
                }
                else if (font === null || font === void 0 ? void 0 : font.underline) {
                    styles += 'text-decoration: underline;';
                }
                if (font === null || font === void 0 ? void 0 : font.outline) {
                    styles += 'outline: solid;';
                }
                if (font === null || font === void 0 ? void 0 : font.size) {
                    styles += "font-size: ".concat(font.size, "px;");
                }
                outputStr = "<".concat(htmlTag, " ").concat(styles ? "style=".concat(styles) : '', ">").concat(text, "</").concat(htmlTag, ">");
            }
            return outputStr;
        });
        return result.join('');
    };
    /**
     * 读取工作表内容
     */
    ExcelControl.prototype.readWorksheet = function (worksheet, plainText) {
        var _this = this;
        if (plainText === void 0) { plainText = true; }
        var result = [];
        var _a = this.props, parseMode = _a.parseMode, includeEmpty = _a.includeEmpty;
        if (parseMode === 'array') {
            worksheet.eachRow(function (_row) {
                var values = _row.values;
                values.shift(); // excel 返回的值是从 1 开始的，0 节点永远是 null
                if (plainText) {
                    values = values.map(function (item) {
                        if (item instanceof Object) {
                            if (item.hyperlink) {
                                if (item.hyperlink.startsWith('mailto:')) {
                                    return item.hyperlink.substring(7);
                                }
                                return item.hyperlink;
                            }
                            else if (item.result) {
                                return item.result;
                            }
                            else if (item.richText) {
                                return _this.richText2PlainString(item);
                            }
                        }
                        return item;
                    });
                }
                result.push(values);
            });
            return result;
        }
        else {
            var firstRowValues_1 = [];
            worksheet.eachRow(function (row, rowNumber) {
                var _a;
                // 将第一列作为字段名
                if (rowNumber == 1) {
                    firstRowValues_1 = ((_a = row.values) !== null && _a !== void 0 ? _a : []).map(function (item) {
                        return _this.isRichTextValue(item)
                            ? _this.richText2PlainString(item)
                            : item;
                    });
                }
                else {
                    var data_1 = {};
                    if (includeEmpty) {
                        firstRowValues_1.forEach(function (item) {
                            data_1[item] = '';
                        });
                    }
                    row.eachCell(function (cell, colNumber) {
                        if (firstRowValues_1[colNumber]) {
                            var value = cell.value;
                            if (plainText) {
                                var ExcelValueType = _this.ExcelJS.ValueType;
                                if (cell.type === ExcelValueType.Hyperlink) {
                                    value = cell.value.hyperlink;
                                    if (value.startsWith('mailto:')) {
                                        value = value.substring(7);
                                    }
                                }
                                else if (cell.type === ExcelValueType.Formula) {
                                    value = cell.value.result;
                                }
                                else if (cell.type === ExcelValueType.RichText) {
                                    value = _this.richText2PlainString(cell.value);
                                }
                                else if (cell.type === ExcelValueType.Error) {
                                    value = '';
                                }
                            }
                            data_1[firstRowValues_1[colNumber]] = value;
                        }
                    });
                    result.push(data_1);
                }
            });
            return result;
        }
    };
    ExcelControl.prototype.doAction = function (action, _data, _throwErrors) {
        var _a, _b;
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        var _c = this.props, onChange = _c.onChange, resetValue = _c.resetValue, formStore = _c.formStore, store = _c.store, name = _c.name;
        if (actionType === 'clear') {
            onChange('');
        }
        else if (actionType === 'reset') {
            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
            onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
        }
    };
    ExcelControl.prototype.render = function () {
        var _this = this;
        var _a = this.props, className = _a.className, cx = _a.classnames, maxLength = _a.maxLength, disabled = _a.disabled, __ = _a.translate, placeholder = _a.placeholder, testIdBuilder = _a.testIdBuilder, multiple = _a.multiple, env = _a.env, container = _a.container;
        var files = this.state.files;
        var isMaxLength = !!maxLength && files.length >= maxLength;
        // 单文件模式使用简化UI
        if (multiple !== true) {
            return (jsx("div", __assign({ className: cx('ExcelControl', className) }, { children: jsx(DropZone, __assign({ ref: this.dropzone, onDrop: this.handleDrop, accept: ".xlsx,.xls", multiple: false, disabled: disabled }, { children: function (_a) {
                        var getRootProps = _a.getRootProps, getInputProps = _a.getInputProps;
                        return (jsx("section", __assign({ className: cx('ExcelControl-container', className) }, { children: jsxs("div", __assign({}, getRootProps({ className: cx('ExcelControl-dropzone') }), testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId(), { children: [jsx("input", __assign({}, getInputProps(), testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('input').getTestId())), files.length > 0 && files[0].state === 'parsed' ? (jsx("p", { children: __('Excel.parsed', {
                                            filename: files[0].name
                                        }) })) : (jsx("p", { children: placeholder !== null && placeholder !== void 0 ? placeholder : __('Excel.placeholder') }))] })) })));
                    } }), "drop-zone") })));
        }
        // 多文件模式使用当前UI，但简化风格
        return (jsx("div", __assign({ className: cx('ExcelControl', className) }, { children: jsx(DropZone, __assign({ ref: this.dropzone, onDrop: this.handleDrop, accept: ".xlsx,.xls", multiple: !!multiple, disabled: disabled || isMaxLength, noClick: true }, { children: function (_a) {
                    var getRootProps = _a.getRootProps, getInputProps = _a.getInputProps, isDragActive = _a.isDragActive;
                    return (jsxs("div", __assign({}, getRootProps({
                        className: cx('ExcelControl-container', className)
                    }), { children: [jsx(TooltipWrapper, __assign({ placement: "top", container: container || (env === null || env === void 0 ? void 0 : env.getModalContainer), tooltip: maxLength === 1
                                    ? __('Excel.singleFile')
                                    : maxLength
                                        ? __('File.maxLength', {
                                            maxLength: maxLength,
                                            uploaded: files.length
                                        })
                                        : '', tooltipClassName: cx('ExcelControl-tooltip') }, { children: jsxs("div", __assign({ className: cx('ExcelControl-dropzone', {
                                        'is-disabled': disabled || isMaxLength,
                                        'is-empty': !files.length,
                                        'is-active': isDragActive
                                    }), onClick: disabled || isMaxLength ? undefined : _this.handleSelect }, { children: [jsx("input", __assign({}, getInputProps(), testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('input').getTestId())), isDragActive ? (jsx("div", __assign({ className: cx('ExcelControl-acceptTip') }, { children: jsx("p", { children: __('File.dragDrop') }) }))) : (jsx("div", __assign({ className: cx('ExcelControl-acceptTip') }, { children: jsx("p", { children: placeholder !== null && placeholder !== void 0 ? placeholder : __('Excel.placeholder') }) })))] })) })), files.length > 0 && (jsx("ul", __assign({ className: cx('ExcelControl-list') }, { children: files.map(function (file) { return (jsxs("li", { children: [jsx(TooltipWrapper, __assign({ placement: "top", container: container || (env === null || env === void 0 ? void 0 : env.getModalContainer), tooltipClassName: cx('ExcelControl-list-tooltip', file.state === 'error' && 'is-invalid'), tooltip: file.state === 'error' ? file.error : file.name }, { children: jsxs("div", __assign({ className: cx('ExcelControl-itemInfo', {
                                                    'is-invalid': file.state === 'error'
                                                }) }, { children: [jsx("span", __assign({ className: cx('ExcelControl-itemInfoIcon') }, { children: jsx(Icon, { icon: "file-excel", className: "icon" }) })), jsx("span", __assign({ className: cx('ExcelControl-itemInfoText') }, { children: file.name })), !disabled && (jsx("a", __assign({ "data-tooltip": __('Select.clear'), "data-position": "left", className: cx('ExcelControl-clear'), onClick: function (e) {
                                                            e.stopPropagation();
                                                            _this.removeFile(file);
                                                        } }, { children: jsx(Icon, { icon: "close", className: "icon" }) })))] })) })), file.state === 'pending' && (jsx("div", __assign({ className: cx('ExcelControl-progressInfo') }, { children: jsx("p", { children: __('Excel.parsing') }) })))] }, file.id)); }) })))] })));
                } }), "drop-zone") })));
    };
    ExcelControl.defaultProps = {
        allSheets: false,
        parseMode: 'object',
        includeEmpty: true,
        plainText: true,
        parseImage: false,
        imageDataURI: true
    };
    __decorate([
        autobind
    ], ExcelControl.prototype, "syncAutoFill", null);
    __decorate([
        autobind
    ], ExcelControl.prototype, "handleDrop", null);
    __decorate([
        autobind
    ], ExcelControl.prototype, "handleSelect", null);
    __decorate([
        autobind
    ], ExcelControl.prototype, "removeFile", null);
    __decorate([
        autobind
    ], ExcelControl.prototype, "updateFormValue", null);
    return ExcelControl;
}(React.PureComponent));
var ExcelControlRenderer = /** @class */ (function (_super) {
    __extends(ExcelControlRenderer, _super);
    function ExcelControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ExcelControlRenderer = __decorate([
        FormItem({
            type: 'input-excel'
        })
    ], ExcelControlRenderer);
    return ExcelControlRenderer;
}(ExcelControl));

export { ExcelControlRenderer, ExcelControl as default };
