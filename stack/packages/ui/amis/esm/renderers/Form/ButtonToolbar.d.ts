import React from 'react';
import { BaseSchema } from '../../Schema';
import { ActionSchema } from '../Action';
import { FormControlProps, FormBaseControlWithoutSize } from 'amis-core';
/**
 * Button Toolar 渲染器。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/button-toolbar
 */
export interface ButtonToolbarSchema extends BaseSchema, FormBaseControlWithoutSize {
    /**
     * 指定为按钮工具集合类型
     */
    type: 'button-toolbar';
    buttons: Array<ActionSchema>;
}
export interface ButtonToolbarProps extends FormControlProps, Omit<ButtonToolbarSchema, 'className' | 'descriptionClassName' | 'inputClassName'> {
}
export default class ButtonToolbar extends React.Component<ButtonToolbarProps, object> {
    static propsList: Array<string>;
    /**
     * 这个方法editor里要用作hack，所以不能删掉这个方法
     * @returns
     */
    renderButtons(): any[] | null;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class ButtonToolbarRenderer extends ButtonToolbar {
}
//# sourceMappingURL=ButtonToolbar.d.ts.map