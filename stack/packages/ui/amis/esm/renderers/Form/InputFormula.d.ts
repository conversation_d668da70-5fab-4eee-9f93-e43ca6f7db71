import React from 'react';
import { FormControlProps } from 'amis-core';
import type { FormBaseControlSchema, SchemaIcon } from '../../Schema';
import type { FuncGroup, VariableItem } from 'amis-ui/lib/components/formula/CodeEditor';
import type { FormulaPickerInputSettings } from 'amis-ui/lib/components/formula/Picker';
/**
 * InputFormula 公式编辑器
 * 文档：https://baidu.gitee.io/amis/zh-CN/components/form/input-formula
 */
export interface BaseInputFormulaControlSchema extends FormBaseControlSchema {
    /**
     * evalMode 即直接就是表达式，否则
     * 需要 ${这里面才是表达式}
     * 默认为 true
     */
    evalMode?: boolean;
    /**
     * 混合模式，意味着这个输入框既可以输入不同文本
     * 也可以输入公式。
     * 当输入公式时，值格式为 ${公式内容}
     * 其他内容当字符串。
     */
    mixedMode?: boolean;
    /**
     * 用于提示的变量集合，默认为空
     */
    variables: Array<VariableItem>;
    /**
     * 变量展现模式，可选值：'tabs' ｜ 'tree'
     */
    variableMode?: 'tabs' | 'tree';
    /**
     * 函数集合，默认不需要传，即  amis-formula 里面那个函数
     * 如果有扩充，则需要传。
     */
    functions: Array<FuncGroup>;
    /**
     * 编辑器标题
     */
    title?: string;
    /**
     * 顶部标题，默认为表达式
     */
    header: string;
    /**
     * 控件模式
     */
    inputMode?: 'button' | 'input-button' | 'input-group';
    /**
     * 外层input是否允许输入，否需要点击fx在弹窗中输入
     */
    allowInput?: boolean;
    /**
     * 按钮图标
     */
    icon?: SchemaIcon;
    /**
     * 按钮Label，inputMode为button时生效
     */
    btnLabel?: string;
    /**
     * 按钮样式
     */
    level?: 'info' | 'success' | 'warning' | 'danger' | 'link' | 'primary' | 'dark' | 'light';
    /**
     * 按钮大小
     */
    btnSize?: 'xs' | 'sm' | 'md' | 'lg';
    /**
     * 边框模式，全边框，还是半边框，或者没边框。
     */
    borderMode?: 'full' | 'half' | 'none';
    /**
     * 输入框占位符
     */
    placeholder?: string;
    /**
     * 变量面板CSS样式类名
     */
    variableClassName?: string;
    /**
     * 函数面板CSS样式类名
     */
    functionClassName?: string;
    /**
     * 当前输入项字段 name: 用于避免循环绑定自身导致无限渲染
     */
    selfVariableName?: string;
    /**
     * 输入框的类型
     */
    inputSettings?: FormulaPickerInputSettings;
}
export interface InputFormulaControlSchema extends BaseInputFormulaControlSchema {
    type: 'input-formula';
}
export interface InputFormulaProps extends FormControlProps, Omit<InputFormulaControlSchema, 'options' | 'inputClassName' | 'className' | 'descriptionClassName'> {
}
export declare class InputFormulaRenderer extends React.Component<InputFormulaProps> {
    static defaultProps: Pick<InputFormulaControlSchema, 'inputMode' | 'borderMode' | 'evalMode'>;
    ref: any;
    formulaRef(ref: any): void;
    validate(): any;
    render(): import("react/jsx-runtime").JSX.Element;
}
//# sourceMappingURL=InputFormula.d.ts.map