/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __rest, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { filter, isVisible, makeH<PERSON><PERSON><PERSON><PERSON>eeper, getWidthR<PERSON>, FormItemWrap, Renderer } from 'amis-core';
import { reaction } from 'mobx';

var ControlGroupRenderer = /** @class */ (function (_super) {
    __extends(ControlGroupRenderer, _super);
    function ControlGroupRenderer(props) {
        var _this = _super.call(this, props) || this;
        _this.renderInput = _this.renderInput.bind(_this);
        var body = props.body;
        if (Array.isArray(body)) {
            // 监听statusStore更新
            _this.reaction = reaction(function () {
                return body
                    .map(function (item) {
                    var _a;
                    var id = filter(item.id, props.data);
                    var name = filter(item.name, props.data);
                    return "".concat((_a = props.statusStore.visibleState[id]) !== null && _a !== void 0 ? _a : props.statusStore.visibleState[name]);
                })
                    .join('-');
            }, function () { return _this.forceUpdate(); });
        }
        return _this;
    }
    ControlGroupRenderer.prototype.componentWillUnmount = function () {
        var _a;
        (_a = this.reaction) === null || _a === void 0 ? void 0 : _a.call(this);
    };
    ControlGroupRenderer.prototype.renderControl = function (control, index, otherProps) {
        var _a = this.props, render = _a.render, disabled = _a.disabled, data = _a.data, mode = _a.mode, horizontal = _a.horizontal, formMode = _a.formMode, formHorizontal = _a.formHorizontal, subFormMode = _a.subFormMode, subFormHorizontal = _a.subFormHorizontal;
        if (!control) {
            return null;
        }
        var subSchema = control;
        return render("".concat(index), subSchema, __assign({ disabled: control.disabled || disabled, formMode: subFormMode || mode || formMode, formHorizontal: subFormHorizontal || horizontal || formHorizontal }, otherProps));
    };
    ControlGroupRenderer.prototype.renderVertical = function (props) {
        var _this = this;
        if (props === void 0) { props = this.props; }
        var body = props.body, className = props.className, style = props.style, cx = props.classnames, mode = props.mode, formMode = props.formMode, data = props.data, statusStore = props.statusStore;
        formMode = mode || formMode;
        if (!Array.isArray(body)) {
            return null;
        }
        return (jsx("div", __assign({ className: cx("Form-group Form-group--ver Form-group--".concat(formMode), className) }, { children: body.map(function (control, index) {
                var _a;
                if (!isVisible(control, data, statusStore)) {
                    return null;
                }
                return _this.renderControl(control, index, {
                    key: "".concat((_a = control.name) !== null && _a !== void 0 ? _a : '', "-").concat(index)
                });
            }) })));
    };
    ControlGroupRenderer.prototype.renderHorizontal = function (props) {
        var _this = this;
        if (props === void 0) { props = this.props; }
        var body = props.body, className = props.className, style = props.style, ns = props.classPrefix, cx = props.classnames, mode = props.mode, horizontal = props.horizontal, formMode = props.formMode, formHorizontal = props.formHorizontal, subFormMode = props.subFormMode, subFormHorizontal = props.subFormHorizontal, data = props.data, gap = props.gap, statusStore = props.statusStore;
        if (!Array.isArray(body)) {
            return null;
        }
        formMode = subFormMode || mode || formMode;
        var horizontalDeeper = subFormHorizontal ||
            horizontal ||
            (formHorizontal
                ? makeHorizontalDeeper(formHorizontal, body.filter(function (item) {
                    return (item === null || item === void 0 ? void 0 : item.mode) !== 'inline' &&
                        isVisible(item, data, statusStore);
                }).length)
                : undefined);
        return (jsx("div", __assign({ className: cx("Form-group Form-group--hor Form-group--".concat(formMode), gap ? "Form-group--".concat(gap) : '', className) }, { children: body.map(function (control, index) {
                var _a, _b;
                if (!isVisible(control, data, statusStore)) {
                    return null;
                }
                var controlMode = (control === null || control === void 0 ? void 0 : control.mode) || formMode;
                if (controlMode === 'inline' ||
                    // hidden 直接渲染，否则会有个空 Form-groupColumn 层
                    ((control === null || control === void 0 ? void 0 : control.type) &&
                        ['formula', 'hidden'].includes(control.type))) {
                    return _this.renderControl(control, index, {
                        key: "".concat((_a = control.name) !== null && _a !== void 0 ? _a : '', "-").concat(index),
                        className: cx(control.className, control.columnClassName)
                    });
                }
                var columnWidth = control.columnRatio ||
                    getWidthRate(control && control.columnClassName, true);
                return (jsx("div", __assign({ className: cx("".concat(ns, "Form-groupColumn"), columnWidth ? "".concat(ns, "Form-groupColumn--").concat(columnWidth) : '', control && control.columnClassName) }, { children: _this.renderControl(control, index, {
                        key: "".concat((_b = control.name) !== null && _b !== void 0 ? _b : '', "-").concat(index),
                        formHorizontal: horizontalDeeper,
                        formMode: controlMode
                    }) }), index));
            }) })));
    };
    ControlGroupRenderer.prototype.renderInput = function (props) {
        if (props === void 0) { props = this.props; }
        var direction = props.direction;
        return direction === 'vertical'
            ? this.renderVertical(props)
            : this.renderHorizontal(props);
    };
    ControlGroupRenderer.prototype.render = function () {
        var _a = this.props, label = _a.label, rest = __rest(_a, ["label"]);
        if (typeof label !== 'undefined') {
            return (jsx(FormItemWrap, __assign({}, rest, { sizeMutable: false, label: label, renderControl: this.renderInput })));
        }
        return this.renderInput();
    };
    ControlGroupRenderer = __decorate([
        Renderer({
            type: 'group'
        })
    ], ControlGroupRenderer);
    return ControlGroupRenderer;
}(React.Component));

export { ControlGroupRenderer };
