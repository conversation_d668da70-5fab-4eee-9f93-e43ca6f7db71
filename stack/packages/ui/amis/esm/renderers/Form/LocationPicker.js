/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx, Fragment, jsxs } from 'react/jsx-runtime';
import React from 'react';
import { resolveEventData, getVariable, filter, autobind, FormItem } from 'amis-core';
import { BaiduMapPicker, LocationPicker } from 'amis-ui';
import { supportStatic } from './StaticHoc.js';

var LocationControl = /** @class */ (function (_super) {
    __extends(LocationControl, _super);
    function LocationControl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.domRef = React.createRef();
        _this.state = {
            isOpened: false
        };
        return _this;
    }
    LocationControl.prototype.close = function () {
        this.setState({
            isOpened: false
        });
    };
    LocationControl.prototype.open = function () {
        this.setState({
            isOpened: true
        });
    };
    LocationControl.prototype.handleClick = function () {
        this.state.isOpened ? this.close() : this.open();
    };
    LocationControl.prototype.handleChange = function (value) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, dispatchEvent, onChange, dispatcher;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, dispatchEvent = _a.dispatchEvent, onChange = _a.onChange;
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, { value: value }))];
                    case 1:
                        dispatcher = _b.sent();
                        if (dispatcher === null || dispatcher === void 0 ? void 0 : dispatcher.prevented) {
                            return [2 /*return*/];
                        }
                        onChange(value);
                        return [2 /*return*/];
                }
            });
        });
    };
    LocationControl.prototype.getParent = function () {
        var _a;
        return (_a = this.domRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;
    };
    LocationControl.prototype.getTarget = function () {
        return this.domRef.current;
    };
    LocationControl.prototype.doAction = function (action, data, throwErrors) {
        var _a, _b, _c;
        var _d = this.props, resetValue = _d.resetValue, onChange = _d.onChange, formStore = _d.formStore, store = _d.store, name = _d.name;
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        switch (actionType) {
            case 'clear':
                onChange('');
                break;
            case 'reset':
                onChange === null || onChange === void 0 ? void 0 : onChange((_c = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue) !== null && _c !== void 0 ? _c : '');
                break;
        }
    };
    LocationControl.prototype.renderStatic = function (displayValue) {
        if (displayValue === void 0) { displayValue = '-'; }
        var _a = this.props, cx = _a.classnames, value = _a.value, staticSchema = _a.staticSchema, ak = _a.ak, coordinatesType = _a.coordinatesType, _b = _a.hideViewControl, hideViewControl = _b === void 0 ? false : _b, mobileUI = _a.mobileUI;
        var __ = this.props.translate;
        if (!value) {
            return jsx(Fragment, { children: displayValue });
        }
        return (jsx("div", __assign({ className: this.props.classnames('LocationControl', {
                'is-mobile': mobileUI
            }), ref: this.domRef }, { children: (staticSchema === null || staticSchema === void 0 ? void 0 : staticSchema.embed) ? (jsxs(Fragment, { children: [staticSchema.showAddress === false ? null : (jsx("div", __assign({ className: "mb-2" }, { children: value.address }))), jsx(BaiduMapPicker, { ak: ak, value: value, coordinatesType: coordinatesType, autoSelectCurrentLoc: false, onlySelectCurrentLoc: true, showSug: false, showGeoLoc: staticSchema.showGeoLoc, mapStyle: staticSchema.mapStyle, hideViewControl: hideViewControl })] })) : (jsx("span", { children: value.address })) })));
    };
    LocationControl.prototype.render = function () {
        var _a = this.props, style = _a.style, env = _a.env, mobileUI = _a.mobileUI;
        var ak = filter(this.props.ak, this.props.data) || env.locationPickerAK;
        return (jsx("div", __assign({ className: this.props.classnames('LocationControl', {
                'is-mobile': mobileUI
            }) }, { children: jsx(LocationPicker, __assign({}, this.props, { placeholder: this.props.placeholder, ak: filter(this.props.ak, this.props.data), onChange: this.handleChange })) })));
    };
    LocationControl.defaultProps = {
        vendor: 'baidu',
        coordinatesType: 'bd09'
    };
    __decorate([
        autobind
    ], LocationControl.prototype, "close", null);
    __decorate([
        autobind
    ], LocationControl.prototype, "open", null);
    __decorate([
        autobind
    ], LocationControl.prototype, "handleClick", null);
    __decorate([
        autobind
    ], LocationControl.prototype, "handleChange", null);
    __decorate([
        autobind
    ], LocationControl.prototype, "getParent", null);
    __decorate([
        autobind
    ], LocationControl.prototype, "getTarget", null);
    __decorate([
        supportStatic()
    ], LocationControl.prototype, "render", null);
    return LocationControl;
}(React.Component));
var LocationRenderer = /** @class */ (function (_super) {
    __extends(LocationRenderer, _super);
    function LocationRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    LocationRenderer = __decorate([
        FormItem({
            type: 'location-picker'
        })
    ], LocationRenderer);
    return LocationRenderer;
}(LocationControl));

export { LocationControl, LocationRenderer };
