/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import { getVariable, resolveEventData, formateCheckThemeCss, setThemeClassName, CustomStyle, autobind, FormItem } from 'amis-core';
import cx from 'classnames';
import { Checkbox, withBadge } from 'amis-ui';
import { supportStatic } from './StaticHoc.js';

var CheckboxControl = /** @class */ (function (_super) {
    __extends(CheckboxControl, _super);
    function CheckboxControl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CheckboxControl.prototype.doAction = function (action, data, throwErrors) {
        var _a, _b;
        var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        if (actionType === 'clear') {
            onChange('');
        }
        else if (actionType === 'reset') {
            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
            onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
        }
    };
    CheckboxControl.prototype.dispatchChangeEvent = function (eventData) {
        if (eventData === void 0) { eventData = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, dispatchEvent, onChange, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, dispatchEvent = _a.dispatchEvent, onChange = _a.onChange;
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, { value: eventData }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onChange && onChange(eventData);
                        return [2 /*return*/];
                }
            });
        });
    };
    CheckboxControl.prototype.renderStatic = function () {
        var _a = this.props, value = _a.value, trueValue = _a.trueValue, falseValue = _a.falseValue, option = _a.option, render = _a.render, partial = _a.partial, optionType = _a.optionType, checked = _a.checked, labelClassName = _a.labelClassName;
        return (jsx(Checkbox, __assign({ inline: true, value: value || '', trueValue: trueValue, falseValue: falseValue, disabled: true, partial: partial, optionType: optionType, checked: checked, labelClassName: labelClassName }, { children: option ? render('option', option) : null })));
    };
    CheckboxControl.prototype.render = function () {
        var _this = this;
        var _a = this.props, className = _a.className, style = _a.style, value = _a.value, trueValue = _a.trueValue, falseValue = _a.falseValue, option = _a.option, onChange = _a.onChange, disabled = _a.disabled, render = _a.render, partial = _a.partial, optionType = _a.optionType, checked = _a.checked, labelClassName = _a.labelClassName, testIdBuilder = _a.testIdBuilder, ns = _a.classPrefix, id = _a.id, env = _a.env, themeCss = _a.themeCss;
        var css = formateCheckThemeCss(themeCss, 'checkbox');
        return (jsxs("div", __assign({ className: cx("".concat(ns, "CheckboxControl"), className, setThemeClassName(__assign(__assign({}, this.props), { name: [
                    'checkboxControlClassName',
                    'checkboxControlCheckedClassName',
                    'checkboxClassName',
                    'checkboxCheckedClassName',
                    'checkboxInnerClassName',
                    'checkboxShowClassName'
                ], id: id, themeCss: css }))) }, { children: [jsx(Checkbox, __assign({ inline: true, value: value || '', trueValue: trueValue, falseValue: falseValue, disabled: disabled, onChange: function (value) { return _this.dispatchChangeEvent(value); }, partial: partial, optionType: optionType, checked: checked, labelClassName: labelClassName, testIdBuilder: testIdBuilder, className: "first last" }, { children: option ? render('option', option) : null })), jsx(CustomStyle, __assign({}, this.props, { config: {
                        themeCss: css,
                        classNames: [
                            {
                                key: 'checkboxControlClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.checked):not(.disabled)")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled):not(.checked)")
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled:not(.checked)")
                                    }
                                }
                            },
                            {
                                key: 'checkboxControlCheckedClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox.checked:not(.disabled)")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox.checked:not(.disabled)")
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.checked.disabled")
                                    }
                                }
                            },
                            {
                                key: 'checkboxClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.checked):not(.disabled) > i")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled):not(.checked)"),
                                        inner: '> i'
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled:not(.checked) > i")
                                    }
                                }
                            },
                            {
                                key: 'checkboxCheckedClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.disabled) > i")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled)"),
                                        inner: '> i'
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled > i")
                                    }
                                }
                            },
                            {
                                key: 'checkboxInnerClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.disabled) > i .icon")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled)"),
                                        inner: '> i .icon'
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled > i .icon")
                                    }
                                }
                            },
                            {
                                key: 'checkboxShowClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox > i")
                                    }
                                }
                            }
                        ],
                        id: id
                    }, env: env }))] })));
    };
    CheckboxControl.defaultProps = {
        trueValue: true,
        falseValue: false
    };
    __decorate([
        autobind
    ], CheckboxControl.prototype, "dispatchChangeEvent", null);
    __decorate([
        supportStatic()
    ], CheckboxControl.prototype, "render", null);
    return CheckboxControl;
}(React.Component));
// @ts-ignore
var CheckboxControlRenderer = /** @class */ (function (_super) {
    __extends(CheckboxControlRenderer, _super);
    function CheckboxControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CheckboxControlRenderer = __decorate([
        withBadge,
        FormItem({
            type: 'checkbox',
            sizeMutable: false,
            thin: true
        })
    ], CheckboxControlRenderer);
    return CheckboxControlRenderer;
}(CheckboxControl));

export { CheckboxControlRenderer, CheckboxControl as default };
