/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __awaiter, __generator, __decorate } from 'tslib';
import { jsxs, jsx, Fragment } from 'react/jsx-runtime';
import React from 'react';
import cx from 'classnames';
import { matchSorter } from 'match-sorter';
import { autobind, FormItem } from 'amis-core';
import { Icon, SearchBox, Spinner, Modal, Button } from 'amis-ui';
import debounce from 'lodash/debounce';
import * as IconSelectStore from './IconSelectStore.js';
import { svgIcons, refreshIconList } from './IconSelectStore.js';

/**
 * 新图标选择器
 */
var IconSelectControl = /** @class */ (function (_super) {
    __extends(IconSelectControl, _super);
    function IconSelectControl(props) {
        var _this = _super.call(this, props) || this;
        _this.state = {
            activeTypeIndex: 0,
            showModal: false,
            tmpCheckIconId: null,
            searchValue: '',
            isRefreshLoading: false
        };
        _this.handleSearchValueChange = debounce(_this.handleSearchValueChange.bind(_this), 300);
        return _this;
    }
    IconSelectControl.prototype.getSvgName = function (value) {
        var _a;
        if (typeof value === 'string') {
            return ((_a = /data-name="(.*?)"/.exec(value)) === null || _a === void 0 ? void 0 : _a[1]) || '';
        }
        else {
            return (value === null || value === void 0 ? void 0 : value.name) || (value === null || value === void 0 ? void 0 : value.id) || '';
        }
    };
    IconSelectControl.prototype.getSvgId = function (value) {
        var _a;
        if (typeof value === 'string') {
            return ((_a = /data-id="(.*?)"/.exec(value)) === null || _a === void 0 ? void 0 : _a[1]) || '';
        }
        else {
            return (value === null || value === void 0 ? void 0 : value.id) || '';
        }
    };
    IconSelectControl.prototype.getValueBySvg = function (svg) {
        var _a;
        if (!svg) {
            return null;
        }
        if (typeof svg !== 'string') {
            return __assign(__assign({}, svg), { svg: (_a = svg.svg) === null || _a === void 0 ? void 0 : _a.replace(/'/g, '') });
        }
        var svgName = this.getSvgName(svg);
        var svgId = this.getSvgId(svg);
        return { name: svgName, id: svgId, svg: svg.replace(/'/g, '') };
    };
    IconSelectControl.prototype.handleClick = function () {
        if (this.props.disabled) {
            return;
        }
        this.toggleModel(true);
    };
    IconSelectControl.prototype.handleClear = function (e) {
        e.preventDefault();
        e.stopPropagation();
        this.props.onChange && this.props.onChange('');
    };
    IconSelectControl.prototype.renderInputArea = function () {
        var _a = this.props, ns = _a.classPrefix, disabled = _a.disabled, value = _a.value, placeholder = _a.placeholder, clearable = _a.clearable;
        var svg = this.getValueBySvg(value);
        return (jsxs("div", __assign({ className: cx("".concat(ns, "IconSelectControl-input-area")) }, { children: [jsx("div", __assign({ className: cx("".concat(ns, "IconSelectControl-input-icon-show")) }, { children: jsx(Icon, { icon: svg === null || svg === void 0 ? void 0 : svg.svg, className: "icon" }) })), jsx("span", __assign({ className: cx("".concat(ns, "IconSelectControl-input-icon-id")) }, { children: svg === null || svg === void 0 ? void 0 : svg.name })), clearable && !disabled && svg ? (jsx("a", __assign({ onClick: this.handleClear, className: cx("".concat(ns, "IconSelectControl-clear")) }, { children: jsx(Icon, { icon: "input-clear", className: "icon" }) }))) : null, (!svg && placeholder && (jsx("span", __assign({ className: cx("".concat(ns, "IconSelectControl-input-icon-placeholder")) }, { children: placeholder })))) ||
                    null] })));
    };
    IconSelectControl.prototype.handleIconTypeClick = function (item, index) {
        this.setState({
            activeTypeIndex: index
        });
    };
    IconSelectControl.prototype.renderIconTypes = function () {
        var _this = this;
        var ns = this.props.classPrefix;
        var types = svgIcons.map(function (item) { return ({
            id: item.groupId,
            label: item.name
        }); });
        return (jsx("ul", __assign({ className: cx("".concat(ns, "IconSelectControl-type-list")) }, { children: types.map(function (item, index) { return (jsx("li", __assign({ onClick: function () { return _this.handleIconTypeClick(item, index); }, className: cx({
                    active: index === _this.state.activeTypeIndex
                }) }, { children: item.label }), item.id)); }) })));
    };
    IconSelectControl.prototype.handleConfirm = function () {
        var checkedIcon = this.state.tmpCheckIconId;
        if (this.props.returnSvg) {
            var svg = (checkedIcon && checkedIcon.svg) || '';
            svg = svg.replace(/<svg/, "<svg data-name=\"".concat(checkedIcon === null || checkedIcon === void 0 ? void 0 : checkedIcon.name, "\" data-id=\"").concat(checkedIcon === null || checkedIcon === void 0 ? void 0 : checkedIcon.id, "\""));
            if (this.props.noSize) {
                svg = svg.replace(/width=".*?"/, '').replace(/height=".*?"/, '');
            }
            this.props.onChange && this.props.onChange(svg);
        }
        else {
            this.props.onChange &&
                this.props.onChange(checkedIcon && checkedIcon.id
                    ? __assign(__assign({}, checkedIcon), { id: 'svg-' + checkedIcon.id }) : '');
        }
        this.toggleModel(false);
    };
    IconSelectControl.prototype.handleLocalUpload = function (icon) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.props.onChange && this.props.onChange(icon);
                this.toggleModel(false);
                return [2 /*return*/];
            });
        });
    };
    IconSelectControl.prototype.handleClickIconInModal = function (icon) {
        var _a;
        this.setState({
            tmpCheckIconId: (icon === null || icon === void 0 ? void 0 : icon.id) === ((_a = this.state.tmpCheckIconId) === null || _a === void 0 ? void 0 : _a.id) ? null : icon
        });
    };
    IconSelectControl.prototype.renderIconList = function (icons) {
        var _this = this;
        var _a = this.props, ns = _a.classPrefix, noDataTip = _a.noDataTip, __ = _a.translate;
        if (!icons || !icons.length) {
            return (jsx("p", __assign({ className: cx("".concat(ns, "IconSelectControl-icon-list-empty")) }, { children: __(noDataTip) })));
        }
        return (jsx("ul", __assign({ className: cx("".concat(ns, "IconSelectControl-icon-list")) }, { children: icons.map(function (item, index) {
                var _a;
                return (jsx("li", { children: jsxs("div", __assign({ className: cx("".concat(ns, "IconSelectControl-icon-list-item"), {
                            active: ((_a = _this.state.tmpCheckIconId) === null || _a === void 0 ? void 0 : _a.id) === item.id
                        }), onClick: function () { return _this.handleClickIconInModal(item); } }, { children: [jsx("svg", { children: jsx("use", { xlinkHref: "#".concat(item.id) }) }), jsx("div", __assign({ className: cx("".concat(ns, "IconSelectControl-icon-list-item-info")) }, { children: jsx("p", __assign({ className: cx("".concat(ns, "IconSelectControl-icon-list-item-info-name")) }, { children: item.name })) }))] })) }, item.id));
            }) })));
    };
    IconSelectControl.prototype.handleSearchValueChange = function (e) {
        this.setState({
            searchValue: e
        });
    };
    IconSelectControl.prototype.handleRefreshIconList = function () {
        return __awaiter(this, void 0, void 0, function () {
            var refreshIconList$1, e_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        refreshIconList$1 = refreshIconList;
                        if (!(refreshIconList$1 && typeof refreshIconList$1 === 'function')) return [3 /*break*/, 5];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, 4, 5]);
                        this.setState({
                            isRefreshLoading: true
                        });
                        return [4 /*yield*/, Promise.resolve(refreshIconList$1())];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 3:
                        e_1 = _a.sent();
                        console.error(e_1);
                        return [3 /*break*/, 5];
                    case 4:
                        this.setState({
                            isRefreshLoading: false
                        });
                        return [7 /*endfinally*/];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    IconSelectControl.prototype.renderModalContent = function () {
        var _a = this.props, render = _a.render, ns = _a.classPrefix, loadingConfig = _a.loadingConfig, funcSchema = _a.funcSchema, FuncCom = _a.funcCom;
        var icons = this.getIconsByType();
        var inputValue = this.state.searchValue;
        var filteredIcons = inputValue
            ? matchSorter(icons, inputValue, {
                keys: ['name'],
                threshold: matchSorter.rankings.CONTAINS
            })
            : icons;
        return (jsxs(Fragment, { children: [jsx(SearchBox, { className: cx("".concat(ns, "IconSelectControl-Modal-search")), mini: false, clearable: true, onChange: this.handleSearchValueChange }), (refreshIconList &&
                    render('refresh-btn', {
                        type: 'button',
                        icon: 'fa fa-refresh'
                    }, {
                        className: cx("".concat(ns, "IconSelectControl-Modal-refresh")),
                        onClick: this.handleRefreshIconList
                    })) ||
                    null, FuncCom ? (jsx("div", __assign({ className: cx("".concat(ns, "IconSelectControl-Modal-func")) }, { children: jsx(FuncCom, { onUpload: this.handleLocalUpload }) }))) : null, jsxs("div", __assign({ className: cx("".concat(ns, "IconSelectControl-Modal-content")) }, { children: [jsx(Spinner, { size: "lg", loadingConfig: loadingConfig, overlay: true, show: this.state.isRefreshLoading }, "info"), jsx("div", __assign({ className: cx("".concat(ns, "IconSelectControl-Modal-content-aside")) }, { children: this.renderIconTypes() })), jsx("div", __assign({ className: cx("".concat(ns, "IconSelectControl-Modal-content-main")) }, { children: this.renderIconList(filteredIcons) }))] }))] }));
    };
    IconSelectControl.prototype.getIconsByType = function () {
        return (((IconSelectStore === null || IconSelectStore === void 0 ? void 0 : svgIcons.length) &&
            svgIcons[this.state.activeTypeIndex]
                .children) ||
            []);
    };
    IconSelectControl.prototype.toggleModel = function (isShow) {
        var valueTemp = this.props.value;
        var value = typeof valueTemp === 'string' ? this.getValueBySvg(valueTemp) : valueTemp;
        if (isShow === undefined) {
            this.setState({
                showModal: !this.state.showModal,
                searchValue: ''
            });
            return;
        }
        this.setState({
            showModal: isShow,
            // tmpCheckIconId: isShow ? String(value).replace('svg-', '') : '',
            tmpCheckIconId: isShow && (value === null || value === void 0 ? void 0 : value.id)
                ? __assign(__assign({}, value), { id: String(value.id).replace(/^svg-/, '') }) : null,
            searchValue: ''
        });
    };
    IconSelectControl.prototype.render = function () {
        var _this = this;
        var _a = this.props, className = _a.className, style = _a.style, ns = _a.classPrefix, disabled = _a.disabled, __ = _a.translate;
        return (jsxs("div", __assign({ className: cx(className, "".concat(ns, "IconSelectControl"), {
                'is-focused': this.state.showModal,
                'is-disabled': disabled
            }) }, { children: [jsx("div", __assign({ className: cx("".concat(ns, "IconSelectControl-input")), onClick: this.handleClick }, { children: this.renderInputArea() })), jsxs(Modal, __assign({ show: this.state.showModal, closeOnOutside: true, closeOnEsc: true, size: "lg", overlay: true, onHide: function () { return _this.toggleModel(false); } }, { children: [jsx(Modal.Header, __assign({ onClose: function () { return _this.toggleModel(false); } }, { children: __('IconSelect.choice') })), jsx(Modal.Body, { children: this.renderModalContent() }), jsxs(Modal.Footer, { children: [jsx(Button, __assign({ type: "button", className: "m-l", onClick: function () { return _this.toggleModel(false); } }, { children: __('cancel') })), jsx(Button, __assign({ type: "button", level: "primary", onClick: this.handleConfirm }, { children: __('confirm') }))] })] }))] })));
    };
    IconSelectControl.defaultProps = {
        noDataTip: 'placeholder.noData',
        clearable: true
    };
    __decorate([
        autobind
    ], IconSelectControl.prototype, "handleClick", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "handleClear", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "renderInputArea", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "handleIconTypeClick", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "renderIconTypes", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "handleConfirm", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "handleLocalUpload", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "renderIconList", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "handleRefreshIconList", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "renderModalContent", null);
    __decorate([
        autobind
    ], IconSelectControl.prototype, "toggleModel", null);
    return IconSelectControl;
}(React.PureComponent));
var IconSelectControlRenderer = /** @class */ (function (_super) {
    __extends(IconSelectControlRenderer, _super);
    function IconSelectControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    IconSelectControlRenderer = __decorate([
        FormItem({
            type: 'icon-select'
        })
    ], IconSelectControlRenderer);
    return IconSelectControlRenderer;
}(IconSelectControl));

export { IconSelectControlRenderer, IconSelectControl as default };
