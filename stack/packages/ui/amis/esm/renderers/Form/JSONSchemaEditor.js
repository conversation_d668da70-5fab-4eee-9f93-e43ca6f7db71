/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __rest, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import pick from 'lodash/pick';
import { isObject, autobind, FormItem } from 'amis-core';
import { JSONSchemaEditor } from 'amis-ui';
import { schemaEditorItemPlaceholder } from 'amis-ui/lib/components/schema-editor/Common';

var JSONSchemaEditorControl = /** @class */ (function (_super) {
    __extends(JSONSchemaEditorControl, _super);
    function JSONSchemaEditorControl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    JSONSchemaEditorControl.prototype.normalizePlaceholder = function () {
        var placeholder = this.props.placeholder;
        if (isObject(placeholder)) {
            return __assign(__assign({}, schemaEditorItemPlaceholder), pick(placeholder, [
                'key',
                'title',
                'description',
                'default',
                'empty'
            ]));
        }
        return schemaEditorItemPlaceholder;
    };
    JSONSchemaEditorControl.prototype.renderModalProps = function (value, onChange) {
        var _a = this.props, render = _a.render, advancedSettings = _a.advancedSettings;
        var fields = (advancedSettings === null || advancedSettings === void 0 ? void 0 : advancedSettings[value === null || value === void 0 ? void 0 : value.type]) || [];
        return render("modal", {
            type: 'form',
            wrapWithPanel: false,
            body: fields,
            submitOnChange: true
        }, {
            data: value,
            onSubmit: function (value) { return onChange(value); }
        });
    };
    JSONSchemaEditorControl.prototype.render = function () {
        var _a = this.props, enableAdvancedSetting = _a.enableAdvancedSetting, mobileUI = _a.mobileUI, env = _a.env, rest = __rest(_a, ["enableAdvancedSetting", "mobileUI", "env"]);
        return (jsx(JSONSchemaEditor, __assign({}, rest, { mobileUI: mobileUI, placeholder: this.normalizePlaceholder(), enableAdvancedSetting: enableAdvancedSetting, renderModalProps: this.renderModalProps, popOverContainer: mobileUI
                ? env === null || env === void 0 ? void 0 : env.getModalContainer
                : rest.popOverContainer || env.getModalContainer })));
    };
    JSONSchemaEditorControl.defaultProps = {
        enableAdvancedSetting: false,
        placeholder: schemaEditorItemPlaceholder
    };
    __decorate([
        autobind
    ], JSONSchemaEditorControl.prototype, "renderModalProps", null);
    return JSONSchemaEditorControl;
}(React.PureComponent));
var JSONSchemaEditorRenderer = /** @class */ (function (_super) {
    __extends(JSONSchemaEditorRenderer, _super);
    function JSONSchemaEditorRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    JSONSchemaEditorRenderer = __decorate([
        FormItem({
            type: 'json-schema-editor'
        })
    ], JSONSchemaEditorRenderer);
    return JSONSchemaEditorRenderer;
}(JSONSchemaEditorControl));

export { JSONSchemaEditorRenderer, JSONSchemaEditorControl as default };
