import React from 'react';
import { FormControlProps } from 'amis-core';
import type { PresetColor } from 'amis-ui';
import { FormBaseControlSchema } from '../../Schema';
export declare const ColorPicker: React.LazyExoticComponent<React.ComponentType<any>>;
/**
 * Color 颜色选择框
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/color
 */
export interface InputColorControlSchema extends FormBaseControlSchema {
    /**
     * 指定为颜色选择框
     */
    type: 'input-color';
    /**
     * 是否显示清除按钮
     */
    clearable?: boolean;
    /**
     * 颜色格式
     */
    format?: 'hex' | 'hexa' | 'rgb' | 'rgba' | 'hsl';
    /**
     * 选中颜色后是否关闭弹出层。
     */
    closeOnSelect?: boolean;
    /**
     * 预设颜色，用户可以直接从预设中选。
     */
    presetColors?: Array<PresetColor>;
    /**
     * 是否允许用户输入颜色。
     */
    allowCustomColor?: boolean;
    /**
     * 弹窗容器选择器
     */
    popOverContainerSelector?: string;
}
export interface ColorProps extends FormControlProps, Omit<InputColorControlSchema, 'type' | 'className' | 'descriptionClassName' | 'inputClassName'> {
}
export interface ColorControlState {
    open: boolean;
}
export default class ColorControl extends React.PureComponent<ColorProps, ColorControlState> {
    static defaultProps: Partial<ColorProps>;
    state: ColorControlState;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class ColorControlRenderer extends ColorControl {
}
//# sourceMappingURL=InputColor.d.ts.map