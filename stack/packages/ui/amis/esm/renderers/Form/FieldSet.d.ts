import React from 'react';
import { RendererProps } from 'amis-core';
import { SchemaCollection, SchemaTpl } from '../../Schema';
import { BaseCollapseSchema } from '../Collapse';
import { FormBaseControlWithoutSize, BaseSchemaWithoutType } from 'amis-core';
import type { FormHorizontal } from 'amis-core';
/**
 * FieldSet 表单项集合
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/fieldset
 */
export interface FieldSetControlSchema extends FormBaseControlWithoutSize, BaseSchemaWithoutType, BaseCollapseSchema {
    /**
     * 指定为表单项集合
     */
    type: 'fieldset' | 'fieldSet';
    /**
     * 标题展示位置
     */
    titlePosition: 'top' | 'bottom';
    /**
     * 是否可折叠
     */
    collapsable?: boolean;
    /**
     * 默认是否折叠
     */
    collapsed?: boolean;
    /**
     * 内容区域
     */
    body?: SchemaCollection;
    /**
     * 标题
     */
    title?: SchemaTpl;
    /**
     * 收起的标题
     */
    collapseTitle?: SchemaTpl;
    /**
     * 点开时才加载内容
     */
    mountOnEnter?: boolean;
    /**
     * 卡片隐藏就销毁内容。
     */
    unmountOnExit?: boolean;
    /**
     * 配置子表单项默认的展示方式。
     */
    subFormMode?: 'normal' | 'inline' | 'horizontal';
    /**
     * 如果是水平排版，这个属性可以细化水平排版的左右宽度占比。
     */
    subFormHorizontal?: FormHorizontal;
}
export interface FieldSetProps extends RendererProps, Omit<FieldSetControlSchema, 'type' | 'className' | 'descriptionClassName' | 'inputClassName'> {
}
export default class FieldSetControl extends React.Component<FieldSetProps, any> {
    constructor(props: FieldSetProps);
    static defaultProps: {
        titlePosition: string;
        headingClassName: string;
        collapsable: boolean;
    };
    static propsList: Array<string>;
    renderBody(): JSX.Element;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class FieldSetRenderer extends FieldSetControl {
}
//# sourceMappingURL=FieldSet.d.ts.map