/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __rest, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { Renderer } from 'amis-core';
import Collapse from '../Collapse.js';

var FieldSetControl = /** @class */ (function (_super) {
    __extends(FieldSetControl, _super);
    function FieldSetControl(props) {
        var _this = _super.call(this, props) || this;
        _this.renderBody = _this.renderBody.bind(_this);
        return _this;
    }
    FieldSetControl.prototype.renderBody = function () {
        var _a = this.props, body = _a.body, collapsable = _a.collapsable, horizontal = _a.horizontal, render = _a.render, mode = _a.mode, formMode = _a.formMode, cx = _a.classnames, store = _a.store, formClassName = _a.formClassName, disabled = _a.disabled, formHorizontal = _a.formHorizontal, subFormMode = _a.subFormMode, subFormHorizontal = _a.subFormHorizontal;
        var props = {
            render: render,
            disabled: disabled,
            formMode: subFormMode || formMode,
            formHorizontal: subFormHorizontal || formHorizontal
        };
        mode && (props.mode = mode);
        horizontal && (props.horizontal = horizontal);
        return (jsx("div", __assign({ className: cx("Form--".concat(props.mode || formMode || 'normal'), formClassName) }, { children: body ? render('body', body, props) : null })));
    };
    FieldSetControl.prototype.render = function () {
        var _a = this.props, controls = _a.controls, className = _a.className, mode = _a.mode, body = _a.body, rest = __rest(_a, ["controls", "className", "mode", "body"]);
        return (jsx(Collapse, __assign({}, rest, { body: body, className: className, children: this.renderBody, wrapperComponent: "fieldset", headingComponent: rest.titlePosition === 'bottom' ? 'div' : 'legend' })));
    };
    FieldSetControl.defaultProps = {
        titlePosition: 'top',
        headingClassName: '',
        collapsable: false
    };
    FieldSetControl.propsList = [
        'collapsable',
        'collapsed',
        'collapseTitle',
        'titlePosition',
        'collapseTitle'
    ];
    return FieldSetControl;
}(React.Component));
var FieldSetRenderer = /** @class */ (function (_super) {
    __extends(FieldSetRenderer, _super);
    function FieldSetRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    FieldSetRenderer = __decorate([
        Renderer({
            type: 'fieldset',
            weight: -100,
            name: 'fieldset'
        })
    ], FieldSetRenderer);
    return FieldSetRenderer;
}(FieldSetControl));

export { FieldSetRenderer, FieldSetControl as default };
