/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx, Fragment, jsxs } from 'react/jsx-runtime';
import React from 'react';
import cx from 'classnames';
import { Radios } from 'amis-ui';
import { getVariable, resolveEventData, filter, formateCheckThemeCss, setThemeClassName, CustomStyle, autobind, OptionsControl } from 'amis-core';
import { supportStatic } from './StaticHoc.js';

var RadiosControl = /** @class */ (function (_super) {
    __extends(RadiosControl, _super);
    function RadiosControl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    RadiosControl.prototype.doAction = function (action, data, throwErrors) {
        var _a, _b;
        var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        if (actionType === 'clear') {
            onChange === null || onChange === void 0 ? void 0 : onChange('');
        }
        else if (actionType === 'reset') {
            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
            onChange === null || onChange === void 0 ? void 0 : onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
        }
    };
    RadiosControl.prototype.handleChange = function (option) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, joinValues, extractValue, valueField, onChange, dispatchEvent, options, selectedOptions, value, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, joinValues = _a.joinValues, extractValue = _a.extractValue, valueField = _a.valueField, onChange = _a.onChange, dispatchEvent = _a.dispatchEvent, options = _a.options, selectedOptions = _a.selectedOptions;
                        value = option;
                        if (option && (joinValues || extractValue)) {
                            value = option[valueField || 'value'];
                        }
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, {
                                value: value,
                                options: options,
                                items: options,
                                selectedItems: option
                            }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onChange && onChange(value);
                        return [2 /*return*/];
                }
            });
        });
    };
    RadiosControl.prototype.reload = function (subpath, query) {
        var reload = this.props.reloadOptions;
        reload && reload(subpath, query);
    };
    RadiosControl.prototype.renderLabel = function (option, _a) {
        var labelField = _a.labelField;
        var data = this.props.data;
        var label = option[labelField || 'label'];
        return jsx(Fragment, { children: typeof label === 'string' ? filter(label, data) : "".concat(label) });
    };
    RadiosControl.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, ns = _a.classPrefix, value = _a.value, onChange = _a.onChange, disabled = _a.disabled, joinValues = _a.joinValues, extractValue = _a.extractValue, delimiter = _a.delimiter, placeholder = _a.placeholder, options = _a.options, _b = _a.inline, inline = _b === void 0 ? true : _b, formMode = _a.formMode, columnsCount = _a.columnsCount, classPrefix = _a.classPrefix, itemClassName = _a.itemClassName, labelClassName = _a.labelClassName, optionClassName = _a.optionClassName, labelField = _a.labelField, valueField = _a.valueField, data = _a.data, __ = _a.translate, optionType = _a.optionType, level = _a.level, testIdBuilder = _a.testIdBuilder, themeCss = _a.themeCss, id = _a.id, env = _a.env;
        var css = formateCheckThemeCss(themeCss, 'radios');
        return (jsxs(Fragment, { children: [jsx(Radios, { inline: inline || formMode === 'inline', className: cx("".concat(ns, "RadiosControl"), className, setThemeClassName(__assign(__assign({}, this.props), { name: [
                            'radiosControlClassName',
                            'radiosControlCheckedClassName',
                            'radiosClassName',
                            'radiosCheckedClassName',
                            'radiosCheckedInnerClassName',
                            'radiosShowClassName'
                        ], id: id, themeCss: css }))), value: typeof value === 'undefined' || value === null ? '' : value, disabled: disabled, onChange: this.handleChange, joinValues: joinValues, extractValue: extractValue, delimiter: delimiter, 
                    /** 兼容一下错误的用法 */
                    labelClassName: optionClassName !== null && optionClassName !== void 0 ? optionClassName : labelClassName, labelField: labelField, valueField: valueField, placeholder: __(placeholder), options: options, renderLabel: this.renderLabel, columnsCount: columnsCount, classPrefix: classPrefix, itemClassName: itemClassName, optionType: optionType, level: level, testIdBuilder: testIdBuilder }), jsx(CustomStyle, __assign({}, this.props, { config: {
                        themeCss: css,
                        classNames: [
                            {
                                key: 'radiosControlClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.checked):not(.disabled)")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled):not(.checked)")
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled:not(.checked)")
                                    }
                                }
                            },
                            {
                                key: 'radiosControlCheckedClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox.checked:not(.disabled)")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox.checked:not(.disabled)")
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.checked.disabled")
                                    }
                                }
                            },
                            {
                                key: 'radiosClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.checked):not(.disabled) > i")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled):not(.checked)"),
                                        inner: '> i'
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled:not(.checked) > i")
                                    }
                                }
                            },
                            {
                                key: 'radiosCheckedClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.disabled) > i")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled)"),
                                        inner: '> i'
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled > i")
                                    }
                                }
                            },
                            {
                                key: 'radiosCheckedInnerClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox:not(.disabled) > i .icon")
                                    },
                                    hover: {
                                        suf: " .".concat(ns, "Checkbox:not(.disabled)"),
                                        inner: '> i .icon'
                                    },
                                    disabled: {
                                        inner: ".".concat(ns, "Checkbox.disabled > i:before")
                                    }
                                }
                            },
                            {
                                key: 'radiosShowClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Checkbox > i")
                                    }
                                }
                            }
                        ],
                        id: id
                    }, env: env }))] }));
    };
    RadiosControl.defaultProps = {
        columnsCount: 1
    };
    __decorate([
        autobind
    ], RadiosControl.prototype, "handleChange", null);
    __decorate([
        autobind
    ], RadiosControl.prototype, "renderLabel", null);
    __decorate([
        supportStatic()
    ], RadiosControl.prototype, "render", null);
    return RadiosControl;
}(React.Component));
var RadiosControlRenderer = /** @class */ (function (_super) {
    __extends(RadiosControlRenderer, _super);
    function RadiosControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    RadiosControlRenderer.defaultProps = {
        multiple: false,
        inline: true
    };
    RadiosControlRenderer = __decorate([
        OptionsControl({
            type: 'radios',
            sizeMutable: false,
            thin: true
        })
    ], RadiosControlRenderer);
    return RadiosControlRenderer;
}(RadiosControl));

export { RadiosControlRenderer, RadiosControl as default };
