/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { getVariable, resolveEventData, autobind, toNumber, filter, FormItem } from 'amis-core';
import { Rating } from 'amis-ui';
import { supportStatic } from './StaticHoc.js';

var RatingControl = /** @class */ (function (_super) {
    __extends(RatingControl, _super);
    function RatingControl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    RatingControl.prototype.doAction = function (action, data, throwErrors) {
        var _a, _b;
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        var _c = this.props, onChange = _c.onChange, resetValue = _c.resetValue, formStore = _c.formStore, store = _c.store, name = _c.name;
        if (actionType === 'clear') {
            onChange === null || onChange === void 0 ? void 0 : onChange('');
        }
        else if (actionType === 'reset') {
            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
            onChange === null || onChange === void 0 ? void 0 : onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
        }
    };
    RatingControl.prototype.handleChange = function (value) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, onChange, dispatchEvent, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, onChange = _a.onChange, dispatchEvent = _a.dispatchEvent;
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, { value: value }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onChange === null || onChange === void 0 ? void 0 : onChange(value);
                        return [2 /*return*/];
                }
            });
        });
    };
    RatingControl.prototype.renderStatic = function () {
        var _a = this.props, className = _a.className, style = _a.style, value = _a.value, count = _a.count, half = _a.half, char = _a.char, inactiveColor = _a.inactiveColor, colors = _a.colors, texts = _a.texts, charClassName = _a.charClassName, textClassName = _a.textClassName, textPosition = _a.textPosition, cx = _a.classnames;
        return (jsx("div", __assign({ className: cx('RatingControl', className) }, { children: jsx(Rating, { classnames: cx, value: value, disabled: true, count: count, half: half, char: char, inactiveColor: inactiveColor, colors: colors, texts: texts, charClassName: charClassName, textClassName: textClassName, textPosition: textPosition }) })));
    };
    RatingControl.prototype.render = function () {
        var _a = this.props, className = _a.className, value = _a.value, count = _a.count, half = _a.half, readOnly = _a.readOnly, disabled = _a.disabled, onHoverChange = _a.onHoverChange, allowClear = _a.allowClear, char = _a.char, inactiveColor = _a.inactiveColor, colors = _a.colors, texts = _a.texts, charClassName = _a.charClassName, textClassName = _a.textClassName, textPosition = _a.textPosition, cx = _a.classnames;
        var finalCount = getFinalCount(count, this.props.data);
        // 限制最大 100 星，避免渲染卡死问题
        finalCount > 100 && (finalCount = 100);
        return (jsx("div", __assign({ className: cx('RatingControl', className) }, { children: jsx(Rating, { classnames: cx, value: value, disabled: disabled, count: finalCount, half: half, allowClear: allowClear, readOnly: readOnly, char: char, inactiveColor: inactiveColor, colors: colors, texts: texts, charClassName: charClassName, textClassName: textClassName, textPosition: textPosition, onChange: this.handleChange, onHoverChange: function (value) {
                    onHoverChange && onHoverChange(value);
                } }) })));
    };
    RatingControl.defaultProps = {
        value: 0,
        count: 5,
        half: false,
        readOnly: false
    };
    __decorate([
        autobind
    ], RatingControl.prototype, "handleChange", null);
    __decorate([
        supportStatic()
    ], RatingControl.prototype, "render", null);
    return RatingControl;
}(React.Component));
function getFinalCount(name, data) {
    if (typeof name === 'number') {
        return name;
    }
    return toNumber(filter(name, data));
}
var RatingControlRenderer = /** @class */ (function (_super) {
    __extends(RatingControlRenderer, _super);
    function RatingControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    RatingControlRenderer = __decorate([
        FormItem({
            type: 'input-rating',
            sizeMutable: false,
            shouldComponentUpdate: function (props, prevProps) {
                return getFinalCount(props.count, props.data) !==
                    getFinalCount(prevProps.count, prevProps.data);
            },
            detectProps: [
                'half',
                'allowClear',
                'colors',
                'inactiveColor',
                'texts',
                'textPosition',
                'char'
            ]
        })
    ], RatingControlRenderer);
    return RatingControlRenderer;
}(RatingControl));

export { RatingControlRenderer, RatingControl as default };
