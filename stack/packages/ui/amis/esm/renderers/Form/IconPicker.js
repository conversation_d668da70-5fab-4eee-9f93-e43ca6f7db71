/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import React, { createElement } from 'react';
import { jsx, jsxs } from 'react/jsx-runtime';
import cx from 'classnames';
import { matchSorter } from 'match-sorter';
import keycode from 'keycode';
import Downshift from 'downshift';
import { autobind, FormItem } from 'amis-core';
import { ICONS } from './IconPickerIcons.js';
import { Icon } from 'amis-ui';

var IconPickerControl = /** @class */ (function (_super) {
    __extends(IconPickerControl, _super);
    function IconPickerControl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.state = {
            isOpen: false,
            inputValue: '',
            isFocused: false,
            vendorIndex: 0
        };
        return _this;
    }
    IconPickerControl.prototype.componentDidUpdate = function (prevProps) {
        var props = this.props;
        if (prevProps.value !== props.value) {
            this.setState({
                inputValue: ''
            });
        }
    };
    IconPickerControl.prototype.changeVendor = function (index) {
        this.setState({
            vendorIndex: index
        }, this.formatOptions);
    };
    IconPickerControl.prototype.formatOptions = function () {
        var vendorIndex = this.state.vendorIndex || 0;
        var _a = ICONS[vendorIndex], prefix = _a.prefix, icons = _a.icons;
        return icons.map(function (icon) { return ({
            label: prefix + icon,
            value: prefix + icon
        }); });
    };
    IconPickerControl.prototype.getVendors = function () {
        return ICONS.map(function (icons) { return icons.name; });
    };
    IconPickerControl.prototype.inputRef = function (ref) {
        this.input = ref;
    };
    IconPickerControl.prototype.focus = function () {
        if (!this.input) {
            return;
        }
        this.input.focus();
        var len = this.input.value.length;
        len && this.input.setSelectionRange(len, len);
    };
    IconPickerControl.prototype.handleClick = function () {
        if (this.props.disabled) {
            return;
        }
        this.focus();
        this.setState({
            isOpen: true
        });
    };
    IconPickerControl.prototype.handleFocus = function (e) {
        this.setState({
            isOpen: true,
            isFocused: true
        });
        this.props.onFocus && this.props.onFocus(e);
    };
    IconPickerControl.prototype.handleBlur = function (e) {
        var _a = this.props, onBlur = _a.onBlur, trimContents = _a.trimContents, value = _a.value, onChange = _a.onChange;
        this.setState({
            isFocused: false
        }, function () {
            if (trimContents && value && typeof value === 'string') {
                onChange(value.trim());
            }
        });
        onBlur && onBlur(e);
    };
    IconPickerControl.prototype.handleInputChange = function (evt) {
        var value = evt.currentTarget.value;
        this.setState({
            inputValue: value
        });
    };
    IconPickerControl.prototype.handleKeyDown = function (evt) {
        var code = keycode(evt.keyCode);
        if (code !== 'backspace') {
            return;
        }
        var onChange = this.props.onChange;
        if (!this.state.inputValue) {
            onChange('');
            this.setState({
                inputValue: ''
            });
        }
    };
    IconPickerControl.prototype.handleChange = function (value) {
        var _a = this.props, onChange = _a.onChange, disabled = _a.disabled;
        if (disabled) {
            return;
        }
        onChange(value);
        this.setState({
            isFocused: false,
            inputValue: ''
        });
    };
    IconPickerControl.prototype.handleStateChange = function (changes) {
        switch (changes.type) {
            case Downshift.stateChangeTypes.itemMouseEnter:
            case Downshift.stateChangeTypes.changeInput:
                this.setState({
                    isOpen: true
                });
                break;
            default:
                var state = {};
                if (typeof changes.isOpen !== 'undefined') {
                    state.isOpen = changes.isOpen;
                }
                if (this.state.isOpen && changes.isOpen === false) {
                    state.inputValue = '';
                }
                this.setState(state);
                break;
        }
    };
    IconPickerControl.prototype.handleClear = function () {
        var _this = this;
        var _a = this.props, onChange = _a.onChange, resetValue = _a.resetValue;
        onChange === null || onChange === void 0 ? void 0 : onChange(resetValue);
        this.setState({
            inputValue: resetValue,
            isFocused: true
        }, function () {
            _this.focus();
        });
    };
    IconPickerControl.prototype.renderFontIcons = function () {
        var _this = this;
        var _a = this.props, className = _a.className, inputOnly = _a.inputOnly, placeholder = _a.placeholder, cx = _a.classnames, name = _a.name, value = _a.value, noDataTip = _a.noDataTip, disabled = _a.disabled, clearable = _a.clearable, __ = _a.translate;
        var options = this.formatOptions();
        var vendors = this.getVendors();
        return (jsx(Downshift, __assign({ isOpen: this.state.isOpen, inputValue: this.state.inputValue, onChange: this.handleChange, onOuterClick: this.handleBlur, onStateChange: this.handleStateChange, selectedItem: [value] }, { children: function (_a) {
                var getInputProps = _a.getInputProps, getItemProps = _a.getItemProps, isOpen = _a.isOpen, inputValue = _a.inputValue;
                var filteredOptions = inputValue && isOpen
                    ? matchSorter(options, inputValue, {
                        keys: ['label', 'value'],
                        threshold: matchSorter.rankings.CONTAINS
                    })
                    : options;
                return (jsxs("div", __assign({ className: cx("IconPickerControl-input IconPickerControl-input--withAC", inputOnly ? className : '', {
                        'is-opened': isOpen
                    }), onClick: _this.handleClick }, { children: [jsxs("div", __assign({ className: cx('IconPickerControl-valueWrap') }, { children: [placeholder && !value && !_this.state.inputValue ? (jsx("div", __assign({ className: cx('IconPickerControl-placeholder') }, { children: placeholder }))) : null, !value || (inputValue && isOpen) ? null : (jsxs("div", __assign({ className: cx('IconPickerControl-value') }, { children: [jsx("i", { className: cx(value) }), typeof value === 'string' ? value : ''] }))), jsx("input", __assign({}, getInputProps({
                                    name: name,
                                    ref: _this.inputRef,
                                    onFocus: _this.handleFocus,
                                    onChange: _this.handleInputChange,
                                    onKeyDown: _this.handleKeyDown,
                                    value: _this.state.inputValue
                                }), { autoComplete: "off", disabled: disabled, size: 10 })), clearable && !disabled && value ? (jsx("a", __assign({ onClick: _this.handleClear, className: cx('IconPickerControl-clear') }, { children: jsx(Icon, { icon: "input-clear", className: "icon" }) }))) : null] })), isOpen ? (jsxs("div", __assign({ className: cx('IconPickerControl-sugsPanel') }, { children: [vendors.length > 1 ? (jsx("div", __assign({ className: cx('IconPickerControl-tabs') }, { children: vendors.map(function (vendor, index) { return (jsx("div", __assign({ className: cx('IconPickerControl-tab', {
                                            active: _this.state.vendorIndex === index
                                        }), onClick: function () { return _this.changeVendor(index); } }, { children: vendor }), index)); }) }))) : null, filteredOptions.length ? (jsx("div", __assign({ className: cx('IconPickerControl-sugs', vendors.length > 1
                                        ? 'IconPickerControl-multiVendor'
                                        : 'IconPickerControl-singleVendor') }, { children: filteredOptions.map(function (option, index) { return (createElement("div", __assign({}, getItemProps({
                                        item: option.value,
                                        className: cx("IconPickerControl-sugItem", {
                                            'is-active': value === option.value
                                        })
                                    }), { key: index }),
                                        jsx(Icon, { className: cx("".concat(option.value)), title: "".concat(option.value), icon: option.value }))); }) }))) : (jsx("div", __assign({ className: cx(vendors.length > 1
                                        ? 'IconPickerControl-multiVendor'
                                        : 'IconPickerControl-singleVendor') }, { children: __(noDataTip) })))] }))) : null] })));
            } })));
    };
    IconPickerControl.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, ns = _a.classPrefix, inputOnly = _a.inputOnly, disabled = _a.disabled;
        var input = this.renderFontIcons();
        if (inputOnly) {
            return input;
        }
        return (jsx("div", __assign({ className: cx(className, "".concat(ns, "IconPickerControl"), {
                'is-focused': this.state.isFocused,
                'is-disabled': disabled
            }) }, { children: input })));
    };
    IconPickerControl.defaultProps = {
        resetValue: '',
        placeholder: '',
        noDataTip: 'placeholder.noData'
    };
    __decorate([
        autobind
    ], IconPickerControl.prototype, "changeVendor", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "formatOptions", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "getVendors", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "inputRef", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "focus", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleClick", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleFocus", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleBlur", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleInputChange", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleKeyDown", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleChange", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleStateChange", null);
    __decorate([
        autobind
    ], IconPickerControl.prototype, "handleClear", null);
    return IconPickerControl;
}(React.PureComponent));
var IconPickerControlRenderer = /** @class */ (function (_super) {
    __extends(IconPickerControlRenderer, _super);
    function IconPickerControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    IconPickerControlRenderer = __decorate([
        FormItem({
            type: 'icon-picker'
        })
    ], IconPickerControlRenderer);
    return IconPickerControlRenderer;
}(IconPickerControl));

export { IconPickerControlRenderer, IconPickerControl as default };
