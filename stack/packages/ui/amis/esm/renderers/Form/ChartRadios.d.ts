import React from 'react';
import { OptionsControlProps } from 'amis-core';
import { FormOptionsSchema } from '../../Schema';
/**
 * 图表 Radio 单选框。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/chart-radios
 */
export interface ChartRadiosControlSchema extends FormOptionsSchema {
    /**
     * 指定为 ChartRadios 渲染器。
     * https://aisuda.bce.baidu.com/amis/zh-CN/components/form/chart-radios
     */
    type: 'chart-radios';
    config: any;
    showTooltipOnHighlight?: boolean;
    chartValueField?: string;
}
export interface ChartRadiosProps extends OptionsControlProps {
    placeholder?: any;
    labelClassName?: string;
    labelField?: string;
    config: any;
}
export default class ChartRadiosControl extends React.Component<ChartRadiosProps, any> {
    highlightIndex: number;
    prevIndex: number;
    chart?: any;
    chartRef(chart?: any): void;
    highlight(index?: number): void;
    componentDidMount(): void;
    componentDidUpdate(): void;
    renderStatic(displayValue?: string): import("react/jsx-runtime").JSX.Element;
    render(): any;
}
export declare class RadiosControlRenderer extends ChartRadiosControl {
    static defaultProps: {
        multiple: boolean;
    };
}
//# sourceMappingURL=ChartRadios.d.ts.map