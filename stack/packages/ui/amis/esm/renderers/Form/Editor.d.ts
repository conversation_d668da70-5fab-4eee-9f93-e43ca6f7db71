import React from 'react';
import { FormControlProps, BaseSchemaWithoutType, FormBaseControlWithoutSize } from 'amis-core';
import type { ListenerAction } from 'amis-core';
/**
 * Editor 代码编辑器
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/editor
 */
export interface EditorControlSchema extends BaseSchemaWithoutType, FormBaseControlWithoutSize {
    type: 'editor' | 'bat-editor' | 'c-editor' | 'coffeescript-editor' | 'cpp-editor' | 'csharp-editor' | 'css-editor' | 'dockerfile-editor' | 'fsharp-editor' | 'go-editor' | 'handlebars-editor' | 'html-editor' | 'ini-editor' | 'java-editor' | 'javascript-editor' | 'json-editor' | 'less-editor' | 'lua-editor' | 'markdown-editor' | 'msdax-editor' | 'objective-c-editor' | 'php-editor' | 'plaintext-editor' | 'postiats-editor' | 'powershell-editor' | 'pug-editor' | 'python-editor' | 'r-editor' | 'razor-editor' | 'ruby-editor' | 'sb-editor' | 'scss-editor' | 'sol-editor' | 'sql-editor' | 'swift-editor' | 'typescript-editor' | 'vb-editor' | 'xml-editor' | 'yaml-editor';
    /**
     * 语言类型
     */
    language?: 'bat' | 'c' | 'coffeescript' | 'cpp' | 'csharp' | 'css' | 'dockerfile' | 'fsharp' | 'go' | 'handlebars' | 'html' | 'ini' | 'java' | 'javascript' | 'json' | 'less' | 'lua' | 'markdown' | 'msdax' | 'objective-c' | 'php' | 'plaintext' | 'postiats' | 'powershell' | 'pug' | 'python' | 'r' | 'razor' | 'ruby' | 'sb' | 'scss' | 'shell' | 'sol' | 'sql' | 'swift' | 'typescript' | 'vb' | 'xml' | 'yaml';
    /**
     * 编辑器大小
     */
    size?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
    /**
     * 是否展示全屏模式开关
     */
    allowFullscreen?: boolean;
    /**
     * 获取编辑器底层实例
     */
    editorDidMount?: string;
}
export type EditorRendererEvent = 'blur' | 'focus';
export interface EditorProps extends FormControlProps {
    options?: object;
}
export default class EditorControl extends React.Component<EditorProps, any> {
    static defaultProps: Partial<EditorProps>;
    state: {
        focused: boolean;
    };
    editor: any;
    toDispose: Array<Function>;
    divRef: React.RefObject<HTMLDivElement>;
    constructor(props: EditorProps);
    componentWillUnmount(): void;
    doAction(action: ListenerAction, data: any, throwErrors?: boolean, args?: any): void;
    focus(): void;
    handleFocus(e: any): Promise<void>;
    handleBlur(e: any): Promise<void>;
    handleChange(e: any): Promise<void>;
    handleEditorMounted(editor: any, monaco: any): void;
    prevHeight: number;
    updateContainerSize(editor: any, monaco: any): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare const availableLanguages: string[];
export declare const EditorControls: Array<typeof EditorControl>;
export declare class EditorControlRenderer extends EditorControl {
    static defaultProps: {
        language: string;
        options?: object | undefined;
    };
}
//# sourceMappingURL=Editor.d.ts.map