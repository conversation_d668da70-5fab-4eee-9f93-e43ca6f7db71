/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { getVariable, resolveEventData, isPureVariable, resolveVariableAndFilter, FormItem } from 'amis-core';
import { DiffEditor } from 'amis-ui';

function normalizeValue(value, language) {
    if (value && typeof value !== 'string') {
        value = JSON.stringify(value, null, 2);
    }
    if (language && language === 'json') {
        try {
            value = JSON.stringify(typeof value === 'string' ? JSON.parse(value) : value, null, 2);
        }
        catch (e) { }
    }
    return value || '';
}
var DiffEditorRenderer = /** @class */ (function (_super) {
    __extends(DiffEditorRenderer, _super);
    function DiffEditorRenderer(props) {
        var _this = _super.call(this, props) || this;
        _this.state = {
            focused: false
        };
        _this.handleFocus = _this.handleFocus.bind(_this);
        _this.handleBlur = _this.handleBlur.bind(_this);
        _this.handleEditorMounted = _this.handleEditorMounted.bind(_this);
        _this.handleChange = _this.handleChange.bind(_this);
        return _this;
    }
    DiffEditorRenderer.prototype.doAction = function (action, data, throwErrors, args) {
        var _a, _b;
        if (throwErrors === void 0) { throwErrors = false; }
        var actionType = action === null || action === void 0 ? void 0 : action.actionType;
        var _c = this.props, onChange = _c.onChange, resetValue = _c.resetValue, formStore = _c.formStore, store = _c.store, name = _c.name;
        if (actionType === 'clear') {
            onChange('');
        }
        else if (actionType === 'reset') {
            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
            onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');
        }
        else if (actionType === 'focus') {
            this.focus();
        }
    };
    DiffEditorRenderer.prototype.focus = function () {
        var _a, _b, _c;
        (_a = this.editor) === null || _a === void 0 ? void 0 : _a.focus();
        this.setState({ focused: true });
        // 最近一次光标位置
        var position = (_b = this.editor) === null || _b === void 0 ? void 0 : _b.getPosition();
        (_c = this.editor) === null || _c === void 0 ? void 0 : _c.setPosition(position);
    };
    DiffEditorRenderer.prototype.handleFocus = function (e) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, dispatchEvent, value, onFocus, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, dispatchEvent = _a.dispatchEvent, value = _a.value, onFocus = _a.onFocus;
                        this.setState({
                            focused: true
                        });
                        return [4 /*yield*/, dispatchEvent('focus', resolveEventData(this.props, { value: value }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);
                        return [2 /*return*/];
                }
            });
        });
    };
    DiffEditorRenderer.prototype.handleBlur = function (e) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, dispatchEvent, value, onBlur, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, dispatchEvent = _a.dispatchEvent, value = _a.value, onBlur = _a.onBlur;
                        this.setState({
                            focused: false
                        });
                        return [4 /*yield*/, dispatchEvent('blur', resolveEventData(this.props, { value: value }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);
                        return [2 /*return*/];
                }
            });
        });
    };
    DiffEditorRenderer.prototype.handleChange = function (value) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, onChange, dispatchEvent, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, onChange = _a.onChange, dispatchEvent = _a.dispatchEvent;
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, { value: value }))];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        onChange && onChange(value);
                        return [2 /*return*/];
                }
            });
        });
    };
    DiffEditorRenderer.prototype.handleEditorMounted = function (editor) {
        this.editor = editor;
    };
    DiffEditorRenderer.prototype.render = function () {
        var _a = this.props, className = _a.className, style = _a.style, value = _a.value, onChange = _a.onChange, disabled = _a.disabled, size = _a.size, options = _a.options, language = _a.language, editorTheme = _a.editorTheme, diffValue = _a.diffValue, cx = _a.classnames, data = _a.data;
        var originValue = isPureVariable(diffValue)
            ? normalizeValue(resolveVariableAndFilter(diffValue || '', data, '| raw'), language)
            : normalizeValue(diffValue, language);
        var finalValue = normalizeValue(value, language);
        return (jsx("div", __assign({ className: cx('EditorControl', size ? "EditorControl--".concat(size) : '', className, {
                'is-focused': this.state.focused
            }) }, { children: jsx(DiffEditor, { value: finalValue, originValue: originValue, onChange: this.handleChange, disabled: disabled, language: language, editorTheme: editorTheme, options: options, onFocus: this.handleFocus, onBlur: this.handleBlur, editorDidMount: this.handleEditorMounted }) })));
    };
    DiffEditorRenderer.defaultProps = {
        language: 'javascript',
        editorTheme: 'vs',
        options: {
            automaticLayout: false,
            selectOnLineNumbers: true,
            scrollBeyondLastLine: false,
            folding: true,
            minimap: {
                enabled: false
            }
        },
        diffValue: ''
    };
    return DiffEditorRenderer;
}(React.Component));
var DiffEditorControlRenderer = /** @class */ (function (_super) {
    __extends(DiffEditorControlRenderer, _super);
    function DiffEditorControlRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    DiffEditorControlRenderer.defaultProps = __assign({}, DiffEditorRenderer.defaultProps);
    DiffEditorControlRenderer = __decorate([
        FormItem({
            type: "diff-editor",
            sizeMutable: false
        })
    ], DiffEditorControlRenderer);
    return DiffEditorControlRenderer;
}(DiffEditorRenderer));

export { DiffEditorControlRenderer, DiffEditorRenderer };
