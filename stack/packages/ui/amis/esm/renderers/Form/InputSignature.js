/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { createObject, normalizeApi, autobind, ScopedContext, FormItem } from 'amis-core';
import { Signature } from 'amis-ui';
import pick from 'lodash/pick';
import { base64ToBlob } from 'file64';

var InputSignatureComp = /** @class */ (function (_super) {
    __extends(InputSignatureComp, _super);
    function InputSignatureComp() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    InputSignatureComp.prototype.uploadFile = function (file, uploadApi) {
        return __awaiter(this, void 0, void 0, function () {
            var api, fd, fileBlob;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        api = normalizeApi(uploadApi, 'post');
                        if (!!api.data) return [3 /*break*/, 2];
                        fd = new FormData();
                        return [4 /*yield*/, base64ToBlob(file)];
                    case 1:
                        fileBlob = _a.sent();
                        fd.append('file', fileBlob, 'signature.png');
                        api.data = fd;
                        _a.label = 2;
                    case 2: return [2 /*return*/, this.props.env.fetcher(api, createObject(this.props.data, {
                            file: file
                        }))];
                }
            });
        });
    };
    InputSignatureComp.prototype.handleChange = function (val) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var _c, __, uploadApi, embed, onChange, res, value, error_1;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        _c = this.props, __ = _c.translate, uploadApi = _c.uploadApi, embed = _c.embed, onChange = _c.onChange;
                        // 非内嵌模式 没有上传api 或是清空直接onChange
                        if (!embed || !uploadApi || val === undefined) {
                            onChange === null || onChange === void 0 ? void 0 : onChange(val);
                            return [2 /*return*/];
                        }
                        _d.label = 1;
                    case 1:
                        _d.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.uploadFile(val, uploadApi)];
                    case 2:
                        res = _d.sent();
                        if (!res.ok || (res.status && res.status !== '0') || !res.data) {
                            throw new Error(res.msg || __('File.errorRetry'));
                        }
                        value = res.data.value || res.data.url || res.data;
                        onChange === null || onChange === void 0 ? void 0 : onChange(value);
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _d.sent();
                        // 失败清空签名组件内的数据，传空字符串会重新触发amis的渲染，underfined不会被重新渲染（连续的空字符串不会被重新渲染，amis底层会对value值进行diff对比）
                        onChange === null || onChange === void 0 ? void 0 : onChange('');
                        (_b = (_a = this.props.env) === null || _a === void 0 ? void 0 : _a.alert) === null || _b === void 0 ? void 0 : _b.call(_a, error_1.message || __('File.errorRetry'));
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    InputSignatureComp.prototype.render = function () {
        var _a = this.props, cx = _a.classnames, className = _a.className;
        var props = pick(this.props, [
            'value',
            'width',
            'height',
            'mobileUI',
            'embed',
            'color',
            'bgColor',
            'clearBtnLabel',
            'clearBtnIcon',
            'undoBtnLabel',
            'undoBtnIcon',
            'confirmBtnLabel',
            'confirmBtnIcon',
            'embedConfirmLabel',
            'embedConfirmIcon',
            'ebmedCancelLabel',
            'ebmedCancelIcon',
            'embedBtnIcon',
            'embedBtnLabel',
            'uploadApi'
        ]);
        return (jsx(Signature, __assign({ classnames: cx, className: className, onChange: this.handleChange }, props)));
    };
    __decorate([
        autobind
    ], InputSignatureComp.prototype, "uploadFile", null);
    __decorate([
        autobind
    ], InputSignatureComp.prototype, "handleChange", null);
    return InputSignatureComp;
}(React.Component));
var InputSignatureRenderer = /** @class */ (function (_super) {
    __extends(InputSignatureRenderer, _super);
    function InputSignatureRenderer(props, context) {
        var _this = _super.call(this, props) || this;
        var scoped = context;
        scoped.registerComponent(_this);
        return _this;
    }
    InputSignatureRenderer.prototype.componentWillUnmount = function () {
        var _a;
        (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);
        var scoped = this.context;
        scoped.unRegisterComponent(this);
    };
    InputSignatureRenderer.contextType = ScopedContext;
    InputSignatureRenderer = __decorate([
        FormItem({
            type: 'input-signature',
            sizeMutable: false
        })
    ], InputSignatureRenderer);
    return InputSignatureRenderer;
}(InputSignatureComp));

export { InputSignatureRenderer, InputSignatureComp as default };
