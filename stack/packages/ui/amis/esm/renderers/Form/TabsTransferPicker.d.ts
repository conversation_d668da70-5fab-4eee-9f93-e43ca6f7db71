import { OptionsControlProps } from 'amis-core';
import { SpinnerExtraProps } from 'amis-ui';
import { BaseTabsTransferRenderer } from './TabsTransfer';
import { BaseTransferControlSchema } from './Transfer';
import { ActionObject } from 'amis-core';
import type { ItemRenderStates } from 'amis-ui/lib/components/Selection';
/**
 * TabsTransferPicker 穿梭器的弹框形态
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/tabs-transfer-picker
 */
export interface TabsTransferPickerControlSchema extends BaseTransferControlSchema, SpinnerExtraProps {
    type: 'tabs-transfer-picker';
}
export interface TabsTransferProps extends OptionsControlProps, Omit<TabsTransferPickerControlSchema, 'type' | 'options' | 'inputClassName' | 'className' | 'descriptionClassName'>, SpinnerExtraProps {
}
interface BaseTransferState {
    activeKey: number;
}
export declare class TabsTransferPickerRenderer extends BaseTabsTransferRenderer<TabsTransferProps> {
    state: BaseTransferState;
    dispatchEvent(name: string): void;
    optionItemRender(option: any, states: ItemRenderStates): any;
    doAction(action: ActionObject): void;
    render(): import("react/jsx-runtime").JSX.Element;
}
export {};
//# sourceMappingURL=TabsTransferPicker.d.ts.map