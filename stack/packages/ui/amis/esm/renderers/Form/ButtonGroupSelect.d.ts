import React from 'react';
import { OptionsControlProps, FormOptionsControlSelf, FormBaseControlWithoutSize } from 'amis-core';
import { Option, TestIdBuilder } from 'amis-core';
import { ActionObject } from 'amis-core';
import type { BadgeObject } from 'amis-ui';
import { BaseButtonGroupSchema } from '../ButtonGroup';
/**
 * 按钮组控件。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/form/button-group
 */
export interface ButtonGroupControlSchema extends FormOptionsControlSelf, FormBaseControlWithoutSize, BaseButtonGroupSchema {
    type: 'button-group-select';
}
export interface ButtonGroupProps extends OptionsControlProps, Omit<ButtonGroupControlSchema, 'size' | 'source' | 'type' | 'className' | 'descriptionClassName' | 'inputClassName' | 'btnClassName'> {
    options: Array<Option>;
    testIdBuilder?: TestIdBuilder;
}
export default class ButtonGroupControl extends React.Component<ButtonGroupProps, any> {
    static defaultProps: Partial<ButtonGroupProps>;
    doAction(action: ActionObject, data: object, throwErrors: boolean): void;
    handleToggle(option: Option): void;
    reload(subpath?: string, query?: any): void;
    getBadgeConfig(config: BadgeObject, item: Option): any;
    render(props?: Readonly<ButtonGroupProps>): import("react/jsx-runtime").JSX.Element;
}
export declare class ButtonGroupControlRenderer extends ButtonGroupControl {
}
//# sourceMappingURL=ButtonGroupSelect.d.ts.map