/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __rest, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React, { createElement } from 'react';
import { isVisible, Renderer } from 'amis-core';
import { Checkbox } from 'amis-ui';
import ColumnToggler from '../Table/ColumnToggler.js';

var ColumnTogglerRenderer = /** @class */ (function (_super) {
    __extends(ColumnTogglerRenderer, _super);
    function ColumnTogglerRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ColumnTogglerRenderer.prototype.render = function () {
        var _a = this.props, className = _a.className, store = _a.store, render = _a.render, ns = _a.classPrefix, cx = _a.classnames, tooltip = _a.tooltip, align = _a.align, cols = _a.cols, toggleAllColumns = _a.toggleAllColumns, toggleToggle = _a.toggleToggle, data = _a.data, size = _a.size, popOverContainer = _a.popOverContainer, rest = __rest(_a, ["className", "store", "render", "classPrefix", "classnames", "tooltip", "align", "cols", "toggleAllColumns", "toggleToggle", "data", "size", "popOverContainer"]);
        var __ = rest.translate;
        var env = rest.env;
        if (!cols) {
            return null;
        }
        var toggableColumns = cols.filter(function (item) {
            return isVisible(item.pristine || item, data) && item.toggable !== false;
        });
        var activeToggaleColumns = toggableColumns.filter(function (item) { return item.toggled !== false; });
        return (createElement(ColumnToggler, __assign({}, rest, { render: render, tooltip: tooltip || __('Table.columnsVisibility'), tooltipContainer: popOverContainer || env.getModalContainer, isActived: cols.findIndex(function (column) { return !column.toggled; }) !== -1, align: align !== null && align !== void 0 ? align : 'right', size: size || 'sm', classnames: cx, classPrefix: ns, key: "columns-toggable", columns: cols, activeToggaleColumns: activeToggaleColumns, data: data }),
            (toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.length) ? (jsx("li", __assign({ className: cx('ColumnToggler-menuItem'), onClick: function () {
                    toggleAllColumns &&
                        toggleAllColumns((activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) <= 0);
                } }, { children: jsx(Checkbox, __assign({ size: "sm", classPrefix: ns, checked: !!(activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length), partial: !!((activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) &&
                        (activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) !== (toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.length)) }, { children: __('Select.checkAll') }), "checkall") }), 'selectAll')) : null, toggableColumns === null || toggableColumns === void 0 ? void 0 :
            toggableColumns.map(function (column, index) { return (jsx("li", __assign({ className: cx('ColumnToggler-menuItem'), onClick: function () {
                    toggleToggle && toggleToggle(index);
                } }, { children: jsx(Checkbox, __assign({ size: "sm", classPrefix: ns, checked: column.toggled !== false }, { children: column.title
                        ? render('tpl', column.title)
                        : column.label || null })) }), 'item' + (column.index || index))); })));
    };
    ColumnTogglerRenderer = __decorate([
        Renderer({
            type: 'column-toggler',
            name: 'column-toggler'
        })
    ], ColumnTogglerRenderer);
    return ColumnTogglerRenderer;
}(React.Component));

export { ColumnTogglerRenderer };
