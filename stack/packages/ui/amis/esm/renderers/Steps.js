/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign, __extends, __rest, __decorate } from 'tslib';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import React from 'react';
import { resolveVariableAndFilter, isPureVariable, filter, getPropValue, resolveVariable, setThemeClassName, CustomStyle, Renderer } from 'amis-core';
import { Steps, withRemoteConfig } from 'amis-ui';
import { StepStatus } from 'amis-ui/lib/components/Steps';
import isPlainObject from 'lodash/isPlainObject';

function StepsCmpt(props) {
    var _a;
    var className = props.className, style = props.style, steps = props.steps, status = props.status, mode = props.mode, iconPosition = props.iconPosition, labelPlacement = props.labelPlacement, progressDot = props.progressDot, data = props.data, source = props.source, render = props.render, mobileUI = props.mobileUI, iconClassName = props.iconClassName, titleClassName = props.titleClassName, subTitleClassName = props.subTitleClassName, descriptionClassName = props.descriptionClassName;
    var sourceResult = resolveVariableAndFilter(source, data, '| raw');
    /** 步骤数据源 */
    var stepsRow = (Array.isArray(sourceResult) ? sourceResult : undefined) || steps || [];
    /** 状态数据源 */
    var statusValue = isPureVariable(status)
        ? resolveVariableAndFilter(status, data, '| raw')
        : status;
    var resolveRender = function (val) {
        return typeof val === 'string' ? filter(val, data) : val && render('inner', val);
    };
    var value = (_a = getPropValue(props)) !== null && _a !== void 0 ? _a : 0;
    var resolveValue = typeof value === 'string' && isNaN(+value)
        ? resolveVariable(value, data) || value
        : +value;
    var valueIndex = stepsRow.findIndex(function (item) { return item.value && item.value === resolveValue; });
    var currentValue = valueIndex !== -1 ? valueIndex : resolveValue;
    var resolveSteps = stepsRow.map(function (step, i) {
        var stepStatus = getStepStatus(step, i);
        return __assign(__assign({}, step), { status: stepStatus, title: resolveRender(step.title), subTitle: resolveRender(step.subTitle), description: resolveRender(step.description) });
    });
    function getStepStatus(step, i) {
        var stepStatus;
        if (typeof statusValue === 'string') {
            if (i === currentValue) {
                stepStatus = statusValue || status || StepStatus.process;
            }
        }
        else if (typeof statusValue === 'object') {
            var key = step.value;
            key && statusValue[key] && (stepStatus = statusValue[key]);
        }
        return stepStatus;
    }
    return (jsx(Steps, { current: currentValue, steps: resolveSteps, className: className, iconClassName: iconClassName, subTitleClassName: subTitleClassName, titleClassName: titleClassName, descriptionClassName: descriptionClassName, style: style, status: statusValue, mode: mode, iconPosition: iconPosition, progressDot: progressDot, labelPlacement: labelPlacement, mobileUI: mobileUI }));
}
var StepsWithRemoteConfig = withRemoteConfig()(/** @class */ (function (_super) {
    __extends(class_1, _super);
    function class_1() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    class_1.prototype.render = function () {
        var _a = this.props, cx = _a.classnames, config = _a.config, deferLoad = _a.deferLoad, loading = _a.loading, updateConfig = _a.updateConfig, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env, themeCss = _a.themeCss, className = _a.className, ns = _a.classPrefix, rest = __rest(_a, ["classnames", "config", "deferLoad", "loading", "updateConfig", "id", "wrapperCustomStyle", "env", "themeCss", "className", "classPrefix"]);
        var sourceConfig = isPlainObject(config) ? config : null;
        return (jsxs(Fragment, { children: [jsx(StepsCmpt, __assign({}, rest, sourceConfig, { className: cx("".concat(ns, "StepsControl"), className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss }))), iconClassName: setThemeClassName(__assign(__assign({}, this.props), { name: [
                            'iconControlClassNameDefault',
                            'iconControlClassNameFinish',
                            'iconControlClassNameProcess',
                            'iconControlClassNameWait',
                            'iconControlClassNameError'
                        ], id: id, themeCss: themeCss })), subTitleClassName: setThemeClassName(__assign(__assign({}, this.props), { name: [
                            'subTitleControlClassNameDefault',
                            'subTitleControlClassNameFinish',
                            'subTitleControlClassNameProcess',
                            'subTitleControlClassNameWait',
                            'subTitleControlClassNameError'
                        ], id: id, themeCss: themeCss })), titleClassName: cx(setThemeClassName(__assign(__assign({}, this.props), { name: [
                            'titleControlClassNameDefault',
                            'titleControlClassNameFinish',
                            'titleControlClassNameProcess',
                            'titleControlClassNameWait',
                            'titleControlClassNameError'
                        ], id: id, themeCss: themeCss }))), descriptionClassName: setThemeClassName(__assign(__assign({}, this.props), { name: [
                            'descriptionControlClassNameDefault',
                            'descriptionControlClassNameFinish',
                            'descriptionControlClassNameProcess',
                            'descriptionControlClassNameWait',
                            'descriptionControlClassNameError'
                        ], id: id, themeCss: themeCss })) })), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            { key: 'baseControlClassName' },
                            {
                                key: 'iconControlClassNameDefault',
                                weights: {
                                    default: {
                                        important: true
                                    }
                                }
                            },
                            {
                                key: 'iconControlClassNameFinish',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-finish'
                                    }
                                }
                            },
                            {
                                key: 'iconControlClassNameProcess',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-process'
                                    }
                                }
                            },
                            {
                                key: 'iconControlClassNameWait',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-wait'
                                    }
                                }
                            },
                            {
                                key: 'iconControlClassNameError',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-error'
                                    }
                                }
                            },
                            {
                                key: 'subTitleControlClassNameDefault',
                                weights: {
                                    default: {
                                        important: true
                                    }
                                }
                            },
                            {
                                key: 'subTitleControlClassNameProcess',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-process'
                                    }
                                }
                            },
                            {
                                key: 'subTitleControlClassNameFinish',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-finish'
                                    }
                                }
                            },
                            {
                                key: 'subTitleControlClassNameWait',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-wait'
                                    }
                                }
                            },
                            {
                                key: 'subTitleControlClassNameError',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-error'
                                    }
                                }
                            },
                            {
                                key: 'titleControlClassNameDefault',
                                weights: {
                                    default: {
                                        important: true
                                    }
                                }
                            },
                            {
                                key: 'titleControlClassNameProcess',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-process'
                                    }
                                }
                            },
                            {
                                key: 'titleControlClassNameFinish',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-finish'
                                    }
                                }
                            },
                            {
                                key: 'titleControlClassNameWait',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-wait'
                                    }
                                }
                            },
                            {
                                key: 'titleControlClassNameError',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-error'
                                    }
                                }
                            },
                            {
                                key: 'descriptionControlClassNameDefault',
                                weights: {
                                    default: {
                                        important: true
                                    }
                                }
                            },
                            {
                                key: 'descriptionControlClassNameFinish',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-finish'
                                    }
                                }
                            },
                            {
                                key: 'descriptionControlClassNameProcess',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-process'
                                    }
                                }
                            },
                            {
                                key: 'descriptionControlClassNameWait',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-wait'
                                    }
                                }
                            },
                            {
                                key: 'descriptionControlClassNameError',
                                weights: {
                                    default: {
                                        important: true,
                                        parent: '.is-error'
                                    }
                                }
                            }
                        ]
                    }, env: env }))] }));
    };
    return class_1;
}(React.Component)));
var StepsRenderer = /** @class */ (function (_super) {
    __extends(StepsRenderer, _super);
    function StepsRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    StepsRenderer.prototype.render = function () {
        return jsx(StepsWithRemoteConfig, __assign({}, this.props));
    };
    StepsRenderer = __decorate([
        Renderer({
            type: 'steps'
        })
    ], StepsRenderer);
    return StepsRenderer;
}(React.Component));

export { StepsCmpt, StepsRenderer };
