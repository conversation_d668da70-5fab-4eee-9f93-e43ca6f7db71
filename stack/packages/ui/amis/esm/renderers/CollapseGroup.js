/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import React from 'react';
import { resolveEventData, isPureVariable, resolveVariableAndFilter, setThemeClassName, CustomStyle, autobind, Renderer } from 'amis-core';
import { CollapseGroup } from 'amis-ui';
import cx from 'classnames';

var CollapseGroupRender = /** @class */ (function (_super) {
    __extends(CollapseGroupRender, _super);
    function CollapseGroupRender(props) {
        return _super.call(this, props) || this;
    }
    CollapseGroupRender.prototype.handleCollapseChange = function (activeKeys, collapseId, collapsed) {
        return __awaiter(this, void 0, void 0, function () {
            var dispatchEvent, renderEvent;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        dispatchEvent = this.props.dispatchEvent;
                        return [4 /*yield*/, dispatchEvent('change', resolveEventData(this.props, {
                                activeKeys: activeKeys,
                                collapseId: collapseId,
                                collapsed: collapsed
                            }))];
                    case 1:
                        renderEvent = _a.sent();
                        if (renderEvent === null || renderEvent === void 0 ? void 0 : renderEvent.prevented) {
                            return [2 /*return*/];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    CollapseGroupRender.prototype.render = function () {
        var _a = this.props, defaultActiveKey = _a.defaultActiveKey, accordion = _a.accordion, expandIcon = _a.expandIcon, expandIconPosition = _a.expandIconPosition, body = _a.body, className = _a.className, style = _a.style, render = _a.render, mobileUI = _a.mobileUI, data = _a.data, id = _a.id, themeCss = _a.themeCss, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env;
        var enableFieldSetStyle = this.props.enableFieldSetStyle;
        if (isPureVariable(enableFieldSetStyle)) {
            enableFieldSetStyle = resolveVariableAndFilter(enableFieldSetStyle, data, '| raw');
        }
        return (jsxs(Fragment, { children: [jsx(CollapseGroup, __assign({ defaultActiveKey: defaultActiveKey, accordion: accordion, expandIcon: expandIcon, expandIconPosition: expandIconPosition, className: cx(className, setThemeClassName(__assign(__assign({}, this.props), { name: 'className', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), style: style, mobileUI: mobileUI, onCollapseChange: this.handleCollapseChange }, { children: render('body', body || '', { enableFieldSetStyle: enableFieldSetStyle }) })), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'className'
                            }
                        ]
                    }, env: env }))] }));
    };
    CollapseGroupRender.defaultProps = {
        enableFieldSetStyle: true
    };
    __decorate([
        autobind
    ], CollapseGroupRender.prototype, "handleCollapseChange", null);
    return CollapseGroupRender;
}(React.Component));
var CollapseGroupRenderer = /** @class */ (function (_super) {
    __extends(CollapseGroupRenderer, _super);
    function CollapseGroupRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CollapseGroupRenderer = __decorate([
        Renderer({
            type: 'collapse-group'
        })
    ], CollapseGroupRenderer);
    return CollapseGroupRenderer;
}(CollapseGroupRender));

export { CollapseGroupRender, CollapseGroupRenderer };
