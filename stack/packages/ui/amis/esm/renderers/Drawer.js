/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __spreadArray, __assign, __rest, __decorate } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import { guid, setThemeClassName, CustomStyle, autobind, filter, filterTarget, isVisible, createObject, ScopedContext, Renderer, ModalStore, isObjectShallowModified } from 'amis-core';
import { Spinner, Drawer as Drawer$1 } from 'amis-ui';
import { reaction } from 'mobx';
import { findDOMNode } from 'react-dom';
import { isAlive } from 'mobx-state-tree';

var Drawer = /** @class */ (function (_super) {
    __extends(Drawer, _super);
    function Drawer(props) {
        var _this = _super.call(this, props) || this;
        _this.$$id = guid();
        props.store.setEntered(!!props.show);
        _this.handleSelfClose = _this.handleSelfClose.bind(_this);
        _this.handleAction = _this.handleAction.bind(_this);
        _this.handleActionSensor = _this.handleActionSensor.bind(_this);
        _this.handleDrawerConfirm = _this.handleDrawerConfirm.bind(_this);
        _this.handleDrawerClose = _this.handleDrawerClose.bind(_this);
        _this.handleDialogConfirm = _this.handleDialogConfirm.bind(_this);
        _this.handleDialogClose = _this.handleDialogClose.bind(_this);
        _this.handleChildFinished = _this.handleChildFinished.bind(_this);
        _this.handleEntered = _this.handleEntered.bind(_this);
        _this.handleExited = _this.handleExited.bind(_this);
        _this.handleFormInit = _this.handleFormInit.bind(_this);
        _this.handleFormChange = _this.handleFormChange.bind(_this);
        _this.handleFormSaved = _this.handleFormSaved.bind(_this);
        var store = props.store;
        _this.reaction = reaction(function () { return "".concat(store.loading).concat(store.error); }, function () { return _this.forceUpdate(); });
        return _this;
    }
    // shouldComponentUpdate(nextProps:DrawerProps) {
    //     const props = this.props;
    //     if (props.show === nextProps.show && !nextProps.show) {
    //         return false;
    //     }
    //     return isObjectShallowModified(this.props, nextProps);
    // }
    Drawer.prototype.componentWillUnmount = function () {
        this.reaction && this.reaction();
        clearTimeout(this.clearErrorTimer);
    };
    Drawer.prototype.buildActions = function () {
        var _a = this.props, actions = _a.actions, confirm = _a.confirm, __ = _a.translate, testIdBuilder = _a.testIdBuilder;
        if (typeof actions !== 'undefined') {
            return actions;
        }
        var ret = [];
        ret.push({
            type: 'button',
            testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('cancel'),
            actionType: 'close',
            label: __('cancel')
        });
        if (confirm) {
            ret.push({
                type: 'button',
                actionType: 'confirm',
                testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('confirm'),
                label: __('confirm'),
                primary: true
            });
        }
        return ret;
    };
    Drawer.prototype.handleSelfClose = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, onClose, store, dispatchEvent, rendererEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, onClose = _a.onClose, store = _a.store, dispatchEvent = _a.dispatchEvent;
                        // 如果有子弹框，那么就先不隐藏自己
                        if (store.dialogOpen !== false || store.drawerOpen !== false) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, dispatchEvent('cancel', this.props.data)];
                    case 1:
                        rendererEvent = _b.sent();
                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                            return [2 /*return*/];
                        }
                        if (!(rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.pendingPromise.length)) return [3 /*break*/, 3];
                        return [4 /*yield*/, rendererEvent.allDone()];
                    case 2:
                        _b.sent();
                        _b.label = 3;
                    case 3:
                        // clear error
                        store.updateMessage();
                        onClose();
                        return [2 /*return*/];
                }
            });
        });
    };
    Drawer.prototype.handleActionSensor = function (p) {
        var store = this.props.store;
        var origin = store.busying;
        store.markBusying(true);
        // clear error
        store.updateMessage();
        p.then(function () {
            store.markBusying(origin);
        }).catch(function (e) {
            store.updateMessage(e.message, true);
            store.markBusying(origin);
        });
    };
    Drawer.prototype.handleAction = function (e, action, data) {
        var _a = this.props, onClose = _a.onClose, onAction = _a.onAction;
        if (action.actionType === 'close' || action.actionType === 'cancel') {
            onClose();
        }
        else if (onAction) {
            onAction(e, action, data);
        }
    };
    Drawer.prototype.handleDrawerConfirm = function (values, action) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        var store = this.props.store;
        if (action.mergeData && values.length === 1 && values[0]) {
            store.updateData(values[0]);
        }
        var drawerAction = store.action;
        var drawer = drawerAction.drawer;
        if (drawer.onConfirm &&
            drawer.onConfirm.apply(drawer, __spreadArray([values, action], args, false)) === false) {
            return;
        }
        store.closeDrawer(true, values);
    };
    Drawer.prototype.handleDrawerClose = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        var store = this.props.store;
        var action = store.action;
        var drawer = action.drawer;
        if (drawer.onClose && drawer.onClose.apply(drawer, args) === false) {
            return;
        }
        store.closeDrawer.apply(store, args);
    };
    Drawer.prototype.handleDialogConfirm = function (values, action) {
        var args = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            args[_i - 2] = arguments[_i];
        }
        var store = this.props.store;
        if (action.mergeData && values.length === 1 && values[0]) {
            store.updateData(values[0]);
        }
        var dialogAction = store.action;
        var dialog = dialogAction.dialog;
        if (dialog.onConfirm &&
            dialog.onConfirm.apply(dialog, __spreadArray([values, action], args, false)) === false) {
            return;
        }
        store.closeDialog(true, values);
    };
    Drawer.prototype.handleDialogClose = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        var store = this.props.store;
        var action = store.action;
        var dialog = action.dialog;
        if (dialog.onClose && dialog.onClose.apply(dialog, args) === false) {
            return;
        }
        store.closeDialog(args[1]);
    };
    Drawer.prototype.handleChildFinished = function (value, action) {
        // 下面会覆盖
    };
    Drawer.prototype.handleFormInit = function (data) {
        var store = this.props.store;
        store.setFormData(data);
    };
    Drawer.prototype.handleFormChange = function (data, name) {
        var store = this.props.store;
        // 如果 drawer 里面不放 form，而是直接放表单项就会进到这里来。
        if (typeof name === 'string') {
            store.changeValue(name, data);
            return;
        }
        store.setFormData(data);
    };
    Drawer.prototype.handleFormSaved = function (data, response) {
        var store = this.props.store;
        store.setFormData(__assign(__assign({}, data), response));
    };
    Drawer.prototype.handleEntered = function () {
        var _a = this.props, lazySchema = _a.lazySchema, store = _a.store;
        store.setEntered(true);
        // 可能还没来得及关闭，事件动作又打开了这个弹窗，这时候需要重置 busying 状态
        store.markBusying(false);
        if (typeof lazySchema === 'function') {
            store.setSchema(lazySchema(this.props));
        }
    };
    Drawer.prototype.handleExited = function () {
        var _a = this.props, lazySchema = _a.lazySchema, store = _a.store, statusStore = _a.statusStore;
        statusStore && isAlive(statusStore) && statusStore.resetAll();
        if (isAlive(store)) {
            store.reset();
            store.clearMessage();
            store.markBusying(false);
            store.setEntered(false);
            if (typeof lazySchema === 'function') {
                store.setSchema('');
            }
        }
    };
    Drawer.prototype.getPopOverContainer = function () {
        return findDOMNode(this).querySelector(".".concat(this.props.classPrefix, "Drawer-content"));
    };
    Drawer.prototype.renderBody = function (body, key) {
        var _this = this;
        var _a = this.props, render = _a.render, store = _a.store;
        if (Array.isArray(body)) {
            return body.map(function (body, key) { return _this.renderBody(body, key); });
        }
        var schema = body;
        var subProps = {
            key: key,
            disabled: store.loading,
            onAction: this.handleAction,
            onFinished: this.handleChildFinished,
            popOverContainer: this.getPopOverContainer,
            onChange: this.handleFormChange,
            onInit: this.handleFormInit,
            onSaved: this.handleFormSaved,
            onActionSensor: this.handleActionSensor,
            btnDisabled: store.loading,
            inDragging: store.inDragging,
            syncLocation: false
        };
        if (schema.type === 'form') {
            schema = __assign({ mode: 'horizontal', wrapWithPanel: false, submitText: null }, schema);
        }
        return render("body".concat(key ? "/".concat(key) : ''), schema, subProps);
    };
    Drawer.prototype.renderFooter = function () {
        var _this = this;
        var actions = this.buildActions();
        var hideActions = this.props.hideActions;
        if (!actions || !actions.length || hideActions) {
            return null;
        }
        var _a = this.props, store = _a.store, render = _a.render, env = _a.env, cx = _a.classnames, showErrorMsg = _a.showErrorMsg, footerClassName = _a.footerClassName, id = _a.id, themeCss = _a.themeCss;
        return (jsxs("div", __assign({ className: cx('Drawer-footer', footerClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerFooterClassName', id: id, themeCss: themeCss }))) }, { children: [store.loading || store.error ? (jsxs("div", __assign({ className: cx('Drawer-info') }, { children: [jsx(Spinner, { size: "sm", show: store.loading }, "info"), !env.forceSilenceInsideError && showErrorMsg && store.error ? (jsx("span", __assign({ className: cx('Drawer-error') }, { children: store.msg }))) : null] }))) : null, actions.map(function (action, key) {
                    return render("action/".concat(key), action, {
                        onAction: _this.handleAction,
                        onActionSensor: undefined,
                        btnDisabled: store.loading,
                        data: store.formData,
                        key: key,
                        disabled: action.disabled || store.loading
                    });
                })] })));
    };
    Drawer.prototype.openFeedback = function (dialog, ctx) {
        var _this = this;
        return new Promise(function (resolve) {
            var store = _this.props.store;
            store.setCurrentAction({
                type: 'button',
                actionType: 'dialog',
                dialog: dialog
            }, _this.props.resolveDefinitions);
            store.openDialog(ctx, undefined, function (confirmed) {
                resolve(confirmed);
            }, _this.context);
        });
    };
    Drawer.prototype.render = function () {
        var store = this.props.store;
        var _a = __assign(__assign({}, this.props), store.schema), className = _a.className, style = _a.style, size = _a.size, closeOnEsc = _a.closeOnEsc, position = _a.position, title = _a.title, render = _a.render, header = _a.header, body = _a.body, bodyClassName = _a.bodyClassName, headerClassName = _a.headerClassName, show = _a.show, showCloseButton = _a.showCloseButton, width = _a.width, height = _a.height, wrapperComponent = _a.wrapperComponent, env = _a.env, resizable = _a.resizable, overlay = _a.overlay, closeOnOutside = _a.closeOnOutside, ns = _a.classPrefix, cx = _a.classnames, drawerContainer = _a.drawerContainer, loadingConfig = _a.loadingConfig, popOverContainer = _a.popOverContainer, themeCss = _a.themeCss, id = _a.id, rest = __rest(_a, ["className", "style", "size", "closeOnEsc", "position", "title", "render", "header", "body", "bodyClassName", "headerClassName", "show", "showCloseButton", "width", "height", "wrapperComponent", "env", "resizable", "overlay", "closeOnOutside", "classPrefix", "classnames", "drawerContainer", "loadingConfig", "popOverContainer", "themeCss", "id"]);
        var Container = wrapperComponent || Drawer$1;
        return (jsxs(Container, __assign({}, rest, { resizable: resizable, classPrefix: ns, className: className, style: style, drawerClassName: setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerClassName', id: id, themeCss: themeCss })), drawerMaskClassName: setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerMaskClassName', id: id, themeCss: themeCss })), size: size, onHide: this.handleSelfClose, disabled: store.loading, show: show, showCloseButton: showCloseButton, width: width, height: height, position: position, overlay: overlay, onEntered: this.handleEntered, onExited: this.handleExited, onDragging: store.setDragging, closeOnEsc: closeOnEsc, closeOnOutside: !store.drawerOpen && !store.dialogOpen && closeOnOutside, container: drawerContainer ? drawerContainer : env === null || env === void 0 ? void 0 : env.getModalContainer }, { children: [title || header ? (jsxs("div", __assign({ className: cx('Drawer-header', headerClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerHeaderClassName', id: id, themeCss: themeCss }))) }, { children: [title ? (jsx("div", __assign({ className: cx('Drawer-title', setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerTitleClassName', id: id, themeCss: themeCss }))) }, { children: render('title', title, {
                                data: store.formData,
                                onConfirm: this.handleDrawerConfirm,
                                onClose: this.handleDrawerClose,
                                onAction: this.handleAction,
                                onActionSensor: undefined,
                                btnDisabled: store.loading
                            }) }))) : null, header
                            ? render('header', header, {
                                data: store.formData,
                                onConfirm: this.handleDrawerConfirm,
                                onClose: this.handleDrawerClose,
                                onAction: this.handleAction,
                                onActionSensor: undefined,
                                btnDisabled: store.loading
                            })
                            : null] }))) : null, !store.entered ? (jsx("div", __assign({ className: cx('Drawer-body', bodyClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerBodyClassName', id: id, themeCss: themeCss }))) }, { children: jsx(Spinner, { overlay: true, show: true, size: "lg", loadingConfig: loadingConfig }) }))) : body ? (
                // dialog-body 用于在 editor 中定位元素
                jsxs("div", __assign({ className: cx('Drawer-body', bodyClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'drawerBodyClassName', id: id, themeCss: themeCss }))), role: "dialog-body" }, { children: [this.renderBody(body, 'body'), jsx(CustomStyle, __assign({}, this.props, { config: {
                                themeCss: themeCss,
                                classNames: [
                                    {
                                        key: 'drawerClassName'
                                    },
                                    {
                                        key: 'drawerMaskClassName'
                                    },
                                    {
                                        key: 'drawerHeaderClassName'
                                    },
                                    {
                                        key: 'drawerTitleClassName'
                                    },
                                    {
                                        key: 'drawerBodyClassName'
                                    },
                                    {
                                        key: 'drawerFooterClassName'
                                    }
                                ],
                                id: id
                            }, env: env }))] }))) : null, this.renderFooter(), body
                    ? render('dialog', __assign(__assign({}, store.dialogSchema), { type: 'dialog' }), {
                        key: 'dialog',
                        data: store.dialogData,
                        onConfirm: this.handleDialogConfirm,
                        onClose: this.handleDialogClose,
                        onAction: this.handleAction,
                        show: store.dialogOpen
                    })
                    : null, body
                    ? render('drawer', __assign(__assign({}, store.drawerSchema), { type: 'drawer' }), {
                        key: 'drawer',
                        data: store.drawerData,
                        onConfirm: this.handleDrawerConfirm,
                        onClose: this.handleDrawerClose,
                        onAction: this.handleAction,
                        show: store.drawerOpen
                    })
                    : null] })));
    };
    Drawer.propsList = [
        'title',
        'size',
        'closeOnEsc',
        'closeOnOutside',
        'children',
        'className',
        'bodyClassName',
        'headerClassName',
        'footerClassName',
        'confirm',
        'position',
        'onClose',
        'onConfirm',
        'show',
        'showCloseButton',
        'width',
        'height',
        'resizable',
        'overlay',
        'body',
        'popOverContainer',
        'showErrorMsg'
    ];
    Drawer.defaultProps = {
        title: '',
        className: '',
        bodyClassName: '',
        headerClassName: '',
        footerClassName: '',
        confirm: true,
        position: 'right',
        resizable: false,
        showCloseButton: true,
        overlay: true,
        closeOnEsc: false,
        closeOnOutside: false,
        showErrorMsg: true
    };
    __decorate([
        autobind
    ], Drawer.prototype, "getPopOverContainer", null);
    return Drawer;
}(React.Component));
var DrawerRenderer = /** @class */ (function (_super) {
    __extends(DrawerRenderer, _super);
    function DrawerRenderer(props, context) {
        var _this = _super.call(this, props) || this;
        var scoped = context;
        scoped.registerComponent(_this);
        return _this;
    }
    DrawerRenderer.prototype.componentWillUnmount = function () {
        var scoped = this.context;
        scoped.unRegisterComponent(this);
        _super.prototype.componentWillUnmount.call(this);
    };
    DrawerRenderer.prototype.tryChildrenToHandle = function (action, ctx, rawAction) {
        var _this = this;
        var scoped = this.context;
        var targets = [];
        var _a = this.props, onConfirm = _a.onConfirm, store = _a.store;
        if (action.target) {
            targets.push.apply(targets, action.target
                .split(',')
                .map(function (name) { return scoped.getComponentByName(name); })
                .filter(function (item) { return item && item.doAction; }));
        }
        /** 如果为隔离动作, 则不做联动处理, 继续交给handleAction */
        if ((action === null || action === void 0 ? void 0 : action.isolateScope) !== true && !targets.length) {
            var components = scoped
                .getComponents()
                .filter(function (item) { return !~['drawer', 'dialog'].indexOf(item.props.type); });
            var pool = components.concat();
            while (pool.length) {
                var item = pool.pop();
                if (~['crud', 'form', 'wizard'].indexOf(item.props.type)) {
                    targets.push(item);
                    break;
                }
                else if (~['drawer', 'dialog'].indexOf(item.props.type)) {
                    continue;
                }
                else if (~['page', 'service'].indexOf(item.props.type)) {
                    pool.unshift.apply(pool, item.context.getComponents());
                }
            }
        }
        if (targets.length) {
            store.markBusying(true);
            store.updateMessage();
            Promise.all(targets.map(function (target) {
                return target.doAction(__assign(__assign({}, action), { from: _this.$$id }), ctx, true);
            }))
                .then(function (values) {
                if ((action.type === 'submit' ||
                    action.actionType === 'submit' ||
                    action.actionType === 'confirm') &&
                    action.close !== false) {
                    onConfirm && onConfirm(values, rawAction || action, ctx, targets);
                }
                else if (action.close) {
                    action.close === true
                        ? _this.handleSelfClose()
                        : _this.closeTarget(action.close);
                }
                store.markBusying(false);
            })
                .catch(function (reason) {
                store.updateMessage(reason.message, true);
                store.markBusying(false);
                // 通常都是数据错误，过 3 秒自动清理错误信息
                // if (reason.constructor?.name === ValidateError.name) {
                clearTimeout(_this.clearErrorTimer);
                _this.clearErrorTimer = setTimeout(function () {
                    store.updateMessage('');
                }, 3000);
                // }
            });
            return true;
        }
        return false;
    };
    DrawerRenderer.prototype.doAction = function (action, data, throwErrors) {
        this.handleAction(undefined, action, data);
    };
    DrawerRenderer.prototype.handleAction = function (e, action, data, throwErrors, delegate, rendererEvent) {
        if (throwErrors === void 0) { throwErrors = false; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, onClose, onAction, store, env, dispatchEvent, show, scoped, rendererEvent_1, rendererEvent_2;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, onClose = _a.onClose, onAction = _a.onAction, store = _a.store, env = _a.env, dispatchEvent = _a.dispatchEvent, show = _a.show;
                        if (action.from === this.$$id || !show) {
                            // 如果是从 children 里面委托过来的，那就直接向上冒泡。
                            // 或者自己已经关闭了，那就不处理。
                            return [2 /*return*/, onAction
                                    ? onAction(e, action, data, throwErrors, delegate || this.context)
                                    : false];
                        }
                        if (!(rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.pendingPromise.length)) return [3 /*break*/, 2];
                        return [4 /*yield*/, rendererEvent.allDone()];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2:
                        scoped = this.context;
                        if (!(action.actionType === 'close' || action.actionType === 'cancel')) return [3 /*break*/, 6];
                        return [4 /*yield*/, dispatchEvent('cancel', createObject(this.props.data, data))];
                    case 3:
                        rendererEvent_1 = _b.sent();
                        if (rendererEvent_1 === null || rendererEvent_1 === void 0 ? void 0 : rendererEvent_1.prevented) {
                            return [2 /*return*/];
                        }
                        if (!(rendererEvent_1 === null || rendererEvent_1 === void 0 ? void 0 : rendererEvent_1.pendingPromise.length)) return [3 /*break*/, 5];
                        return [4 /*yield*/, rendererEvent_1.allDone()];
                    case 4:
                        _b.sent();
                        _b.label = 5;
                    case 5:
                        store.setCurrentAction(action, this.props.resolveDefinitions);
                        onClose();
                        if (action.close) {
                            action.close === true
                                ? this.handleSelfClose()
                                : this.closeTarget(action.close);
                        }
                        return [3 /*break*/, 17];
                    case 6:
                        if (!(action.actionType === 'confirm')) return [3 /*break*/, 10];
                        return [4 /*yield*/, dispatchEvent('confirm', createObject(this.props.data, data))];
                    case 7:
                        rendererEvent_2 = _b.sent();
                        if (rendererEvent_2 === null || rendererEvent_2 === void 0 ? void 0 : rendererEvent_2.prevented) {
                            return [2 /*return*/];
                        }
                        if (!(rendererEvent_2 === null || rendererEvent_2 === void 0 ? void 0 : rendererEvent_2.pendingPromise.length)) return [3 /*break*/, 9];
                        return [4 /*yield*/, rendererEvent_2.allDone()];
                    case 8:
                        _b.sent();
                        _b.label = 9;
                    case 9:
                        store.setCurrentAction(action, this.props.resolveDefinitions);
                        this.tryChildrenToHandle(action, data) || onClose();
                        return [3 /*break*/, 17];
                    case 10:
                        if (!(action.actionType === 'drawer')) return [3 /*break*/, 11];
                        store.setCurrentAction(action, this.props.resolveDefinitions);
                        return [2 /*return*/, new Promise(function (resolve) {
                                store.openDrawer(data, undefined, function (confirmed, value) {
                                    var _a;
                                    (_a = action.callback) === null || _a === void 0 ? void 0 : _a.call(action, confirmed, value);
                                    resolve({
                                        confirmed: confirmed,
                                        value: value
                                    });
                                });
                            })];
                    case 11:
                        if (!(action.actionType === 'dialog')) return [3 /*break*/, 12];
                        store.setCurrentAction(action, this.props.resolveDefinitions);
                        return [2 /*return*/, new Promise(function (resolve) {
                                store.openDialog(data, undefined, function (confirmed, value) {
                                    var _a;
                                    (_a = action.callback) === null || _a === void 0 ? void 0 : _a.call(action, confirmed, value);
                                    resolve({
                                        confirmed: confirmed,
                                        value: value
                                    });
                                }, delegate || _this.context);
                            })];
                    case 12:
                        if (!(action.actionType === 'reload')) return [3 /*break*/, 13];
                        store.setCurrentAction(action, this.props.resolveDefinitions);
                        action.target && scoped.reload(action.target, data);
                        if (action.close) {
                            action.close === true
                                ? this.handleSelfClose()
                                : this.closeTarget(action.close);
                        }
                        return [3 /*break*/, 17];
                    case 13:
                        if (!(!action.from && this.tryChildrenToHandle(action, data))) return [3 /*break*/, 14];
                        return [3 /*break*/, 17];
                    case 14:
                        if (!(action.actionType === 'ajax')) return [3 /*break*/, 15];
                        store.setCurrentAction(action, this.props.resolveDefinitions);
                        store
                            .saveRemote(action.api, data, {
                            successMessage: action.messages && action.messages.success,
                            errorMessage: action.messages && action.messages.failed
                        })
                            .then(function () { return __awaiter(_this, void 0, void 0, function () {
                            var redirect;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        if (!(action.feedback && isVisible(action.feedback, store.data))) return [3 /*break*/, 2];
                                        return [4 /*yield*/, this.openFeedback(action.feedback, store.data)];
                                    case 1:
                                        _a.sent();
                                        _a.label = 2;
                                    case 2:
                                        redirect = action.redirect && filter(action.redirect, store.data);
                                        redirect && env.jumpTo(redirect, action, store.data);
                                        action.reload &&
                                            this.reloadTarget(filterTarget(action.reload, store.data), store.data);
                                        if (action.close) {
                                            action.close === true
                                                ? this.handleSelfClose()
                                                : this.closeTarget(action.close);
                                        }
                                        return [2 /*return*/];
                                }
                            });
                        }); })
                            .catch(function (e) {
                            if (throwErrors || action.countDown) {
                                throw e;
                            }
                        });
                        return [3 /*break*/, 17];
                    case 15:
                        if (!onAction) return [3 /*break*/, 17];
                        return [4 /*yield*/, onAction(e, action, data, throwErrors, delegate || this.context)];
                    case 16:
                        _b.sent();
                        if (action.close) {
                            action.close === true
                                ? this.handleSelfClose()
                                : this.closeTarget(action.close);
                        }
                        _b.label = 17;
                    case 17: return [2 /*return*/];
                }
            });
        });
    };
    DrawerRenderer.prototype.handleChildFinished = function (value, action) {
        if ((action && action.from === this.$$id) || action.close === false) {
            return;
        }
        var scoped = this.context;
        var components = scoped
            .getComponents()
            .filter(function (item) {
            return !~['drawer', 'dialog', 'action', 'button', 'submit', 'reset'].indexOf(item.props.type);
        });
        var onConfirm = this.props.onConfirm;
        if (components.length === 1 &&
            (components[0].props.type === 'form' ||
                components[0].props.type === 'wizard')) {
            onConfirm([value], action, {}, components);
        }
    };
    DrawerRenderer.prototype.handleDialogConfirm = function (values, action) {
        var _a, _b;
        var rest = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            rest[_i - 2] = arguments[_i];
        }
        _super.prototype.handleDialogConfirm.apply(this, __spreadArray([values, action], rest, false));
        var store = this.props.store;
        var scoped = store.getDialogScoped() || this.context;
        var dialogAction = store.action;
        var reload = (_a = action.reload) !== null && _a !== void 0 ? _a : dialogAction.reload;
        if (reload) {
            scoped.reload(reload, store.data);
        }
        else if (scoped.component !== this && ((_b = scoped.component) === null || _b === void 0 ? void 0 : _b.reload)) {
            scoped.component.reload();
        }
        else {
            // 没有设置，则自动让页面中 crud 刷新。
            this.context
                .getComponents()
                .filter(function (item) { return item.props.type === 'crud'; })
                .forEach(function (item) { return item.reload && item.reload(); });
        }
    };
    DrawerRenderer.prototype.handleDrawerConfirm = function (values, action) {
        var _this = this;
        var _a;
        var rest = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            rest[_i - 2] = arguments[_i];
        }
        _super.prototype.handleDrawerConfirm.call(this, values, action);
        var store = this.props.store;
        var scoped = store.getDialogScoped() || this.context;
        var drawerAction = store.action;
        var reload = (_a = action.reload) !== null && _a !== void 0 ? _a : drawerAction.reload;
        // 稍等会，等动画结束。
        setTimeout(function () {
            var _a;
            if (reload) {
                scoped.reload(reload, store.data);
            }
            else if (scoped.component !== _this && ((_a = scoped.component) === null || _a === void 0 ? void 0 : _a.reload)) {
                scoped.component.reload();
            }
            else {
                // 没有设置，则自动让页面中 crud 刷新。
                _this.context
                    .getComponents()
                    .filter(function (item) { return item.props.type === 'crud'; })
                    .forEach(function (item) { return item.reload && item.reload(); });
            }
        }, 300);
    };
    DrawerRenderer.prototype.reloadTarget = function (target, data) {
        var scoped = this.context;
        scoped.reload(target, data);
    };
    DrawerRenderer.prototype.closeTarget = function (target) {
        var scoped = this.context;
        scoped.close(target);
    };
    DrawerRenderer.prototype.setData = function (values, replace) {
        return this.props.store.updateData(values, undefined, replace);
    };
    DrawerRenderer.prototype.getData = function () {
        var store = this.props.store;
        return store.data;
    };
    DrawerRenderer.contextType = ScopedContext;
    DrawerRenderer = __decorate([
        Renderer({
            type: 'drawer',
            storeType: ModalStore.name,
            storeExtendsData: false,
            isolateScope: true,
            shouldSyncSuperStore: function (store, props, prevProps) {
                return !!((store.drawerOpen || props.show) &&
                    (props.show !== prevProps.show ||
                        isObjectShallowModified(prevProps.data, props.data)));
            }
        })
    ], DrawerRenderer);
    return DrawerRenderer;
}(Drawer));

export { DrawerRenderer, Drawer as default };
