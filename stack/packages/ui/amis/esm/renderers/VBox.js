/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { <PERSON><PERSON><PERSON> } from 'amis-core';
import cx from 'classnames';

var VBox = /** @class */ (function (_super) {
    __extends(VBox, _super);
    function VBox() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    VBox.prototype.renderChild = function (region, node) {
        var render = this.props.render;
        return render(region, node);
    };
    VBox.prototype.renderCell = function (row, key) {
        var ns = this.props.classPrefix;
        return (jsx("div", __assign({ className: cx("".concat(ns, "Vbox-cell"), row.cellClassName) }, { children: this.renderChild("row/".concat(key), row) })));
    };
    VBox.prototype.render = function () {
        var _this = this;
        var _a = this.props, className = _a.className, style = _a.style, rows = _a.rows, ns = _a.classPrefix;
        return (jsx("div", __assign({ className: cx("".concat(ns, "Vbox"), className), style: style }, { children: Array.isArray(rows)
                ? rows.map(function (row, key) { return (jsx("div", __assign({ className: cx('row-row', row.rowClassName) }, { children: _this.renderCell(row, key) }), key)); })
                : null })));
    };
    VBox.propsList = ['rows'];
    VBox.defaultProps = {};
    return VBox;
}(React.Component));
var VBoxRenderer = /** @class */ (function (_super) {
    __extends(VBoxRenderer, _super);
    function VBoxRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    VBoxRenderer = __decorate([
        Renderer({
            type: 'vbox'
        })
    ], VBoxRenderer);
    return VBoxRenderer;
}(VBox));

export { VBoxRenderer, VBox as default };
