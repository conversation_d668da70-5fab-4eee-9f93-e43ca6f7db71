/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { normalizeDate, Renderer } from 'amis-core';

var DateRangeField = /** @class */ (function (_super) {
    __extends(DateRangeField, _super);
    function DateRangeField() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    DateRangeField.prototype.render = function () {
        var _a = this.props, _b = _a.delimiter, delimiter = _b === void 0 ? ',' : _b, _c = _a.connector, connector = _c === void 0 ? '~' : _c, value = _a.value, valueFormat = _a.valueFormat, _d = _a.format, format = _d === void 0 ? 'YYYY-MM-DD' : _d, displayFormat = _a.displayFormat, cx = _a.classnames, className = _a.className, style = _a.style;
        if (!value) {
            return null;
        }
        if (typeof value === 'string') {
            value = value.split(delimiter);
        }
        var _e = value[0], startTime = _e === void 0 ? '' : _e, _f = value[1], endTime = _f === void 0 ? '' : _f;
        if (valueFormat) {
            startTime = normalizeDate(startTime, valueFormat);
            endTime = normalizeDate(endTime, valueFormat);
        }
        else {
            startTime = normalizeDate(startTime * 1000);
            endTime = normalizeDate(endTime * 1000);
        }
        startTime = (startTime === null || startTime === void 0 ? void 0 : startTime.isValid())
            ? startTime.format(displayFormat || format)
            : '';
        endTime = (endTime === null || endTime === void 0 ? void 0 : endTime.isValid()) ? endTime.format(displayFormat || format) : '';
        return (jsx("span", __assign({ className: cx('DateRangeField', className), style: style }, { children: [startTime, endTime].join(" ".concat(connector, " ")) })));
    };
    DateRangeField.defaultProps = {
        format: 'YYYY-MM-DD',
        valueFormat: 'X',
        connector: '~'
    };
    return DateRangeField;
}(React.Component));
var DateRangeFieldRenderer = /** @class */ (function (_super) {
    __extends(DateRangeFieldRenderer, _super);
    function DateRangeFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    DateRangeFieldRenderer = __decorate([
        Renderer({
            type: 'date-range'
        })
    ], DateRangeFieldRenderer);
    return DateRangeFieldRenderer;
}(DateRangeField));

export { DateRangeField, DateRangeFieldRenderer };
