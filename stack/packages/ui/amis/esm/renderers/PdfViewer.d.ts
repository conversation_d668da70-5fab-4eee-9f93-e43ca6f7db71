/**
 * @file PdfViewer.tsx PDF 预览
 *
 * @created: 2024/02/26
 */
import React from 'react';
import { IScopedContext, RendererProps } from 'amis-core';
import { BaseSchema } from '../Schema';
export declare const PdfView: React.LazyExoticComponent<React.ComponentType<any>>;
export interface PdfViewerSchema extends BaseSchema {
    type: 'pdf-viewer';
    /**
     * 文件地址
     */
    src?: string;
    /**
     * 文件取值，一般配合表单使用
     */
    name?: string;
    width?: number;
    height?: number;
    background?: string;
}
export interface PdfViewerProps extends RendererProps {
}
interface PdfViewerState {
    loading: boolean;
    inited: boolean;
    width?: number;
    error: boolean;
}
export default class PdfViewer extends React.Component<PdfViewerProps, PdfViewerState> {
    file?: ArrayBuffer;
    reader?: FileReader;
    fetchCancel?: Function;
    wrapper: React.RefObject<HTMLDivElement>;
    constructor(props: PdfViewerProps);
    componentDidMount(): void;
    componentDidUpdate(prevProps: PdfViewerProps): void;
    componentWillUnmount(): void;
    abortLoad(): void;
    renderPdf(): Promise<void>;
    fetchPdf(): Promise<void>;
    renderFormFile(): Promise<void>;
    renderEmpty(): import("react/jsx-runtime").JSX.Element | null;
    renderError(): import("react/jsx-runtime").JSX.Element | null;
    renderTip(): import("react/jsx-runtime").JSX.Element;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class PdfViewerRenderer extends PdfViewer {
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    constructor(props: PdfViewerProps, context: IScopedContext);
    componentWillUnmount(): void;
}
export {};
//# sourceMappingURL=PdfViewer.d.ts.map