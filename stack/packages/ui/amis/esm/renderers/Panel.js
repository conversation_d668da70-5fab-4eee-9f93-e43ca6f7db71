/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __rest, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import { RENDERER_TRANSMISSION_OMIT_PROPS, setThemeClassName, CustomStyle, Renderer } from 'amis-core';
import omit from 'lodash/omit';
import { Icon } from 'amis-ui';

var Panel = /** @class */ (function (_super) {
    __extends(Panel, _super);
    function Panel(props) {
        var _this = _super.call(this, props) || this;
        _this.state = {
            collapsed: false
        };
        props.mobileUI && props.collapsible && (_this.state.collapsed = true);
        return _this;
    }
    Panel.prototype.renderBody = function () {
        var _a = this.props, type = _a.type, className = _a.className, style = _a.style, data = _a.data, header = _a.header, body = _a.body, render = _a.render, bodyClassName = _a.bodyClassName, headerClassName = _a.headerClassName, actionsClassName = _a.actionsClassName, footerClassName = _a.footerClassName, children = _a.children, title = _a.title, actions = _a.actions, footer = _a.footer, ns = _a.classPrefix, formMode = _a.formMode, formHorizontal = _a.formHorizontal, subFormMode = _a.subFormMode, subFormHorizontal = _a.subFormHorizontal, id = _a.id, themeCss = _a.themeCss, wrapperCustomStyle = _a.wrapperCustomStyle, rest = __rest(_a, ["type", "className", "style", "data", "header", "body", "render", "bodyClassName", "headerClassName", "actionsClassName", "footerClassName", "children", "title", "actions", "footer", "classPrefix", "formMode", "formHorizontal", "subFormMode", "subFormHorizontal", "id", "themeCss", "wrapperCustomStyle"]);
        var subProps = __assign(__assign({ data: data }, omit(rest, RENDERER_TRANSMISSION_OMIT_PROPS)), { formMode: subFormMode || formMode, formHorizontal: subFormHorizontal || formHorizontal });
        return children
            ? typeof children === 'function'
                ? children(this.props)
                : children
            : body
                ? render('body', body, subProps)
                : null;
    };
    Panel.prototype.renderActions = function () {
        var _a = this.props, actions = _a.actions, render = _a.render;
        if (Array.isArray(actions)) {
            return actions.map(function (action, key) {
                return render('action', action, {
                    type: action.type || 'button',
                    key: key
                });
            });
        }
        return null;
    };
    Panel.prototype.render = function () {
        var _this = this;
        var _a = this.props, type = _a.type, className = _a.className, style = _a.style, data = _a.data, header = _a.header, body = _a.body, render = _a.render, bodyClassName = _a.bodyClassName, headerClassName = _a.headerClassName, actionsClassName = _a.actionsClassName, footerClassName = _a.footerClassName, footerWrapClassName = _a.footerWrapClassName, headerControlClassName = _a.headerControlClassName, headerTitleControlClassName = _a.headerTitleControlClassName, bodyControlClassName = _a.bodyControlClassName, actionsControlClassName = _a.actionsControlClassName, children = _a.children, title = _a.title, footer = _a.footer, affixFooter = _a.affixFooter, ns = _a.classPrefix, cx = _a.classnames, id = _a.id, collapsible = _a.collapsible, themeCss = _a.themeCss, wrapperCustomStyle = _a.wrapperCustomStyle, rest = __rest(_a, ["type", "className", "style", "data", "header", "body", "render", "bodyClassName", "headerClassName", "actionsClassName", "footerClassName", "footerWrapClassName", "headerControlClassName", "headerTitleControlClassName", "bodyControlClassName", "actionsControlClassName", "children", "title", "footer", "affixFooter", "classPrefix", "classnames", "id", "collapsible", "themeCss", "wrapperCustomStyle"]);
        var subProps = __assign({ data: data }, rest);
        var footerDoms = [];
        var collapsed = this.state.collapsed;
        if (!collapsed) {
            if (Array.isArray(this.props.actions) && this.props.actions.length) {
                footerDoms.push(jsx("div", __assign({ className: cx("Panel-btnToolbar", actionsClassName || "Panel-footer", actionsControlClassName) }, { children: this.renderActions() }), "actions"));
            }
            footer &&
                footerDoms.push(jsx("div", __assign({ className: cx(footerClassName || "Panel-footer", actionsControlClassName) }, { children: render('footer', footer, subProps) }), "footer"));
        }
        var footerDom = footerDoms.length ? (jsx("div", __assign({ className: cx('Panel-footerWrap', footerWrapClassName, affixFooter ? 'Panel-fixedBottom' : '', setThemeClassName(__assign(__assign({}, this.props), { name: 'footerControlClassName', id: id, themeCss: themeCss }))) }, { children: footerDoms }))) : null;
        return (jsxs("div", __assign({ "data-id": id, "data-role": "container", className: cx("Panel", className || "Panel--default", setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), style: style }, { children: [header ? (jsx("div", __assign({ className: cx(headerClassName || "Panel-heading", headerControlClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'headerControlClassName', id: id, themeCss: themeCss }))) }, { children: render('header', header, subProps) }))) : title ? (jsxs("div", __assign({ className: cx(headerClassName || "Panel-heading", headerControlClassName, {
                        'is-collapsible': collapsible
                    }, setThemeClassName(__assign(__assign({}, this.props), { name: 'headerControlClassName', id: id, themeCss: themeCss }))) }, { children: [jsx("h3", __assign({ className: cx("Panel-title", headerTitleControlClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'titleControlClassName', id: id, themeCss: themeCss }))) }, { children: render('title', title, subProps) })), collapsible ? (jsx("span", __assign({ className: cx('Panel-arrow-wrap'), onClick: function () {
                                _this.setState({
                                    collapsed: !collapsed
                                });
                            } }, { children: jsx(Icon, { icon: "down-arrow-bold", className: cx('Panel-arrow', 'icon', {
                                    'is-collapsed': collapsed
                                }) }) }))) : null] }))) : null, !collapsed ? (jsx("div", __assign({ className: cx(bodyClassName || "Panel-body", bodyControlClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'bodyControlClassName', id: id, themeCss: themeCss }))) }, { children: this.renderBody() }))) : null, footerDom, jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            },
                            {
                                key: 'bodyControlClassName'
                            },
                            {
                                key: 'headerControlClassName',
                                weights: {
                                    default: {
                                        suf: ".".concat(ns, "Panel-heading")
                                    }
                                }
                            },
                            {
                                key: 'titleControlClassName'
                            },
                            {
                                key: 'footerControlClassName'
                            }
                        ]
                    }, env: this.props.env }))] })));
    };
    Panel.propsList = [
        'header',
        'actions',
        'children',
        'headerClassName',
        'footerClassName',
        'footerWrapClassName',
        'actionsClassName',
        'bodyClassName'
    ];
    Panel.defaultProps = {
    // className: 'Panel--default',
    // headerClassName: 'Panel-heading',
    // footerClassName: 'Panel-footer bg-light lter Wrapper',
    // actionsClassName: 'Panel-footer',
    // bodyClassName: 'Panel-body'
    };
    return Panel;
}(React.Component));
var PanelRenderer = /** @class */ (function (_super) {
    __extends(PanelRenderer, _super);
    function PanelRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PanelRenderer = __decorate([
        Renderer({
            type: 'panel'
        })
    ], PanelRenderer);
    return PanelRenderer;
}(Panel));

export { PanelRenderer, Panel as default };
