/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import React from 'react';
import { resolveEventData, setThemeClassName, CustomStyle, autobind, ScopedContext, Renderer } from 'amis-core';
import { Collapse as Collapse$1, Icon } from 'amis-ui';
import cx from 'classnames';

var Collapse = /** @class */ (function (_super) {
    __extends(Collapse, _super);
    function Collapse() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.basicCollapse = React.createRef();
        return _this;
    }
    Collapse.prototype.handleCollapseChange = function (collapsed) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, dispatchEvent, onCollapse, eventData, changeEvent, toggleEvent;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this.props, dispatchEvent = _a.dispatchEvent, onCollapse = _a.onCollapse;
                        eventData = resolveEventData(this.props, {
                            collapsed: collapsed
                        });
                        return [4 /*yield*/, dispatchEvent('change', eventData)];
                    case 1:
                        changeEvent = _b.sent();
                        return [4 /*yield*/, dispatchEvent(collapsed ? 'collapse' : 'expand', eventData)];
                    case 2:
                        toggleEvent = _b.sent();
                        if ((changeEvent === null || changeEvent === void 0 ? void 0 : changeEvent.prevented) || (toggleEvent === null || toggleEvent === void 0 ? void 0 : toggleEvent.prevented)) {
                            return [2 /*return*/];
                        }
                        onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(collapsed);
                        return [2 /*return*/];
                }
            });
        });
    };
    Collapse.prototype.doAction = function (action, data, throwErrors, args) {
        var _a, _b, _c, _d;
        if (this.props.disabled || this.props.collapsable === false) {
            return;
        }
        if (['expand', 'collapse'].includes(action.actionType)) {
            var targetState = action.actionType === 'collapse';
            /**
             * 说明：changeCollapsedState 会执行 onCollapse 方法（间接执行handleCollapseChange），
             * 所以这里不需要再重复调用。
             */
            // this.handleCollapseChange(targetState);
            var collapseInstance = (_c = (_b = (_a = this.basicCollapse) === null || _a === void 0 ? void 0 : _a.current) === null || _b === void 0 ? void 0 : _b.getWrappedInstance) === null || _c === void 0 ? void 0 : _c.call(_b);
            (_d = collapseInstance === null || collapseInstance === void 0 ? void 0 : collapseInstance.changeCollapsedState) === null || _d === void 0 ? void 0 : _d.call(collapseInstance, targetState);
        }
    };
    Collapse.prototype.render = function () {
        var _a = this.props, id = _a.id, ns = _a.classPrefix, cx$1 = _a.classnames, size = _a.size, wrapperComponent = _a.wrapperComponent, headingComponent = _a.headingComponent, className = _a.className, style = _a.style, headingClassName = _a.headingClassName, children = _a.children, titlePosition = _a.titlePosition, headerPosition = _a.headerPosition, title = _a.title, collapseTitle = _a.collapseTitle, collapseHeader = _a.collapseHeader, header = _a.header, body = _a.body, bodyClassName = _a.bodyClassName, render = _a.render, collapsable = _a.collapsable, __ = _a.translate, mountOnEnter = _a.mountOnEnter, unmountOnExit = _a.unmountOnExit, showArrow = _a.showArrow, expandIcon = _a.expandIcon, disabled = _a.disabled, collapsed = _a.collapsed, propsUpdate = _a.propsUpdate, mobileUI = _a.mobileUI, divideLine = _a.divideLine, enableFieldSetStyle = _a.enableFieldSetStyle, themeCss = _a.themeCss, wrapperCustomStyle = _a.wrapperCustomStyle;
        var heading = title || header || '';
        return (jsxs(Fragment, { children: [jsx(Collapse$1, { id: id, ref: this.basicCollapse, classnames: cx$1, classPrefix: ns, mountOnEnter: mountOnEnter, unmountOnExit: unmountOnExit, size: size, wrapperComponent: wrapperComponent, headingComponent: headingComponent, className: cx(className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), style: style, headingClassName: cx(headingClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'headerControlClassName', id: id, themeCss: themeCss }))), bodyClassName: cx(bodyClassName, setThemeClassName(__assign(__assign({}, this.props), { name: 'bodyControlClassName', id: id, themeCss: themeCss }))), headerPosition: titlePosition || headerPosition, collapsable: collapsable, collapsed: collapsed, showArrow: showArrow, disabled: disabled, propsUpdate: propsUpdate, expandIcon: expandIcon ? (typeof expandIcon.icon === 'object' ? (jsx(Icon, { cx: cx$1, icon: expandIcon.icon, className: cx$1('Collapse-icon-tranform') })) : (render('arrow-icon', expandIcon || '', {
                        className: cx$1('Collapse-icon-tranform')
                    }))) : null, collapseHeader: collapseTitle || collapseHeader
                        ? render('heading', collapseTitle || collapseHeader)
                        : null, header: heading ? render('heading', heading) : null, body: children
                        ? typeof children === 'function'
                            ? children(this.props)
                            : children
                        : body
                            ? render('body', body)
                            : null, mobileUI: mobileUI, onCollapse: this.handleCollapseChange, divideLine: divideLine, enableFieldSetStyle: enableFieldSetStyle }), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            },
                            {
                                key: 'bodyControlClassName',
                                weights: {
                                    default: {
                                        inner: ".".concat(ns, "Collapse-content")
                                    }
                                }
                            },
                            {
                                key: 'headerControlClassName'
                            }
                        ]
                    }, env: this.props.env }))] }));
    };
    Collapse.propsList = [
        'collapsable',
        'collapsed',
        'collapseTitle',
        'showArrow',
        'headerPosition',
        'bodyClassName',
        'headingClassName',
        'collapseHeader',
        'size'
    ];
    __decorate([
        autobind
    ], Collapse.prototype, "handleCollapseChange", null);
    return Collapse;
}(React.Component));
var CollapseRenderer = /** @class */ (function (_super) {
    __extends(CollapseRenderer, _super);
    function CollapseRenderer(props, context) {
        var _this = _super.call(this, props) || this;
        var scoped = context;
        scoped.registerComponent(_this);
        return _this;
    }
    CollapseRenderer.prototype.componentWillUnmount = function () {
        var scoped = this.context;
        scoped.unRegisterComponent(this);
    };
    CollapseRenderer.contextType = ScopedContext;
    CollapseRenderer = __decorate([
        Renderer({
            type: 'collapse'
        })
    ], CollapseRenderer);
    return CollapseRenderer;
}(Collapse));

export { CollapseRenderer, Collapse as default };
