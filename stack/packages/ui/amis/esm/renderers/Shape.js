/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __rest, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import { filter, autobind, Renderer } from 'amis-core';
import { Shape } from 'amis-ui';
import cx from 'classnames';

var ShapeRenderer = /** @class */ (function (_super) {
    __extends(ShapeRenderer, _super);
    function ShapeRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ShapeRenderer.prototype.handleClick = function () {
        this.props.dispatchEvent('click', this.props.data);
    };
    ShapeRenderer.prototype.render = function () {
        var _a = this.props, className = _a.className, radius = _a.radius, shapeType = _a.shapeType, data = _a.data, rest = __rest(_a, ["className", "radius", "shapeType", "data"]);
        var shapeTypeValue = filter(shapeType, data) || shapeType;
        var radiusValue = +filter(radius, data) || radius;
        return (jsx(Shape, __assign({}, rest, { className: cx(className), shapeType: shapeTypeValue, radius: radiusValue, onClick: this.handleClick })));
    };
    __decorate([
        autobind
    ], ShapeRenderer.prototype, "handleClick", null);
    ShapeRenderer = __decorate([
        Renderer({
            type: 'shape'
        })
    ], ShapeRenderer);
    return ShapeRenderer;
}(React.Component));

export { ShapeRenderer };
