/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import React, { createElement } from 'react';
import { jsx, jsxs } from 'react/jsx-runtime';
import mapValues from 'lodash/mapValues';
import { Tab, Tabs } from 'amis-ui';
import { str2AsyncFunction, filter, resolveVariable, createObject, isObject, isVisible, isDisabled, autobind, Renderer } from 'amis-core';

var Portlet = /** @class */ (function (_super) {
    __extends(Portlet, _super);
    function Portlet(props) {
        var _this = _super.call(this, props) || this;
        var activeKey = props.activeKey || 0;
        _this.state = {
            activeKey: activeKey
        };
        return _this;
    }
    Portlet.prototype.handleSelect = function (key) {
        var _a = this.props, onSelect = _a.onSelect, tabs = _a.tabs;
        if (typeof key === 'number' && key < tabs.length) {
            this.setState({
                activeKey: key
            });
        }
        if (typeof onSelect === 'string') {
            var selectFunc = str2AsyncFunction(onSelect, 'key', 'props');
            selectFunc && selectFunc(key, this.props);
        }
        else if (typeof onSelect === 'function') {
            onSelect(key, this.props);
        }
    };
    Portlet.prototype.renderToolbarItem = function (toolbar) {
        var render = this.props.render;
        var actions = [];
        if (Array.isArray(toolbar)) {
            toolbar.forEach(function (action, index) {
                return actions.push(render("toolbar/".concat(index), __assign({ type: 'button', level: 'link', size: 'sm' }, action), {
                    key: index
                }));
            });
        }
        return actions;
    };
    Portlet.prototype.renderToolbar = function () {
        var _a = this.props, toolbar = _a.toolbar, cx = _a.classnames, ns = _a.classPrefix, tabs = _a.tabs;
        var activeKey = this.state.activeKey;
        var tabToolbar = null;
        var tabToolbarTpl = null;
        // tabs里的toolbar
        var toolbarTpl = toolbar ? (jsx("div", __assign({ className: cx("".concat(ns, "toolbar")) }, { children: this.renderToolbarItem(toolbar) }))) : null;
        // tab里的toolbar
        if (typeof activeKey !== 'undefined') {
            tabToolbar = tabs[activeKey].toolbar;
            tabToolbarTpl = tabToolbar ? (jsx("div", __assign({ className: cx("".concat(ns, "tab-toolbar")) }, { children: this.renderToolbarItem(tabToolbar) }))) : null;
        }
        return toolbarTpl || tabToolbarTpl ? (jsxs("div", __assign({ className: cx("".concat(ns, "Portlet-toolbar")) }, { children: [toolbarTpl, tabToolbarTpl] }))) : null;
    };
    Portlet.prototype.renderDesc = function () {
        var _a = this.props, descTpl = _a.description, render = _a.render, cx = _a.classnames, ns = _a.classPrefix, data = _a.data;
        var desc = filter(descTpl, data);
        return desc ? (jsx("span", __assign({ className: cx("".concat(ns, "Portlet-header-desc")) }, { children: desc }))) : null;
    };
    Portlet.prototype.renderTabs = function () {
        var _a;
        var _this = this;
        var _b = this.props, cx = _b.classnames, ns = _b.classPrefix, tabsClassName = _b.tabsClassName, contentClassName = _b.contentClassName, linksClassName = _b.linksClassName, tabRender = _b.tabRender, render = _b.render, data = _b.data, dMode = _b.mode, tabsMode = _b.tabsMode, unmountOnExit = _b.unmountOnExit, source = _b.source, mountOnEnter = _b.mountOnEnter, scrollable = _b.scrollable, __ = _b.translate, addBtnText = _b.addBtnText, divider = _b.divider;
        var mode = tabsMode || dMode;
        var arr = resolveVariable(source, data);
        var tabs = this.props.tabs;
        if (!tabs) {
            return null;
        }
        tabs = Array.isArray(tabs) ? tabs : [tabs];
        var children = [];
        var tabClassname = cx("".concat(ns, "Portlet-tab"), tabsClassName, (_a = {},
            _a['unactive-select'] = tabs.length <= 1,
            _a['no-divider'] = !divider,
            _a));
        if (Array.isArray(arr)) {
            arr.forEach(function (value, index) {
                var ctx = createObject(data, isObject(value) ? __assign({ index: index }, value) : { item: value, index: index });
                children.push.apply(children, tabs.map(function (tab, tabIndex) {
                    return isVisible(tab, ctx) ? (createElement(Tab, __assign({}, tab, { title: filter(tab.title, ctx), disabled: isDisabled(tab, ctx), key: "".concat(index * 1000 + tabIndex), eventKey: index * 1000 + tabIndex, mountOnEnter: mountOnEnter, unmountOnExit: typeof tab.reload === 'boolean'
                            ? tab.reload
                            : typeof tab.unmountOnExit === 'boolean'
                                ? tab.unmountOnExit
                                : unmountOnExit }), render("item/".concat(index, "/").concat(tabIndex), (tab === null || tab === void 0 ? void 0 : tab.type) ? tab : tab.tab || tab.body, {
                        data: ctx
                    }))) : null;
                }));
            });
        }
        else {
            children = tabs.map(function (tab, index) {
                return isVisible(tab, data) ? (createElement(Tab, __assign({}, tab, { title: filter(tab.title, data), disabled: isDisabled(tab, data), key: index, eventKey: index, mountOnEnter: mountOnEnter, unmountOnExit: typeof tab.reload === 'boolean'
                        ? tab.reload
                        : typeof tab.unmountOnExit === 'boolean'
                            ? tab.unmountOnExit
                            : unmountOnExit }), _this.renderTab
                    ? _this.renderTab(tab, _this.props, index)
                    : tabRender
                        ? tabRender(tab, _this.props, index)
                        : render("tab/".concat(index), (tab === null || tab === void 0 ? void 0 : tab.type) ? tab : tab.tab || tab.body))) : null;
            });
        }
        return (jsx(Tabs, __assign({ addBtnText: __(addBtnText || 'add'), classPrefix: ns, classnames: cx, mode: mode, className: tabClassname, contentClassName: contentClassName, linksClassName: linksClassName, activeKey: this.state.activeKey, onSelect: this.handleSelect, toolbar: this.renderToolbar(), additionBtns: this.renderDesc(), scrollable: scrollable }, { children: children })));
    };
    Portlet.prototype.render = function () {
        var _a;
        var _b = this.props, className = _b.className, data = _b.data, cx = _b.classnames, ns = _b.classPrefix, style = _b.style, hideHeader = _b.hideHeader;
        var portletClassname = cx("".concat(ns, "Portlet"), className, (_a = {},
            _a['no-header'] = hideHeader,
            _a));
        var styleVar = typeof style === 'string'
            ? resolveVariable(style, data) || {}
            : mapValues(style, function (s) { return resolveVariable(s, data) || s; });
        return (jsx("div", __assign({ className: portletClassname, style: styleVar }, { children: this.renderTabs() })));
    };
    Portlet.defaultProps = {
        className: '',
        mode: 'line',
        divider: true
    };
    __decorate([
        autobind
    ], Portlet.prototype, "handleSelect", null);
    return Portlet;
}(React.Component));
var PortletRenderer = /** @class */ (function (_super) {
    __extends(PortletRenderer, _super);
    function PortletRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PortletRenderer = __decorate([
        Renderer({
            type: 'portlet'
        })
    ], PortletRenderer);
    return PortletRenderer;
}(Portlet));

export { Portlet, PortletRenderer };
