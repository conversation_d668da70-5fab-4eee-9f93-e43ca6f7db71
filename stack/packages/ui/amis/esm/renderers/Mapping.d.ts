import React from 'react';
import { RendererProps } from 'amis-core';
import { BaseSchema, SchemaApi, SchemaTokenizeableString, SchemaTpl, SchemaCollection } from '../Schema';
import { Instance } from 'mobx-state-tree';
/**
 * Mapping 映射展示控件。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/mapping
 */
export interface MappingSchema extends BaseSchema {
    /**
     * 指定为映射展示控件
     */
    type: 'map' | 'mapping';
    /**
     * 关联字段名。
     */
    name?: string;
    /**
     * 配置映射规则，值可以使用模板语法。当 key 为 * 时表示 else，也就是说值没有映射到任何规则时用 * 对应的值展示。
     */
    map?: {
        [propName: string]: SchemaTpl;
    };
    /**
     * map或source为对象数组时，作为value值的字段名
     */
    valueField?: string;
    /**
     * map或source为对象数组时，作为label值的字段名
     */
    labelField?: string;
    /**
     * 自定义渲染映射值，支持html或schema
     */
    itemSchema?: SchemaCollection;
    /**
     * 如果想远程拉取字典，请配置 source 为接口。
     */
    source?: SchemaApi | SchemaTokenizeableString;
    /**
     * 占位符
     */
    placeholder?: string;
}
export declare const Store: import("mobx-state-tree").IModelType<{
    id: import("mobx-state-tree").ISimpleType<string>;
    path: import("mobx-state-tree").IType<string | undefined, string, string>;
    storeType: import("mobx-state-tree").ISimpleType<string>;
    disposed: import("mobx-state-tree").IType<boolean | undefined, boolean, boolean>;
    parentId: import("mobx-state-tree").IType<string | undefined, string, string>;
    childrenIds: import("mobx-state-tree").IOptionalIType<import("mobx-state-tree").IArrayType<import("mobx-state-tree").ISimpleType<string>>, [undefined]>;
} & {
    fetching: import("mobx-state-tree").IType<boolean | undefined, boolean, boolean>;
    errorMsg: import("mobx-state-tree").IType<string | undefined, string, string>;
    valueField: import("mobx-state-tree").IType<string | undefined, string, string>;
    map: import("mobx-state-tree").IType<{
        [propName: string]: any;
    } | null | undefined, {
        [propName: string]: any;
    }, {
        [propName: string]: any;
    }>;
}, {
    readonly parentStore: any;
    readonly __: any;
    readonly hasChildren: boolean;
    readonly children: any[];
} & {
    onChildStoreDispose(child: any): void; /**
     * 关联字段名。
     */
    syncProps(props: any, prevProps: any, list?: string[] | undefined): void;
    dispose: (callback?: (() => void) | undefined) => void;
    addChildId: (id: string) => void;
    removeChildId: (id: string) => void;
} & {
    load: (env: RendererEnv, api: Api, data: any) => Promise<any>;
    setMap(options: any): void;
}, import("mobx-state-tree")._NotCustomized, import("mobx-state-tree")._NotCustomized>;
export type IStore = Instance<typeof Store>;
export interface MappingProps extends Omit<RendererProps, 'store'>, Omit<MappingSchema, 'type' | 'className'> {
    store: IStore;
    renderValue: (map: any, key: any) => String;
    renderViewValue: (value: any, key: any) => React.ReactNode;
}
export declare const MappingField: any;
export declare class MappingFieldRenderer extends React.Component<RendererProps> {
    render(): import("react/jsx-runtime").JSX.Element;
}
//# sourceMappingURL=Mapping.d.ts.map