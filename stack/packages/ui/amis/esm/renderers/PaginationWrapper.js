/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import { Ren<PERSON><PERSON>, PaginationStore } from 'amis-core';

var PaginationWrapper = /** @class */ (function (_super) {
    __extends(PaginationWrapper, _super);
    function PaginationWrapper(props) {
        var _this = _super.call(this, props) || this;
        props.store.syncProps(props, undefined, [
            'perPage',
            'mode',
            'ellipsisPageGap',
            'inputName',
            'outputName'
        ]);
        return _this;
    }
    PaginationWrapper.prototype.componentDidUpdate = function (prevProps) {
        var store = this.props.store;
        store.syncProps(this.props, prevProps, [
            'perPage',
            'mode',
            'ellipsisPageGap',
            'inputName',
            'outputName'
        ]);
    };
    PaginationWrapper.prototype.render = function () {
        var _a = this.props, position = _a.position, render = _a.render, store = _a.store, cx = _a.classnames, style = _a.style, body = _a.body, __ = _a.translate;
        var pagination = position !== 'none'
            ? render('pager', {
                type: 'pagination'
            }, {
                activePage: store.page,
                lastPage: store.lastPage,
                mode: store.mode,
                ellipsisPageGap: store.ellipsisPageGap,
                onPageChange: store.switchTo,
                perPage: store.perPage,
                className: 'PaginationWrapper-pager'
            })
            : null;
        return (jsxs("div", __assign({ className: cx('PaginationWrapper'), style: style }, { children: [position === 'top' ? pagination : null, body ? (render('body', body, {
                    data: store.locals
                })) : (jsx("span", { children: __('PaginationWrapper.placeholder') })), position === 'bottom' ? pagination : null] })));
    };
    PaginationWrapper.defaultProps = {
        inputName: 'items',
        outputName: 'items',
        perPage: 10,
        position: 'top'
    };
    return PaginationWrapper;
}(React.Component));
var PaginationWrapperRenderer = /** @class */ (function (_super) {
    __extends(PaginationWrapperRenderer, _super);
    function PaginationWrapperRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PaginationWrapperRenderer = __decorate([
        Renderer({
            type: 'pagination-wrapper',
            storeType: PaginationStore.name
        })
    ], PaginationWrapperRenderer);
    return PaginationWrapperRenderer;
}(PaginationWrapper));

export { PaginationWrapper, PaginationWrapperRenderer };
