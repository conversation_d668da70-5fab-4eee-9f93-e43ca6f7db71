/**
 * @file Words
 */
import React from 'react';
import { RendererProps } from 'amis-core';
import { BaseSchema, SchemaObject } from '../Schema';
import { TagSchema } from './Tag';
type Words = string | string[];
/**
 * Words
 */
export interface WordsSchema extends BaseSchema {
    type: 'words';
    /**
     * 展示限制, 为0时也无限制
     */
    limit?: number;
    /**
     * 展示文字
     */
    expendButtonText?: string;
    /**
     * 展示文字
     */
    expendButton?: SchemaObject;
    /**
     * 收起文字
     */
    collapseButtonText?: string;
    /**
     * 展示文字
     */
    collapseButton?: SchemaObject;
    /**
     * tags数据
     */
    words: Words;
    /**
     * useTag 当数据是数组时，是否使用tag的方式展示
     */
    inTag?: boolean | TagSchema;
    /**
     * 分割符
     */
    delimiter?: string;
    /**
     * 标签模板
     */
    labelTpl?: string;
}
export interface WordsProps extends RendererProps, Omit<WordsSchema, 'type' | 'className'> {
}
export declare class WordsField extends React.Component<WordsProps, object> {
    static defaultProps: Partial<WordsProps>;
    state: {
        isExpend: boolean;
    };
    toggleExpend(): void;
    getLimit(words: Words): number;
    handleItemClick(e: React.MouseEvent<HTMLSpanElement>): void;
    renderContent(words: Words): string | import("react/jsx-runtime").JSX.Element[];
    renderAll(words: Words, hasBtn?: boolean): import("react/jsx-runtime").JSX.Element;
    renderPart(words: Words): import("react/jsx-runtime").JSX.Element;
    getWords(): any;
    render(): import("react/jsx-runtime").JSX.Element | null;
}
export declare class WordsRenderer extends WordsField {
}
export declare class TagsRenderer extends WordsField {
    static defaultProps: {
        inTag: boolean;
    };
}
export {};
//# sourceMappingURL=Words.d.ts.map