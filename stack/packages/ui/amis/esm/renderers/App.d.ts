import React from 'react';
import { SpinnerExtraProps } from 'amis-ui';
import { RendererProps } from 'amis-core';
import { BaseSchema, SchemaApi, SchemaClassName, SchemaCollection } from '../Schema';
import { IScopedContext } from 'amis-core';
import { IAppStore } from 'amis-core';
export interface AppPage extends SpinnerExtraProps {
    /**
     * 菜单文字
     */
    label?: string;
    /**
     * 菜单图标，比如： fa fa-file
     */
    icon?: string;
    /**
     * 路由规则。比如：/banner/:id。当地址以 / 打头，则不继承上层的路径，否则将集成父级页面的路径。
     */
    url?: string;
    /**
     * 当match url 时跳转到目标地址.没有配置 schema 和 shcemaApi  时有效.
     */
    redirect?: string;
    /**
     * 当match url 转成渲染目标地址的页面.没有配置 schema 和 shcemaApi  时有效.
     */
    rewrite?: string;
    /**
     * 不要出现多个，如果出现多个只有第一个有用。在路由找不到的时候作为默认页面。
     */
    isDefaultPage?: boolean;
    /**
     * 二选一，如果配置了 url 一定要配置。否则不知道如何渲染。
     */
    schema?: any;
    schemaApi?: any;
    /**
     * 单纯的地址。可以设置外部链接。
     */
    link?: string;
    /**
     * 支持多层级。
     */
    children?: Array<AppPage>;
    /**
     * 菜单上的类名
     */
    className?: SchemaClassName;
    /**
     * 是否在导航中可见，适合于那种需要携带参数才显示的页面。比如具体某个数据的编辑页面。
     */
    visible?: boolean;
}
/**
 * App 渲染器，适合 JSSDK 用来做多页渲染。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/app
 */
export interface AppSchema extends BaseSchema, SpinnerExtraProps {
    /**
     * 指定为 app 类型。
     */
    type: 'app';
    api?: SchemaApi;
    /**
     * 系统名称
     */
    brandName?: string;
    /**
     * logo 图片地址，可以是 svg。
     */
    logo?: string;
    /**
     * 顶部区域
     */
    header?: SchemaCollection;
    /**
     * 边栏菜单前面的区域
     */
    asideBefore?: SchemaCollection;
    /**
     * 边栏菜单后面的区域
     */
    asideAfter?: SchemaCollection;
    /**
     * 页面集合。
     */
    pages?: Array<AppPage> | AppPage;
    /**
     * 底部区域。
     */
    footer?: SchemaCollection;
    /**
     * css 类名。
     */
    className?: SchemaClassName;
    /**
     * 显示面包屑路径。
     */
    showBreadcrumb?: boolean;
    /**
     * 显示面包屑完整路径。
     */
    showFullBreadcrumbPath?: boolean;
    /**
     * 显示面包屑首页路径。
     */
    showBreadcrumbHomePath?: boolean;
}
export interface AppProps extends RendererProps, Omit<AppSchema, 'type' | 'className'> {
    children?: JSX.Element | ((props?: any) => JSX.Element);
    store: IAppStore;
}
export declare class App extends React.Component<AppProps, object> {
    static propsList: Array<string>;
    static defaultProps: {};
    unWatchRouteChange?: () => void;
    constructor(props: AppProps);
    componentDidMount(): Promise<void>;
    componentDidUpdate(prevProps: AppProps): Promise<void>;
    componentWillUnmount(): void;
    reload(subpath?: any, query?: any, ctx?: any, silent?: boolean, replace?: boolean): Promise<any>;
    receive(values: object, subPath?: string, replace?: boolean): Promise<any>;
    /**
     * 支持页面层定义 definitions，并且优先取页面层的 definitions
     * @param name
     * @returns
     */
    resolveDefinitions(name: string): any;
    handleNavClick(e: React.MouseEvent): void;
    renderHeader(): import("react/jsx-runtime").JSX.Element | null;
    renderAside(): import("react/jsx-runtime").JSX.Element;
    renderFooter(): any;
    render(): import("react/jsx-runtime").JSX.Element;
}
export default class AppRenderer extends App {
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    constructor(props: AppProps, context: IScopedContext);
    componentWillUnmount(): void;
    setData(values: object, replace?: boolean): any;
    getData(): any;
}
//# sourceMappingURL=App.d.ts.map