/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __spreadArray } from 'tslib';
import { registerCustomVendor } from 'amis-core';

function guid() {
    return ('mk-' +
        'xxxxxxxx'.replace(/x/g, function (c) {
            var r = (Math.random() * 16) | 0;
            var v = (r & 0x3) | 0x8;
            return v.toString(16);
        }));
}
function setBorderStyle(d, style) {
    style.borderWidth !== undefined &&
        d.setAttribute('stroke-width', "".concat(style.borderWidth));
    style.borderColor !== undefined &&
        d.setAttribute('stroke', style.borderColor);
    style.borderRadius !== undefined &&
        d.setAttribute('rx', "".concat(style.borderRadius));
    if (style.borderRadius) {
        d.setAttribute('stroke-linecap', 'round');
    }
}
function mk(icon, _a) {
    var _b;
    var colorScheme = _a.colorScheme, borderRadius = _a.borderRadius, borderWidth = _a.borderWidth, borderColor = _a.borderColor, supportBorderRadius = _a.supportBorderRadius, width = _a.width, height = _a.height, shadow = _a.shadow;
    var parser = new DOMParser();
    var svgDoc = parser.parseFromString(icon, 'image/svg+xml');
    var svg = svgDoc.documentElement;
    var rect = svg.querySelectorAll('rect');
    var path = svg.querySelectorAll('path');
    var polygon = svg.querySelectorAll('polygon');
    var p = __spreadArray(__spreadArray(__spreadArray([], path, true), polygon, true), rect, true);
    if (supportBorderRadius !== false) {
        // 需要将width和height写到svg的viewBox属性里面和rect的width和height属性里面
        var viewBox = svg.getAttribute('viewBox') || '0 0';
        var _c = viewBox.split(' '), x = _c[0], y = _c[1];
        svg.setAttribute('viewBox', "".concat(x, " ").concat(y, " ").concat(width, " ").concat(height));
        rect.forEach(function (r) {
            r.setAttribute('width', "".concat(width));
            r.setAttribute('height', "".concat(height));
        });
    }
    p.forEach(function (d) {
        setBorderStyle(d, {
            borderWidth: borderWidth,
            borderColor: borderColor,
            borderRadius: borderRadius
        });
    });
    var linearGradient = svg.querySelectorAll('linearGradient');
    var styleDom = svg.querySelector('style');
    var replaceMap = new Map();
    if (linearGradient.length) {
        linearGradient.forEach(function (lg) {
            var id = lg.getAttribute('id');
            if (id) {
                replaceMap.set(id, guid());
            }
        });
    }
    if (styleDom) {
        var style_1 = styleDom.textContent || '';
        // 解析出所有的className
        var classNames = ((_b = style_1.match(/\.[a-zA-Z0-9_-]+/g)) === null || _b === void 0 ? void 0 : _b.map(function (n) { return n.replace('.', ''); })) || [];
        classNames.forEach(function (className) {
            replaceMap.set(className, guid());
        });
    }
    var style = {
        overflow: 'visible'
    };
    if (shadow === null || shadow === void 0 ? void 0 : shadow.enable) {
        style.filter = "drop-shadow(".concat(shadow.direction, "px ").concat(shadow.direction, "px ").concat(shadow.blur, "px ").concat(shadow.color, ")");
    }
    var svgStr = svg.outerHTML;
    if (colorScheme) {
        Object.keys(colorScheme).forEach(function (key) {
            svgStr = svgStr.replace(new RegExp(key, 'g'), colorScheme[key]);
        });
    }
    replaceMap.forEach(function (newId, oldId) {
        svgStr = svgStr.replace(new RegExp(oldId, 'g'), newId);
    });
    return {
        icon: svgStr,
        style: style
    };
}
registerCustomVendor('mk', mk);

export { mk };
