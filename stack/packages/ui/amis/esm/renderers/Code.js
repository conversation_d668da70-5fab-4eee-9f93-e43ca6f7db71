/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __awaiter, __generator, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import React from 'react';
import isPlainObject from 'lodash/isPlainObject';
import { anyChanged, getPropValue, isPureVariable, resolveVariableAndFilter, Renderer } from 'amis-core';

var Code = /** @class */ (function (_super) {
    __extends(Code, _super);
    function Code(props) {
        var _this = _super.call(this, props) || this;
        _this.toDispose = [];
        _this.codeRef = React.createRef();
        return _this;
    }
    Code.prototype.shouldComponentUpdate = function (nextProps) {
        return (anyChanged(Code.propsList, this.props, nextProps) ||
            this.resolveLanguage(this.props) !== this.resolveLanguage(nextProps) ||
            getPropValue(this.props) !== getPropValue(nextProps));
    };
    Code.prototype.componentDidMount = function () {
        var _this = this;
        import('monaco-editor').then(function (monaco) { return _this.handleMonaco(monaco); });
    };
    Code.prototype.componentDidUpdate = function (preProps) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var props, dom, tabSize, sourceCode, language, theme, colorizedHtml;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        props = this.props;
                        dom = this.codeRef.current;
                        if (!(((_a = this === null || this === void 0 ? void 0 : this.monaco) === null || _a === void 0 ? void 0 : _a.editor) && dom)) return [3 /*break*/, 2];
                        tabSize = props.tabSize;
                        sourceCode = (_b = getPropValue(this.props)) !== null && _b !== void 0 ? _b : '';
                        language = this.resolveLanguage();
                        theme = this.registerAndGetTheme();
                        /**
                         * FIXME: https://github.com/microsoft/monaco-editor/issues/338
                         * 已知问题：变量的样式存储在顶层，所以同页面中存在多个editor时，切换主题对所有editor生效
                         * 每个组件单独实例化一个editor可以处理，但是成本较高，目前官方的处理方式是iframe嵌套隔离
                         */
                        this.monaco.editor.setTheme(theme);
                        return [4 /*yield*/, this.monaco.editor.colorize(sourceCode, language, {
                                tabSize: tabSize
                            })];
                    case 1:
                        colorizedHtml = _c.sent();
                        dom.innerHTML = colorizedHtml;
                        _c.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    Code.prototype.handleMonaco = function (monaco) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var tabSize, sourceCode, language, dom, theme, colorizedHtml;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!monaco) {
                            return [2 /*return*/];
                        }
                        this.monaco = monaco;
                        tabSize = this.props.tabSize;
                        sourceCode = (_a = getPropValue(this.props)) !== null && _a !== void 0 ? _a : '';
                        language = this.resolveLanguage();
                        dom = this.codeRef.current;
                        if (!(dom && ((_b = this.monaco) === null || _b === void 0 ? void 0 : _b.editor))) return [3 /*break*/, 2];
                        theme = this.registerAndGetTheme();
                        // 这里必须是异步才能准确，可能是因为 monaco 里注册主题是异步的
                        this.monaco.editor.setTheme(theme);
                        return [4 /*yield*/, this.monaco.editor.colorize(sourceCode, language, {
                                tabSize: tabSize
                            })];
                    case 1:
                        colorizedHtml = _c.sent();
                        dom.innerHTML = colorizedHtml;
                        _c.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    Code.prototype.resolveLanguage = function (props) {
        var currentProps = props !== null && props !== void 0 ? props : this.props;
        var customLang = currentProps.customLang, data = currentProps.data;
        var _a = currentProps.language, language = _a === void 0 ? 'plaintext' : _a;
        if (isPureVariable(language)) {
            language = resolveVariableAndFilter(language, data);
        }
        if (customLang) {
            if (customLang.name) {
                language = customLang.name;
            }
        }
        return language;
    };
    /** 注册并返回当前主题名称，如果未自定义主题，则范围editorTheme值，默认为'vs' */
    Code.prototype.registerAndGetTheme = function () {
        var _a, _b;
        var monaco = this.monaco;
        var _c = this.props, theme = _c.theme, editorTheme = _c.editorTheme;
        editorTheme = editorTheme || (theme === 'dark' ? 'vs-dark' : 'vs');
        if (!monaco) {
            return editorTheme;
        }
        if (this.customLang &&
            this.customLang.name &&
            Array.isArray(this.customLang.tokens) &&
            this.customLang.tokens.length) {
            var langName = this.customLang.name;
            var colors = ((_a = this.customLang) === null || _a === void 0 ? void 0 : _a.colors) && isPlainObject((_b = this.customLang) === null || _b === void 0 ? void 0 : _b.colors)
                ? this.customLang.colors
                : {};
            monaco.languages.register({ id: langName });
            var tokenizers = [];
            var rules = [];
            for (var _i = 0, _d = this.customLang.tokens; _i < _d.length; _i++) {
                var token = _d[_i];
                var regex = new RegExp(token.regex, token.regexFlags || undefined);
                tokenizers.push([regex, token.name]);
                rules.push({
                    token: token.name,
                    foreground: token.color,
                    background: token.background,
                    fontStyle: token.fontStyle
                });
            }
            monaco.languages.setMonarchTokensProvider(langName, {
                tokenizer: {
                    root: tokenizers
                }
            });
            monaco.editor.defineTheme(langName, {
                base: 'vs',
                inherit: false,
                rules: rules,
                colors: colors
            });
            return langName;
        }
        return editorTheme;
    };
    Code.prototype.render = function () {
        var sourceCode = getPropValue(this.props);
        var _a = this.props, className = _a.className, maxHeight = _a.maxHeight, _b = _a.style, style = _b === void 0 ? {} : _b, cx = _a.classnames, editorTheme = _a.editorTheme, customLang = _a.customLang, wordWrap = _a.wordWrap, wrapperComponent = _a.wrapperComponent;
        var language = this.resolveLanguage();
        var isMultiLine = typeof sourceCode === 'string' && sourceCode.split(/\r?\n/).length > 1;
        var Component = wrapperComponent || (isMultiLine ? 'pre' : 'code');
        if (customLang) {
            this.customLang = customLang;
        }
        if (maxHeight) {
            style.maxHeight = style.maxHeight || maxHeight;
            style.overflow = 'auto';
        }
        return (jsx(Component, __assign({ ref: this.codeRef, className: cx('Code', {
                // 使用内置暗色主题时设置一下背景，避免看不清
                'Code--dark': editorTheme && ['vs-dark', 'hc-black'].includes(editorTheme),
                'Code-pre-wrap': Component === 'pre',
                'word-break': wordWrap
            }, className), style: style, "data-lang": language }, { children: sourceCode })));
    };
    Code.propsList = [
        'language',
        'editorTheme',
        'tabSize',
        'wordWrap',
        'customLang',
        'style'
    ];
    Code.defaultProps = {
        language: 'plaintext',
        tabSize: 4,
        wordWrap: true
    };
    return Code;
}(React.Component));
var CodeRenderer = /** @class */ (function (_super) {
    __extends(CodeRenderer, _super);
    function CodeRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CodeRenderer = __decorate([
        Renderer({
            type: 'code'
        })
    ], CodeRenderer);
    return CodeRenderer;
}(Code));

export { CodeRenderer, Code as default };
