/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate, __awaiter, __generator, __spreadArray, __rest } from 'tslib';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import React from 'react';
import { findDOMNode } from 'react-dom';
import Sortable from 'sortablejs';
import omit from 'lodash/omit';
import { getPropValue, resolveVariableAndFilter, anyChanged, isPureVariable, createObject, difference, filter, autobind, getMatchedEventTargets, ScopedContext, Renderer, ListStore, isClickOnInput, isVisible, isDisabled, filterClassNameObject } from 'amis-core';
import { Icon, Button, AlphabetIndexer, Spinner, Checkbox } from 'amis-ui';
import { HocQuickEdit } from './QuickEdit.js';
import { HocPopOver } from './PopOver.js';
import './Table/index.js';
import { HocCopyable } from './Copyable.js';
import find from 'lodash/find';
import { TableCell } from './Table/TableCell.js';

var List = /** @class */ (function (_super) {
    __extends(List, _super);
    function List(props) {
        var _this = _super.call(this, props) || this;
        _this.observer = null;
        _this.itemRefs = [];
        _this.userClick = false;
        _this.state = {
            currentLetter: undefined
        };
        _this.handleAction = _this.handleAction.bind(_this);
        _this.handleCheck = _this.handleCheck.bind(_this);
        _this.handleCheckAll = _this.handleCheckAll.bind(_this);
        _this.handleQuickChange = _this.handleQuickChange.bind(_this);
        _this.handleSave = _this.handleSave.bind(_this);
        _this.handleSaveOrder = _this.handleSaveOrder.bind(_this);
        _this.reset = _this.reset.bind(_this);
        _this.dragTipRef = _this.dragTipRef.bind(_this);
        _this.getPopOverContainer = _this.getPopOverContainer.bind(_this);
        _this.bodyRef = _this.bodyRef.bind(_this);
        _this.renderToolbar = _this.renderToolbar.bind(_this);
        _this.handleLetterClick = _this.handleLetterClick.bind(_this);
        _this.setItemRef = _this.setItemRef.bind(_this);
        var store = props.store, selectable = props.selectable, draggable = props.draggable, orderBy = props.orderBy, orderDir = props.orderDir, multiple = props.multiple, strictMode = props.strictMode, hideCheckToggler = props.hideCheckToggler, itemCheckableOn = props.itemCheckableOn, itemDraggableOn = props.itemDraggableOn;
        store.update({
            /** Card嵌套List情况下该属性获取到的值为ListStore的默认值, 会导致Schema中的配置被覆盖 */
            multiple: multiple || (props === null || props === void 0 ? void 0 : props.$schema.multiple),
            strictMode: strictMode || (props === null || props === void 0 ? void 0 : props.$schema.strictMode),
            selectable: selectable || (props === null || props === void 0 ? void 0 : props.$schema.selectable),
            draggable: draggable || (props === null || props === void 0 ? void 0 : props.$schema.draggable),
            orderBy: orderBy,
            orderDir: orderDir,
            hideCheckToggler: hideCheckToggler,
            itemCheckableOn: itemCheckableOn,
            itemDraggableOn: itemDraggableOn
        });
        List.syncItems(store, _this.props) && _this.syncSelected();
        return _this;
    }
    List.syncItems = function (store, props, prevProps) {
        var source = props.source;
        var value = getPropValue(props, function (props) { return props.items; });
        var items = [];
        var updateItems = false;
        if (Array.isArray(value)) {
            if (!prevProps ||
                getPropValue(prevProps, function (props) { return props.items; }) !== value) {
                items = value;
                updateItems = true;
            }
        }
        else if (typeof source === 'string') {
            var resolved = resolveVariableAndFilter(source, props.data, '| raw');
            var prev = prevProps
                ? resolveVariableAndFilter(source, prevProps.data, '| raw')
                : null;
            if (prev === resolved) {
                updateItems = false;
            }
            else {
                items = Array.isArray(resolved) ? resolved : [];
                updateItems = true;
            }
        }
        updateItems && store.initItems(items, props.fullItems, props.selected);
        Array.isArray(props.selected) &&
            store.updateSelected(props.selected, props.valueField);
        return updateItems;
    };
    List.prototype.componentDidMount = function () {
        if (this.props.showIndexBar) {
            this.observeItems();
        }
    };
    List.prototype.componentDidUpdate = function (prevProps) {
        var _a, _b;
        var props = this.props;
        var store = props.store;
        if (this.props.showIndexBar) {
            if (!prevProps.showIndexBar || prevProps.items !== props.items) {
                (_a = this.observer) === null || _a === void 0 ? void 0 : _a.disconnect();
                this.observeItems();
            }
        }
        else if (prevProps.showIndexBar) {
            (_b = this.observer) === null || _b === void 0 ? void 0 : _b.disconnect();
        }
        if (anyChanged([
            'selectable',
            'draggable',
            'orderBy',
            'orderDir',
            'multiple',
            'strictMode',
            'hideCheckToggler',
            'itemCheckableOn',
            'itemDraggableOn'
        ], prevProps, props)) {
            store.update({
                multiple: props.multiple,
                strictMode: props.strictMode,
                selectable: props.selectable,
                draggable: props.draggable,
                orderBy: props.orderBy,
                orderDir: props.orderDir,
                hideCheckToggler: props.hideCheckToggler,
                itemCheckableOn: props.itemCheckableOn,
                itemDraggableOn: props.itemDraggableOn
            });
        }
        if (anyChanged(['source', 'value', 'items'], prevProps, props) ||
            (!props.value &&
                !props.items &&
                (props.data !== prevProps.data ||
                    (typeof props.source === 'string' && isPureVariable(props.source))))) {
            List.syncItems(store, props, prevProps) && this.syncSelected();
        }
        else if (prevProps.selected !== props.selected) {
            store.updateSelected(props.selected || [], props.valueField);
        }
    };
    List.prototype.componentWillUnmount = function () {
        var _a;
        (_a = this.observer) === null || _a === void 0 ? void 0 : _a.disconnect();
    };
    List.prototype.getIndexDataField = function (listItem, indexField) {
        // 确定用于索引的配置字段名，默认为 'title'
        var configFieldName = indexField || 'title';
        // 从配置中提取实际数据字段名（假设格式为 ${fieldName}）
        var dataFieldNameTemplate = listItem === null || listItem === void 0 ? void 0 : listItem[configFieldName];
        // 从 "${fieldName}" 格式中提取出 "fieldName"
        return dataFieldNameTemplate === null || dataFieldNameTemplate === void 0 ? void 0 : dataFieldNameTemplate.substring(2, (dataFieldNameTemplate === null || dataFieldNameTemplate === void 0 ? void 0 : dataFieldNameTemplate.length) - 1);
    };
    List.prototype.bodyRef = function (ref) {
        this.body = ref;
    };
    List.prototype.getPopOverContainer = function () {
        return findDOMNode(this);
    };
    List.prototype.handleAction = function (e, action, ctx) {
        var _a;
        var _b = this.props, data = _b.data, dispatchEvent = _b.dispatchEvent, onAction = _b.onAction, onEvent = _b.onEvent;
        var hasClickActions = onEvent &&
            Array.isArray((_a = onEvent === null || onEvent === void 0 ? void 0 : onEvent.itemClick) === null || _a === void 0 ? void 0 : _a.actions) &&
            onEvent.itemClick.actions.length > 0;
        if (hasClickActions) {
            dispatchEvent('itemClick', createObject(data, {
                item: ctx
            }));
        }
        else {
            /** action无值代表List自身已经处理, 无需交给上层处理 */
            return action && (onAction === null || onAction === void 0 ? void 0 : onAction(e, action, ctx));
        }
    };
    List.prototype.handleCheck = function (item) {
        item.toggle();
        this.syncSelected();
        var _a = this.props, dispatchEvent = _a.dispatchEvent, store = _a.store;
        dispatchEvent(
        //增删改查卡片模式选择表格项
        'selectedChange', createObject(store.data, __assign(__assign({}, store.eventContext), { item: item.data })));
    };
    List.prototype.handleCheckAll = function () {
        var store = this.props.store;
        store.toggleAll();
        this.syncSelected();
        var dispatchEvent = this.props.dispatchEvent;
        dispatchEvent(
        //增删改查卡片模式选择表格项
        'selectedChange', createObject(store.data, __assign({}, store.eventContext)));
    };
    List.prototype.syncSelected = function () {
        var _a = this.props, store = _a.store, onSelect = _a.onSelect;
        onSelect &&
            onSelect(store.selectedItems.map(function (item) { return item.data; }), store.unSelectedItems.map(function (item) { return item.data; }));
    };
    List.prototype.handleQuickChange = function (item, values, saveImmediately, savePristine, options) {
        item.change(values, savePristine);
        if (!saveImmediately || savePristine) {
            return;
        }
        if (saveImmediately && saveImmediately.api) {
            this.props.onAction(null, {
                actionType: 'ajax',
                api: saveImmediately.api,
                reload: options === null || options === void 0 ? void 0 : options.reload
            }, item.locals);
            return;
        }
        var _a = this.props, onSave = _a.onSave, primaryField = _a.primaryField;
        if (!onSave) {
            return;
        }
        onSave(item.data, difference(item.data, item.pristine, ['id', primaryField]), item.index, undefined, item.pristine, options);
    };
    List.prototype.handleSave = function () {
        var _a = this.props, store = _a.store, onSave = _a.onSave, primaryField = _a.primaryField;
        if (!onSave || !store.modifiedItems.length) {
            return;
        }
        var items = store.modifiedItems.map(function (item) { return item.data; });
        var itemIndexes = store.modifiedItems.map(function (item) { return item.index; });
        var diff = store.modifiedItems.map(function (item) {
            return difference(item.data, item.pristine, ['id', primaryField]);
        });
        var unModifiedItems = store.items
            .filter(function (item) { return !item.modified; })
            .map(function (item) { return item.data; });
        return onSave(items, diff, itemIndexes, unModifiedItems, store.modifiedItems.map(function (item) { return item.pristine; }));
    };
    List.prototype.handleSaveOrder = function () {
        var _a = this.props, store = _a.store, onSaveOrder = _a.onSaveOrder;
        if (!onSaveOrder || !store.movedItems.length) {
            return;
        }
        onSaveOrder(store.movedItems.map(function (item) { return item.data; }), store.items.map(function (item) { return item.data; }));
    };
    List.prototype.reset = function () {
        var store = this.props.store;
        store.reset();
    };
    List.prototype.bulkUpdate = function (value, items) {
        // const {store} = this.props;
        // const items2 = store.items.filter(item => ~items.indexOf(item.pristine));
        // items2.forEach(item => item.change(value));
        var _a = this.props, store = _a.store, primaryField = _a.primaryField;
        if (primaryField && value.ids) {
            var ids_1 = value.ids.split(',');
            var rows = store.items.filter(function (item) {
                return find(ids_1, function (id) { return id && id == item.data[primaryField]; });
            });
            var newValue_1 = __assign(__assign({}, value), { ids: undefined });
            rows.forEach(function (item) { return item.change(newValue_1); });
        }
        else if (Array.isArray(items)) {
            var rows = store.items.filter(function (item) { return ~items.indexOf(item.pristine); });
            rows.forEach(function (item) { return item.change(value); });
        }
    };
    List.prototype.getSelected = function () {
        var store = this.props.store;
        return store.selectedItems.map(function (item) { return item.data; });
    };
    List.prototype.dragTipRef = function (ref) {
        if (!this.dragTip && ref) {
            this.initDragging();
        }
        else if (this.dragTip && !ref) {
            this.destroyDragging();
        }
        this.dragTip = ref;
    };
    List.prototype.initDragging = function () {
        var store = this.props.store;
        var dom = findDOMNode(this);
        var ns = this.props.classPrefix;
        this.sortable = new Sortable(dom.querySelector(".".concat(ns, "List-items")), {
            group: 'table',
            animation: 150,
            handle: ".".concat(ns, "ListItem-dragBtn"),
            ghostClass: 'is-dragging',
            onEnd: function (e) {
                // 没有移动
                if (e.newIndex === e.oldIndex) {
                    return;
                }
                var parent = e.to;
                if (e.oldIndex < parent.childNodes.length - 1) {
                    parent.insertBefore(e.item, parent.childNodes[e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex]);
                }
                else {
                    parent.appendChild(e.item);
                }
                store.exchange(e.oldIndex, e.newIndex);
            }
        });
    };
    List.prototype.destroyDragging = function () {
        this.sortable && this.sortable.destroy();
    };
    List.prototype.renderActions = function (region) {
        var _this = this;
        var _a = this.props, actions = _a.actions, render = _a.render, store = _a.store, multiple = _a.multiple, selectable = _a.selectable, env = _a.env, ns = _a.classPrefix, cx = _a.classnames;
        var btn;
        actions = Array.isArray(actions) ? actions.concat() : [];
        if (region === 'header' &&
            !~this.renderedToolbars.indexOf('check-all') &&
            (btn = this.renderCheckAll())) {
            actions.unshift({
                type: 'button',
                children: btn
            });
        }
        if (region === 'header' &&
            !~this.renderedToolbars.indexOf('drag-toggler') &&
            (btn = this.renderDragToggler())) {
            actions.unshift({
                type: 'button',
                children: btn
            });
        }
        return Array.isArray(actions) && actions.length ? (jsx("div", __assign({ className: cx('List-actions') }, { children: actions.map(function (action, key) {
                return render("action/".concat(key), __assign({ type: 'button' }, action), {
                    onAction: _this.handleAction,
                    key: key,
                    btnDisabled: store.dragging
                });
            }) }))) : null;
    };
    List.prototype.renderHeading = function () {
        var _a = this.props, title = _a.title, store = _a.store, hideQuickSaveBtn = _a.hideQuickSaveBtn, cx = _a.classnames, data = _a.data;
        if (title || (store.modified && !hideQuickSaveBtn) || store.moved) {
            return (jsx("div", __assign({ className: cx('List-heading') }, { children: store.modified && !hideQuickSaveBtn ? (jsxs("span", { children: ["\u5F53\u524D\u6709 ".concat(store.modified, " \u6761\u8BB0\u5F55\u4FEE\u6539\u4E86\u5185\u5BB9, \u4F46\u5E76\u6CA1\u6709\u63D0\u4EA4\u3002\u8BF7\u9009\u62E9:"), jsxs("button", __assign({ type: "button", className: cx('Button Button--xs Button--success m-l-sm'), onClick: this.handleSave }, { children: [jsx(Icon, { icon: "check", className: "icon m-r-xs" }), "\u63D0\u4EA4"] })), jsxs("button", __assign({ type: "button", className: cx('Button Button--xs Button--danger m-l-sm'), onClick: this.reset }, { children: [jsx(Icon, { icon: "close", className: "icon m-r-xs" }), "\u653E\u5F03"] }))] })) : store.moved ? (jsxs("span", { children: ["\u5F53\u524D\u6709 ".concat(store.moved, " \u6761\u8BB0\u5F55\u4FEE\u6539\u4E86\u987A\u5E8F, \u4F46\u5E76\u6CA1\u6709\u63D0\u4EA4\u3002\u8BF7\u9009\u62E9:"), jsxs("button", __assign({ type: "button", className: cx('Button Button--xs Button--success m-l-sm'), onClick: this.handleSaveOrder }, { children: [jsx(Icon, { icon: "check", className: "icon m-r-xs" }), "\u63D0\u4EA4"] })), jsxs("button", __assign({ type: "button", className: cx('Button Button--xs Button--danger m-l-sm'), onClick: this.reset }, { children: [jsx(Icon, { icon: "close", className: "icon m-r-xs" }), "\u653E\u5F03"] }))] })) : title ? (filter(title, data)) : ('') })));
        }
        return null;
    };
    List.prototype.renderHeader = function () {
        var _a = this.props, header = _a.header, headerClassName = _a.headerClassName, headerToolbar = _a.headerToolbar, headerToolbarRender = _a.headerToolbarRender, render = _a.render, showHeader = _a.showHeader, store = _a.store, cx = _a.classnames;
        if (showHeader === false) {
            return null;
        }
        var child = headerToolbarRender
            ? headerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar)
            : null;
        var actions = this.renderActions('header');
        var toolbarNode = actions || child || store.dragging ? (jsxs("div", __assign({ className: cx('List-toolbar', headerClassName) }, { children: [actions, child, store.dragging ? (jsx("div", __assign({ className: cx('List-dragTip'), ref: this.dragTipRef }, { children: "\u8BF7\u62D6\u52A8\u5DE6\u8FB9\u7684\u6309\u94AE\u8FDB\u884C\u6392\u5E8F" }))) : null] }), "header-toolbar")) : null;
        var headerNode = header && (!Array.isArray(header) || header.length) ? (jsx("div", __assign({ className: cx('List-header', headerClassName) }, { children: render('header', header) }), "header")) : null;
        return headerNode && toolbarNode
            ? [headerNode, toolbarNode]
            : headerNode || toolbarNode || null;
    };
    List.prototype.renderFooter = function () {
        var _a = this.props, footer = _a.footer, footerClassName = _a.footerClassName, footerToolbar = _a.footerToolbar, footerToolbarRender = _a.footerToolbarRender, render = _a.render, showFooter = _a.showFooter, store = _a.store, cx = _a.classnames, affixFooter = _a.affixFooter;
        if (showFooter === false) {
            return null;
        }
        var child = footerToolbarRender
            ? footerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar)
            : null;
        var actions = this.renderActions('footer');
        var footerNode = footer && (!Array.isArray(footer) || footer.length) ? (jsx("div", __assign({ className: cx('List-footer', footerClassName, affixFooter ? 'List-footer--affix' : '') }, { children: render('footer', footer) }), "footer")) : null;
        var toolbarNode = actions || child ? (jsxs("div", __assign({ className: cx('List-toolbar', footerClassName, !footerNode && affixFooter ? 'List-footToolbar--affix' : '') }, { children: [actions, child] }), "footer-toolbar")) : null;
        return footerNode && toolbarNode
            ? [toolbarNode, footerNode]
            : footerNode || toolbarNode || null;
    };
    List.prototype.renderCheckAll = function () {
        var _a = this.props, store = _a.store, multiple = _a.multiple, selectable = _a.selectable;
        if (!store.selectable ||
            !multiple ||
            !selectable ||
            store.dragging ||
            !store.items.length) {
            return null;
        }
        return (jsx(Button, __assign({ tooltip: "\u5207\u6362\u5168\u9009", onClick: this.handleCheckAll, size: "sm", level: store.allChecked ? 'info' : 'default' }, { children: "\u5168\u9009" }), "checkall"));
    };
    List.prototype.renderDragToggler = function () {
        var _a = this.props, store = _a.store, multiple = _a.multiple, selectable = _a.selectable, popOverContainer = _a.popOverContainer, env = _a.env;
        if (!store.draggable || store.items.length < 2) {
            return null;
        }
        return (jsx(Button, __assign({ iconOnly: true, tooltip: "\u5BF9\u5217\u8868\u8FDB\u884C\u6392\u5E8F\u64CD\u4F5C", tooltipContainer: popOverContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer), size: "sm", active: store.dragging, onClick: function (e) {
                e.preventDefault();
                store.toggleDragging();
                store.dragging && store.clear();
            } }, { children: jsx(Icon, { icon: "exchange", className: "icon r90" }) }), "dragging-toggle"));
    };
    List.prototype.renderToolbar = function (toolbar, index) {
        var type = toolbar.type || toolbar;
        if (type === 'drag-toggler') {
            this.renderedToolbars.push(type);
            return this.renderDragToggler();
        }
        else if (type === 'check-all') {
            this.renderedToolbars.push(type);
            return this.renderCheckAll();
        }
        return void 0;
    };
    // editor重写该方法，不要改名或参数
    List.prototype.renderListItem = function (index, template, item, itemClassName) {
        var _a;
        var _b = this.props, render = _b.render, multiple = _b.multiple, store = _b.store, onAction = _b.onAction, onEvent = _b.onEvent, hideCheckToggler = _b.hideCheckToggler, checkOnItemClick = _b.checkOnItemClick, itemAction = _b.itemAction, cx = _b.classnames, __ = _b.translate, testIdBuilder = _b.testIdBuilder, indexBarOffset = _b.indexBarOffset;
        var hasClickActions = onEvent &&
            Array.isArray((_a = onEvent === null || onEvent === void 0 ? void 0 : onEvent.itemClick) === null || _a === void 0 ? void 0 : _a.actions) &&
            onEvent.itemClick.actions.length > 0;
        return render("".concat(index), __assign({ type: 'list-item' }, template), {
            key: item.index,
            className: cx(itemClassName, {
                'is-checked': item.checked,
                'is-modified': item.modified,
                'is-moved': item.moved
            }),
            testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(index),
            selectable: store.selectable,
            checkable: item.checkable,
            multiple: multiple,
            item: item,
            itemIndex: item.index,
            hideCheckToggler: hideCheckToggler,
            checkOnItemClick: checkOnItemClick,
            itemAction: itemAction,
            hasClickActions: hasClickActions,
            selected: item.checked,
            onCheck: this.handleCheck,
            onAction: this.handleAction,
            dragging: store.dragging,
            data: item.locals,
            onQuickChange: store.dragging ? null : this.handleQuickChange,
            popOverContainer: this.getPopOverContainer,
            indexBarOffset: indexBarOffset,
            itemRef: this.setItemRef
        });
    };
    List.prototype.handleLetterClick = function (letter) {
        var _a = this.props, _b = _a.indexField, indexField = _b === void 0 ? 'title' : _b, store = _a.store, listItem = _a.listItem;
        if (!store)
            return;
        var dataFieldName = this.getIndexDataField(listItem, indexField);
        this.setState({ currentLetter: letter });
        var targetItem = store.items.find(function (item) {
            var value = getPropValue({ data: item.data }, function () { return item.data[dataFieldName]; });
            return typeof value === 'string'
                ? value.charAt(0).toUpperCase() === letter
                : false;
        });
        if (targetItem) {
            var itemElement = this.itemRefs[targetItem.index];
            if (itemElement) {
                this.userClick = true;
                itemElement.element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    };
    List.prototype.render = function () {
        var _a;
        var _this = this;
        var _b = this.props, className = _b.className, style = _b.style, itemClassName = _b.itemClassName, store = _b.store, placeholder = _b.placeholder, render = _b.render, listItem = _b.listItem, affixHeader = _b.affixHeader, cx = _b.classnames, size = _b.size, __ = _b.translate, _c = _b.loading, loading = _c === void 0 ? false : _c, loadingConfig = _b.loadingConfig, showIndexBar = _b.showIndexBar, _d = _b.indexField, indexField = _d === void 0 ? 'title' : _d;
        var currentLetter = this.state.currentLetter;
        this.renderedToolbars = [];
        var heading = this.renderHeading();
        var header = this.renderHeader();
        return (jsxs("div", __assign({ className: cx('List', className, (_a = {},
                _a["List--".concat(size)] = size,
                _a['List--unsaved'] = !!store.modified || !!store.moved,
                _a)), style: style, ref: this.bodyRef }, { children: [jsxs("div", __assign({ className: cx('List-content-wrapper') }, { children: [jsxs("div", __assign({ className: cx('List-main') }, { children: [affixHeader ? (jsxs("div", __assign({ className: cx('List-fixedTop') }, { children: [header, heading] }))) : (jsxs(Fragment, { children: [header, heading] })), store.items.length ? (jsx("div", __assign({ className: cx('List-items') }, { children: store.items.map(function (item, index) {
                                        return _this.renderListItem(index, listItem, item, itemClassName);
                                    }) }))) : (jsx("div", __assign({ className: cx('List-placeholder') }, { children: render('placeholder', __(placeholder)) }))), this.renderFooter()] })), showIndexBar && store.items.length > 0 && (jsx(AlphabetIndexer, { items: store.items, getItemLetter: function (item) {
                                var dataFieldName = _this.getIndexDataField(listItem, indexField);
                                var value = getPropValue({ data: item.data }, function () { return item.data[dataFieldName]; });
                                return typeof value === 'string' && /^[A-Za-z]/.test(value)
                                    ? value
                                    : '';
                            }, onLetterClick: this.handleLetterClick, classnames: cx, currentLetter: currentLetter }))] })), jsx(Spinner, { overlay: true, show: loading, loadingConfig: loadingConfig })] })));
    };
    List.prototype.observeItems = function () {
        var _this = this;
        this.observer = new IntersectionObserver(this.scrollObserver);
        this.itemRefs.forEach(function (item) {
            _this.observer.observe(item.element);
        });
    };
    List.prototype.scrollObserver = function (entries) {
        var _this = this;
        entries.forEach(function (entry) {
            var index = entry.target.getAttribute('data-index');
            var currentSection = _this.itemRefs[parseInt(index, 10)];
            if (currentSection) {
                currentSection.isIntersecting = entry.isIntersecting;
            }
        });
        // 找到第一个可见的区域
        var firstVisibleIndex = this.itemRefs.findIndex(function (item) { return item.isIntersecting; });
        if (!this.userClick) {
            if (typeof firstVisibleIndex === 'number') {
                var item = this.itemRefs[firstVisibleIndex];
                this.setState({ currentLetter: item.letter });
            }
        }
        else {
            // 滚动结束后，重置userClick状态
            if (this.userClickTimer) {
                clearTimeout(this.userClickTimer);
            }
            this.userClickTimer = setTimeout(function () {
                _this.userClick = false;
            }, 300);
        }
    };
    List.prototype.setItemRef = function (index, item, ref) {
        if (ref) {
            var _a = this.props, indexField = _a.indexField, listItem = _a.listItem;
            var dataFieldName_1 = this.getIndexDataField(listItem, indexField);
            var value = getPropValue({ data: item.data }, function () { return item.data[dataFieldName_1]; });
            this.itemRefs[index] = {
                element: ref,
                letter: value === null || value === void 0 ? void 0 : value.charAt(0).toUpperCase()
            };
        }
    };
    List.propsList = [
        'header',
        'headerToolbarRender',
        'footer',
        'footerToolbarRender',
        'placeholder',
        'source',
        'selectable',
        'headerClassName',
        'footerClassName',
        'hideQuickSaveBtn',
        'hideCheckToggler',
        'itemCheckableOn',
        'itemDraggableOn',
        'actions',
        'items',
        'valueField'
    ];
    List.defaultProps = {
        className: '',
        placeholder: 'placeholder.noData',
        source: '$items',
        selectable: false,
        headerClassName: '',
        footerClassName: '',
        affixHeader: true
    };
    __decorate([
        autobind
    ], List.prototype, "scrollObserver", null);
    return List;
}(React.Component));
var ListRenderer = /** @class */ (function (_super) {
    __extends(ListRenderer, _super);
    function ListRenderer(props, scoped) {
        var _this = _super.call(this, props) || this;
        scoped.registerComponent(_this);
        return _this;
    }
    ListRenderer.prototype.componentWillUnmount = function () {
        var _a;
        (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);
        this.context.unRegisterComponent(this);
    };
    ListRenderer.prototype.receive = function (values, subPath) {
        var _a, _b, _c;
        var scoped = this.context;
        /**
         * 因为List在scope上注册，导致getComponentByName查询组件时会优先找到List，和CRUD联动的动作都会失效
         * 这里先做兼容处理，把动作交给上层的CRUD处理
         */
        if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
            // CRUD会把自己透传给List，这样可以保证找到CRUD
            return (_c = (_b = this.props.host).receive) === null || _c === void 0 ? void 0 : _c.call(_b, values, subPath);
        }
        if (subPath) {
            return scoped.send(subPath, values);
        }
    };
    ListRenderer.prototype.reload = function (subPath, query, ctx, silent, replace, args) {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var store, scoped;
            return __generator(this, function (_e) {
                store = this.props.store;
                if ((args === null || args === void 0 ? void 0 : args.index) || (args === null || args === void 0 ? void 0 : args.condition)) {
                    // 局部刷新
                    // todo 后续考虑添加局部刷新
                    // const targets = await getMatchedEventTargets<IItem>(
                    //   store.items,
                    //   ctx || this.props.data,
                    //   args.index,
                    //   args?.condition
                    // );
                    // await Promise.all(targets.map(target => this.loadDeferredRow(target)));
                    return [2 /*return*/];
                }
                scoped = this.context;
                if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
                    // CRUD会把自己透传给List，这样可以保证找到CRUD
                    return [2 /*return*/, (_d = (_b = this.props) === null || _b === void 0 ? void 0 : (_c = _b.host).reload) === null || _d === void 0 ? void 0 : _d.call(_c, subPath, query, ctx)];
                }
                if (subPath) {
                    return [2 /*return*/, scoped.reload(subPath, ctx)];
                }
                return [2 /*return*/];
            });
        });
    };
    ListRenderer.prototype.setData = function (values, replace, index, condition) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function () {
            var store, targets;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        store = this.props.store;
                        if (!(index !== undefined || condition !== undefined)) return [3 /*break*/, 2];
                        return [4 /*yield*/, getMatchedEventTargets(store.items, this.props.data, index, condition)];
                    case 1:
                        targets = _d.sent();
                        targets.forEach(function (target) {
                            target.updateData(values);
                        });
                        return [3 /*break*/, 3];
                    case 2:
                        if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
                            // 如果在 CRUD 里面，优先让 CRUD 去更新状态
                            return [2 /*return*/, (_c = (_b = this.props.host).setData) === null || _c === void 0 ? void 0 : _c.call(_b, values, replace, index, condition)];
                        }
                        else {
                            return [2 /*return*/, store.updateData(values, undefined, replace)];
                        }
                        _d.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    ListRenderer.prototype.getData = function () {
        var _a = this.props, store = _a.store, data = _a.data;
        return store.getData(data);
    };
    ListRenderer.prototype.hasModifiedItems = function () {
        return this.props.store.modified;
    };
    ListRenderer.prototype.doAction = function (action, ctx, throwErrors, args) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, store, valueField, data, actionType, _b, rows;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        _a = this.props, store = _a.store, valueField = _a.valueField, data = _a.data;
                        actionType = action === null || action === void 0 ? void 0 : action.actionType;
                        _b = actionType;
                        switch (_b) {
                            case 'selectAll': return [3 /*break*/, 1];
                            case 'clearAll': return [3 /*break*/, 2];
                            case 'select': return [3 /*break*/, 3];
                            case 'initDrag': return [3 /*break*/, 5];
                            case 'cancelDrag': return [3 /*break*/, 6];
                            case 'submitQuickEdit': return [3 /*break*/, 7];
                        }
                        return [3 /*break*/, 9];
                    case 1:
                        store.clear();
                        store.toggleAll();
                        this.syncSelected();
                        return [3 /*break*/, 10];
                    case 2:
                        store.clear();
                        this.syncSelected();
                        return [3 /*break*/, 10];
                    case 3: return [4 /*yield*/, getMatchedEventTargets(store.items, ctx || this.props.data, args.index, args.condition, args.selected)];
                    case 4:
                        rows = _c.sent();
                        store.updateSelected(rows.map(function (item) { return item.data; }), valueField);
                        this.syncSelected();
                        return [3 /*break*/, 10];
                    case 5:
                        store.startDragging();
                        return [3 /*break*/, 10];
                    case 6:
                        store.stopDragging();
                        return [3 /*break*/, 10];
                    case 7: return [4 /*yield*/, this.handleSave()];
                    case 8:
                        _c.sent();
                        return [3 /*break*/, 10];
                    case 9: return [2 /*return*/, this.handleAction(undefined, action, data)];
                    case 10: return [2 /*return*/];
                }
            });
        });
    };
    ListRenderer.contextType = ScopedContext;
    ListRenderer = __decorate([
        Renderer({
            type: 'list',
            storeType: ListStore.name
        })
    ], ListRenderer);
    return ListRenderer;
}(List));
var ListItem = /** @class */ (function (_super) {
    __extends(ListItem, _super);
    function ListItem(props) {
        var _this = _super.call(this, props) || this;
        _this.itemRender = _this.itemRender.bind(_this);
        _this.handleAction = _this.handleAction.bind(_this);
        _this.handleQuickChange = _this.handleQuickChange.bind(_this);
        _this.handleClick = _this.handleClick.bind(_this);
        _this.handleCheck = _this.handleCheck.bind(_this);
        return _this;
    }
    ListItem.prototype.handleClick = function (e) {
        if (isClickOnInput(e)) {
            return;
        }
        var _a = this.props, checkable = _a.checkable, checkOnItemClick = _a.checkOnItemClick, itemAction = _a.itemAction, onAction = _a.onAction, item = _a.item, onCheck = _a.onCheck, hasClickActions = _a.hasClickActions;
        // itemAction和itemClick事件统一交给List处理，itemClick事件会覆盖itemAction
        onAction === null || onAction === void 0 ? void 0 : onAction(e, hasClickActions ? undefined : itemAction, hasClickActions ? item : item.locals);
        // itemAction, itemClick事件和checkOnItemClick为互斥关系
        if (checkable && checkOnItemClick && !hasClickActions && !itemAction) {
            onCheck === null || onCheck === void 0 ? void 0 : onCheck(item);
        }
    };
    ListItem.prototype.handleCheck = function () {
        var _a = this.props, onCheck = _a.onCheck, item = _a.item;
        onCheck === null || onCheck === void 0 ? void 0 : onCheck(item);
    };
    ListItem.prototype.handleAction = function (e, action, ctx) {
        var _a = this.props, onAction = _a.onAction, item = _a.item;
        onAction && onAction(e, action, ctx || item.data);
    };
    ListItem.prototype.handleQuickChange = function (values, saveImmediately, savePristine, options) {
        var _a = this.props, onQuickChange = _a.onQuickChange, item = _a.item;
        onQuickChange &&
            onQuickChange(item, values, saveImmediately, savePristine, options);
    };
    ListItem.prototype.renderLeft = function () {
        var _a = this.props, dragging = _a.dragging, selectable = _a.selectable, selected = _a.selected, checkable = _a.checkable, multiple = _a.multiple, hideCheckToggler = _a.hideCheckToggler, checkOnItemClick = _a.checkOnItemClick, cx = _a.classnames, ns = _a.classPrefix, testIdBuilder = _a.testIdBuilder;
        if (dragging) {
            return (jsx("div", __assign({ className: cx('ListItem-dragBtn') }, { children: jsx(Icon, { icon: "drag-bar", className: "icon" }) })));
        }
        else if (selectable && !hideCheckToggler) {
            return (jsx("div", __assign({ className: cx('ListItem-checkBtn') }, { children: jsx(Checkbox, { classPrefix: ns, type: multiple !== false ? 'checkbox' : 'radio', disabled: !checkable, checked: selected, onChange: this.handleCheck, inline: true, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('checkbox') }) })));
        }
        return null;
    };
    ListItem.prototype.renderRight = function () {
        var _this = this;
        var _a = this.props, actions = _a.actions, render = _a.render, data = _a.data, dragging = _a.dragging, cx = _a.classnames;
        if (Array.isArray(actions)) {
            return (jsx("div", __assign({ className: cx('ListItem-actions') }, { children: actions.map(function (action, index) {
                    if (!isVisible(action, data)) {
                        return null;
                    }
                    return render("action/".concat(index), __assign({ size: 'sm', level: 'link', type: 'button' }, action // todo 等后面修复了干掉 https://github.com/microsoft/TypeScript/pull/38577
                    ), {
                        key: index,
                        disabled: dragging || isDisabled(action, data),
                        onAction: _this.handleAction
                    });
                }) })));
        }
        return null;
    };
    ListItem.prototype.renderChild = function (node, region, key) {
        if (region === void 0) { region = 'body'; }
        if (key === void 0) { key = 0; }
        var render = this.props.render;
        /*if (Array.isArray(node)) {
                return (
                    <div className="hbox" key={key}>
                        {node.map((item, index) => (
                            <div key={index} className="col">{this.renderChild(item, `${region}/${index}`)}</div>
                        ))}
                    </div>
                );
            } else */ if (typeof node === 'string' || typeof node === 'number') {
            return render(region, node, { key: key });
        }
        var childNode = node;
        if (childNode.type === 'hbox' || childNode.type === 'grid') {
            return render(region, node, {
                key: key,
                itemRender: this.itemRender.bind(this)
            });
        }
        return this.renderField(region, childNode, key, this.props);
    };
    ListItem.prototype.itemRender = function (field, index, len, props) {
        return this.renderField("column/".concat(index), field, index, props);
    };
    ListItem.prototype.renderField = function (region, field, key, props) {
        var render = (props === null || props === void 0 ? void 0 : props.render) || this.props.render;
        var data = this.props.data;
        var cx = this.props.classnames;
        var itemIndex = this.props.itemIndex;
        var $$id = field.$$id ? "".concat(field.$$id, "-field") : '';
        if (!isVisible(field, data)) {
            return null;
        }
        return (jsxs("div", __assign({ className: cx('ListItem-field') }, { children: [field && field.label ? (jsx("label", __assign({ className: cx('ListItem-fieldLabel', field.labelClassName) }, { children: field.label }))) : null, render(region, __assign(__assign({}, field), { field: field, $$id: $$id, type: 'list-item-field' }), {
                    rowIndex: itemIndex,
                    colIndex: key,
                    className: cx('ListItem-fieldValue', filterClassNameObject(field.className, data)),
                    // 同 Cell 一样， 这里不要下发 value
                    // value: field.name ? resolveVariable(field.name, data) : undefined,
                    onAction: this.handleAction,
                    onQuickChange: this.handleQuickChange
                })] }), key));
    };
    ListItem.prototype.renderBody = function () {
        var _this = this;
        var body = this.props.body;
        if (!body) {
            return null;
        }
        else if (Array.isArray(body)) {
            return body.map(function (child, index) {
                return _this.renderChild(__assign({ type: 'plain' }, (typeof child === 'string' ? { type: 'tpl', tpl: child } : child)), "body/".concat(index), index);
            });
        }
        return this.renderChild(body, 'body');
    };
    ListItem.prototype.render = function () {
        var _a = this.props, className = _a.className, data = _a.data, avatarTpl = _a.avatar, titleTpl = _a.title, titleClassName = _a.titleClassName, subTitleTpl = _a.subTitle, descTpl = _a.desc, avatarClassName = _a.avatarClassName, render = _a.render, cx = _a.classnames, actionsPosition = _a.actionsPosition, itemAction = _a.itemAction, onEvent = _a.onEvent, hasClickActions = _a.hasClickActions, itemIndex = _a.itemIndex, indexBarOffset = _a.indexBarOffset, itemRef = _a.itemRef, item = _a.item;
        var avatar = filter(avatarTpl, data);
        var title = filter(titleTpl, data);
        var subTitle = filter(subTitleTpl, data);
        var desc = filter(descTpl, data);
        return (jsxs("div", __assign({ "data-index": itemIndex, onClick: this.handleClick, className: cx("ListItem ListItem--actions-at-".concat(actionsPosition || 'right'), {
                'ListItem--hasItemAction': itemAction || hasClickActions
            }, className), style: {
                scrollMarginTop: indexBarOffset !== undefined
                    ? "".concat(indexBarOffset, "px")
                    : 'var(--affix-offset-top)'
            }, ref: function (ref) {
                return itemRef && itemIndex !== undefined && itemRef(itemIndex, item, ref);
            } }, { children: [this.renderLeft(), this.renderRight(), avatar ? (jsx("span", __assign({ className: cx('ListItem-avatar', avatarClassName) }, { children: jsx("img", { src: avatar, alt: "..." }) }))) : null, jsxs("div", __assign({ className: cx('ListItem-content') }, { children: [title ? (jsx("p", __assign({ className: cx('ListItem-title', titleClassName) }, { children: title }))) : null, subTitle ? (jsx("div", { children: jsx("small", __assign({ className: cx('ListItem-subtitle') }, { children: subTitle })) })) : null, desc ? render('description', desc) : null, this.renderBody()] }))] })));
    };
    ListItem.defaultProps = {
        avatarClassName: 'thumb-sm avatar m-r',
        titleClassName: 'h5'
    };
    ListItem.propsList = [
        'avatarClassName',
        'titleClassName',
        'itemAction'
    ];
    return ListItem;
}(React.Component));
var ListItemRenderer = /** @class */ (function (_super) {
    __extends(ListItemRenderer, _super);
    function ListItemRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ListItemRenderer.propsList = __spreadArray(['multiple'], ListItem.propsList, true);
    ListItemRenderer = __decorate([
        Renderer({
            type: 'list-item'
        })
    ], ListItemRenderer);
    return ListItemRenderer;
}(ListItem));
var ListItemFieldRenderer = /** @class */ (function (_super) {
    __extends(ListItemFieldRenderer, _super);
    function ListItemFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ListItemFieldRenderer.prototype.render = function () {
        var _a = this.props, className = _a.className, render = _a.render, style = _a.style, Component = _a.wrapperComponent, contentsOnly = _a.contentsOnly, labelClassName = _a.labelClassName, value = _a.value, data = _a.data, children = _a.children, width = _a.width, innerClassName = _a.innerClassName, label = _a.label, tabIndex = _a.tabIndex, onKeyUp = _a.onKeyUp, field = _a.field, rest = __rest(_a, ["className", "render", "style", "wrapperComponent", "contentsOnly", "labelClassName", "value", "data", "children", "width", "innerClassName", "label", "tabIndex", "onKeyUp", "field"]);
        var schema = __assign(__assign({}, field), { className: innerClassName, type: (field && field.type) || 'plain' });
        var body = children
            ? children
            : render('field', schema, __assign(__assign({}, omit(rest, Object.keys(schema))), { value: value, data: data }));
        if (width) {
            style = style || {};
            style.width = style.width || width;
            body = (jsx("div", __assign({ style: { width: !/%/.test(String(width)) ? width : '' } }, { children: body })));
        }
        if (contentsOnly) {
            return body;
        }
        Component = Component || 'div';
        return (jsx(Component, __assign({ style: style, className: className, tabIndex: tabIndex, onKeyUp: onKeyUp }, { children: body })));
    };
    ListItemFieldRenderer.defaultProps = __assign(__assign({}, TableCell.defaultProps), { wrapperComponent: 'div' });
    ListItemFieldRenderer.propsList = __spreadArray([
        'quickEdit',
        'quickEditEnabledOn',
        'popOver',
        'copyable',
        'inline'
    ], TableCell.propsList, true);
    ListItemFieldRenderer = __decorate([
        Renderer({
            type: 'list-item-field'
        }),
        HocQuickEdit(),
        HocPopOver(),
        HocCopyable()
    ], ListItemFieldRenderer);
    return ListItemFieldRenderer;
}(TableCell));

export { ListItem, ListItemFieldRenderer, ListItemRenderer, ListRenderer, List as default };
