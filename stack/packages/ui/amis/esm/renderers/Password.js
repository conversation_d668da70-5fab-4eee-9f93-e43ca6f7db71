/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsxs, jsx } from 'react/jsx-runtime';
import React from 'react';
import { autobind, Renderer } from 'amis-core';
import { Icon } from 'amis-ui';

var PasswordField = /** @class */ (function (_super) {
    __extends(PasswordField, _super);
    function PasswordField() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.state = {
            visible: false
        };
        return _this;
    }
    PasswordField.prototype.toggleVisible = function () {
        this.setState({
            visible: !this.state.visible
        });
    };
    PasswordField.prototype.render = function () {
        var _a = this.props, cx = _a.classnames, className = _a.className, style = _a.style, _b = _a.mosaicText, mosaicText = _b === void 0 ? '********' : _b, value = _a.value;
        return (jsxs("span", __assign({ className: cx('Password-field', className), style: style }, { children: [this.state.visible ? value : mosaicText, this.state.visible
                    ? jsx(Icon, { icon: "view", className: "icon", onClick: this.toggleVisible })
                    : jsx(Icon, { icon: "invisible", className: "icon", onClick: this.toggleVisible })] })));
    };
    __decorate([
        autobind
    ], PasswordField.prototype, "toggleVisible", null);
    return PasswordField;
}(React.Component));
var PasswordFieldRenderer = /** @class */ (function (_super) {
    __extends(PasswordFieldRenderer, _super);
    function PasswordFieldRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PasswordFieldRenderer = __decorate([
        Renderer({
            type: 'password'
        })
    ], PasswordFieldRenderer);
    return PasswordFieldRenderer;
}(PasswordField));

export { PasswordField, PasswordFieldRenderer };
