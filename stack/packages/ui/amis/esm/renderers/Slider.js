/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __rest, __assign, __decorate } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import { autobind, Render<PERSON> } from 'amis-core';
import { Slider } from 'amis-ui';
import React from 'react';

var SliderRenderer = /** @class */ (function (_super) {
    __extends(SliderRenderer, _super);
    function SliderRenderer() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.state = {
            leftShow: false,
            rightShow: false
        };
        return _this;
    }
    SliderRenderer.prototype.handleLeftShow = function () {
        this.props.dispatchEvent('leftShow', this.props.data);
    };
    SliderRenderer.prototype.handleRightShow = function () {
        this.props.dispatchEvent('rightShow', this.props.data);
    };
    SliderRenderer.prototype.handleLeftHide = function () {
        this.props.dispatchEvent('leftHide', this.props.data);
    };
    SliderRenderer.prototype.handleRightHide = function () {
        this.props.dispatchEvent('rightHide', this.props.data);
    };
    SliderRenderer.prototype.showLeft = function () {
        this.setState({
            leftShow: true
        });
    };
    SliderRenderer.prototype.hideLeft = function () {
        this.setState({
            leftShow: false
        });
    };
    SliderRenderer.prototype.showRight = function () {
        this.setState({
            rightShow: true
        });
    };
    SliderRenderer.prototype.hideRight = function () {
        this.setState({
            rightShow: false
        });
    };
    SliderRenderer.prototype.render = function () {
        var _a = this.props, render = _a.render, body = _a.body, left = _a.left, right = _a.right, env = _a.env, rest = __rest(_a, ["render", "body", "left", "right", "env"]);
        var _b = this.state, leftShow = _b.leftShow, rightShow = _b.rightShow;
        return (jsx(Slider, __assign({ body: render('body', body, __assign({}, rest.data)), left: left && render('left', left, __assign({}, rest.data)), right: right && render('right', right, __assign({}, rest.data)), showLeft: leftShow, showRight: rightShow, onLeftShow: this.handleLeftShow, onRightShow: this.handleRightShow, onLeftHide: this.handleLeftHide, onRightHide: this.handleRightHide }, rest)));
    };
    __decorate([
        autobind
    ], SliderRenderer.prototype, "handleLeftShow", null);
    __decorate([
        autobind
    ], SliderRenderer.prototype, "handleRightShow", null);
    __decorate([
        autobind
    ], SliderRenderer.prototype, "handleLeftHide", null);
    __decorate([
        autobind
    ], SliderRenderer.prototype, "handleRightHide", null);
    SliderRenderer = __decorate([
        Renderer({
            type: 'slider'
        })
    ], SliderRenderer);
    return SliderRenderer;
}(React.Component));

export { SliderRenderer };
