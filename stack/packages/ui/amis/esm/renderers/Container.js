/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsx, jsxs } from 'react/jsx-runtime';
import React from 'react';
import merge from 'lodash/merge';
import { isPureVariable, resolveVariableAndFilter, setThemeClassName, buildStyle, CustomStyle, autobind, Renderer } from 'amis-core';
import { DndContainer } from 'amis-ui';

var Container = /** @class */ (function (_super) {
    __extends(Container, _super);
    function Container() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Container.prototype.handleClick = function (e) {
        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
        dispatchEvent(e, data);
    };
    Container.prototype.handleMouseEnter = function (e) {
        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
        dispatchEvent(e, data);
    };
    Container.prototype.handleMouseLeave = function (e) {
        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
        dispatchEvent(e, data);
    };
    Container.prototype.renderBody = function () {
        var _a = this.props, children = _a.children, body = _a.body, render = _a.render, cx = _a.classnames, bodyClassName = _a.bodyClassName, disabled = _a.disabled, wrapperBody = _a.wrapperBody, testIdBuilder = _a.testIdBuilder;
        var isWrapperBody = wrapperBody !== null && wrapperBody !== void 0 ? wrapperBody : true;
        var containerBody = children
            ? typeof children === 'function'
                ? children(this.props)
                : children
            : render('body', body ? body : [], { disabled: disabled });
        if (isWrapperBody) {
            return (jsx("div", __assign({ className: cx('Container-body', bodyClassName) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId(), { children: containerBody })));
        }
        else {
            return containerBody;
        }
    };
    Container.prototype.render = function () {
        var _a = this.props, className = _a.className, wrapperComponent = _a.wrapperComponent, size = _a.size, cx = _a.classnames, style = _a.style, data = _a.data, draggable = _a.draggable, draggableConfig = _a.draggableConfig, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env, themeCss = _a.themeCss, baseControlClassName = _a.baseControlClassName;
        var finalDraggable = isPureVariable(draggable)
            ? resolveVariableAndFilter(draggable, data, '| raw')
            : draggable;
        var finalDraggableConfig = merge(Container.defaultProps.draggableConfig, isPureVariable(draggableConfig)
            ? resolveVariableAndFilter(draggableConfig, data, '| raw')
            : draggableConfig);
        var Component = wrapperComponent || 'div';
        var contentDom = (jsxs(Component, __assign({ className: cx('Container', size && size !== 'none' ? "Container--".concat(size) : '', className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), onClick: this.handleClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave, style: buildStyle(style, data), "data-id": id, "data-role": "container" }, { children: [this.renderBody(), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            }
                        ]
                    }, env: env }))] })));
        return finalDraggable ? (jsx(DndContainer, __assign({}, finalDraggableConfig, { draggable: true }, { children: contentDom }))) : (contentDom);
    };
    Container.propsList = ['body', 'className'];
    Container.defaultProps = {
        className: '',
        draggableConfig: {
            axis: 'both',
            scale: 1,
            enableUserSelect: false
        }
    };
    __decorate([
        autobind
    ], Container.prototype, "handleClick", null);
    __decorate([
        autobind
    ], Container.prototype, "handleMouseEnter", null);
    __decorate([
        autobind
    ], Container.prototype, "handleMouseLeave", null);
    return Container;
}(React.Component));
var ContainerRenderer = /** @class */ (function (_super) {
    __extends(ContainerRenderer, _super);
    function ContainerRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ContainerRenderer = __decorate([
        Renderer({
            type: 'container'
        })
    ], ContainerRenderer);
    return ContainerRenderer;
}(Container));

export { ContainerRenderer, Container as default };
