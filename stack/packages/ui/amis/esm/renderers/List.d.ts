import React from 'react';
import Sortable from 'sortablejs';
import { ScopedContext, BaseSchemaWithoutType } from 'amis-core';
import { SpinnerExtraProps } from 'amis-ui';
import { IListStore, RendererProps, SchemaNode, ActionObject } from 'amis-core';
import { SchemaQuickEdit } from './QuickEdit';
import { SchemaPopOver } from './PopOver';
import { TableCell } from './Table';
import { SchemaCopyable } from './Copyable';
import { BaseSchema, SchemaClassName, SchemaCollection, SchemaExpression, SchemaObject, SchemaTokenizeableString, SchemaTpl, SchemaUrlPath } from '../Schema';
import { ActionSchema } from './Action';
import { SchemaRemark } from './Remark';
import type { IItem, IScopedContext } from 'amis-core';
import type { OnEventProps } from 'amis-core';
/**
 * 不指定类型默认就是文本
 */
export type ListBodyFieldObject = {
    /**
     * 列标题
     */
    label?: string;
    /**
     * label 类名
     */
    labelClassName?: SchemaClassName;
    /**
     * 内层组件的CSS类名
     */
    innerClassName?: SchemaClassName;
    /**
     * 绑定字段名
     */
    name?: string;
    /**
     * 配置查看详情功能
     */
    popOver?: SchemaPopOver;
    /**
     * 配置快速编辑功能
     */
    quickEdit?: SchemaQuickEdit;
    /**
     * 配置点击复制功能
     */
    copyable?: SchemaCopyable;
};
export type ListBodyField = SchemaObject & ListBodyFieldObject;
export interface ListItemSchema extends BaseSchemaWithoutType {
    actions?: Array<ActionSchema>;
    /**
     * 操作位置，默认在右侧，可以设置成左侧。
     */
    actionsPosition?: 'left' | 'right';
    /**
     * 图片地址
     */
    avatar?: SchemaUrlPath;
    /**
     * 内容区域
     */
    body?: Array<ListBodyField | ListBodyFieldObject>;
    /**
     * 描述
     */
    desc?: SchemaTpl;
    /**
     * tooltip 说明
     */
    remark?: SchemaRemark;
    /**
     * 标题
     */
    title?: SchemaTpl;
    /**
     * 副标题
     */
    subTitle?: SchemaTpl;
}
/**
 * List 列表展示控件。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/card
 */
export interface BaseListSchema extends BaseSchema {
    /**
     * 标题
     */
    title?: SchemaTpl;
    /**
     * 底部区域
     */
    footer?: SchemaCollection;
    /**
     * 底部区域类名
     */
    footerClassName?: SchemaClassName;
    /**
     * 顶部区域
     */
    header?: SchemaCollection;
    /**
     * 顶部区域类名
     */
    headerClassName?: SchemaClassName;
    /**
     * 单条数据展示内容配置
     */
    listItem?: ListItemSchema;
    /**
     * 数据源: 绑定当前环境变量
     *
     * @default ${items}
     */
    source?: SchemaTokenizeableString;
    /**
     * 是否显示底部
     */
    showFooter?: boolean;
    /**
     * 是否显示头部
     */
    showHeader?: boolean;
    /**
     * 无数据提示
     *
     * @default 暂无数据
     */
    placeholder?: SchemaTpl;
    /**
     * 是否隐藏勾选框
     */
    hideCheckToggler?: boolean;
    /**
     * 是否固顶
     */
    affixHeader?: boolean;
    /**
     * 是否固底
     */
    affixFooter?: boolean;
    /**
     * 配置某项是否可以点选
     */
    itemCheckableOn?: SchemaExpression;
    /**
     * 配置某项是否可拖拽排序，前提是要开启拖拽功能
     */
    itemDraggableOn?: SchemaExpression;
    /**
     * 点击列表单行时，是否选择
     */
    checkOnItemClick?: boolean;
    /**
     * 可以用来作为值的字段
     */
    valueField?: string;
    /**
     * 大小
     */
    size?: 'sm' | 'base';
    /**
     * 点击列表项的行为
     */
    itemAction?: ActionSchema;
    /**
     * 是否显示右侧字母索引条
     */
    showIndexBar?: boolean;
    /**
     * 索引依据字段
     */
    indexField?: string;
    /**
     * 索引条偏移量
     */
    indexBarOffset?: number;
}
export interface ListSchema extends BaseListSchema {
    /**
     * 指定为 List 列表展示控件。
     */
    type: 'list' | 'static-list';
}
export interface Column {
    type: string;
    [propName: string]: any;
}
export interface ListProps extends RendererProps, Omit<ListSchema, 'type' | 'className'>, SpinnerExtraProps {
    store: IListStore;
    selectable?: boolean;
    selected?: Array<any>;
    draggable?: boolean;
    items?: Array<object>;
    fullItems?: Array<object>;
    onSelect: (selectedItems: Array<object>, unSelectedItems: Array<object>) => void;
    onItemChange?: (item: object, diff: object, rowIndex: string) => void;
    onSave?: (items: Array<object> | object, diff: Array<object> | object, rowIndexes: Array<number> | number, unModifiedItems?: Array<object>, rowOrigins?: Array<object> | object, options?: {
        resetOnFailed?: boolean;
        reload?: string;
    }) => void;
    onSaveOrder?: (moved: Array<object>, items: Array<object>) => void;
    onQuery: (values: object) => any;
}
export interface ListState {
    currentLetter?: string;
}
export default class List extends React.Component<ListProps, ListState> {
    static propsList: Array<keyof ListProps>;
    static defaultProps: Partial<ListProps>;
    dragTip?: HTMLElement;
    sortable?: Sortable;
    parentNode?: any;
    body?: any;
    renderedToolbars: Array<string>;
    private observer;
    itemRefs: Array<{
        element: HTMLElement;
        letter: string;
        isIntersecting?: boolean;
    }>;
    userClick: boolean;
    userClickTimer: any;
    constructor(props: ListProps);
    static syncItems(store: IListStore, props: ListProps, prevProps?: ListProps): boolean;
    componentDidMount(): void;
    componentDidUpdate(prevProps: ListProps): void;
    componentWillUnmount(): void;
    private getIndexDataField;
    bodyRef(ref: HTMLDivElement): void;
    getPopOverContainer(): Element | Text | null;
    handleAction(e: React.UIEvent<any> | undefined, action: ActionObject, ctx: object): any;
    handleCheck(item: IItem): void;
    handleCheckAll(): void;
    syncSelected(): void;
    handleQuickChange(item: IItem, values: object, saveImmediately?: boolean | any, savePristine?: boolean, options?: {
        resetOnFailed?: boolean;
        reload?: string;
    }): void;
    handleSave(): void;
    handleSaveOrder(): void;
    reset(): void;
    bulkUpdate(value: any, items: Array<object>): void;
    getSelected(): any;
    dragTipRef(ref: any): void;
    initDragging(): void;
    destroyDragging(): void;
    renderActions(region: string): import("react/jsx-runtime").JSX.Element | null;
    renderHeading(): import("react/jsx-runtime").JSX.Element | null;
    renderHeader(): import("react/jsx-runtime").JSX.Element | import("react/jsx-runtime").JSX.Element[] | null;
    renderFooter(): import("react/jsx-runtime").JSX.Element | import("react/jsx-runtime").JSX.Element[] | null;
    renderCheckAll(): import("react/jsx-runtime").JSX.Element | null;
    renderDragToggler(): import("react/jsx-runtime").JSX.Element | null;
    renderToolbar(toolbar: SchemaNode, index: number): import("react/jsx-runtime").JSX.Element | null | undefined;
    renderListItem(index: number, template: ListItemSchema | undefined, item: IItem, itemClassName: string): React.ReactNode;
    handleLetterClick(letter: string): void;
    render(): import("react/jsx-runtime").JSX.Element;
    private observeItems;
    scrollObserver(entries: IntersectionObserverEntry[]): void;
    setItemRef(index: number, item: IItem, ref: HTMLElement | null): void;
}
export declare class ListRenderer extends List {
    dragging: boolean;
    selectable: boolean;
    selected: boolean;
    title?: string;
    subTitle?: string;
    desc?: string;
    avatar?: string;
    avatarClassName?: string;
    body?: SchemaNode;
    actions?: Array<ActionObject>;
    onCheck: (item: IItem) => void;
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    context: React.ContextType<typeof ScopedContext>;
    constructor(props: ListProps, scoped: IScopedContext);
    componentWillUnmount(): void;
    receive(values: any, subPath?: string): any;
    reload(subPath?: string, query?: any, ctx?: any, silent?: boolean, replace?: boolean, args?: any): Promise<any>;
    setData(values: any, replace?: boolean, index?: number | string, condition?: any): Promise<any>;
    getData(): any;
    hasModifiedItems(): any;
    doAction(action: ActionObject, ctx: any, throwErrors: boolean, args: any): Promise<any>;
}
export interface ListItemProps extends RendererProps, Omit<ListItemSchema, 'type' | 'className'> {
    hideCheckToggler?: boolean;
    item: IItem;
    itemIndex?: number;
    checkable?: boolean;
    checkOnItemClick?: boolean;
    itemAction?: ActionSchema;
    onEvent?: OnEventProps['onEvent'];
    hasClickActions?: boolean;
    itemRef?: (index: number, item: IItem, ref: HTMLElement | null) => void;
}
export declare class ListItem extends React.Component<ListItemProps> {
    static defaultProps: Partial<ListItemProps>;
    static propsList: Array<string>;
    constructor(props: ListItemProps);
    handleClick(e: React.MouseEvent<HTMLDivElement>): void;
    handleCheck(): void;
    handleAction(e: React.UIEvent<any>, action: ActionObject, ctx: object): void;
    handleQuickChange(values: object, saveImmediately?: boolean, savePristine?: boolean, options?: {
        resetOnFailed?: boolean;
        reload?: string;
    }): void;
    renderLeft(): import("react/jsx-runtime").JSX.Element | null;
    renderRight(): import("react/jsx-runtime").JSX.Element | null;
    renderChild(node: SchemaNode, region?: string, key?: any): React.ReactNode;
    itemRender(field: any, index: number, len: number, props: any): import("react/jsx-runtime").JSX.Element | null;
    renderField(region: string, field: any, key: any, props: any): import("react/jsx-runtime").JSX.Element | null;
    renderBody(): React.ReactNode;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class ListItemRenderer extends ListItem {
    static propsList: string[];
}
export declare class ListItemFieldRenderer extends TableCell {
    static defaultProps: {
        wrapperComponent: string;
    };
    static propsList: string[];
    render(): import("react/jsx-runtime").JSX.Element;
}
//# sourceMappingURL=List.d.ts.map