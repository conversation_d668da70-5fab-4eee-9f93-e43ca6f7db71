/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __extends, __assign, __decorate } from 'tslib';
import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import { SparkLine } from 'amis-ui';
import { createObject, getPropValue, CustomStyle, autobind, Renderer } from 'amis-core';
import React from 'react';

var SparkLineRenderer = /** @class */ (function (_super) {
    __extends(SparkLineRenderer, _super);
    function SparkLineRenderer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    SparkLineRenderer.prototype.handleClick = function (e, ctx) {
        var _a = this.props, disabled = _a.disabled, onAction = _a.onAction, clickAction = _a.clickAction, data = _a.data;
        if (e.defaultPrevented || !clickAction || disabled) {
            return;
        }
        onAction === null || onAction === void 0 ? void 0 : onAction(null, clickAction, ctx ? createObject(data, ctx) : data);
    };
    SparkLineRenderer.prototype.render = function () {
        var _a = this.props, value = _a.value, name = _a.name, clickAction = _a.clickAction, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, env = _a.env, themeCss = _a.themeCss;
        var finalValue = getPropValue(this.props) || [1, 1];
        return (jsxs(Fragment, { children: [jsx(SparkLine, __assign({ onClick: clickAction ? this.handleClick : undefined }, this.props, { value: finalValue })), jsx(CustomStyle, __assign({}, this.props, { config: {
                        wrapperCustomStyle: wrapperCustomStyle,
                        id: id,
                        themeCss: themeCss,
                        classNames: [
                            {
                                key: 'baseControlClassName'
                            }
                        ]
                    }, env: env }))] }));
    };
    __decorate([
        autobind
    ], SparkLineRenderer.prototype, "handleClick", null);
    SparkLineRenderer = __decorate([
        Renderer({
            type: 'sparkline'
        })
    ], SparkLineRenderer);
    return SparkLineRenderer;
}(React.Component));

export { SparkLineRenderer };
