import React from 'react';
import { IScopedContext, RendererEvent } from 'amis-core';
import { RendererProps } from 'amis-core';
import { SchemaNode, ActionObject } from 'amis-core';
import { SpinnerExtraProps } from 'amis-ui';
import { IModalStore } from 'amis-core';
import { BaseSchema, SchemaClassName, SchemaCollection, SchemaName } from '../Schema';
import { ActionSchema } from './Action';
/**
 * Dialog 弹框渲染器。
 * 文档：https://aisuda.bce.baidu.com/amis/zh-CN/components/dialog
 */
export interface DialogSchemaBase extends BaseSchema {
    type: 'dialog';
    /**
     * 弹窗参数说明，值格式为 JSONSchema。
     */
    inputParams?: any;
    /**
     * 默认不用填写，自动会创建确认和取消按钮。
     */
    actions?: Array<ActionSchema>;
    /**
     * 内容区域
     */
    body?: SchemaCollection;
    /**
     * 配置 Body 容器 className
     */
    bodyClassName?: SchemaClassName;
    /**
     * 是否支持按 ESC 关闭 Dialog
     */
    closeOnEsc?: boolean;
    /**
     * 是否支持点其它区域关闭 Dialog
     */
    closeOnOutside?: boolean;
    name?: SchemaName;
    /**
     * Dialog 大小
     */
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
    /**
     * Dialog 高度
     */
    height?: string;
    /**
     * Dialog 宽度
     */
    width?: string;
    /**
     * 请通过配置 title 设置标题
     */
    title?: SchemaCollection;
    header?: SchemaCollection;
    headerClassName?: SchemaClassName;
    footer?: SchemaCollection;
    /**
     * 影响自动生成的按钮，如果自己配置了按钮这个配置无效。
     */
    confirm?: boolean;
    /**
     * 是否显示关闭按钮
     */
    showCloseButton?: boolean;
    /**
     * 是否显示错误信息
     */
    showErrorMsg?: boolean;
    /**
     * 是否显示 spinner
     */
    showLoading?: boolean;
    /**
     * 是否显示蒙层
     */
    overlay?: boolean;
    /**
     * 弹框类型 confirm 确认弹框
     */
    dialogType?: 'confirm';
    /**
     * 可拖拽
     */
    draggable?: boolean;
    /**
     * 可全屏
     */
    allowFullscreen?: boolean;
    /**
     * 数据映射
     */
    data?: {
        [propName: string]: any;
    };
}
export interface DialogSchema extends DialogSchemaBase {
    type: 'dialog';
}
export interface DialogProps extends RendererProps, Omit<DialogSchema, 'className' | 'data'>, SpinnerExtraProps {
    onClose: (confirmed?: boolean) => void;
    onConfirm: (values: Array<object>, action: ActionObject, ctx: object, targets: Array<any>) => void;
    children?: React.ReactNode | ((props?: any) => React.ReactNode);
    store: IModalStore;
    show?: boolean;
    lazyRender?: boolean;
    lazySchema?: (props: DialogProps) => SchemaCollection;
    wrapperComponent: React.ElementType;
}
export default class Dialog extends React.Component<DialogProps> {
    static propsList: Array<string>;
    static defaultProps: {
        title: string;
        bodyClassName: string;
        confirm: boolean;
        show: boolean;
        lazyRender: boolean;
        showCloseButton: boolean;
        wrapperComponent: any;
        closeOnEsc: boolean;
        closeOnOutside: boolean;
        showErrorMsg: boolean;
    };
    reaction: any;
    isDead: boolean;
    $$id: string;
    constructor(props: DialogProps);
    componentWillUnmount(): void;
    buildActions(): Array<ActionSchema>;
    handleSelfScreen(e?: any): void;
    handleSelfClose(e?: any, confirmed?: boolean): Promise<void>;
    handleActionSensor(p: Promise<any>): void;
    handleAction(e: React.UIEvent<any>, action: ActionObject, data: object): void;
    handleDialogConfirm(values: object[], action: ActionObject, ...args: Array<any>): void;
    handleDialogClose(...args: Array<any>): void;
    handleDrawerConfirm(values: object[], action: ActionObject, ...args: Array<any>): void;
    handleDrawerClose(...args: Array<any>): void;
    handleEntered(): void;
    handleExited(): void;
    handleFormInit(data: any): void;
    handleFormChange(data: any, name?: string): void;
    handleFormSaved(data: any, response: any): void;
    handleChildFinished(value: any, action: ActionObject): void;
    openFeedback(dialog: any, ctx: any): Promise<unknown>;
    getPopOverContainer(): Element | null;
    renderBody(body: SchemaNode, key?: any): React.ReactNode;
    renderFooter(): import("react/jsx-runtime").JSX.Element | null;
    render(): import("react/jsx-runtime").JSX.Element;
}
export declare class DialogRenderer extends Dialog {
    static contextType: React.Context<import("amis-core/esm").IScopedContext>;
    clearErrorTimer: ReturnType<typeof setTimeout>;
    constructor(props: DialogProps, context: IScopedContext);
    componentWillUnmount(): void;
    tryChildrenToHandle(action: ActionObject, ctx: object, rawAction?: ActionObject): boolean;
    doAction(action: ActionObject, data: object, throwErrors: boolean): any;
    handleAction(e: React.UIEvent<any> | void, action: ActionObject, data: object, throwErrors?: boolean, delegate?: IScopedContext, rendererEvent?: RendererEvent<any>): Promise<any>;
    handleChildFinished(value: any, action: ActionObject): void;
    handleDialogConfirm(values: object[], action: ActionObject, ...rest: Array<any>): void;
    handleDrawerConfirm(values: object[], action: ActionObject, ...rest: Array<any>): void;
    reloadTarget(target: string, data?: any): void;
    closeTarget(target: string): void;
    setData(values: object, replace?: boolean): any;
    getData(): any;
}
//# sourceMappingURL=Dialog.d.ts.map