/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { __assign } from 'tslib';
import { jsx } from 'react/jsx-runtime';
import { extendDefaultEnv, render, addRootWrapper, themeable, LazyComponent } from 'amis-core';
import { HTMLFilterContext, ImageGallery } from 'amis-ui';
import { alert, confirm, setRenderSchemaFn } from 'amis-ui/lib/components/Alert';
import { toast } from 'amis-ui/lib/components/Toast';

extendDefaultEnv({
    alert: alert,
    confirm: confirm,
    notify: function (type, msg, conf) {
        return toast[type] ? toast[type](msg, conf) : console.warn('[Notify]', type, msg);
    }
});
setRenderSchemaFn(function (controls, value, callback, scopeRef, theme) {
    return render({
        name: 'form',
        type: 'form',
        wrapWithPanel: false,
        mode: 'horizontal',
        controls: controls,
        messages: {
            validateFailed: ''
        }
    }, {
        data: value,
        onFinished: callback,
        scopeRef: scopeRef,
        theme: theme
    }, {
        session: 'prompt'
    });
});
addRootWrapper(function (props) {
    var env = props.env, children = props.children;
    return (jsx(HTMLFilterContext.Provider, __assign({ value: env.filterHtml }, { children: jsx(ImageGallery, __assign({ modalContainer: env.getModalContainer }, { children: children })) })));
});
var SimpleSpinner = themeable(function (props) {
    var cx = props.classnames;
    return (jsx("div", __assign({ "data-testid": "spinner", className: cx("Spinner", 'in', props.className) }, { children: jsx("div", { className: cx("Spinner-icon", 'Spinner-icon--default', 'Spinner-icon--sm', props.spinnerClassName) }) })));
});
LazyComponent.defaultProps.placeholder = jsx(SimpleSpinner, {});
