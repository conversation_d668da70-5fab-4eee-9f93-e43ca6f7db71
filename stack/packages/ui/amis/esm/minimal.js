/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

import { registerRenderer } from 'amis-core';
export * from 'amis-core';
import './preset.js';
import './renderers/Form/Static.js';
import './renderers/Form/Editor.js';
import './renderers/Grid.js';
import './compat.js';
import './schemaExtend.js';

/**
 * 只包含 amis 最小集，不引入其他模块
 */
// 注册渲染器
// import './renderers/Action';
registerRenderer({
    type: 'action',
    alias: ['button', 'submit', 'reset'],
    getComponent: function () { return import('./renderers/Action.js'); }
});
// import './renderers/Alert';
registerRenderer({
    type: 'alert',
    getComponent: function () { return import('./renderers/Alert.js'); }
});
// import './renderers/App';
registerRenderer({
    type: 'app',
    getComponent: function () { return import('./renderers/App.js'); }
});
// import './renderers/Avatar';
registerRenderer({
    type: 'avatar',
    getComponent: function () { return import('./renderers/Avatar.js'); }
});
// import './renderers/Remark';
registerRenderer({
    type: 'remark',
    getComponent: function () { return import('./renderers/Remark.js'); }
});
// import './renderers/ButtonGroup';
registerRenderer({
    type: 'button-group',
    getComponent: function () { return import('./renderers/ButtonGroup.js'); }
});
// import './renderers/Form/ButtonToolbar';
registerRenderer({
    type: 'button-toolbar',
    getComponent: function () { return import('./renderers/Form/ButtonToolbar.js'); }
});
// import './renderers/Breadcrumb';
registerRenderer({
    type: 'breadcrumb',
    getComponent: function () { return import('./renderers/Breadcrumb.js'); }
});
// import './renderers/DropDownButton';
registerRenderer({
    type: 'dropdown-button',
    getComponent: function () { return import('./renderers/DropDownButton.js'); }
});
// import './renderers/Calendar';
registerRenderer({
    type: 'calendar',
    getComponent: function () { return import('./renderers/Calendar.js'); }
});
// import './renderers/Collapse';
registerRenderer({
    type: 'collapse',
    getComponent: function () { return import('./renderers/Collapse.js'); }
});
// import './renderers/CollapseGroup';
registerRenderer({
    type: 'collapse-group',
    getComponent: function () { return import('./renderers/CollapseGroup.js'); }
});
// import './renderers/Color';
registerRenderer({
    type: 'color',
    getComponent: function () { return import('./renderers/Color.js'); }
});
// import './renderers/CRUD';
registerRenderer({
    type: 'crud',
    getComponent: function () { return import('./renderers/CRUD.js'); }
});
// import './renderers/CRUD2';
registerRenderer({
    type: 'crud2',
    getComponent: function () { return import('./renderers/CRUD2.js'); }
});
// import './renderers/Pagination';
registerRenderer({
    type: 'pagination',
    alias: ['pager'],
    getComponent: function () { return import('./renderers/Pagination.js'); }
});
// import './renderers/Cards';
registerRenderer({
    type: 'cards',
    getComponent: function () { return import('./renderers/Cards.js'); }
});
// import './renderers/Card';
registerRenderer({
    type: 'card',
    getComponent: function () { return import('./renderers/Card.js'); }
});
// import './renderers/Card2';
registerRenderer({
    type: 'card2',
    getComponent: function () { return import('./renderers/Card2.js'); }
});
// import './renderers/Custom';
registerRenderer({
    type: 'custom',
    getComponent: function () { return import('./renderers/Custom.js'); }
});
// import './renderers/Date';
registerRenderer({
    type: 'date',
    getComponent: function () { return import('./renderers/Date.js'); }
});
registerRenderer({
    type: 'datetime',
    getComponent: function () { return import('./renderers/Date.js'); }
});
registerRenderer({
    type: 'time',
    getComponent: function () { return import('./renderers/Date.js'); }
});
registerRenderer({
    type: 'month',
    getComponent: function () { return import('./renderers/Date.js'); }
});
// import './renderers/Dialog';
registerRenderer({
    type: 'dialog',
    getComponent: function () { return import('./renderers/Dialog.js'); }
});
// import './renderers/Divider';
registerRenderer({
    type: 'divider',
    getComponent: function () { return import('./renderers/Divider.js'); }
});
// import './renderers/Each';
registerRenderer({
    type: 'each',
    getComponent: function () { return import('./renderers/Each.js'); }
});
// import './renderers/Flex';
registerRenderer({
    type: 'flex',
    getComponent: function () { return import('./renderers/Flex.js'); }
});
registerRenderer({
    type: 'shape',
    getComponent: function () { return import('./renderers/Shape.js'); }
});
// import './renderers/Form/ButtonGroupSelect';
registerRenderer({
    type: 'button-group-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/ButtonGroupSelect.js'); }
});
// import './renderers/Form/Control';
registerRenderer({
    type: 'control',
    getComponent: function () { return import('./renderers/Form/Control.js'); }
});
// import './renderers/Form/Hidden';
registerRenderer({
    type: 'hidden',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Hidden.js'); }
});
// import './renderers/Form/InputText';
registerRenderer({
    type: 'input-text',
    alias: [
        'input-password',
        'native-date',
        'native-time',
        'native-number',
        'input-email',
        'input-url'
    ],
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputText.js'); }
});
// import './renderers/Form/InputTag';
registerRenderer({
    type: 'input-tag',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputTag.js'); }
});
// import './renderers/Form/InputNumber';
registerRenderer({
    type: 'input-number',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputNumber.js'); }
});
// import './renderers/Form/Textarea';
registerRenderer({
    type: 'textarea',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Textarea.js'); }
});
// import './renderers/Form/Checkboxes';
registerRenderer({
    type: 'checkboxes',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Checkboxes.js'); }
});
// import './renderers/Form/Checkbox';
registerRenderer({
    type: 'checkbox',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Checkbox.js'); }
});
// import './renderers/Form/InputCity';
registerRenderer({
    type: 'input-city',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputCity.js'); }
});
// import './renderers/Form/ChartRadios';
registerRenderer({
    type: 'chart-radios',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/ChartRadios.js'); }
});
// import './renderers/Form/InputRating';
registerRenderer({
    type: 'input-rating',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputRating.js'); }
});
// import './renderers/Form/Switch';
registerRenderer({
    type: 'switch',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Switch.js'); }
});
// import './renderers/Form/Radios';
registerRenderer({
    type: 'radios',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Radios.js'); }
});
// import './renderers/Form/Radio';
registerRenderer({
    type: 'radio',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Radio.js'); }
});
// import './renderers/Form/JSONSchema';
registerRenderer({
    type: 'json-schema',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/JSONSchema.js'); }
});
// import './renderers/Form/JSONSchemaEditor';
registerRenderer({
    type: 'json-schema-editor',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/JSONSchemaEditor.js'); }
});
// import './renderers/Form/ListSelect';
registerRenderer({
    type: 'list-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/ListSelect.js'); }
});
// import './renderers/Form/LocationPicker';
registerRenderer({
    type: 'location-picker',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/LocationPicker.js'); }
});
// import './renderers/Form/Select';
registerRenderer({
    type: 'select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Select.js'); }
});
registerRenderer({
    type: 'multi-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Select.js'); }
});
// import './renderers/Form/InputDate';
registerRenderer({
    type: 'input-date',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDate.js'); }
});
registerRenderer({
    type: 'input-datetime',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDate.js'); }
});
registerRenderer({
    type: 'input-time',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDate.js'); }
});
registerRenderer({
    type: 'input-month',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDate.js'); }
});
registerRenderer({
    type: 'input-quarter',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDate.js'); }
});
registerRenderer({
    type: 'input-year',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDate.js'); }
});
// import './renderers/Form/InputDateRange';
registerRenderer({
    type: 'input-date-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDateRange.js'); }
});
registerRenderer({
    type: 'input-datetime-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDateRange.js'); }
});
registerRenderer({
    type: 'input-time-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputDateRange.js'); }
});
// import './renderers/Form/InputFormula';
registerRenderer({
    type: 'input-formula',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputFormula.js'); }
});
// import './renderers/Form/InputRepeat';
registerRenderer({
    type: 'input-repeat',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputRepeat.js'); }
});
// import './renderers/Form/InputTree';
registerRenderer({
    type: 'input-tree',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputTree.js'); }
});
// import './renderers/Form/TreeSelect';
registerRenderer({
    type: 'tree-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/TreeSelect.js'); }
});
// import './renderers/Form/InputImage';
registerRenderer({
    type: 'input-image',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputImage.js'); }
});
// import './renderers/Form/InputFile';
registerRenderer({
    type: 'input-file',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputFile.js'); }
});
// import './renderers/Form/UUID';
registerRenderer({
    type: 'uuid',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/UUID.js'); }
});
// import './renderers/Form/MatrixCheckboxes';
registerRenderer({
    type: 'matrix-checkboxes',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/MatrixCheckboxes.js'); }
});
// import './renderers/Form/InputMonthRange';
registerRenderer({
    type: 'input-month-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputMonthRange.js'); }
});
// import './renderers/Form/InputQuarterRange';
registerRenderer({
    type: 'input-quarter-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputQuarterRange.js'); }
});
// import './renderers/Form/InputYearRange';
registerRenderer({
    type: 'input-year-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputYearRange.js'); }
});
// import './renderers/Form/InputRange';
registerRenderer({
    type: 'input-range',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputRange.js'); }
});
// import './renderers/Form/InputArray';
registerRenderer({
    type: 'input-array',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputArray.js'); }
});
// import './renderers/Form/Combo';
registerRenderer({
    type: 'combo',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Combo.js'); }
});
registerRenderer({
    type: 'input-kv',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Combo.js'); }
});
registerRenderer({
    type: 'input-kvs',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Combo.js'); }
});
// import './renderers/Form/ConditionBuilder';
registerRenderer({
    type: 'condition-builder',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/ConditionBuilder.js'); }
});
// import './renderers/Form/InputSubForm';
registerRenderer({
    type: 'input-sub-form',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputSubForm.js'); }
});
// import './renderers/Form/InputExcel';
registerRenderer({
    type: 'input-excel',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputExcel.js'); }
});
// import './renderers/Form/InputRichText';
registerRenderer({
    type: 'input-rich-text',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputRichText.js'); }
});
// registerRenderer({
//   type: 'input-rich-text',
//   getComponent: () => import('./renderers/Form/Editor')
// });
// import './renderers/Form/DiffEditor';
registerRenderer({
    type: 'diff-editor',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/DiffEditor.js'); }
});
// import './renderers/Form/InputColor';
registerRenderer({
    type: 'input-color',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputColor.js'); }
});
// import './renderers/Form/ChainedSelect';
registerRenderer({
    type: 'chained-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/ChainedSelect.js'); }
});
// import './renderers/Form/NestedSelect';
registerRenderer({
    type: 'nested-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/NestedSelect.js'); }
});
// import './renderers/Form/Transfer';
registerRenderer({
    type: 'transfer',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Transfer.js'); }
});
// import './renderers/Form/TransferPicker';
registerRenderer({
    type: 'transfer-picker',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/TransferPicker.js'); }
});
// import './renderers/Form/InputTable';
registerRenderer({
    type: 'input-table',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputTable.js'); }
});
// import './renderers/Form/Picker';
registerRenderer({
    type: 'picker',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Picker.js'); }
});
// import './renderers/Form/IconPicker';
registerRenderer({
    type: 'icon-picker',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/IconPicker.js'); }
});
// import './renderers/Form/IconSelect';
registerRenderer({
    type: 'icon-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/IconSelect.js'); }
});
// import './renderers/Form/Formula';
registerRenderer({
    type: 'formula',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/Formula.js'); }
});
// import './renderers/Form/FieldSet';
registerRenderer({
    type: 'fieldset',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/FieldSet.js'); }
});
// import './renderers/Form/TabsTransfer';
registerRenderer({
    type: 'tabs-transfer',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/TabsTransfer.js'); }
});
// import './renderers/Form/TabsTransferPicker';
registerRenderer({
    type: 'tabs-transfer-picker',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/TabsTransferPicker.js'); }
});
// import './renderers/Form/Group';
registerRenderer({
    type: 'group',
    getComponent: function () { return import('./renderers/Form/Group.js'); }
});
// import './renderers/Form/InputGroup';
registerRenderer({
    type: 'input-group',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputGroup.js'); }
});
// import './renderers/Form/UserSelect';
registerRenderer({
    type: 'users-select',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/UserSelect.js'); }
});
// import './renderers/Form/InputSignature';
registerRenderer({
    type: 'input-signature',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputSignature.js'); }
});
// import './renderers/Form/InputVerificationCode';
registerRenderer({
    type: 'input-verification-code',
    isFormItem: true,
    getComponent: function () { return import('./renderers/Form/InputVerificationCode.js'); }
});
// import './renderers/Grid2D';
registerRenderer({
    type: 'grid-2d',
    getComponent: function () { return import('./renderers/Grid2D.js'); }
});
// import './renderers/HBox';
registerRenderer({
    type: 'hbox',
    getComponent: function () { return import('./renderers/HBox.js'); }
});
// import './renderers/VBox';
registerRenderer({
    type: 'vbox',
    getComponent: function () { return import('./renderers/VBox.js'); }
});
// import './renderers/Image';
registerRenderer({
    type: 'image',
    getComponent: function () { return import('./renderers/Image.js'); }
});
// import './renderers/Images';
registerRenderer({
    type: 'images',
    getComponent: function () { return import('./renderers/Images.js'); }
});
// import './renderers/List';
registerRenderer({
    type: 'list',
    getComponent: function () { return import('./renderers/List.js'); }
});
// import './renderers/Log';
registerRenderer({
    type: 'log',
    getComponent: function () { return import('./renderers/Log.js'); }
});
// import './renderers/Operation';
registerRenderer({
    type: 'operation',
    getComponent: function () { return import('./renderers/Operation.js'); }
});
// import './renderers/Page';
registerRenderer({
    type: 'page',
    getComponent: function () { return import('./renderers/Page.js'); }
});
// import './renderers/PaginationWrapper';
registerRenderer({
    type: 'pagination-wrapper',
    getComponent: function () { return import('./renderers/PaginationWrapper.js'); }
});
// import './renderers/Panel';
registerRenderer({
    type: 'panel',
    getComponent: function () { return import('./renderers/Panel.js'); }
});
// import './renderers/Plain';
registerRenderer({
    type: 'plain',
    alias: ['text'],
    getComponent: function () { return import('./renderers/Plain.js'); }
});
// import './renderers/Property';
registerRenderer({
    type: 'property',
    getComponent: function () { return import('./renderers/Property.js'); }
});
// import './renderers/Portlet';
registerRenderer({
    type: 'portlet',
    getComponent: function () { return import('./renderers/Portlet.js'); }
});
// import './renderers/Spinner';
registerRenderer({
    type: 'spinner',
    getComponent: function () { return import('./renderers/Spinner.js'); }
});
// import './renderers/Table/index';
registerRenderer({
    type: 'table',
    getComponent: function () { return import('./renderers/Table/index.js'); }
});
// import './renderers/Tabs';
registerRenderer({
    type: 'tabs',
    getComponent: function () { return import('./renderers/Tabs.js'); }
});
// import './renderers/Tpl';
registerRenderer({
    type: 'tpl',
    alias: ['html'],
    getComponent: function () { return import('./renderers/Tpl.js'); }
});
// import './renderers/Mapping';
registerRenderer({
    type: 'mapping',
    alias: ['map'],
    getComponent: function () { return import('./renderers/Mapping.js'); }
});
// import './renderers/Progress';
registerRenderer({
    type: 'progress',
    getComponent: function () { return import('./renderers/Progress.js'); }
});
// import './renderers/Status';
registerRenderer({
    type: 'status',
    getComponent: function () { return import('./renderers/Status.js'); }
});
// import './renderers/Json';
registerRenderer({
    type: 'json',
    getComponent: function () { return import('./renderers/Json.js'); }
});
// import './renderers/Link';
registerRenderer({
    type: 'link',
    getComponent: function () { return import('./renderers/Link.js'); }
});
// import './renderers/Wizard';
registerRenderer({
    type: 'wizard',
    getComponent: function () { return import('./renderers/Wizard.js'); }
});
// import './renderers/Chart';
registerRenderer({
    type: 'chart',
    getComponent: function () { return import('./renderers/Chart.js'); }
});
// import './renderers/Container';
registerRenderer({
    type: 'container',
    getComponent: function () { return import('./renderers/Container.js'); }
});
// import './renderers/SwitchContainer';
registerRenderer({
    type: 'switch-container',
    getComponent: function () { return import('./renderers/SwitchContainer.js'); }
});
// import './renderers/SearchBox';
registerRenderer({
    type: 'search-box',
    getComponent: function () { return import('./renderers/SearchBox.js'); }
});
// import './renderers/Service';
registerRenderer({
    type: 'service',
    getComponent: function () { return import('./renderers/Service.js'); }
});
// import './renderers/SparkLine';
registerRenderer({
    type: 'sparkline',
    getComponent: function () { return import('./renderers/SparkLine.js'); }
});
// import './renderers/Video';
registerRenderer({
    type: 'video',
    getComponent: function () { return import('./renderers/Video.js'); }
});
// import './renderers/Audio';
registerRenderer({
    type: 'audio',
    getComponent: function () { return import('./renderers/Audio.js'); }
});
// import './renderers/Nav';
registerRenderer({
    type: 'nav',
    alias: ['navigation'],
    getComponent: function () { return import('./renderers/Nav.js'); }
});
// import './renderers/Number';
registerRenderer({
    type: 'number',
    getComponent: function () { return import('./renderers/Number.js'); }
});
// import './renderers/Tasks';
registerRenderer({
    type: 'tasks',
    getComponent: function () { return import('./renderers/Tasks.js'); }
});
// import './renderers/Drawer';
registerRenderer({
    type: 'drawer',
    getComponent: function () { return import('./renderers/Drawer.js'); }
});
// import './renderers/Wrapper';
registerRenderer({
    type: 'wrapper',
    getComponent: function () { return import('./renderers/Wrapper.js'); }
});
// import './renderers/IFrame';
registerRenderer({
    type: 'iframe',
    getComponent: function () { return import('./renderers/IFrame.js'); }
});
// import './renderers/BarCode';
registerRenderer({
    type: 'barcode',
    getComponent: function () { return import('./renderers/BarCode.js'); }
});
// import './renderers/QRCode';
registerRenderer({
    type: 'qrcode',
    alias: ['qr-code'],
    getComponent: function () { return import('./renderers/QRCode.js'); }
});
// import './renderers/Icon';
registerRenderer({
    type: 'icon',
    getComponent: function () { return import('./renderers/Icon.js'); }
});
// import './renderers/Carousel';
registerRenderer({
    type: 'carousel',
    getComponent: function () { return import('./renderers/Carousel.js'); }
});
// import './renderers/AnchorNav';
registerRenderer({
    type: 'anchor-nav',
    getComponent: function () { return import('./renderers/AnchorNav.js'); }
});
// import './renderers/Steps';
registerRenderer({
    type: 'steps',
    getComponent: function () { return import('./renderers/Steps.js'); }
});
// import './renderers/Timeline';
registerRenderer({
    type: 'timeline',
    getComponent: function () { return import('./renderers/Timeline.js'); }
});
// import './renderers/Markdown';
registerRenderer({
    type: 'markdown',
    getComponent: function () { return import('./renderers/Markdown.js'); }
});
// import './renderers/TableView';
registerRenderer({
    type: 'table-view',
    getComponent: function () { return import('./renderers/TableView.js'); }
});
// import './renderers/Code';
registerRenderer({
    type: 'code',
    getComponent: function () { return import('./renderers/Code.js'); }
});
// import './renderers/WebComponent';
registerRenderer({
    type: 'web-component',
    getComponent: function () { return import('./renderers/WebComponent.js'); }
});
// import './renderers/GridNav';
registerRenderer({
    type: 'grid-nav',
    getComponent: function () { return import('./renderers/GridNav.js'); }
});
// import './renderers/TooltipWrapper';
registerRenderer({
    type: 'tooltip-wrapper',
    getComponent: function () { return import('./renderers/TooltipWrapper.js'); }
});
// import './renderers/Tag';
registerRenderer({
    type: 'tag',
    getComponent: function () { return import('./renderers/Tag.js'); }
});
// import './renderers/Table2/index';
registerRenderer({
    type: 'table2',
    getComponent: function () { return import('./renderers/Table2/index.js'); }
});
registerRenderer({
    type: 'column-toggler',
    getComponent: function () { return import('./renderers/Table2/ColumnToggler.js'); }
});
// import './renderers/Words';
registerRenderer({
    type: 'words',
    getComponent: function () { return import('./renderers/Words.js'); }
});
registerRenderer({
    type: 'tags',
    getComponent: function () { return import('./renderers/Words.js'); }
});
// import './renderers/Password';
registerRenderer({
    type: 'password',
    getComponent: function () { return import('./renderers/Password.js'); }
});
// import './renderers/DateRange';
registerRenderer({
    type: 'date-range',
    getComponent: function () { return import('./renderers/DateRange.js'); }
});
// import './renderers/MultilineText';
registerRenderer({
    type: 'multiline-text',
    getComponent: function () { return import('./renderers/MultilineText.js'); }
});
// import './renderers/OfficeViewer';
registerRenderer({
    type: 'office-viewer',
    getComponent: function () { return import('./renderers/OfficeViewer.js'); }
});
// import './renderers/PdfViewer';
registerRenderer({
    type: 'pdf-viewer',
    getComponent: function () { return import('./renderers/PdfViewer.js'); }
});
// import './renderers/AMIS';
registerRenderer({
    type: 'amis',
    getComponent: function () { return import('./renderers/AMIS.js'); }
});
registerRenderer({
    type: 'slider',
    getComponent: function () { return import('./renderers/Slider.js'); }
});
