/**
 * amis v6.13.0
 * build time: 2025-09-04
 * Copyright 2018-2025 baidu
 */

export * from 'amis-core';
export * from 'amis-ui';
import './minimal.js';
export { registerFilter, registerFormula } from 'amis-formula';
export { availableLanguages as EditorAvailableLanguages } from './renderers/Form/Editor.js';
export { overrideSupportStatic } from './renderers/Form/StaticHoc.js';
import './renderers/icons/mk.js';
export { ICONS, setIconVendor } from './renderers/Form/IconPickerIcons.js';
export { mountIconSpriteToDom, refreshIconList, setRefreshSvgListAction, setSvgIconList, svgIcons } from './renderers/Form/IconSelectStore.js';
