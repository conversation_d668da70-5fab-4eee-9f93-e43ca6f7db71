#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/cross-env@7.0.3/node_modules:/Users/<USER>/workspace/elcube/elcube-backend/untitled/stack/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../cross-env/src/bin/cross-env.js" "$@"
else
  exec node  "$basedir/../cross-env/src/bin/cross-env.js" "$@"
fi
