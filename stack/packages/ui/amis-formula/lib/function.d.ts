import { Evaluator } from './evalutor';
import { FunctionDocMap, FunctionDocItem } from './types';
export declare function registerFunction(name: string, fn: (this: Evaluator, ...args: Array<any>) => any): void;
export declare const functionDocs: FunctionDocMap;
export declare function registerFunctionDoc(groupName: string, item: FunctionDocItem): void;
export declare function bulkRegisterFunctionDoc(fnDocs: {
    name: string;
    description: string;
    example: string;
    params: {
        type: string;
        name: string;
        description: string | null;
    }[];
    returns: {
        type: string;
        description: string | null;
    };
    namespace: string;
}[]): void;
/**
 * 注册公式，并同时注册公式说明
 * @param name
 * @param fn
 * @param fnInfo
 */
export declare function registerFormula(name: string, fn: (this: Evaluator, ...args: Array<any>) => any, fnInfo?: FunctionDocItem): void;
//# sourceMappingURL=function.d.ts.map