/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var evalutor = require('./evalutor.js');
var error = require('./error.js');

function runSequence(arr, fn) {
    return tslib.__awaiter(this, void 0, void 0, function () {
        var result;
        var _this = this;
        return tslib.__generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    result = [];
                    return [4 /*yield*/, arr.reduce(function (promise, item, index) { return tslib.__awaiter(_this, void 0, void 0, function () {
                            var _a, _b;
                            return tslib.__generator(this, function (_c) {
                                switch (_c.label) {
                                    case 0: return [4 /*yield*/, promise];
                                    case 1:
                                        _c.sent();
                                        _b = (_a = result).push;
                                        return [4 /*yield*/, fn(item, index)];
                                    case 2:
                                        _b.apply(_a, [_c.sent()]);
                                        return [2 /*return*/];
                                }
                            });
                        }); }, Promise.resolve())];
                case 1:
                    _a.sent();
                    return [2 /*return*/, result];
            }
        });
    });
}
var AsyncEvaluator = /** @class */ (function (_super) {
    tslib.__extends(AsyncEvaluator, _super);
    function AsyncEvaluator(data, options) {
        return _super.call(this, data, options) || this;
    }
    AsyncEvaluator.prototype.document = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var isString, content;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!ast.body.length) {
                            return [2 /*return*/, undefined];
                        }
                        isString = ast.body.length > 1;
                        return [4 /*yield*/, runSequence(ast.body, function (item) { return tslib.__awaiter(_this, void 0, void 0, function () {
                                var result;
                                return tslib.__generator(this, function (_a) {
                                    result = this.evalute(item);
                                    if (isString && result == null) {
                                        // 不要出现 undefined, null 之类的文案
                                        return [2 /*return*/, ''];
                                    }
                                    return [2 /*return*/, result];
                                });
                            }); })];
                    case 1:
                        content = _a.sent();
                        return [2 /*return*/, content.length === 1 ? content[0] : content.join('')];
                }
            });
        });
    };
    AsyncEvaluator.prototype.filter = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var input, filters, context, filter, fn, argsRes;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.input)];
                    case 1:
                        input = _a.sent();
                        filters = ast.filters.concat();
                        context = {
                            filter: undefined,
                            data: this.context,
                            restFilters: filters
                        };
                        _a.label = 2;
                    case 2:
                        if (!filters.length) return [3 /*break*/, 4];
                        filter = filters.shift();
                        fn = this.filters[filter.name];
                        if (!fn) {
                            throw new Error("filter `".concat(filter.name, "` not exists."));
                        }
                        context.filter = filter;
                        return [4 /*yield*/, runSequence(filter.args, function (item) { return tslib.__awaiter(_this, void 0, void 0, function () {
                                var res;
                                var _this = this;
                                return tslib.__generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            if (!((item === null || item === void 0 ? void 0 : item.type) === 'mixed')) return [3 /*break*/, 2];
                                            return [4 /*yield*/, runSequence(item.body, function (item) {
                                                    return typeof item === 'string' ? item : _this.evalute(item);
                                                })];
                                        case 1:
                                            res = _a.sent();
                                            return [2 /*return*/, res.join('')];
                                        case 2:
                                            if (item.type) {
                                                return [2 /*return*/, this.evalute(item)];
                                            }
                                            _a.label = 3;
                                        case 3: return [2 /*return*/, item];
                                    }
                                });
                            }); })];
                    case 3:
                        argsRes = _a.sent();
                        input = fn.apply(context, [input].concat(argsRes));
                        return [3 /*break*/, 2];
                    case 4: return [2 /*return*/, input];
                }
            });
        });
    };
    AsyncEvaluator.prototype.template = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var args;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, runSequence(ast.body, function (arg) { return _this.evalute(arg); })];
                    case 1:
                        args = _a.sent();
                        return [2 /*return*/, args.join('')];
                }
            });
        });
    };
    // 下标获取
    AsyncEvaluator.prototype.getter = function (ast) {
        var _a;
        return tslib.__awaiter(this, void 0, void 0, function () {
            var host, key;
            return tslib.__generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.host)];
                    case 1:
                        host = _b.sent();
                        return [4 /*yield*/, this.evalute(ast.key)];
                    case 2:
                        key = _b.sent();
                        if (typeof key === 'undefined' && ((_a = ast.key) === null || _a === void 0 ? void 0 : _a.type) === 'variable') {
                            key = ast.key.name;
                        }
                        return [2 /*return*/, host === null || host === void 0 ? void 0 : host[key]];
                }
            });
        });
    };
    // 位操作如 +2 ~3 !
    AsyncEvaluator.prototype.unary = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var value;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.value)];
                    case 1:
                        value = _a.sent();
                        switch (ast.op) {
                            case '+':
                                return [2 /*return*/, +value];
                            case '-':
                                return [2 /*return*/, -value];
                            case '~':
                                return [2 /*return*/, ~value];
                            case '!':
                                return [2 /*return*/, !value];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AsyncEvaluator.prototype.power = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        return [2 /*return*/, Math.pow(this.formatNumber(left), this.formatNumber(right))];
                }
            });
        });
    };
    AsyncEvaluator.prototype.multiply = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        return [2 /*return*/, evalutor.stripNumber(this.formatNumber(left) * this.formatNumber(right))];
                }
            });
        });
    };
    AsyncEvaluator.prototype.divide = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        return [2 /*return*/, evalutor.stripNumber(this.formatNumber(left) / this.formatNumber(right))];
                }
            });
        });
    };
    AsyncEvaluator.prototype.remainder = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        return [2 /*return*/, this.formatNumber(left) % this.formatNumber(right)];
                }
            });
        });
    };
    AsyncEvaluator.prototype.add = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // 如果有一个不是数字就变成字符串拼接
                        if (isNaN(left) || isNaN(right)) {
                            return [2 /*return*/, left + right];
                        }
                        return [2 /*return*/, evalutor.stripNumber(this.formatNumber(left) + this.formatNumber(right))];
                }
            });
        });
    };
    AsyncEvaluator.prototype.minus = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        return [2 /*return*/, evalutor.stripNumber(this.formatNumber(left) - this.formatNumber(right))];
                }
            });
        });
    };
    AsyncEvaluator.prototype.shift = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.formatNumber(this.evalute(ast.right), true)];
                    case 2:
                        right = _a.sent();
                        if (ast.op === '<<') {
                            return [2 /*return*/, left << right];
                        }
                        else if (ast.op == '>>') {
                            return [2 /*return*/, left >> right];
                        }
                        else {
                            return [2 /*return*/, left >>> right];
                        }
                }
            });
        });
    };
    AsyncEvaluator.prototype.lt = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left < right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.gt = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left > right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.le = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left <= right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.ge = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left >= right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.eq = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left == right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.ne = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left != right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.streq = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left === right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.strneq = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        // todo 如果是日期的对比，这个地方可以优化一下。
                        return [2 /*return*/, left !== right];
                }
            });
        });
    };
    AsyncEvaluator.prototype.binary = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left, right;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [4 /*yield*/, this.evalute(ast.right)];
                    case 2:
                        right = _a.sent();
                        if (ast.op === '&') {
                            return [2 /*return*/, left & right];
                        }
                        else if (ast.op === '^') {
                            return [2 /*return*/, left ^ right];
                        }
                        else {
                            return [2 /*return*/, left | right];
                        }
                }
            });
        });
    };
    AsyncEvaluator.prototype.and = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [2 /*return*/, left && this.evalute(ast.right)];
                }
            });
        });
    };
    AsyncEvaluator.prototype.or = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var left;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.left)];
                    case 1:
                        left = _a.sent();
                        return [2 /*return*/, left || this.evalute(ast.right)];
                }
            });
        });
    };
    AsyncEvaluator.prototype.array = function (ast) {
        var _this = this;
        return runSequence(ast.members, function (member) { return _this.evalute(member); });
    };
    AsyncEvaluator.prototype.object = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var object;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        object = {};
                        return [4 /*yield*/, ast.members.reduce(function (promise, _a, index) {
                                var key = _a.key, value = _a.value;
                                return tslib.__awaiter(_this, void 0, void 0, function () {
                                    var objKey, objVal;
                                    return tslib.__generator(this, function (_b) {
                                        switch (_b.label) {
                                            case 0: return [4 /*yield*/, promise];
                                            case 1:
                                                _b.sent();
                                                return [4 /*yield*/, this.evalute(key)];
                                            case 2:
                                                objKey = _b.sent();
                                                return [4 /*yield*/, this.evalute(value)];
                                            case 3:
                                                objVal = _b.sent();
                                                object[objKey] = objVal;
                                                return [2 /*return*/];
                                        }
                                    });
                                });
                            }, Promise.resolve())];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, object];
                }
            });
        });
    };
    AsyncEvaluator.prototype.conditional = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var _a;
            return tslib.__generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.evalute(ast.test)];
                    case 1:
                        if (!(_b.sent())) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.evalute(ast.consequent)];
                    case 2:
                        _a = _b.sent();
                        return [3 /*break*/, 5];
                    case 3: return [4 /*yield*/, this.evalute(ast.alternate)];
                    case 4:
                        _a = _b.sent();
                        _b.label = 5;
                    case 5: return [2 /*return*/, _a];
                }
            });
        });
    };
    AsyncEvaluator.prototype.funcCall = function (ast) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var fnName, fn, args;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        fnName = "fn".concat(ast.identifier);
                        fn = this.functions[fnName] ||
                            this[fnName] ||
                            (this.filters.hasOwnProperty(ast.identifier) &&
                                this.filters[ast.identifier]);
                        if (!fn) {
                            throw new error.FormulaEvalError("".concat(ast.identifier, "\u51FD\u6570\u6CA1\u6709\u5B9A\u4E49"));
                        }
                        args = ast.args;
                        if (!~['IF', 'AND', 'OR', 'XOR', 'IFS'].indexOf(ast.identifier)) return [3 /*break*/, 1];
                        args = args.map(function (a) { return function () { return _this.evalute(a); }; });
                        return [3 /*break*/, 3];
                    case 1: return [4 /*yield*/, runSequence(args, function (a) { return _this.evalute(a); })];
                    case 2:
                        args = _a.sent();
                        _a.label = 3;
                    case 3: return [2 /*return*/, fn.apply(this, args)];
                }
            });
        });
    };
    AsyncEvaluator.prototype.callAnonymousFunction = function (ast, args) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var ctx, result;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ctx = evalutor.createObject(this.contextStack[this.contextStack.length - 1]('&') || {}, {});
                        ast.args.forEach(function (arg) {
                            if (arg.type !== 'variable') {
                                throw new Error('expected a variable as argument');
                            }
                            ctx[arg.name] = args.shift();
                        });
                        this.contextStack.push(function (varName) {
                            return varName === '&' ? ctx : ctx[varName];
                        });
                        return [4 /*yield*/, this.evalute(ast.return)];
                    case 1:
                        result = _a.sent();
                        this.contextStack.pop();
                        return [2 /*return*/, result];
                }
            });
        });
    };
    /**
     * 示例：IF(A, B, C)
     *
     * 如果满足条件A，则返回B，否则返回C，支持多层嵌套IF函数。
     *
     * 也可以用表达式如：A ? B : C
     *
     * @example IF(condition, consequent, alternate)
     * @param {expression} condition - 条件表达式.
     * @param {any} consequent 条件判断通过的返回结果
     * @param {any} alternate 条件判断不通过的返回结果
     * @namespace 逻辑函数
     *
     * @returns {any} 根据条件返回不同的结果
     */
    AsyncEvaluator.prototype.fnIF = function (condition, trueValue, falseValue) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var _a;
            return tslib.__generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, condition()];
                    case 1:
                        if (!(_b.sent())) return [3 /*break*/, 3];
                        return [4 /*yield*/, trueValue()];
                    case 2:
                        _a = _b.sent();
                        return [3 /*break*/, 5];
                    case 3: return [4 /*yield*/, falseValue()];
                    case 4:
                        _a = _b.sent();
                        _b.label = 5;
                    case 5: return [2 /*return*/, _a];
                }
            });
        });
    };
    /**
     * 条件全部符合，返回 true，否则返回 false
     *
     * 示例：AND(语文成绩>80, 数学成绩>80)
     *
     * 语文成绩和数学成绩都大于 80，则返回 true，否则返回 false
     *
     * 也可以直接用表达式如：语文成绩>80 && 数学成绩>80
     *
     * @example AND(expression1, expression2, ...expressionN)
     * @param {...expression} conditions - 条件表达式.
     * @namespace 逻辑函数
     *
     * @returns {boolean}
     */
    AsyncEvaluator.prototype.fnAND = function () {
        var condtions = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            condtions[_i] = arguments[_i];
        }
        return tslib.__awaiter(this, void 0, void 0, function () {
            var _this = this;
            return tslib.__generator(this, function (_a) {
                if (!condtions.length) {
                    return [2 /*return*/, false];
                }
                return [2 /*return*/, condtions.reduce(function (promise, c) { return tslib.__awaiter(_this, void 0, void 0, function () {
                        var result;
                        return tslib.__generator(this, function (_a) {
                            switch (_a.label) {
                                case 0: return [4 /*yield*/, promise];
                                case 1:
                                    result = _a.sent();
                                    if (result) {
                                        return [2 /*return*/, c()];
                                    }
                                    return [2 /*return*/, result];
                            }
                        });
                    }); }, Promise.resolve(true))];
            });
        });
    };
    /**
     * 条件任意一个满足条件，返回 true，否则返回 false
     *
     * 示例：OR(语文成绩>80, 数学成绩>80)
     *
     * 语文成绩和数学成绩任意一个大于 80，则返回 true，否则返回 false
     *
     * 也可以直接用表达式如：语文成绩>80 || 数学成绩>80
     *
     * @example OR(expression1, expression2, ...expressionN)
     * @param {...expression} conditions - 条件表达式.
     * @namespace 逻辑函数
     *
     * @returns {boolean}
     */
    AsyncEvaluator.prototype.fnOR = function () {
        var condtions = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            condtions[_i] = arguments[_i];
        }
        return tslib.__awaiter(this, void 0, void 0, function () {
            var _this = this;
            return tslib.__generator(this, function (_a) {
                if (!condtions.length) {
                    return [2 /*return*/, false];
                }
                return [2 /*return*/, condtions.reduce(function (promise, c) { return tslib.__awaiter(_this, void 0, void 0, function () {
                        var result;
                        return tslib.__generator(this, function (_a) {
                            switch (_a.label) {
                                case 0: return [4 /*yield*/, promise];
                                case 1:
                                    result = _a.sent();
                                    if (result) {
                                        return [2 /*return*/, true];
                                    }
                                    return [2 /*return*/, c()];
                            }
                        });
                    }); }, Promise.resolve(false))];
            });
        });
    };
    /**
     * 异或处理，多个表达式组中存在奇数个真时认为真。
     *
     * @example XOR(condition1, condition2)
     * @param {expression} condition1 - 条件表达式1
     * @param {expression} condition2 - 条件表达式2
     * @namespace 逻辑函数
     *
     * @returns {boolean}
     */
    AsyncEvaluator.prototype.fnXOR = function () {
        var condtions = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            condtions[_i] = arguments[_i];
        }
        return tslib.__awaiter(this, void 0, void 0, function () {
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!condtions.length) {
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, runSequence(condtions, function (c) { return c(); })];
                    case 1: return [2 /*return*/, !!((_a.sent()).filter(function (item) { return item; }).length % 2)];
                }
            });
        });
    };
    /**
     * 判断函数集合，相当于多个 else if 合并成一个。
     *
     * 示例：IFS(语文成绩 > 80, "优秀", 语文成绩 > 60, "良", "继续努力")
     *
     * 如果语文成绩大于 80，则返回优秀，否则判断大于 60 分，则返回良，否则返回继续努力。
     *
     * @example IFS(condition1, result1, condition2, result2,...conditionN, resultN)
     * @param {...any} args - 条件，返回值集合
     * @namespace 逻辑函数
     * @returns {any} 第一个满足条件的结果，没有命中的返回 false。
     */
    AsyncEvaluator.prototype.fnIFS = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        return tslib.__awaiter(this, void 0, void 0, function () {
            var c, v;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (args.length % 2) {
                            args.splice(args.length - 1, 0, function () { return true; });
                        }
                        _a.label = 1;
                    case 1:
                        if (!args.length) return [3 /*break*/, 5];
                        c = args.shift();
                        v = args.shift();
                        return [4 /*yield*/, c()];
                    case 2:
                        if (!_a.sent()) return [3 /*break*/, 4];
                        return [4 /*yield*/, v()];
                    case 3: return [2 /*return*/, _a.sent()];
                    case 4: return [3 /*break*/, 1];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 数组做数据转换，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。
     *
     * @param {Array<any>} arr 数组
     * @param {Function<any>} iterator 箭头函数
     * @namespace 数组
     * @example ARRAYMAP(arr, item => item)
     * @returns {boolean} 结果
     */
    AsyncEvaluator.prototype.fnARRAYMAP = function (value, iterator) {
        var _this = this;
        if (!iterator || iterator.type !== 'anonymous_function') {
            throw new Error('expected an anonymous function get ' + iterator);
        }
        return (Array.isArray(value) ? value : []).reduce(function (promise, item, index) { return tslib.__awaiter(_this, void 0, void 0, function () {
            var arr, _a, _b;
            return tslib.__generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, promise];
                    case 1:
                        arr = _c.sent();
                        _b = (_a = arr).push;
                        return [4 /*yield*/, this.callAnonymousFunction(iterator, [item, index])];
                    case 2:
                        _b.apply(_a, [_c.sent()]);
                        return [2 /*return*/, arr];
                }
            });
        }); }, Promise.resolve([]));
    };
    /**
     * 数据做数据过滤，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。
     * 将第二个箭头函数返回为 false 的成员过滤掉。
     *
     * @param {Array<any>} arr 数组
     * @param {Function<any>} iterator 箭头函数
     * @namespace 数组
     * @example ARRAYFILTER(arr, item => item)
     * @returns {boolean} 结果
     */
    AsyncEvaluator.prototype.fnARRAYFILTER = function (value, iterator) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!iterator || iterator.type !== 'anonymous_function') {
                            throw new Error('expected an anonymous function get ' + iterator);
                        }
                        return [4 /*yield*/, (Array.isArray(value) ? value : []).reduce(function (promise, item, index) { return tslib.__awaiter(_this, void 0, void 0, function () {
                                var arr, hit;
                                return tslib.__generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, promise];
                                        case 1:
                                            arr = _a.sent();
                                            return [4 /*yield*/, this.callAnonymousFunction(iterator, [item, index])];
                                        case 2:
                                            hit = _a.sent();
                                            if (hit) {
                                                arr.push(item);
                                            }
                                            return [2 /*return*/, arr];
                                    }
                                });
                            }); }, Promise.resolve([]))];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    /**
     * 数据做数据查找，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。
     * 找出第二个箭头函数返回为 true 的成员的索引。
     *
     * 示例：
     *
     * ARRAYFINDINDEX([0, 2, false], item => item === 2) 得到 1
     *
     * @param {Array<any>} arr 数组
     * @param {Function<any>} iterator 箭头函数
     * @namespace 数组
     * @example ARRAYFINDINDEX(arr, item => item === 2)
     * @returns {number} 结果
     */
    AsyncEvaluator.prototype.fnARRAYFINDINDEX = function (arr, iterator) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var hitIndex, items, _i, _a, _b, index, item, hit;
            return tslib.__generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!iterator || iterator.type !== 'anonymous_function') {
                            throw new Error('expected an anonymous function get ' + iterator);
                        }
                        hitIndex = -1;
                        items = Array.isArray(arr) ? arr : [];
                        _i = 0, _a = items.entries();
                        _c.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 4];
                        _b = _a[_i], index = _b[0], item = _b[1];
                        return [4 /*yield*/, this.callAnonymousFunction(iterator, [item, index])];
                    case 2:
                        hit = _c.sent();
                        if (hit) {
                            hitIndex = index;
                            return [3 /*break*/, 4];
                        }
                        _c.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, hitIndex];
                }
            });
        });
    };
    /**
     * 数据做数据查找，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。
     * 找出第二个箭头函数返回为 true 的成员。
     *
     * 示例：
     *
     * ARRAYFIND([0, 2, false], item => item === 2) 得到 2
     *
     * @param {Array<any>} arr 数组
     * @param {Function<any>} iterator 箭头函数
     * @namespace 数组
     * @example ARRAYFIND(arr, item => item === 2)
     * @returns {any} 结果
     */
    AsyncEvaluator.prototype.fnARRAYFIND = function (arr, iterator) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var hitItem, items, _i, _a, _b, index, item, hit;
            return tslib.__generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!iterator || iterator.type !== 'anonymous_function') {
                            throw new Error('expected an anonymous function get ' + iterator);
                        }
                        hitItem = undefined;
                        items = Array.isArray(arr) ? arr : [];
                        _i = 0, _a = items.entries();
                        _c.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 4];
                        _b = _a[_i], index = _b[0], item = _b[1];
                        return [4 /*yield*/, this.callAnonymousFunction(iterator, [item, index])];
                    case 2:
                        hit = _c.sent();
                        if (hit) {
                            hitItem = item;
                            return [3 /*break*/, 4];
                        }
                        _c.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, hitItem];
                }
            });
        });
    };
    /**
     * 数据做数据遍历判断，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。
     * 判断第二个箭头函数是否存在返回为 true 的成员。
     *
     * 示例：
     *
     * ARRAYSOME([0, 2, false], item => item === 2) 得到 true
     *
     * @param {Array<any>} arr 数组
     * @param {Function<any>} iterator 箭头函数
     * @namespace 数组
     * @example ARRAYSOME(arr, item => item === 2)
     * @returns {boolean} 结果
     */
    AsyncEvaluator.prototype.fnARRAYSOME = function (arr, iterator) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var result;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!iterator || iterator.type !== 'anonymous_function') {
                            throw new Error('expected an anonymous function get ' + iterator);
                        }
                        return [4 /*yield*/, (Array.isArray(arr) ? arr : []).reduce(function (promise, item, index) { return tslib.__awaiter(_this, void 0, void 0, function () {
                                var prev, hit;
                                return tslib.__generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, promise];
                                        case 1:
                                            prev = _a.sent();
                                            return [4 /*yield*/, this.callAnonymousFunction(iterator, [item, index])];
                                        case 2:
                                            hit = _a.sent();
                                            return [2 /*return*/, prev || hit];
                                    }
                                });
                            }); }, Promise.resolve(false))];
                    case 1:
                        result = _a.sent();
                        return [2 /*return*/, result];
                }
            });
        });
    };
    /**
     * 数据做数据遍历判断，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。
     * 判断第二个箭头函数返回是否都为 true。
     *
     * 示例：
     *
     * ARRAYEVERY([0, 2, false], item => item === 2) 得到 false
     *
     * @param {Array<any>} arr 数组
     * @param {Function<any>} iterator 箭头函数
     * @namespace 数组
     * @example ARRAYEVERY(arr, item => item === 2)
     * @returns {boolean} 结果
     */
    AsyncEvaluator.prototype.fnARRAYEVERY = function (arr, iterator) {
        return tslib.__awaiter(this, void 0, void 0, function () {
            var result;
            var _this = this;
            return tslib.__generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!iterator || iterator.type !== 'anonymous_function') {
                            throw new Error('expected an anonymous function get ' + iterator);
                        }
                        return [4 /*yield*/, (Array.isArray(arr) ? arr : []).reduce(function (promise, item, index) { return tslib.__awaiter(_this, void 0, void 0, function () {
                                var prev, hit;
                                return tslib.__generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0: return [4 /*yield*/, promise];
                                        case 1:
                                            prev = _a.sent();
                                            return [4 /*yield*/, this.callAnonymousFunction(iterator, [item, index])];
                                        case 2:
                                            hit = _a.sent();
                                            return [2 /*return*/, prev && hit];
                                    }
                                });
                            }); }, Promise.resolve(true))];
                    case 1:
                        result = _a.sent();
                        return [2 /*return*/, result];
                }
            });
        });
    };
    return AsyncEvaluator;
}(evalutor.Evaluator));

exports.AsyncEvaluator = AsyncEvaluator;
exports.runSequence = runSequence;
