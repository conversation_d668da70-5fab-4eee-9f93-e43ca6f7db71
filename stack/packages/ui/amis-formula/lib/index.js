/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');
var evalutor = require('./evalutor.js');
var evalutorForAsync = require('./evalutorForAsync.js');
var parser = require('./parser.js');
var lexer = require('./lexer.js');
var filter = require('./filter.js');
var _function = require('./function.js');

function evaluate(astOrString, data, options) {
    var ast = astOrString;
    if (typeof astOrString === 'string') {
        ast = parser.parse(astOrString, options);
    }
    return new evalutor.Evaluator(data, options).evalute(ast);
}
function evaluateForAsync(astOrString, data, options) {
    return tslib.__awaiter(this, void 0, void 0, function () {
        var ast;
        return tslib.__generator(this, function (_a) {
            ast = astOrString;
            if (typeof astOrString === 'string') {
                ast = parser.parse(astOrString, options);
            }
            return [2 /*return*/, new evalutorForAsync.AsyncEvaluator(data, options).evalute(ast)];
        });
    });
}
evalutor.Evaluator.extendDefaultFilters(filter.getFilters());
evalutorForAsync.AsyncEvaluator.setDefaultFilters(filter.getFilters());
function getFunctionsDoc() {
    return tslib.__awaiter(this, void 0, void 0, function () {
        return tslib.__generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, Promise.resolve().then(function() {return new Promise(function(fullfill) {require(['./doc.js', "tslib"], function(mod, tslib) {fullfill(tslib.__importStar(mod))})})})];
                case 1:
                    _a.sent();
                    return [2 /*return*/, Object.entries(_function.functionDocs).map(function (_a) {
                            var k = _a[0], items = _a[1];
                            return ({
                                groupName: k,
                                items: items
                            });
                        })];
            }
        });
    });
}

exports.Evaluator = evalutor.Evaluator;
exports.AsyncEvaluator = evalutorForAsync.AsyncEvaluator;
exports.parse = parser.parse;
exports.lexer = lexer.lexer;
exports.extendsFilters = filter.extendsFilters;
exports.filters = filter.filters;
exports.getFilters = filter.getFilters;
exports.registerFilter = filter.registerFilter;
exports.registerFormula = _function.registerFormula;
exports.registerFunction = _function.registerFunction;
exports.registerFunctionDoc = _function.registerFunctionDoc;
exports.evaluate = evaluate;
exports.evaluateForAsync = evaluateForAsync;
exports.getFunctionsDoc = getFunctionsDoc;
