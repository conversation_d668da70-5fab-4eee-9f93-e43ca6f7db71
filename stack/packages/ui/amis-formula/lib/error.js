/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib = require('tslib');

/**
 * 表达式解析错误
 */
var FormulaEvalError = /** @class */ (function (_super) {
    tslib.__extends(FormulaEvalError, _super);
    function FormulaEvalError(message) {
        var _this = _super.call(this, message) || this;
        _this.name = 'FormulaEvalError';
        return _this;
    }
    return FormulaEvalError;
}(Error));

exports.FormulaEvalError = FormulaEvalError;
