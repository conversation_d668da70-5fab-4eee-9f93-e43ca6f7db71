/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var evalutor = require('./evalutor.js');

var entityMap = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;'
};
var escapeHtml = function (str) {
    return String(str).replace(/[&<>"'\/]/g, function (s) {
        return entityMap[s];
    });
};
/**
 * filter 是历史包袱，不建议使用。因为这是之前的语法，所以在公式解析里面做了兼容。
 * 建议用 ${ LEFT(xxx) } 这种函数调用语法。
 */
var filters = {
    raw: function (input) { return input; },
    html: function (input) {
        if (input == null) {
            return input;
        }
        return escapeHtml(input);
    }
};
function registerFilter(name, fn) {
    filters[name] = fn;
    evalutor.Evaluator.extendDefaultFilters(filters);
}
function extendsFilters(value) {
    Object.assign(filters, value);
    evalutor.Evaluator.extendDefaultFilters(filters);
}
function getFilters() {
    return filters;
}

exports.extendsFilters = extendsFilters;
exports.filters = filters;
exports.getFilters = getFilters;
exports.registerFilter = registerFilter;
