/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

import { __awaiter, __generator } from 'tslib';
import { Evaluator } from './evalutor.js';
export { Evaluator } from './evalutor.js';
import { AsyncEvaluator } from './evalutorForAsync.js';
export { AsyncEvaluator } from './evalutorForAsync.js';
import { parse } from './parser.js';
export { parse } from './parser.js';
export { lexer } from './lexer.js';
import { getFilters } from './filter.js';
export { extendsFilters, filters, getFilters, registerFilter } from './filter.js';
import { functionDocs } from './function.js';
export { registerFormula, registerFunction, registerFunctionDoc } from './function.js';

function evaluate(astOrString, data, options) {
    var ast = astOrString;
    if (typeof astOrString === 'string') {
        ast = parse(astOrString, options);
    }
    return new Evaluator(data, options).evalute(ast);
}
function evaluateForAsync(astOrString, data, options) {
    return __awaiter(this, void 0, void 0, function () {
        var ast;
        return __generator(this, function (_a) {
            ast = astOrString;
            if (typeof astOrString === 'string') {
                ast = parse(astOrString, options);
            }
            return [2 /*return*/, new AsyncEvaluator(data, options).evalute(ast)];
        });
    });
}
Evaluator.extendDefaultFilters(getFilters());
AsyncEvaluator.setDefaultFilters(getFilters());
function getFunctionsDoc() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, import('./doc.js')];
                case 1:
                    _a.sent();
                    return [2 /*return*/, Object.entries(functionDocs).map(function (_a) {
                            var k = _a[0], items = _a[1];
                            return ({
                                groupName: k,
                                items: items
                            });
                        })];
            }
        });
    });
}

export { evaluate, evaluateForAsync, getFunctionsDoc };
