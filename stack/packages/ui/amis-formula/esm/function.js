/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

import { Evaluator } from './evalutor.js';

function registerFunction(name, fn) {
    var _a;
    Evaluator.extendDefaultFunctions((_a = {},
        _a["fn".concat(name)] = fn,
        _a));
}
var functionDocs = {};
function registerFunctionDoc(groupName, item) {
    if (functionDocs[groupName]) {
        functionDocs[groupName].push(item);
    }
    else {
        functionDocs[groupName] = [item];
    }
}
function bulkRegisterFunctionDoc(fnDocs) {
    fnDocs.forEach(function (item) { return registerFunctionDoc(item.namespace || 'Others', item); });
}
/**
 * 注册公式，并同时注册公式说明
 * @param name
 * @param fn
 * @param fnInfo
 */
function registerFormula(name, fn, fnInfo) {
    registerFunction(name, fn);
    fnInfo && registerFunctionDoc(fnInfo.namespace || 'Others', fnInfo);
}

export { bulkRegisterFunctionDoc, functionDocs, registerFormula, registerFunction, registerFunctionDoc };
