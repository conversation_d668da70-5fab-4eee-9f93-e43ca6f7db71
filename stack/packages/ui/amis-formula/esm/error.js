/**
 * amis-formula v6.13.0
 * Copyright 2021-2025 fex
 */

import { __extends } from 'tslib';

/**
 * 表达式解析错误
 */
var FormulaEvalError = /** @class */ (function (_super) {
    __extends(FormulaEvalError, _super);
    function FormulaEvalError(message) {
        var _this = _super.call(this, message) || this;
        _this.name = 'FormulaEvalError';
        return _this;
    }
    return FormulaEvalError;
}(Error));

export { FormulaEvalError };
